// GeoNames location search API controller
import { Request, Response } from 'express';
import { GeoNamesSearchService } from '../../services/geonames/search-service';
import { LocationSearchService } from '../../services/geonames/location-search';
import { GeoNamesSyncService } from '../../services/geonames/sync-service';
import { GeoNamesConfigManager } from '../../config/geonames-scope';
import { cacheBackend } from '../../dal/cache/redisCache';

export class GeoNamesController {
  private searchService = new GeoNamesSearchService();
  private locationSearchService = new LocationSearchService();
  private syncService = new GeoNamesSyncService();
  private configManager = new GeoNamesConfigManager();

  // Public search endpoints
  async searchLocations(req: Request, res: Response): Promise<void> {
    try {
      const {
        q: query,
        lang,
        language,
        limit = 10,
        country,
        lat,
        lng,
        minPopulation
      } = req.query;

      // Support both 'lang' and 'language' parameters, default to 'en'
      const selectedLang = (lang || language || 'en') as string;

      if (!query || typeof query !== 'string' || query.length < 2) {
        res.status(400).json({
          success: false,
          message: 'Query must be at least 2 characters long'
        });
        return;
      }

      console.log(`[GEONAMES] Search request: "${query}" (${selectedLang})`);

      // Check cache first
      const cacheKey = `search:${query}:${selectedLang}:${country || 'all'}:${limit}`;
      const cached = await cacheBackend.get(cacheKey);
      
      if (cached) {
        console.log('[GEONAMES] ✅ Cache HIT for search');
        res.json({ success: true, data: cached });
        return;
      }

      const userCoordinates = (lat && lng) ? {
        lat: parseFloat(lat as string),
        lng: parseFloat(lng as string)
      } : undefined;

      // Use advanced fuzzy search with tourism region support
      const results = await this.locationSearchService.fuzzyLocationSearch(
        query,
        parseInt(limit as string),
        0.25
      );

      // Cache results for 1 hour
      await cacheBackend.set(cacheKey, results, 3600);

      console.log(`[GEONAMES] Search completed: ${results.length} results`);
      
      res.json({ success: true, data: results });

    } catch (error) {
      console.error('[GEONAMES] Search error:', error);
      res.status(500).json({
        success: false,
        message: 'Search failed',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  async autocomplete(req: Request, res: Response): Promise<void> {
    try {
      const {
        q: query,
        lang = 'en',
        limit = 5
      } = req.query;

      if (!query || typeof query !== 'string' || query.length < 2) {
        res.json({ success: true, data: [] });
        return;
      }

      console.log(`[GEONAMES] Autocomplete request: "${query}"`);

      // Check cache first
      const cacheKey = `autocomplete:${query}:${lang}:${limit}`;
      const cached = await cacheBackend.get(cacheKey);
      
      if (cached) {
        res.json({ success: true, data: cached });
        return;
      }

      const results = await this.searchService.autocompleteSearch({
        query,
        language: lang as string,
        limit: parseInt(limit as string)
      });

      // Cache for 2 hours (autocomplete results are more stable)
      await cacheBackend.set(cacheKey, results, 7200);

      res.json({ success: true, data: results });

    } catch (error) {
      console.error('[GEONAMES] Autocomplete error:', error);
      res.status(500).json({
        success: false,
        message: 'Autocomplete failed',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  async getLocationDetails(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { lang = 'en' } = req.query;

      if (!id) {
        res.status(400).json({
          success: false,
          message: 'Location ID is required'
        });
        return;
      }

      console.log(`[GEONAMES] Location details request: ${id}`);

      // Check cache first
      const cacheKey = `location:${id}:${lang}`;
      const cached = await cacheBackend.get(cacheKey);
      
      if (cached) {
        res.json({ success: true, data: cached });
        return;
      }

      const location = await this.searchService.getLocationWithNames(
        id,
        lang as string
      );

      if (!location) {
        res.status(404).json({
          success: false,
          message: 'Location not found'
        });
        return;
      }

      // Cache for 6 hours (location details change rarely)
      await cacheBackend.set(cacheKey, location, 21600);

      res.json({ success: true, data: location });

    } catch (error) {
      console.error('[GEONAMES] Location details error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get location details',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  async getPopularDestinations(req: Request, res: Response): Promise<void> {
    try {
      const {
        country,
        lang = 'en',
        limit = 20
      } = req.query;

      console.log(`[GEONAMES] Popular destinations request: ${country || 'all'}`);

      // Check cache first
      const cacheKey = `popular:${country || 'all'}:${lang}:${limit}`;
      const cached = await cacheBackend.get(cacheKey);
      
      if (cached) {
        res.json({ success: true, data: cached });
        return;
      }

      const destinations = await this.searchService.getPopularDestinations(
        country as string,
        lang as string,
        parseInt(limit as string)
      );

      // Cache for 12 hours (popular destinations are stable)
      await cacheBackend.set(cacheKey, destinations, 43200);

      res.json({ success: true, data: destinations });

    } catch (error) {
      console.error('[GEONAMES] Popular destinations error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get popular destinations',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Admin sync endpoints
  async triggerSync(req: Request, res: Response): Promise<void> {
    try {
      console.log('[GEONAMES] Sync triggered by admin');

      // Start sync in background
      const syncPromise = this.syncService.performFullSync();

      // Return immediate response
      res.json({
        success: true,
        message: 'Sync started in background',
        estimatedDuration: '5-10 minutes for Spain configuration'
      });

      // Handle sync completion in background
      syncPromise
        .then(result => {
          console.log('[GEONAMES] ✅ Background sync completed:', result);
        })
        .catch(error => {
          console.error('[GEONAMES] ❌ Background sync failed:', error);
        });

    } catch (error) {
      console.error('[GEONAMES] Sync trigger error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to start sync',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  async getSyncStatus(req: Request, res: Response): Promise<void> {
    try {
      console.log('[GEONAMES] Sync status requested');

      const status = await this.syncService.getHealthStatus();

      res.json({
        success: true,
        data: status
      });

    } catch (error) {
      console.error('[GEONAMES] Sync status error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get sync status',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  async getConfiguration(req: Request, res: Response): Promise<void> {
    try {
      const config = this.configManager.getConfig();
      
      res.json({
        success: true,
        data: {
          countries: config.countries.map(c => ({
            code: c.code,
            name: c.name,
            languages: c.languages,
            minPopulation: c.minPopulation
          })),
          globalLanguages: config.globalLanguages,
          featureCodes: config.featureCodes,
          supportedLanguages: this.configManager.getAllLanguages(),
          estimatedData: {
            totalLocations: '~8,000 for Spain',
            totalNames: '~25,000 for 4 languages', 
            storageSize: '~10MB',
            syncTime: '5-10 minutes'
          }
        }
      });

    } catch (error) {
      console.error('[GEONAMES] Configuration error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get configuration',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }
}

// Export controller instance
export const geoNamesController = new GeoNamesController();