import React from 'react';
import { useTranslations } from '@/lib/translations';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Clock, Shield, AlertTriangle, Users, FileText, Key } from 'lucide-react';
import { PropertyWizardData } from '../../types/property';

interface PolicyStepProps {
  data: Partial<PropertyWizardData>;
  onUpdate: (data: Partial<PropertyWizardData>) => void;
}

const HOUSE_RULES_OPTIONS = [
  { id: 'no_smoking', labelKey: 'noSmoking', icon: '🚭', popular: true },
  { id: 'no_pets', labelKey: 'noPets', icon: '🐕', popular: true },
  { id: 'no_parties', labelKey: 'noParties', icon: '🎉', popular: true },
  { id: 'no_shoes', labelKey: 'noShoes', icon: '👟', popular: false },
  { id: 'quiet_hours', labelKey: 'quietHours', icon: '🔇', popular: true },
  { id: 'no_children', labelKey: 'noChildren', icon: '👶', popular: false },
  { id: 'check_out_cleaning', labelKey: 'cleanBeforeCheckout', icon: '🧹', popular: false },
  { id: 'respect_neighbors', labelKey: 'respectNeighbors', icon: '🏠', popular: true }
];

const CANCELLATION_POLICIES = [
  {
    id: 'flexible',
    nameKey: 'flexible',
    descriptionKey: 'flexibleDescription',
    guestFriendly: true,
    detailsKey: 'flexibleDetails'
  },
  {
    id: 'moderate',
    nameKey: 'moderate',
    descriptionKey: 'moderateDescription',
    guestFriendly: true,
    detailsKey: 'moderateDetails'
  },
  {
    id: 'strict',
    nameKey: 'strict',
    descriptionKey: 'strictDescription',
    guestFriendly: false,
    detailsKey: 'strictDetails'
  },
  {
    id: 'super_strict',
    nameKey: 'superStrict',
    descriptionKey: 'superStrictDescription',
    guestFriendly: false,
    detailsKey: 'superStrictDetails'
  }
];

const CHECK_IN_TIMES = [
  '14:00', '15:00', '16:00', '17:00', '18:00', 'Flexible'
];

const CHECK_OUT_TIMES = [
  '10:00', '11:00', '12:00', '13:00', 'Flexible'
];

export const PolicyStep = ({ data, onUpdate }: PolicyStepProps) => {
  const t = useTranslations('hostOnboarding.policies');
  
  const selectedRules = data.houseRules || [];
  const cancellationPolicy = data.cancellationPolicy || 'moderate';
  const checkInTime = data.checkInTime || '15:00';
  const checkOutTime = data.checkOutTime || '11:00';
  const touristLicense = data.touristLicense || '';
  const businessRegistration = data.businessRegistration || '';

  const handleRuleToggle = (ruleId: string) => {
    const isSelected = selectedRules.includes(ruleId);
    const newRules = isSelected
      ? selectedRules.filter(id => id !== ruleId)
      : [...selectedRules, ruleId];
    
    onUpdate({ houseRules: newRules });
  };

  const handleCancellationPolicyChange = (policyId: string) => {
    onUpdate({ cancellationPolicy: policyId });
  };

  const handleTimeChange = (field: 'checkInTime' | 'checkOutTime', value: string) => {
    onUpdate({ [field]: value });
  };

  const handleLicenseChange = (field: 'touristLicense' | 'businessRegistration', value: string) => {
    onUpdate({ [field]: value });
  };

  const selectedPolicy = CANCELLATION_POLICIES.find(p => p.id === cancellationPolicy);

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          {t('title')}
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          {t('description')}
        </p>
      </div>

      {/* Spanish Legal Requirements */}
      <Card className="border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-900/20">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-red-800 dark:text-red-200">
            <FileText className="h-5 w-5" />
            <span>{t('legalRequirements')}</span>
            <Badge variant="destructive">Required</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="tourist-license" className="text-red-800 dark:text-red-200">
              {t('touristLicense')} *
            </Label>
            <Input
              id="tourist-license"
              value={touristLicense}
              onChange={(e) => handleLicenseChange('touristLicense', e.target.value)}
              placeholder="VT-XXXXX-A"
              className="mt-1"
            />
            <p className="text-xs text-red-700 dark:text-red-300 mt-1">
              {t('touristLicenseDescription')}
            </p>
          </div>
          
          <div>
            <Label htmlFor="business-registration" className="text-red-800 dark:text-red-200">
              {t('businessRegistration')}
            </Label>
            <Input
              id="business-registration"
              value={businessRegistration}
              onChange={(e) => handleLicenseChange('businessRegistration', e.target.value)}
              placeholder="Optional - for business hosts"
              className="mt-1"
            />
            <p className="text-xs text-red-700 dark:text-red-300 mt-1">
              {t('businessRegistrationDescription')}
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Check-in/Check-out Times */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center space-x-2">
          <Clock className="h-5 w-5" />
          <span>{t('checkInOut')}</span>
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card className="border-gray-200 dark:border-gray-700">
            <CardContent className="p-4">
              <Label htmlFor="check-in" className="font-medium">
                {t('checkInTime')}
              </Label>
              <Select value={checkInTime} onValueChange={(value) => handleTimeChange('checkInTime', value)}>
                <SelectTrigger className="mt-2">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {CHECK_IN_TIMES.map((time) => (
                    <SelectItem key={time} value={time}>
                      {time}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </CardContent>
          </Card>
          
          <Card className="border-gray-200 dark:border-gray-700">
            <CardContent className="p-4">
              <Label htmlFor="check-out" className="font-medium">
                {t('checkOutTime')}
              </Label>
              <Select value={checkOutTime} onValueChange={(value) => handleTimeChange('checkOutTime', value)}>
                <SelectTrigger className="mt-2">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {CHECK_OUT_TIMES.map((time) => (
                    <SelectItem key={time} value={time}>
                      {time}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* House Rules */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center space-x-2">
          <Shield className="h-5 w-5" />
          <span>{t('houseRules')}</span>
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {HOUSE_RULES_OPTIONS.map((rule) => {
            const isSelected = selectedRules.includes(rule.id);
            return (
              <Card
                key={rule.id}
                className={`cursor-pointer transition-all hover:shadow-md ${
                  isSelected
                    ? 'ring-2 ring-primary border-primary bg-primary/5'
                    : 'border-gray-200 dark:border-gray-700'
                }`}
                onClick={() => handleRuleToggle(rule.id)}
              >
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <span className="text-2xl">{rule.icon}</span>
                      <div>
                        <h4 className="font-medium text-gray-900 dark:text-white">
                          {t(rule.labelKey)}
                        </h4>
                        {rule.popular && (
                          <Badge variant="secondary" className="text-xs mt-1">
                            {t('popular')}
                          </Badge>
                        )}
                      </div>
                    </div>
                    <Checkbox checked={isSelected} />
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>

      {/* Cancellation Policy */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center space-x-2">
          <AlertTriangle className="h-5 w-5" />
          <span>{t('cancellationPolicy')}</span>
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {CANCELLATION_POLICIES.map((policy) => {
            const isSelected = cancellationPolicy === policy.id;
            return (
              <Card
                key={policy.id}
                className={`cursor-pointer transition-all hover:shadow-md ${
                  isSelected
                    ? 'ring-2 ring-primary border-primary'
                    : 'border-gray-200 dark:border-gray-700'
                }`}
                onClick={() => handleCancellationPolicyChange(policy.id)}
              >
                <CardContent className="p-4">
                  <div className="text-center">
                    <div className="flex items-center justify-center mb-3">
                      <h4 className="font-medium text-gray-900 dark:text-white">
                        {t(policy.nameKey)}
                      </h4>
                      {policy.guestFriendly && (
                        <Badge variant="default" className="ml-2 text-xs bg-green-600">
                          {t('guestFriendly')}
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                      {t(policy.descriptionKey)}
                    </p>
                    {isSelected && (
                      <div className="text-xs text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-800 p-2 rounded">
                        {t(policy.detailsKey)}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
        
        {selectedPolicy && (
          <Card className="mt-4 border-blue-200 dark:border-blue-800 bg-blue-50 dark:bg-blue-900/20">
            <CardContent className="p-4">
              <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">
                {t('selectedPolicy')}: {t(selectedPolicy.nameKey)}
              </h4>
              <p className="text-sm text-blue-700 dark:text-blue-300">
                {t(selectedPolicy.detailsKey)}
              </p>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Policy Tips */}
      <div className="bg-green-50 dark:bg-green-900/20 p-6 rounded-lg">
        <h4 className="font-medium text-green-800 dark:text-green-200 mb-2">
          {t('policyTips')}
        </h4>
        <ul className="text-sm text-green-700 dark:text-green-300 space-y-1">
          <li>• {t('tip1')}</li>
          <li>• {t('tip2')}</li>
          <li>• {t('tip3')}</li>
          <li>• {t('tip4')}</li>
        </ul>
      </div>

      {/* Validation Status */}
      {!touristLicense && (
        <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg">
          <h4 className="font-medium text-red-800 dark:text-red-200 mb-2">
            {t('validationRequired')}
          </h4>
          <p className="text-sm text-red-700 dark:text-red-300">
            {t('touristLicenseRequired')}
          </p>
        </div>
      )}
    </div>
  );
};