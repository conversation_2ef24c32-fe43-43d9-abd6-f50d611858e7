import { useQuery } from '@tanstack/react-query';
import { useUser } from '@/features/shared/auth/hooks/useAuth';

export const useOverviewData = () => {
  const { data: user } = useUser();

  const { data: hostProperties = [] } = useQuery({
    queryKey: ['/api/properties/host'],
    enabled: !!user,
  });

  const { data: bookings = [] } = useQuery({
    queryKey: ['/api/bookings/host'],
    enabled: !!user,
  });

  const { data: hostMessages = [] } = useQuery({
    queryKey: ['/api/messages/host'],
    enabled: !!user,
  });

  // Cast to proper types
  const propertiesArray = hostProperties as any[];
  const bookingsArray = bookings as any[];
  const messagesArray = hostMessages as any[];

  // Calculate overview metrics
  const totalEarnings = bookingsArray.reduce((sum: number, booking: any) => sum + (booking.total_amount || 0), 0);
  const activeBookings = bookingsArray.filter((b: any) => b.status === 'confirmed' || b.status === 'active').length;
  const occupancyRate = propertiesArray.length > 0 ? 
    ((activeBookings / propertiesArray.length) * 100).toFixed(1) : 0;
  const unreadMessages = messagesArray.filter((m: any) => !m.read).length;

  return {
    hostProperties: propertiesArray,
    bookings: bookingsArray,
    hostMessages: messagesArray,
    user,
    metrics: {
      totalEarnings,
      activeBookings,
      occupancyRate,
      unreadMessages,
      totalProperties: propertiesArray.length,
      totalBookings: bookingsArray.length
    }
  };
};