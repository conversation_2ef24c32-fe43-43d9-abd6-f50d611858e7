import { useState, useEffect } from 'react';
import { useLocale } from './i18n';

type TranslationValues = Record<string, any>;
type TranslationFunction = (key: string, values?: TranslationValues) => string;

// Simple in-memory cache
const translationCache = new Map<string, Record<string, any>>();

// Get nested value from object using dot notation
function getNestedValue(obj: any, path: string): any {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined;
  }, obj);
}

// Format translation with values
function formatTranslation(template: string, values?: TranslationValues): string {
  if (!values) return template;
  
  return template.replace(/\{(\w+)\}/g, (match, key) => {
    return values[key] !== undefined ? String(values[key]) : match;
  });
}

// Load translations from API
async function loadTranslations(locale: string): Promise<Record<string, any>> {
  try {
    // Check simple cache first
    const cacheKey = `translations_${locale}`;
    if (translationCache.has(cacheKey)) {
      console.log(`Using cached translations for locale: ${locale}`);
      return translationCache.get(cacheKey)!;
    }

    console.log(`Fetching fresh translations for locale: ${locale}`);
    
    // Fetch from API
    const response = await fetch(`/api/translations/${locale}`, {
      headers: {
        'Cache-Control': 'no-cache'
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to load translations for ${locale}: ${response.status}`);
    }
    
    const translations = await response.json();
    
    // Store in simple cache
    translationCache.set(cacheKey, translations);
    
    return translations;
  } catch (error) {
    console.error('Failed to load translations:', error);
    return {};
  }
}

// Hook to get translation function for a specific namespace
export function useTranslations(namespace?: string): TranslationFunction {
  const { locale } = useLocale();
  const [translations, setTranslations] = useState<Record<string, any>>({});
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadTranslationData = async () => {
      setIsLoading(true);
      try {
        const translationData = await loadTranslations(locale);
        setTranslations(translationData);
      } catch (error) {
        console.error('Error loading translations:', error);
        setTranslations({});
      } finally {
        setIsLoading(false);
      }
    };

    loadTranslationData();
  }, [locale]);

  const t: TranslationFunction = (key: string, values?: TranslationValues) => {
    if (isLoading) return key; // Return key while loading
    
    // Build the full key path
    const fullKey = namespace ? `${namespace}.${key}` : key;
    
    // Get the translation
    const translation = getNestedValue(translations, fullKey);
    
    // Return formatted translation or fallback to key
    if (translation !== undefined) {
      return formatTranslation(String(translation), values);
    }
    
    // Fallback to key if translation not found
    console.warn(`Translation not found: ${fullKey} (locale: ${locale})`);
    return key;
  };

  return t;
}

// Clear translation cache (useful for development)
export function clearTranslationCache() {
  translationCache.clear();
  console.log('Translation cache cleared');
}