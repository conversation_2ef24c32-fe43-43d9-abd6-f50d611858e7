import { useState } from 'react';
import { format } from 'date-fns';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { 
  MoreVertical, 
  Edit2, 
  Trash2, 
  Flag, 
  Check, 
  X,
  FileText
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useTranslations } from '@/lib/translations';

interface MessageBubbleProps {
  message: {
    id: string;
    sender_id: string;
    sender_type: 'guest' | 'host';
    content: string;
    message_type: 'text' | 'template' | 'system' | 'automated';
    message_status: 'sent' | 'delivered' | 'read';
    created_at: string;
    edited_at?: string;
    sender?: {
      id: string;
      name: string;
      avatar_url?: string;
      user_type: string;
    };
  };
  isOwn: boolean;
  showAvatar?: boolean;
  onEdit?: (messageId: string, newContent: string) => Promise<void>;
  onDelete?: (messageId: string) => Promise<void>;
  onFlag?: (messageId: string, reason: string) => Promise<void>;
}

export const MessageBubble = ({ 
  message, 
  isOwn, 
  showAvatar = true,
  onEdit,
  onDelete,
  onFlag
}: MessageBubbleProps) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState(message.content);
  const [showActions, setShowActions] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const t = useTranslations('messaging');
  
  const handleEdit = async () => {
    if (!onEdit || editContent.trim() === message.content) {
      setIsEditing(false);
      return;
    }
    
    try {
      await onEdit(message.id, editContent.trim());
      setIsEditing(false);
    } catch (error) {
      console.error('Failed to edit message:', error);
    }
  };
  
  const handleDelete = async () => {
    if (!onDelete) return;
    
    try {
      setIsDeleting(true);
      await onDelete(message.id);
    } catch (error) {
      console.error('Failed to delete message:', error);
      setIsDeleting(false);
    }
  };
  
  const handleFlag = async () => {
    if (!onFlag) return;
    
    try {
      await onFlag(message.id, 'inappropriate');
    } catch (error) {
      console.error('Failed to flag message:', error);
    }
  };
  
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };
  
  const formatTime = (dateString: string) => {
    return format(new Date(dateString), 'HH:mm');
  };
  
  if (isDeleting) {
    return (
      <div className={cn(
        "flex gap-2 mb-3",
        isOwn ? "justify-end" : "justify-start"
      )}>
        <div className="bg-muted/50 rounded-lg p-3 max-w-xs">
          <p className="text-sm text-muted-foreground italic">
            {t('messageDeleted')}
          </p>
        </div>
      </div>
    );
  }
  
  return (
    <div className={cn(
      "flex gap-2 mb-3 group",
      isOwn ? "justify-end" : "justify-start"
    )}>
      {/* Avatar (for other's messages) */}
      {!isOwn && showAvatar && (
        <Avatar className="h-8 w-8 flex-shrink-0">
          <AvatarImage src={message.sender?.avatar_url} />
          <AvatarFallback className="bg-primary text-primary-foreground text-xs">
            {message.sender ? getInitials(message.sender.name) : '?'}
          </AvatarFallback>
        </Avatar>
      )}
      
      {/* Message Bubble */}
      <div className={cn(
        "relative max-w-xs lg:max-w-md",
        isOwn && "order-first"
      )}>
        <Card className={cn(
          "p-3",
          isOwn 
            ? "bg-primary text-primary-foreground" 
            : "bg-background border",
          message.message_type === 'system' && "bg-muted border-dashed",
          message.message_type === 'template' && "border-accent"
        )}>
          {/* Message Type Indicator */}
          {message.message_type === 'template' && (
            <div className="flex items-center gap-1 mb-2 opacity-75">
              <FileText className="h-3 w-3" />
              <span className="text-xs">{t('templateMessage')}</span>
            </div>
          )}
          
          {/* Message Content */}
          {isEditing ? (
            <div className="space-y-2">
              <Textarea
                value={editContent}
                onChange={(e) => setEditContent(e.target.value)}
                className="min-h-[60px] text-sm"
                rows={3}
              />
              <div className="flex gap-1 justify-end">
                <Button 
                  size="sm" 
                  variant="ghost"
                  onClick={() => setIsEditing(false)}
                >
                  <X className="h-3 w-3" />
                </Button>
                <Button 
                  size="sm" 
                  onClick={handleEdit}
                  disabled={!editContent.trim()}
                >
                  <Check className="h-3 w-3" />
                </Button>
              </div>
            </div>
          ) : (
            <div>
              <p className="text-sm whitespace-pre-wrap break-words">
                {message.content}
              </p>
              
              {/* Message Info */}
              <div className={cn(
                "flex items-center gap-2 mt-2 text-xs opacity-75",
                isOwn ? "justify-start" : "justify-end"
              )}>
                <span>{formatTime(message.created_at)}</span>
                
                {message.edited_at && (
                  <Badge variant="secondary" className="text-xs px-1 py-0">
                    {t('edited')}
                  </Badge>
                )}
                
                {isOwn && (
                  <div className="flex items-center gap-1">
                    {message.message_status === 'sent' && (
                      <Check className="h-3 w-3" />
                    )}
                    {message.message_status === 'delivered' && (
                      <div className="flex">
                        <Check className="h-3 w-3" />
                        <Check className="h-3 w-3 -ml-1" />
                      </div>
                    )}
                    {message.message_status === 'read' && (
                      <div className="flex text-blue-500">
                        <Check className="h-3 w-3" />
                        <Check className="h-3 w-3 -ml-1" />
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}
        </Card>
        
        {/* Actions Menu */}
        {!isEditing && (
          <div className={cn(
            "absolute top-0 opacity-0 group-hover:opacity-100 transition-opacity",
            isOwn ? "-left-8" : "-right-8"
          )}>
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0"
              onClick={() => setShowActions(!showActions)}
            >
              <MoreVertical className="h-3 w-3" />
            </Button>
            
            {showActions && (
              <Card className="absolute z-10 mt-1 p-1 shadow-lg">
                <div className="flex flex-col gap-1">
                  {isOwn && onEdit && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="justify-start h-8 px-2"
                      onClick={() => {
                        setIsEditing(true);
                        setShowActions(false);
                      }}
                    >
                      <Edit2 className="h-3 w-3 mr-2" />
                      {t('edit')}
                    </Button>
                  )}
                  
                  {isOwn && onDelete && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="justify-start h-8 px-2 text-red-600 hover:text-red-700"
                      onClick={() => {
                        handleDelete();
                        setShowActions(false);
                      }}
                    >
                      <Trash2 className="h-3 w-3 mr-2" />
                      {t('delete')}
                    </Button>
                  )}
                  
                  {!isOwn && onFlag && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="justify-start h-8 px-2 text-orange-600 hover:text-orange-700"
                      onClick={() => {
                        handleFlag();
                        setShowActions(false);
                      }}
                    >
                      <Flag className="h-3 w-3 mr-2" />
                      {t('report')}
                    </Button>
                  )}
                </div>
              </Card>
            )}
          </div>
        )}
      </div>
      
      {/* Avatar placeholder for own messages */}
      {isOwn && showAvatar && (
        <div className="w-8 flex-shrink-0" />
      )}
    </div>
  );
};