#!/usr/bin/env tsx
/**
 * Database Seeder System
 * 
 * Manages data seeding for different environments with idempotent operations.
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

interface SeedModule {
  name: string;
  description: string;
  environment: 'development' | 'production' | 'shared';
  order: number;
  execute: (supabase: any) => Promise<void>;
  rollback?: (supabase: any) => Promise<void>;
}

class DatabaseSeeder {
  private supabase: any;
  private seedsDir: string;

  constructor() {
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY;

    if (!supabaseUrl || !supabaseKey) {
      throw new Error('Missing SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY environment variables');
    }

    this.supabase = createClient(supabaseUrl, supabaseKey);
    this.seedsDir = path.resolve(__dirname, '../seeds');
  }

  /**
   * Initialize seed tracking table
   */
  async initSeedTable(): Promise<void> {
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS _seeds (
        name VARCHAR(100) PRIMARY KEY,
        environment VARCHAR(20) NOT NULL,
        executed_at TIMESTAMPTZ DEFAULT NOW(),
        checksum VARCHAR(64) NOT NULL
      );
      
      CREATE INDEX IF NOT EXISTS idx_seeds_environment 
        ON _seeds(environment);
      CREATE INDEX IF NOT EXISTS idx_seeds_executed_at 
        ON _seeds(executed_at);
    `;

    const { error } = await this.supabase.rpc('exec_sql', { sql: createTableSQL });
    if (error) {
      throw new Error(`Failed to initialize seed table: ${error.message}`);
    }
  }

  /**
   * Load seed modules from filesystem
   */
  async loadSeedModules(environment?: string): Promise<SeedModule[]> {
    const modules: SeedModule[] = [];
    const environments = environment ? [environment] : ['shared', 'development', 'production'];

    for (const env of environments) {
      const envDir = path.join(this.seedsDir, env);
      
      try {
        const files = await fs.readdir(envDir);
        const tsFiles = files.filter(f => f.endsWith('.ts')).sort();

        for (const file of tsFiles) {
          const modulePath = path.join(envDir, file);
          const module = await import(modulePath);
          
          if (module.default && typeof module.default === 'object') {
            const seedModule = module.default as SeedModule;
            seedModule.environment = env as any;
            modules.push(seedModule);
          }
        }
      } catch (error) {
        // Directory might not exist, continue
        console.warn(`Seed directory not found: ${envDir}`);
      }
    }

    return modules.sort((a, b) => a.order - b.order);
  }

  /**
   * Get executed seeds from database
   */
  async getExecutedSeeds(environment?: string): Promise<string[]> {
    let query = this.supabase.from('_seeds').select('name');
    
    if (environment) {
      query = query.eq('environment', environment);
    }

    const { data, error } = await query;

    if (error) {
      throw new Error(`Failed to fetch executed seeds: ${error.message}`);
    }

    return (data || []).map(s => s.name);
  }

  /**
   * Calculate checksum for seed module
   */
  private calculateChecksum(content: string): string {
    const crypto = require('crypto');
    return crypto.createHash('sha256').update(content).digest('hex').substring(0, 16);
  }

  /**
   * Execute a single seed module
   */
  async executeSeed(seedModule: SeedModule): Promise<void> {
    console.log(`🌱 Seeding ${seedModule.name}: ${seedModule.description}`);
    
    try {
      await seedModule.execute(this.supabase);

      // Record successful seed
      const checksum = this.calculateChecksum(seedModule.name);
      const { error } = await this.supabase
        .from('_seeds')
        .upsert({
          name: seedModule.name,
          environment: seedModule.environment,
          checksum
        });

      if (error) {
        throw new Error(`Failed to record seed: ${error.message}`);
      }

      console.log(`✅ Seed ${seedModule.name} completed successfully`);

    } catch (error) {
      console.error(`❌ Seed ${seedModule.name} failed:`, error);
      throw error;
    }
  }

  /**
   * Run seeds for specified environment
   */
  async seed(environment: 'development' | 'production' | 'all' = 'development'): Promise<void> {
    console.log(`🔍 Loading seeds for environment: ${environment}`);

    await this.initSeedTable();

    const modules = await this.loadSeedModules(environment === 'all' ? undefined : environment);
    const executed = await this.getExecutedSeeds(environment === 'all' ? undefined : environment);
    const executedSet = new Set(executed);

    const pending = modules.filter(m => !executedSet.has(m.name));

    if (pending.length === 0) {
      console.log('✅ No pending seeds');
      return;
    }

    console.log(`📋 Found ${pending.length} pending seeds:`);
    pending.forEach(m => console.log(`   - ${m.name} (${m.environment}): ${m.description}`));

    for (const seedModule of pending) {
      await this.executeSeed(seedModule);
    }

    console.log(`🎉 Successfully executed ${pending.length} seeds`);
  }

  /**
   * Clean all data (development only)
   */
  async clean(): Promise<void> {
    if (process.env.NODE_ENV === 'production') {
      throw new Error('Clean operation not allowed in production');
    }

    console.log('🧹 Cleaning database...');

    // Get all tables except system tables
    const { data: tables, error } = await this.supabase.rpc('exec_sql', {
      sql: `
        SELECT tablename 
        FROM pg_tables 
        WHERE schemaname = 'public' 
        AND tablename NOT LIKE '\\_%'
        ORDER BY tablename;
      `
    });

    if (error) {
      throw new Error(`Failed to get tables: ${error.message}`);
    }

    // Disable foreign key checks temporarily
    await this.supabase.rpc('exec_sql', { sql: 'SET session_replication_role = replica;' });

    try {
      for (const table of tables) {
        await this.supabase.rpc('exec_sql', { 
          sql: `TRUNCATE TABLE "${table.tablename}" CASCADE;` 
        });
        console.log(`   - Cleaned table: ${table.tablename}`);
      }

      // Clean seed tracking
      await this.supabase.rpc('exec_sql', { sql: 'TRUNCATE TABLE "_seeds";' });

    } finally {
      // Re-enable foreign key checks
      await this.supabase.rpc('exec_sql', { sql: 'SET session_replication_role = DEFAULT;' });
    }

    console.log('✅ Database cleaned successfully');
  }

  /**
   * Show seed status
   */
  async status(): Promise<void> {
    await this.initSeedTable();

    const modules = await this.loadSeedModules();
    const executed = await this.getExecutedSeeds();
    const executedSet = new Set(executed);

    console.log('\n🌱 Seed Status:\n');
    console.log('Environment  | Status    | Name                | Description');
    console.log('-------------|-----------|---------------------|------------------------');

    for (const module of modules) {
      const status = executedSet.has(module.name) ? '✅ Applied' : '⏳ Pending';
      const env = module.environment.padEnd(11);
      const name = module.name.padEnd(19);
      console.log(`${env} | ${status.padEnd(9)} | ${name} | ${module.description}`);
    }

    const pending = modules.filter(m => !executedSet.has(m.name));
    console.log(`\n📈 Summary: ${executed.length} applied, ${pending.length} pending\n`);
  }
}

// CLI Interface
async function main() {
  const command = process.argv[2];
  const seeder = new DatabaseSeeder();

  try {
    switch (command) {
      case 'seed':
        const env = (process.argv[3] || 'development') as any;
        await seeder.seed(env);
        break;

      case 'clean':
        await seeder.clean();
        break;

      case 'status':
        await seeder.status();
        break;

      case 'reset':
        await seeder.clean();
        await seeder.seed('development');
        break;

      default:
        console.log(`
Database Seeder Tool

Usage:
  tsx database/management/seeder.ts <command>

Commands:
  seed [env]    Run seeds for environment (development|production|all)
  clean         Clean all data (development only)
  status        Show seed status
  reset         Clean and reseed development data

Examples:
  tsx database/management/seeder.ts seed development
  tsx database/management/seeder.ts seed production
  tsx database/management/seeder.ts clean
  tsx database/management/seeder.ts reset
        `);
        process.exit(1);
    }
  } catch (error) {
    console.error('💥 Seeding failed:', error);
    process.exit(1);
  }
}

// Only run if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { DatabaseSeeder };