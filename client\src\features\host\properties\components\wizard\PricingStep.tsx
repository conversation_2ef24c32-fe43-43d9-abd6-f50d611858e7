import React, { useState, useEffect } from 'react';
import { useTranslations } from '@/lib/translations';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { Euro, TrendingUp, Calendar, Zap, Users, Star } from 'lucide-react';
import { PropertyWizardData } from '../../types/property';

interface PricingStepProps {
  data: Partial<PropertyWizardData>;
  onUpdate: (data: Partial<PropertyWizardData>) => void;
}

// Market data for Costa Blanca region
const MARKET_DATA = {
  villa: {
    low: 80,
    average: 150,
    high: 300,
    premium: 500
  },
  apartment: {
    low: 45,
    average: 85,
    high: 150,
    premium: 250
  },
  house: {
    low: 70,
    average: 130,
    high: 250,
    premium: 400
  },
  penthouse: {
    low: 100,
    average: 200,
    high: 400,
    premium: 700
  }
};

const PRICING_STRATEGIES = [
  {
    id: 'competitive',
    nameKey: 'competitive',
    descriptionKey: 'competitiveDescription',
    icon: Users,
    multiplier: 0.9,
    badgeKey: 'moreBookings'
  },
  {
    id: 'market',
    nameKey: 'marketRate',
    descriptionKey: 'marketRateDescription',
    icon: TrendingUp,
    multiplier: 1.0,
    badgeKey: 'recommended'
  },
  {
    id: 'premium',
    nameKey: 'premium',
    descriptionKey: 'premiumDescription',
    icon: Star,
    multiplier: 1.15,
    badgeKey: 'higherIncome'
  }
];

export const PricingStep = ({ data, onUpdate }: PricingStepProps) => {
  const t = useTranslations('hostOnboarding.pricing');
  const [selectedStrategy, setSelectedStrategy] = useState('market');
  const [customPrice, setCustomPrice] = useState<number>(0);
  const [showAdvanced, setShowAdvanced] = useState(false);

  const propertyType = data.propertyType || 'villa';
  const maxGuests = data.maxGuests || 4;
  const marketData = MARKET_DATA[propertyType as keyof typeof MARKET_DATA] || MARKET_DATA.villa;

  // AI-suggested pricing based on property features
  const calculateSuggestedPrice = () => {
    let basePrice = marketData.average;
    
    // Adjust for guest capacity
    if (maxGuests <= 2) {
      basePrice *= 0.8;
    } else if (maxGuests >= 8) {
      basePrice *= 1.3;
    } else if (maxGuests >= 6) {
      basePrice *= 1.1;
    }
    
    // Adjust for amenities
    const amenities = data.amenities || [];
    if (amenities.includes('pool')) basePrice *= 1.2;
    if (amenities.includes('sea_view')) basePrice *= 1.15;
    if (amenities.includes('beach_access')) basePrice *= 1.1;
    if (amenities.includes('wifi')) basePrice *= 1.02;
    if (amenities.includes('parking')) basePrice *= 1.05;
    
    return Math.round(basePrice);
  };

  const suggestedPrice = calculateSuggestedPrice();
  const currentPrice = data.pricePerNight || suggestedPrice;

  useEffect(() => {
    if (!data.pricePerNight) {
      onUpdate({ pricePerNight: suggestedPrice });
      setCustomPrice(suggestedPrice);
    }
  }, [suggestedPrice]);

  const handleStrategySelect = (strategy: typeof PRICING_STRATEGIES[0]) => {
    setSelectedStrategy(strategy.id);
    const strategyPrice = Math.round(suggestedPrice * strategy.multiplier);
    setCustomPrice(strategyPrice);
    onUpdate({ pricePerNight: strategyPrice });
  };

  const handleCustomPriceChange = (price: number) => {
    setCustomPrice(price);
    onUpdate({ pricePerNight: price });
    setSelectedStrategy('custom');
  };

  const calculateEarnings = (price: number, occupancyRate: number) => {
    const monthlyEarnings = price * 30 * (occupancyRate / 100);
    const yearlyEarnings = monthlyEarnings * 12;
    return { monthly: monthlyEarnings, yearly: yearlyEarnings };
  };

  const earnings75 = calculateEarnings(currentPrice, 75);
  const earnings60 = calculateEarnings(currentPrice, 60);
  const earnings50 = calculateEarnings(currentPrice, 50);

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          {t('title')}
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          {t('description')}
        </p>
      </div>

      {/* AI Price Suggestion */}
      <Card className="border-primary/20 bg-primary/5">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Zap className="h-5 w-5 text-primary" />
            <span>{t('aiSuggestion')}</span>
            <Badge variant="default" className="bg-primary">
              {t('recommended')}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center">
            <div className="text-4xl font-bold text-primary mb-2">
              €{suggestedPrice}
              <span className="text-lg font-normal text-gray-600 dark:text-gray-400">
                {t('perNight')}
              </span>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
              {t('basedOnFeatures')}
            </p>
            <Button
              type="button"
              variant="outline"
              onClick={() => handleCustomPriceChange(suggestedPrice)}
              className="border-primary text-primary hover:bg-primary hover:text-white"
            >
              {t('useThisPrice')}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Pricing Strategies */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          {t('pricingStrategies')}
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {PRICING_STRATEGIES.map((strategy) => {
            const strategyPrice = Math.round(suggestedPrice * strategy.multiplier);
            const isSelected = selectedStrategy === strategy.id;
            
            return (
              <Card
                key={strategy.id}
                className={`cursor-pointer transition-all hover:shadow-md ${
                  isSelected
                    ? 'ring-2 ring-primary border-primary'
                    : 'border-gray-200 dark:border-gray-700'
                }`}
                onClick={() => handleStrategySelect(strategy)}
              >
                <CardContent className="p-4">
                  <div className="flex items-start justify-between mb-3">
                    <strategy.icon className="h-6 w-6 text-primary" />
                    <Badge variant={strategy.badgeKey === 'recommended' ? 'default' : 'secondary'}>
                      {t(strategy.badgeKey)}
                    </Badge>
                  </div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                    {t(strategy.nameKey)}
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                    {t(strategy.descriptionKey)}
                  </p>
                  <div className="text-2xl font-bold text-primary">
                    €{strategyPrice}
                    <span className="text-sm font-normal text-gray-600 dark:text-gray-400">
                      {t('perNight')}
                    </span>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>

      {/* Custom Pricing */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          {t('customPricing')}
        </h3>
        <Card className="border-gray-200 dark:border-gray-700">
          <CardContent className="p-6">
            <div className="space-y-6">
              <div>
                <Label htmlFor="price" className="text-base font-medium">
                  {t('pricePerNight')}
                </Label>
                <div className="flex items-center space-x-4 mt-2">
                  <div className="relative flex-1">
                    <Euro className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      id="price"
                      type="number"
                      value={customPrice}
                      onChange={(e) => handleCustomPriceChange(Number(e.target.value))}
                      className="pl-10 text-lg"
                      min={marketData.low}
                      max={marketData.premium}
                    />
                  </div>
                  <span className="text-gray-600 dark:text-gray-400">per night</span>
                </div>
              </div>
              
              <div className="space-y-4">
                <Label className="text-base font-medium">{t('priceRange')}</Label>
                <Slider
                  value={[customPrice]}
                  onValueChange={(value) => handleCustomPriceChange(value[0])}
                  min={marketData.low}
                  max={marketData.premium}
                  step={5}
                  className="w-full"
                />
                <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400">
                  <span>€{marketData.low} (Low)</span>
                  <span>€{marketData.average} (Market)</span>
                  <span>€{marketData.premium} (Premium)</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Earnings Projection */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          {t('earningsProjection')}
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {[
            { rate: 75, label: 'High demand', earnings: earnings75, color: 'green' },
            { rate: 60, label: 'Average demand', earnings: earnings60, color: 'blue' },
            { rate: 50, label: 'Conservative', earnings: earnings50, color: 'gray' }
          ].map((scenario) => (
            <Card key={scenario.rate} className="border-gray-200 dark:border-gray-700">
              <CardContent className="p-4">
                <div className="text-center">
                  <Badge variant="secondary" className="mb-2">
                    {scenario.rate}% occupancy
                  </Badge>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-1">
                    {scenario.label}
                  </h4>
                  <div className="space-y-2">
                    <div>
                      <span className="text-lg font-semibold text-gray-900 dark:text-white">
                        €{Math.round(scenario.earnings.monthly).toLocaleString()}
                      </span>
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        /month
                      </span>
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      €{Math.round(scenario.earnings.yearly).toLocaleString()}/year
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Market Comparison */}
      <div className="bg-gray-50 dark:bg-gray-900/50 p-6 rounded-lg">
        <h4 className="font-medium text-gray-900 dark:text-white mb-4">
          {t('marketComparison')}
        </h4>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
          <div>
            <div className="text-lg font-semibold text-gray-900 dark:text-white">
              €{marketData.low}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              Low range
            </div>
          </div>
          <div>
            <div className="text-lg font-semibold text-gray-900 dark:text-white">
              €{marketData.average}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              Market average
            </div>
          </div>
          <div>
            <div className="text-lg font-semibold text-gray-900 dark:text-white">
              €{marketData.high}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              High range
            </div>
          </div>
          <div>
            <div className="text-lg font-semibold text-gray-900 dark:text-white">
              €{marketData.premium}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              Premium
            </div>
          </div>
        </div>
      </div>

      {/* Pricing Tips */}
      <div className="bg-orange-50 dark:bg-orange-900/20 p-6 rounded-lg">
        <h4 className="font-medium text-orange-800 dark:text-orange-200 mb-2">
          {t('pricingTips')}
        </h4>
        <ul className="text-sm text-orange-700 dark:text-orange-300 space-y-1">
          <li>• {t('tip1')}</li>
          <li>• {t('tip2')}</li>
          <li>• {t('tip3')}</li>
          <li>• {t('tip4')}</li>
        </ul>
      </div>
    </div>
  );
};