import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { useTranslations } from '@/lib/translations';
import { useIsMobile } from '@/hooks/use-mobile';
import { useUser } from '@/features/shared/auth/hooks/useAuth';
import { useLocation } from 'wouter';
import { Card, CardContent } from '@/components/ui/card';
import { ShieldAlert, Loader2 } from 'lucide-react';
import { 
  BarChart3,
  CalendarDays, 
  MapPin, 
  Users, 
  DollarSign, 
  TrendingUp, 
  Settings,
  HelpCircle,
  MessageSquare,
  ClipboardList,
  Package
} from 'lucide-react';

// Feature imports
import { OverviewContent, useOverviewData } from '../overview';
import { BookingsContent, useBookings } from '../bookings';
import { PropertiesContent, useProperties } from '../properties';
import { MessagesContent, useMessages } from '@/features/shared/messaging';

// Mobile components
import { BottomNavigation } from './mobile/BottomNavigation';

// Placeholder imports for future features
import PlaceholderContent from './PlaceholderContent';

interface HostDashboardLayoutProps {
  initialTab?: string;
}

const HostDashboardLayout: React.FC<HostDashboardLayoutProps> = ({ 
  initialTab = 'dashboard' 
}) => {
  const t = useTranslations('hostDashboard');
  const authT = useTranslations('authErrors');
  const isMobile = useIsMobile();
  const [activeTab, setActiveTab] = useState(initialTab);
  const { data: currentUser, isLoading: userLoading } = useUser();
  const [, navigate] = useLocation();
  
  // Secondary protection layer - verify host status at component level
  if (userLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-96">
          <CardContent className="p-8 text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
            <p className="text-gray-600">{authT('loadingDashboard')}</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!currentUser || !currentUser.is_host) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-96">
          <CardContent className="p-8 text-center">
            <ShieldAlert className="h-12 w-12 mx-auto mb-4 text-red-500" />
            <h1 className="text-xl font-semibold mb-2">{authT('unauthorizedAccess')}</h1>
            <p className="text-gray-600 mb-6">
              {authT('restrictedArea')}
            </p>
            <div className="space-y-3">
              <Button 
                onClick={() => navigate('/host/upgrade')}
                className="w-full"
              >
                {authT('upgradeToHost')}
              </Button>
              <Button 
                variant="outline"
                onClick={() => navigate('/')}
                className="w-full"
              >
                {authT('returnToHome')}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Load data using feature hooks (only after auth check passes)
  const { hostProperties, bookings, hostMessages, user } = useOverviewData();
  const { messages: allMessages } = useMessages('host');

  const DesktopNavigationSidebar = () => (
    <div className="hidden md:block w-64 bg-white border-r min-h-[calc(100vh-64px)]">
      <div className="p-6">
        <h1 className="text-2xl font-bold text-gray-900">{t('title')}</h1>
        <p className="text-gray-600 mt-2">{t('greeting')}</p>
      </div>
      
      <nav className="px-4 space-y-2">
        <Button 
          variant={activeTab === 'dashboard' ? 'default' : 'ghost'}
          className="w-full justify-start"
          onClick={() => setActiveTab('dashboard')}
        >
          <BarChart3 className="h-4 w-4 mr-2" />
          {t('navigation.dashboard')}
        </Button>
        
        <Button 
          variant={activeTab === 'bookings' ? 'default' : 'ghost'}
          className="w-full justify-start"
          onClick={() => setActiveTab('bookings')}
        >
          <CalendarDays className="h-4 w-4 mr-2" />
          {t('navigation.bookings')}
        </Button>
        
        <Button 
          variant={activeTab === 'properties' ? 'default' : 'ghost'}
          className="w-full justify-start"
          onClick={() => setActiveTab('properties')}
        >
          <MapPin className="h-4 w-4 mr-2" />
          {t('navigation.properties')}
        </Button>
        
        <Button 
          variant={activeTab === 'messages' ? 'default' : 'ghost'}
          className="w-full justify-start"
          onClick={() => setActiveTab('messages')}
        >
          <MessageSquare className="h-4 w-4 mr-2" />
          {t('navigation.messages')}
        </Button>
        
        <Button 
          variant={activeTab === 'guests' ? 'default' : 'ghost'}
          className="w-full justify-start"
          onClick={() => setActiveTab('guests')}
        >
          <Users className="h-4 w-4 mr-2" />
          {t('navigation.guests')}
        </Button>
        
        <Button 
          variant={activeTab === 'earnings' ? 'default' : 'ghost'}
          className="w-full justify-start"
          onClick={() => setActiveTab('earnings')}
        >
          <DollarSign className="h-4 w-4 mr-2" />
          {t('navigation.earnings')}
        </Button>
        
        <Button 
          variant={activeTab === 'analytics' ? 'default' : 'ghost'}
          className="w-full justify-start"
          onClick={() => setActiveTab('analytics')}
        >
          <TrendingUp className="h-4 w-4 mr-2" />
          {t('navigation.analytics')}
        </Button>
        
        <Button 
          variant={activeTab === 'tasks' ? 'default' : 'ghost'}
          className="w-full justify-start"
          onClick={() => setActiveTab('tasks')}
        >
          <ClipboardList className="h-4 w-4 mr-2" />
          {t('navigation.tasks')}
        </Button>
        
        <Button 
          variant={activeTab === 'inventory' ? 'default' : 'ghost'}
          className="w-full justify-start"
          onClick={() => setActiveTab('inventory')}
        >
          <Package className="h-4 w-4 mr-2" />
          {t('navigation.inventory')}
        </Button>
        
        <Button 
          variant={activeTab === 'settings' ? 'default' : 'ghost'}
          className="w-full justify-start"
          onClick={() => setActiveTab('settings')}
        >
          <Settings className="h-4 w-4 mr-2" />
          {t('navigation.settings')}
        </Button>
        
        <Button 
          variant={activeTab === 'help' ? 'default' : 'ghost'}
          className="w-full justify-start"
          onClick={() => setActiveTab('help')}
        >
          <HelpCircle className="h-4 w-4 mr-2" />
          {t('navigation.help')}
        </Button>
      </nav>
    </div>
  );

  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return (
          <OverviewContent
            hostProperties={hostProperties}
            bookings={bookings}
            hostMessages={hostMessages}
            user={user}
          />
        );
      case 'bookings':
        return <BookingsContent bookings={bookings} />;
      case 'properties':
        return <PropertiesContent hostProperties={hostProperties} bookings={bookings} />;
      case 'messages':
        return <MessagesContent messages={allMessages} userType="host" />;
      case 'guests':
        return (
          <PlaceholderContent
            icon={Users}
            title={t('navigation.guests')}
            description="Guest management features coming soon!"
          />
        );
      case 'earnings':
        return (
          <PlaceholderContent
            icon={DollarSign}
            title={t('navigation.earnings')}
            description="Earnings analytics coming soon!"
          />
        );
      case 'analytics':
        return (
          <PlaceholderContent
            icon={TrendingUp}
            title={t('navigation.analytics')}
            description="Analytics dashboard coming soon!"
          />
        );
      case 'tasks':
        return (
          <PlaceholderContent
            icon={ClipboardList}
            title={t('navigation.tasks')}
            description="Task management coming soon!"
          />
        );
      case 'inventory':
        return (
          <PlaceholderContent
            icon={Package}
            title={t('navigation.inventory')}
            description="Inventory management coming soon!"
          />
        );
      case 'settings':
        return (
          <PlaceholderContent
            icon={Settings}
            title={t('navigation.settings')}
            description="Settings panel coming soon!"
          />
        );
      case 'help':
        return (
          <PlaceholderContent
            icon={HelpCircle}
            title={t('navigation.help')}
            description="Help & support coming soon!"
          />
        );
      default:
        return (
          <OverviewContent
            hostProperties={hostProperties}
            bookings={bookings}
            hostMessages={hostMessages}
            user={user}
          />
        );
    }
  };

  if (isMobile) {
    return (
      <div className="min-h-screen bg-gray-50 overflow-x-hidden max-w-full">
        {/* Mobile Main Content - Full Width */}
        <div className="pb-20 px-4 w-full max-w-full overflow-x-hidden">
          <div className="min-h-[calc(100vh-80px)] w-full max-w-full">
            {renderContent()}
          </div>
        </div>

        {/* Mobile Bottom Navigation */}
        <BottomNavigation
          activeTab={activeTab}
          onTabChange={setActiveTab}
        />
      </div>
    );
  }

  // Desktop Layout
  return (
    <div className="flex min-h-[calc(100vh-64px)] bg-gray-50">
      {/* Desktop Sidebar */}
      <DesktopNavigationSidebar />
      
      {/* Desktop Main Content */}
      <div className="flex-1">
        {renderContent()}
      </div>
    </div>
  );
};

export default HostDashboardLayout;