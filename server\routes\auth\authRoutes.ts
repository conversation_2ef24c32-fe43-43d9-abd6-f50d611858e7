import { Router } from "express";
import { authController } from "../../controllers/shared/authController";
import {
  handleForgotPassword,
  handleResetPassword,
} from "../../controllers/shared/passwordResetController";

const router = Router();

// Traditional email/password auth
router.post("/auth/register", authController.register.bind(authController));
router.post("/auth/login", authController.login.bind(authController));
router.post("/auth/logout", authController.logout.bind(authController));
router.post("/auth/refresh", authController.refreshToken.bind(authController));
router.get("/auth/me", authController.getCurrentUser.bind(authController));

// Social OAuth routes
router.get("/auth/google", authController.loginWithGoogle.bind(authController));
router.get(
  "/auth/facebook",
  authController.loginWithFacebook.bind(authController)
);
router.get(
  "/auth/callback",
  authController.handleOAuthCallback.bind(authController)
);

// Password reset routes
router.post("/auth/forgot-password", handleForgotPassword);
router.post("/auth/reset-password", handleResetPassword);

// Debug endpoint for testing redirect URL detection
router.get(
  "/auth/test-redirect",
  authController.testRedirectUrl.bind(authController)
);

export { router as authRoutes };
