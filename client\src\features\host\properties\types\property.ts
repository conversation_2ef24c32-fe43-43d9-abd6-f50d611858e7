export interface PropertyWizardData {
  // Property basics
  propertyType: string;
  spaceType: 'entire_place' | 'private_room' | 'shared_room';
  
  // Location
  address: string;
  city: string;
  country: string;
  coordinates: { lat: number; lng: number };
  
  // Capacity
  maxGuests: number;
  bedrooms: number;
  bathrooms: number;
  
  // Amenities
  amenities: string[];
  
  // Photos
  photos: string[];
  
  // Listing details
  title: string;
  description: string;
  
  // Pricing
  pricePerNight: number;
  currency: string;
  
  // Policies
  houseRules: string[];
  cancellationPolicy: string;
  checkInTime: string;
  checkOutTime: string;
  
  // Spanish compliance
  touristLicense: string;
  businessRegistration?: string;
}

export interface PropertyDraft {
  id: string;
  data: Partial<PropertyWizardData>;
  currentStep: number;
  lastSaved: Date;
  isPublished: boolean;
}

export interface PropertyDraftResponse {
  success: boolean;
  data?: PropertyDraft;
  message?: string;
}

export interface WizardStep {
  id: string;
  titleKey: string;
  icon: any;
  isValid?: (data: Partial<PropertyWizardData>) => boolean;
}