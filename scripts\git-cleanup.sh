#!/bin/bash

# Git Repository Cleanup Script
# Removes large data files from Git history and creates a clean repository

echo "🧹 Starting Git repository cleanup..."

# 1. Remove large files from working directory
echo "📁 Removing large data files from working directory..."
rm -f data/geonames/alternateNamesV2.txt
rm -f data/geonames/ES.txt
rm -f data/geonames/*.zip

# 2. Remove files from Git cache
echo "🗑️ Removing large files from Git index..."
git rm --cached --ignore-unmatch data/geonames/alternateNamesV2.txt
git rm --cached --ignore-unmatch data/geonames/ES.txt
git rm --cached --ignore-unmatch data/geonames/*.zip

# 3. Update .gitignore to prevent re-adding
echo "📝 Updating .gitignore..."
cat >> .gitignore << 'EOF'

# Large GeoNames data files (downloaded on-demand)
data/geonames/*.txt
data/geonames/*.zip
data/geonames/alternateNamesV2.txt
data/geonames/ES.txt
EOF

# 4. Create clean commit
echo "💾 Creating clean commit..."
git add .gitignore
git add server/
git add replit.md
git commit -m "Clean repository: Remove large data files and add proper .gitignore

- Remove 724MB alternateNamesV2.txt and 3MB ES.txt files
- Update .gitignore to exclude large data files
- Tourism region system works with on-demand data downloads
- Reduces repository size for successful Git push"

# 5. Aggressive cleanup of Git objects
echo "🧹 Cleaning up Git objects..."
git gc --aggressive --prune=now

# 6. Show repository size
echo "📊 Repository size after cleanup:"
du -sh .git

echo "✅ Git cleanup completed! Repository is now ready for push."
echo "🚀 Next steps:"
echo "   1. Run: git push origin main"
echo "   2. If still too large, run: git filter-branch to remove from history"