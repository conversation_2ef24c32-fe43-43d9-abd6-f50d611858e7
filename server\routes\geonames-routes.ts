// GeoNames location search routes
import { Router } from 'express';
import { geoNamesController } from '../controllers/shared/geonames-controller';

export const geoNamesRoutes = Router();

// Public search endpoints (accessible without authentication)
geoNamesRoutes.get('/search', geoNamesController.searchLocations.bind(geoNamesController));
geoNamesRoutes.get('/autocomplete', geoNamesController.autocomplete.bind(geoNamesController));
geoNamesRoutes.get('/popular', geoNamesController.getPopularDestinations.bind(geoNamesController));
geoNamesRoutes.get('/location/:id', geoNamesController.getLocationDetails.bind(geoNamesController));
geoNamesRoutes.get('/config', geoNamesController.getConfiguration.bind(geoNamesController));

// Health and admin endpoints
// Note: Health status available via sync status endpoint

// Admin endpoints (require authentication)
geoNamesRoutes.post('/admin/sync', geoNamesController.triggerSync.bind(geoNamesController));
geoNamesRoutes.get('/admin/sync/status', geoNamesController.getSyncStatus.bind(geoNamesController));