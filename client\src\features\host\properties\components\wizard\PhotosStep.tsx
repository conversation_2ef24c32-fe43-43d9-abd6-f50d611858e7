import React, { useState } from 'react';
import { useTranslations } from '@/lib/translations';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Camera, Upload, X, Plus, Image as ImageIcon, Eye } from 'lucide-react';
import { PropertyWizardData } from '../../types/property';

interface PhotosStepProps {
  data: Partial<PropertyWizardData>;
  onUpdate: (data: Partial<PropertyWizardData>) => void;
}

const PHOTO_CATEGORIES = [
  {
    id: 'exterior',
    nameKey: 'exterior',
    descriptionKey: 'exteriorDescription',
    required: true,
    exampleKeys: ['frontOfHouse', 'gardenPatio', 'poolArea', 'balconyTerrace']
  },
  {
    id: 'living_spaces',
    nameKey: 'livingSpaces',
    descriptionKey: 'livingSpacesDescription',
    required: true,
    exampleKeys: ['livingRoom', 'diningRoom', 'kitchen', 'loungeArea']
  },
  {
    id: 'bedrooms',
    nameKey: 'bedrooms',
    descriptionKey: 'bedroomsDescription',
    required: true,
    exampleKeys: ['masterBedroom', 'guestBedrooms', 'bedDetails', 'bedroomViews']
  },
  {
    id: 'bathrooms',
    nameKey: 'bathrooms',
    descriptionKey: 'bathroomsDescription',
    required: true,
    exampleKeys: ['mainBathroom', 'ensuiteBathrooms', 'showerBath', 'toiletAreas']
  },
  {
    id: 'amenities',
    nameKey: 'amenities',
    descriptionKey: 'amenitiesDescription',
    required: false,
    exampleKeys: ['pool', 'gym', 'bbqArea', 'workspace', 'entertainmentRoom']
  },
  {
    id: 'views',
    nameKey: 'views',
    descriptionKey: 'viewsDescription',
    required: false,
    exampleKeys: ['seaView', 'mountainView', 'cityView', 'gardenView']
  }
];

export const PhotosStep = ({ data, onUpdate }: PhotosStepProps) => {
  const t = useTranslations('hostOnboarding.photos');
  const [selectedCategory, setSelectedCategory] = useState('exterior');
  const [dragOver, setDragOver] = useState(false);
  const photos = data.photos || [];

  const handleFileUpload = (files: FileList | null) => {
    if (!files) return;
    
    const newPhotos = Array.from(files).slice(0, 5).map(file => URL.createObjectURL(file));
    onUpdate({ photos: [...photos, ...newPhotos] });
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    handleFileUpload(e.dataTransfer.files);
  };

  const removePhoto = (index: number) => {
    const newPhotos = photos.filter((_, i) => i !== index);
    onUpdate({ photos: newPhotos });
  };

  const minPhotosRequired = 5;
  const hasEnoughPhotos = photos.length >= minPhotosRequired;

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          {t('title')}
        </h2>
        <p className="text-gray-600 dark:text-gray-400 mb-4">
          {t('description')}
        </p>
        <div className="flex items-center justify-center space-x-2">
          <Badge variant={hasEnoughPhotos ? "default" : "secondary"}>
            {photos.length} / {minPhotosRequired} {t('minimumPhotos')}
          </Badge>
          {hasEnoughPhotos && (
            <Badge variant="default" className="bg-green-600">
              ✓ {t('readyToContinue')}
            </Badge>
          )}
        </div>
      </div>

      {/* Photo Categories */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          {t('photoCategories')}
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {PHOTO_CATEGORIES.map((category) => (
            <Card
              key={category.id}
              className={`cursor-pointer transition-all hover:shadow-md ${
                selectedCategory === category.id
                  ? 'ring-2 ring-primary border-primary'
                  : 'border-gray-200 dark:border-gray-700'
              }`}
              onClick={() => setSelectedCategory(category.id)}
            >
              <CardContent className="p-4">
                <div className="flex items-start justify-between mb-2">
                  <h4 className="font-medium text-gray-900 dark:text-white">
                    {t(category.nameKey)}
                  </h4>
                  {category.required && (
                    <Badge variant="outline" className="text-xs">
                      {t('required')}
                    </Badge>
                  )}
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                  {t(category.descriptionKey)}
                </p>
                <div className="flex flex-wrap gap-1">
                  {category.exampleKeys.slice(0, 2).map((exampleKey, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {t(exampleKey)}
                    </Badge>
                  ))}
                  {category.exampleKeys.length > 2 && (
                    <Badge variant="secondary" className="text-xs">
                      +{category.exampleKeys.length - 2} {t('more')}
                    </Badge>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Upload Area */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          {t('uploadPhotos')}
        </h3>
        <div
          className={`border-2 border-dashed rounded-lg p-8 text-center transition-all ${
            dragOver
              ? 'border-primary bg-primary/5'
              : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
          }`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <div className="flex flex-col items-center space-y-4">
            <div className="flex items-center justify-center w-16 h-16 bg-primary/10 rounded-full">
              <Upload className="h-8 w-8 text-primary" />
            </div>
            <div>
              <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                {t('uploadTitle')}
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                {t('uploadDescription')}
              </p>
              <input
                type="file"
                multiple
                accept="image/*"
                onChange={(e) => handleFileUpload(e.target.files)}
                className="hidden"
                id="photo-upload"
              />
              <label htmlFor="photo-upload">
                <Button asChild className="bg-primary hover:bg-primary/90">
                  <span>
                    <Camera className="h-4 w-4 mr-2" />
                    {t('chooseFiles')}
                  </span>
                </Button>
              </label>
            </div>
          </div>
        </div>
      </div>

      {/* Photo Preview */}
      {photos.length > 0 && (
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            {t('photoPreview')}
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {photos.map((photo, index) => (
              <div key={index} className="relative group">
                <div className="aspect-square rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-800">
                  <img
                    src={photo}
                    alt={`Property photo ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                  <Button
                    type="button"
                    size="sm"
                    variant="destructive"
                    onClick={() => removePhoto(index)}
                    className="h-8 w-8 p-0"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
                {index === 0 && (
                  <Badge className="absolute bottom-2 left-2 bg-primary">
                    {t('mainPhoto')}
                  </Badge>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Photography Tips */}
      <div className="bg-blue-50 dark:bg-blue-900/20 p-6 rounded-lg">
        <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">
          {t('photographyTips')}
        </h4>
        <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
          <li>• {t('tip1')}</li>
          <li>• {t('tip2')}</li>
          <li>• {t('tip3')}</li>
          <li>• {t('tip4')}</li>
          <li>• {t('tip5')}</li>
        </ul>
      </div>

      {/* Illustration */}
      <div className="text-center py-8">
        <div className="inline-flex items-center justify-center w-32 h-32 bg-primary/10 rounded-full mb-4">
          <Camera className="h-16 w-16 text-primary" />
        </div>
        <p className="text-sm text-gray-600 dark:text-gray-400 max-w-md mx-auto">
          {t('illustrationText')}
        </p>
      </div>
    </div>
  );
};