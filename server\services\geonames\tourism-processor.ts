// Tourism Region Processor - Integrates authentic Spanish tourism data
import { createClient } from '@supabase/supabase-js';
import { 
  SPANISH_TOURISM_REGIONS, 
  LOCATION_TOURISM_MAPPINGS,
  TourismRegionData,
  LocationTourismMapping 
} from './tourism-data';
import { GeoNamesPlace } from './types';

export class TourismRegionProcessor {
  private supabase: any;
  private isEnabled: boolean;

  constructor(supabaseUrl: string, supabaseKey: string) {
    // Check if required environment variables are available
    if (!supabaseUrl || !supabaseKey) {
      console.warn('[TOURISM] Supabase credentials not available - tourism processing disabled');
      this.isEnabled = false;
      this.supabase = null;
      return;
    }

    try {
      this.supabase = createClient(supabaseUrl, supabaseKey);
      this.isEnabled = true;
    } catch (error) {
      console.warn('[TOURISM] Failed to initialize Supabase client - tourism processing disabled:', error);
      this.isEnabled = false;
      this.supabase = null;
    }
  }

  /**
   * Process tourism regions - create region entries and update location mappings
   */
  async processTourismRegions(): Promise<void> {
    if (!this.isEnabled) {
      console.log('[TOURISM] Skipping tourism region processing - Supabase not available');
      return;
    }

    console.log('[TOURISM] Processing Spanish tourism regions...');
    
    try {
      // Step 1: Create tourism region entries as special locations
      await this.createTourismRegionEntries();
      
      // Step 2: Update existing locations with tourism region mappings
      await this.updateLocationTourismMappings();
      
      // Step 3: Add alternate names for tourism regions
      await this.addTourismRegionAlternateNames();
      
      console.log('[TOURISM] ✅ Tourism region processing completed');
    } catch (error) {
      console.error('[TOURISM] Failed to process tourism regions:', error);
      throw error;
    }
  }

  /**
   * Create tourism regions as special geonames_locations entries
   */
  private async createTourismRegionEntries(): Promise<void> {
    console.log('[TOURISM] Creating tourism region entries...');
    
    const regionEntries = SPANISH_TOURISM_REGIONS.map(region => this.convertRegionToLocationEntry(region));
    
    const { error } = await this.supabase
      .from('geonames_locations')
      .upsert(regionEntries, {
        onConflict: 'geonames_id',
        ignoreDuplicates: false
      });

    if (error) {
      throw new Error(`Failed to create tourism region entries: ${error.message}`);
    }

    console.log(`[TOURISM] Created ${regionEntries.length} tourism region entries`);
  }

  /**
   * Update existing locations with tourism region assignments
   */
  private async updateLocationTourismMappings(): Promise<void> {
    console.log('[TOURISM] Updating location tourism mappings...');
    
    let totalUpdated = 0;
    
    for (const mapping of LOCATION_TOURISM_MAPPINGS) {
      const updateCount = await this.updateLocationsForRegion(mapping);
      totalUpdated += updateCount;
    }
    
    console.log(`[TOURISM] Updated ${totalUpdated} locations with tourism region mappings`);
  }

  /**
   * Add alternate names for tourism regions in multiple languages
   */
  private async addTourismRegionAlternateNames(): Promise<void> {
    console.log('[TOURISM] Adding tourism region alternate names...');
    
    // Get region location IDs
    const { data: regions, error: regionError } = await this.supabase
      .from('geonames_locations')
      .select('id, geonames_id, name')
      .eq('is_tourism_region', true);

    if (regionError) {
      throw new Error(`Failed to get tourism regions: ${regionError.message}`);
    }

    const alternateNames: any[] = [];
    
    for (const region of regions) {
      const regionData = SPANISH_TOURISM_REGIONS.find(r => 
        this.generateTourismRegionGeonamesId(r.name) === region.geonames_id
      );
      
      if (regionData) {
        // Add Spanish alternate name
        if (regionData.name_es !== regionData.name) {
          alternateNames.push({
            location_id: region.id,
            language_code: 'es',
            name: regionData.name_es,
            is_preferred: true,
            is_short: false,
            is_colloquial: false,
            is_historic: false
          });
        }
        
        // Add Catalan alternate name if available
        if (regionData.name_ca && regionData.name_ca !== regionData.name) {
          alternateNames.push({
            location_id: region.id,
            language_code: 'ca',
            name: regionData.name_ca,
            is_preferred: false,
            is_short: false,
            is_colloquial: false,
            is_historic: false
          });
        }
        
        // Add English alternate name if different
        if (regionData.name_en !== regionData.name) {
          alternateNames.push({
            location_id: region.id,
            language_code: 'en',
            name: regionData.name_en,
            is_preferred: false,
            is_short: false,
            is_colloquial: false,
            is_historic: false
          });
        }
      }
    }
    
    if (alternateNames.length > 0) {
      const { error } = await this.supabase
        .from('geonames_location_names')
        .insert(alternateNames);

      if (error) {
        console.warn('[TOURISM] Failed to insert some alternate names:', error.message);
      } else {
        console.log(`[TOURISM] Added ${alternateNames.length} tourism region alternate names`);
      }
    }
  }

  /**
   * Convert tourism region data to geonames_locations entry
   */
  private convertRegionToLocationEntry(region: TourismRegionData): any {
    return {
      geonames_id: this.generateTourismRegionGeonamesId(region.name),
      name: region.name,
      ascii_name: region.name_en,
      country_code: 'ES',
      admin1_code: region.admin1_code,
      admin2_code: region.admin2_codes[0] || '', // Primary province
      feature_class: 'L', // Landscape/Region
      feature_code: 'TOUR', // Tourism region (custom code)
      latitude: region.center_coordinates.lat,
      longitude: region.center_coordinates.lng,
      population: 0,
      elevation: null,
      timezone: 'Europe/Madrid',
      popularity_score: region.popularity_score,
      property_count: 0,
      tourism_region: region.name,
      tourism_region_type: region.type,
      is_tourism_region: true,
      destination_type: 'region'
    };
  }

  /**
   * Update locations for a specific tourism region
   */
  private async updateLocationsForRegion(mapping: LocationTourismMapping): Promise<number> {
    let whereClause = '';
    const conditions: string[] = [];
    
    // Build WHERE clause based on criteria
    if (mapping.criteria.admin1_code) {
      conditions.push(`admin1_code = '${mapping.criteria.admin1_code}'`);
    }
    
    if (mapping.criteria.admin2_code) {
      conditions.push(`admin2_code = '${mapping.criteria.admin2_code}'`);
    }
    
    if (mapping.criteria.coordinate_bounds) {
      const bounds = mapping.criteria.coordinate_bounds;
      conditions.push(`latitude BETWEEN ${bounds.lat_min} AND ${bounds.lat_max}`);
      conditions.push(`longitude BETWEEN ${bounds.lng_min} AND ${bounds.lng_max}`);
    }
    
    if (conditions.length === 0) {
      console.warn(`[TOURISM] No criteria specified for region ${mapping.tourism_region}`);
      return 0;
    }
    
    whereClause = conditions.join(' AND ');
    
    // Update locations
    const regionData = SPANISH_TOURISM_REGIONS.find(r => r.name === mapping.tourism_region);
    if (!regionData) {
      console.warn(`[TOURISM] Region data not found for ${mapping.tourism_region}`);
      return 0;
    }
    
    const updateQuery = `
      UPDATE geonames_locations 
      SET tourism_region = '${mapping.tourism_region}',
          tourism_region_type = '${regionData.type}'
      WHERE ${whereClause} 
        AND is_tourism_region = false
        AND tourism_region IS NULL
    `;
    
    try {
      const { data, error } = await this.supabase.rpc('execute_raw_sql', {
        query: updateQuery
      });
      
      if (error) {
        console.warn(`[TOURISM] Failed to update locations for ${mapping.tourism_region}:`, error.message);
        return 0;
      }
      
      console.log(`[TOURISM] Updated locations for ${mapping.tourism_region}`);
      return data?.length || 0;
    } catch (error) {
      // Fallback: Use Supabase client update
      const { data, error: updateError } = await this.supabase
        .from('geonames_locations')
        .update({
          tourism_region: mapping.tourism_region,
          tourism_region_type: regionData.type
        })
        .match(this.buildMatchCriteria(mapping.criteria))
        .eq('is_tourism_region', false)
        .is('tourism_region', null);
        
      if (updateError) {
        console.warn(`[TOURISM] Failed to update locations for ${mapping.tourism_region}:`, updateError.message);
        return 0;
      }
      
      return data?.length || 0;
    }
  }

  /**
   * Build match criteria for Supabase query
   */
  private buildMatchCriteria(criteria: LocationTourismMapping['criteria']): any {
    const match: any = {};
    
    if (criteria.admin1_code) {
      match.admin1_code = criteria.admin1_code;
    }
    
    if (criteria.admin2_code) {
      match.admin2_code = criteria.admin2_code;
    }
    
    return match;
  }

  /**
   * Generate consistent geonames_id for tourism regions
   */
  private generateTourismRegionGeonamesId(regionName: string): number {
    // Generate consistent ID based on region name
    // Use high number range (9000000+) to avoid conflicts with real geonames_ids
    const hash = regionName.split('').reduce((acc, char) => {
      return acc + char.charCodeAt(0);
    }, 0);
    
    return 9000000 + (hash % 999999);
  }
}