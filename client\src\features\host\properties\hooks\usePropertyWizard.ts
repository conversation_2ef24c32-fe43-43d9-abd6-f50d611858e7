import { useState, useCallback } from 'react';
import { PropertyWizardData, WizardStep } from '../types/property';
import { Home, Users, Camera, MapPin, Euro, Settings } from 'lucide-react';

const WIZARD_STEPS: WizardStep[] = [
  { 
    id: 'property-type', 
    titleKey: 'propertyType', 
    icon: Home,
    isValid: (data) => !!data.propertyType && !!data.spaceType
  },
  { 
    id: 'location', 
    titleKey: 'location', 
    icon: MapPin,
    isValid: (data) => !!data.address && !!data.city && !!data.coordinates
  },
  { 
    id: 'capacity', 
    titleKey: 'capacity', 
    icon: Users,
    isValid: (data) => !!data.maxGuests && !!data.bedrooms && !!data.bathrooms
  },
  { 
    id: 'amenities', 
    titleKey: 'amenities', 
    icon: Settings,
    isValid: (data) => Array.isArray(data.amenities) && data.amenities.length > 0
  },
  { 
    id: 'photos', 
    titleKey: 'photos', 
    icon: Camera,
    isValid: (data) => Array.isArray(data.photos) && data.photos.length >= 3
  },
  { 
    id: 'listing', 
    titleKey: 'listing', 
    icon: Home,
    isValid: (data) => !!data.title && !!data.description && data.description.length >= 50
  },
  { 
    id: 'pricing', 
    titleKey: 'pricing', 
    icon: Euro,
    isValid: (data) => !!data.pricePerNight && data.pricePerNight > 0
  },
  { 
    id: 'policies', 
    titleKey: 'policies', 
    icon: Settings,
    isValid: (data) => !!data.cancellationPolicy && !!data.checkInTime && !!data.checkOutTime
  },
  { 
    id: 'review', 
    titleKey: 'review', 
    icon: Settings,
    isValid: () => true // Review step is always valid
  }
];

export const usePropertyWizard = (initialStep: number = 0) => {
  const [currentStep, setCurrentStep] = useState(initialStep);
  const [formData, setFormData] = useState<Partial<PropertyWizardData>>({
    spaceType: 'entire_place',
    country: 'Spain',
    currency: 'EUR',
    amenities: [],
    photos: [],
    houseRules: [],
    cancellationPolicy: 'flexible'
  });

  const updateFormData = useCallback((data: Partial<PropertyWizardData>) => {
    setFormData(prev => ({ ...prev, ...data }));
  }, []);

  const goToStep = useCallback((step: number) => {
    if (step >= 0 && step < WIZARD_STEPS.length) {
      setCurrentStep(step);
    }
  }, []);

  const goToNextStep = useCallback(() => {
    if (currentStep < WIZARD_STEPS.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  }, [currentStep]);

  const goToPreviousStep = useCallback(() => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  }, [currentStep]);

  const resetWizard = useCallback((initialData?: Partial<PropertyWizardData>) => {
    setCurrentStep(0);
    setFormData(initialData || {
      spaceType: 'entire_place',
      country: 'Spain',
      currency: 'EUR',
      amenities: [],
      photos: [],
      houseRules: [],
      cancellationPolicy: 'flexible'
    });
  }, []);

  const getCurrentStep = () => WIZARD_STEPS[currentStep];
  
  const isStepValid = (stepIndex: number) => {
    const step = WIZARD_STEPS[stepIndex];
    return step.isValid ? step.isValid(formData) : false;
  };

  const canGoToNextStep = () => isStepValid(currentStep);
  
  const isLastStep = () => currentStep === WIZARD_STEPS.length - 1;
  
  const isFirstStep = () => currentStep === 0;

  const getProgress = () => ((currentStep + 1) / WIZARD_STEPS.length) * 100;

  const getCompletedSteps = () => {
    return WIZARD_STEPS.map((step, index) => ({
      ...step,
      index,
      isCompleted: index < currentStep || isStepValid(index),
      isCurrent: index === currentStep
    }));
  };

  return {
    // Data
    formData,
    currentStep,
    steps: WIZARD_STEPS,
    
    // Current step info
    currentStepData: getCurrentStep(),
    isFirstStep: isFirstStep(),
    isLastStep: isLastStep(),
    canGoToNextStep: canGoToNextStep(),
    progress: getProgress(),
    completedSteps: getCompletedSteps(),
    
    // Actions
    updateFormData,
    goToStep,
    goToNextStep,
    goToPreviousStep,
    resetWizard,
    
    // Validation
    isStepValid
  };
};