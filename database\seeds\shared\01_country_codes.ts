/**
 * Country Codes Seeder
 * 
 * Seeds ISO 3166-1 country codes and basic translations.
 * Replaces hardcoded country mappings from geonames-scope.ts
 */

export default {
  name: '01_country_codes',
  description: 'ISO 3166-1 country codes and translations',
  environment: 'shared' as const,
  order: 2,

  async execute(supabase: any): Promise<void> {
    console.log('   🌍 Seeding country codes...');

    // ISO 3166-1 country codes (numeric, alpha-2, alpha-3)
    const countryCodes = [
      { id: 724, iso_alpha2: 'ES', iso_alpha3: 'ESP' }, // Spain
      { id: 250, iso_alpha2: 'FR', iso_alpha3: 'FRA' }, // France
      { id: 380, iso_alpha2: 'IT', iso_alpha3: 'ITA' }, // Italy
      { id: 620, iso_alpha2: 'PT', iso_alpha3: 'PRT' }, // Portugal
      { id: 276, iso_alpha2: 'DE', iso_alpha3: 'DEU' }, // Germany
      { id: 528, iso_alpha2: 'NL', iso_alpha3: 'NLD' }, // Netherlands
      { id: 826, iso_alpha2: 'GB', iso_alpha3: 'GBR' }, // United Kingdom
      { id: 840, iso_alpha2: 'US', iso_alpha3: 'USA' }, // United States
      { id: 124, iso_alpha2: 'CA', iso_alpha3: 'CAN' }, // Canada
    ];

    // Insert country codes
    const { error: countryError } = await supabase
      .from('country_codes')
      .upsert(countryCodes, { onConflict: 'id' });

    if (countryError) {
      throw new Error(`Failed to seed country codes: ${countryError.message}`);
    }

    console.log(`   ✅ Seeded ${countryCodes.length} country codes`);

    // Country name translations (replaces hardcoded mappings)
    const countryTranslations = [
      // Spain translations
      { entity_type: 'country', entity_id: 724, language_code: 'en', text: 'Spain', is_official: true },
      { entity_type: 'country', entity_id: 724, language_code: 'es', text: 'España', is_official: true },
      { entity_type: 'country', entity_id: 724, language_code: 'nl', text: 'Spanje', is_official: false },
      { entity_type: 'country', entity_id: 724, language_code: 'fr', text: 'Espagne', is_official: false },
      { entity_type: 'country', entity_id: 724, language_code: 'de', text: 'Spanien', is_official: false },
      { entity_type: 'country', entity_id: 724, language_code: 'it', text: 'Spagna', is_official: false },
      { entity_type: 'country', entity_id: 724, language_code: 'ca', text: 'Espanya', is_official: true },
      { entity_type: 'country', entity_id: 724, language_code: 'eu', text: 'Espainia', is_official: true },

      // France translations
      { entity_type: 'country', entity_id: 250, language_code: 'en', text: 'France', is_official: true },
      { entity_type: 'country', entity_id: 250, language_code: 'es', text: 'Francia', is_official: false },
      { entity_type: 'country', entity_id: 250, language_code: 'nl', text: 'Frankrijk', is_official: false },
      { entity_type: 'country', entity_id: 250, language_code: 'fr', text: 'France', is_official: true },
      { entity_type: 'country', entity_id: 250, language_code: 'de', text: 'Frankreich', is_official: false },
      { entity_type: 'country', entity_id: 250, language_code: 'it', text: 'Francia', is_official: false },

      // Italy translations
      { entity_type: 'country', entity_id: 380, language_code: 'en', text: 'Italy', is_official: true },
      { entity_type: 'country', entity_id: 380, language_code: 'es', text: 'Italia', is_official: false },
      { entity_type: 'country', entity_id: 380, language_code: 'nl', text: 'Italië', is_official: false },
      { entity_type: 'country', entity_id: 380, language_code: 'fr', text: 'Italie', is_official: false },
      { entity_type: 'country', entity_id: 380, language_code: 'de', text: 'Italien', is_official: false },
      { entity_type: 'country', entity_id: 380, language_code: 'it', text: 'Italia', is_official: true },

      // Portugal translations
      { entity_type: 'country', entity_id: 620, language_code: 'en', text: 'Portugal', is_official: true },
      { entity_type: 'country', entity_id: 620, language_code: 'es', text: 'Portugal', is_official: false },
      { entity_type: 'country', entity_id: 620, language_code: 'nl', text: 'Portugal', is_official: false },
      { entity_type: 'country', entity_id: 620, language_code: 'fr', text: 'Portugal', is_official: false },
      { entity_type: 'country', entity_id: 620, language_code: 'de', text: 'Portugal', is_official: false },
      { entity_type: 'country', entity_id: 620, language_code: 'pt', text: 'Portugal', is_official: true },

      // Germany translations
      { entity_type: 'country', entity_id: 276, language_code: 'en', text: 'Germany', is_official: true },
      { entity_type: 'country', entity_id: 276, language_code: 'es', text: 'Alemania', is_official: false },
      { entity_type: 'country', entity_id: 276, language_code: 'nl', text: 'Duitsland', is_official: false },
      { entity_type: 'country', entity_id: 276, language_code: 'fr', text: 'Allemagne', is_official: false },
      { entity_type: 'country', entity_id: 276, language_code: 'de', text: 'Deutschland', is_official: true },
      { entity_type: 'country', entity_id: 276, language_code: 'it', text: 'Germania', is_official: false },

      // Netherlands translations
      { entity_type: 'country', entity_id: 528, language_code: 'en', text: 'Netherlands', is_official: true },
      { entity_type: 'country', entity_id: 528, language_code: 'es', text: 'Países Bajos', is_official: false },
      { entity_type: 'country', entity_id: 528, language_code: 'nl', text: 'Nederland', is_official: true },
      { entity_type: 'country', entity_id: 528, language_code: 'fr', text: 'Pays-Bas', is_official: false },
      { entity_type: 'country', entity_id: 528, language_code: 'de', text: 'Niederlande', is_official: false },
      { entity_type: 'country', entity_id: 528, language_code: 'it', text: 'Paesi Bassi', is_official: false },
    ];

    // Insert translations
    const { error: translationError } = await supabase
      .from('translations')
      .upsert(countryTranslations, { onConflict: 'entity_type,entity_id,language_code' });

    if (translationError) {
      throw new Error(`Failed to seed country translations: ${translationError.message}`);
    }

    console.log(`   ✅ Seeded ${countryTranslations.length} country translations`);
  },

  async rollback(supabase: any): Promise<void> {
    await supabase.from('translations').delete().eq('entity_type', 'country');
    await supabase.from('country_codes').delete().neq('id', 0);
    console.log('   🧹 Cleaned country codes and translations');
  }
};
