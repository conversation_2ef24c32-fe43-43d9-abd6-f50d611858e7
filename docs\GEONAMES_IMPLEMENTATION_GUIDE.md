# GeoNames Multi-Language Location Search System

## Overview

This document provides a comprehensive guide for the GeoNames-based location search system implementation. The system replaces the existing basic location table with a robust, multi-language search infrastructure supporting European destinations.

## System Architecture

### Core Components

1. **Configuration System** (`server/config/geonames-scope.ts`)
   - Configurable countries and languages
   - Environment-based overrides
   - Spain-focused initial setup with expansion capability

2. **Data Synchronization** (`server/services/geonames/`)
   - Automatic data download from GeoNames API
   - Intelligent filtering and processing
   - Separate Railway service deployment

3. **Search Engine** (`server/services/geonames/search-service.ts`)
   - Multi-language full-text search
   - Spatial queries with PostGIS
   - Relevance scoring and ranking

4. **API Layer** (`server/controllers/shared/geonames-controller.ts`)
   - RESTful endpoints for search operations
   - Admin endpoints for sync management
   - Comprehensive caching with Redis

### Database Schema

```sql
-- Main locations table with GeoNames structure
CREATE TABLE locations (
    id UUID PRIMARY KEY,
    geonames_id BIGINT UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    ascii_name VARCHAR(255),
    country_code CHAR(2) NOT NULL,
    admin1_code VARCHAR(20),
    admin2_code VARCHAR(20),
    feature_class CHAR(1) NOT NULL,
    feature_code VARCHAR(10) NOT NULL,
    coordinates POINT NOT NULL,
    latitude DECIMAL(10,8) NOT NULL,
    longitude DECIMAL(11,8) NOT NULL,
    population BIGINT DEFAULT 0,
    elevation INTEGER,
    timezone VARCHAR(50),
    popularity_score INTEGER DEFAULT 50,
    property_count INTEGER DEFAULT 0,
    search_vector_en TSVECTOR,
    search_vector_multi TSVECTOR
);

-- Multi-language names table
CREATE TABLE location_names (
    id UUID PRIMARY KEY,
    location_id UUID REFERENCES locations(id),
    language_code VARCHAR(7) NOT NULL,
    name VARCHAR(400) NOT NULL,
    is_preferred BOOLEAN DEFAULT FALSE,
    is_short BOOLEAN DEFAULT FALSE,
    is_colloquial BOOLEAN DEFAULT FALSE,
    is_historic BOOLEAN DEFAULT FALSE
);
```

## Configuration

### Environment Variables

```bash
# GeoNames Configuration
GEONAMES_COUNTRIES="ES"                    # Comma-separated country codes
GEONAMES_LANGUAGES="en,nl,es,ca"           # Supported languages
GEONAMES_MIN_POPULATION="1000"            # Minimum population threshold
GEONAMES_FOCUS_REGIONS="VC,AN,CT,IB"     # Spanish regions (optional)
GEONAMES_USERNAME="your_username"         # GeoNames API username
SYNC_BATCH_SIZE="1000"                    # Database insert batch size

# Database Configuration
SUPABASE_URL="your_supabase_url"
SUPABASE_SERVICE_ROLE_KEY="your_service_key"

# Caching Configuration
USE_REDIS_CACHE="true"
UPSTASH_REDIS_REST_URL="your_redis_url"
UPSTASH_REDIS_REST_TOKEN="your_redis_token"
```

### Current Spain Configuration

- **Countries**: Spain (ES)
- **Languages**: English (en), Dutch (nl), Spanish (es), Catalan (ca)
- **Regions**: Valencia, Andalusia, Catalonia, Balearic Islands
- **Data Size**: ~8,000 locations, ~25,000 alternate names (~10MB total)
- **Sync Time**: 5-10 minutes initial, 1-2 minutes incremental

## Deployment Process

### 1. Database Migration

```bash
# Apply the GeoNames schema migration
npm run migrate:geonames
```

### 2. Initial Data Sync

Two deployment options:

#### Option A: Integrated Sync (Single Service)
```bash
# Trigger sync via API endpoint
curl -X POST http://localhost:5000/api/public/geonames/admin/sync
```

#### Option B: Separate Sync Service (Railway)
```bash
# Deploy dedicated sync service
railway deploy --dockerfile Dockerfile.sync

# Configure environment variables in Railway dashboard:
# GEONAMES_COUNTRIES=ES
# GEONAMES_LANGUAGES=en,nl,es,ca
# SUPABASE_URL=...
# SUPABASE_SERVICE_ROLE_KEY=...
```

### 3. Verification

```bash
# Test system components
npm run test:geonames

# Test search functionality
curl "http://localhost:5000/api/public/geonames/search?q=madrid&lang=en"

# Check sync status
curl "http://localhost:5000/api/public/geonames/admin/sync/status"
```

## API Endpoints

### Public Search Endpoints

| Endpoint | Method | Description | Parameters |
|----------|--------|-------------|------------|
| `/api/public/geonames/search` | GET | Location search | q, lang, limit, country, lat, lng |
| `/api/public/geonames/autocomplete` | GET | Autocomplete suggestions | q, lang, limit |
| `/api/public/geonames/popular` | GET | Popular destinations | country, lang, limit |
| `/api/public/geonames/location/:id` | GET | Location details | lang |
| `/api/public/geonames/config` | GET | System configuration | - |

### Admin Endpoints

| Endpoint | Method | Description | Auth Required |
|----------|--------|-------------|---------------|
| `/api/public/geonames/admin/sync` | POST | Trigger data sync | Yes |
| `/api/public/geonames/admin/sync/status` | GET | Sync status | Yes |

### Example Requests

```javascript
// Search locations
const results = await fetch('/api/public/geonames/search?q=valencia&lang=en&limit=5');

// Autocomplete
const suggestions = await fetch('/api/public/geonames/autocomplete?q=mad&lang=es&limit=3');

// Popular destinations in Spain
const popular = await fetch('/api/public/geonames/popular?country=ES&lang=en&limit=10');

// Location details with alternate names
const location = await fetch('/api/public/geonames/location/123?lang=ca');
```

## Frontend Integration

### Service Layer

```typescript
import { geoNamesService } from '@/services/geonames-service';

// Search locations
const results = await geoNamesService.searchLocations('madrid', {
  language: 'en',
  limit: 10,
  country: 'ES'
});

// Autocomplete
const suggestions = await geoNamesService.autocomplete('barce', {
  language: 'es',
  limit: 5
});

// Get location details
const location = await geoNamesService.getLocationDetails('location-id', 'ca');
```

### React Hook Integration

```typescript
// Enhanced location autocomplete hook
export const useLocationAutocomplete = (query: string, language = 'en') => {
  return useQuery({
    queryKey: ['geonames', 'autocomplete', query, language],
    queryFn: () => geoNamesService.autocomplete(query, { language }),
    enabled: query.length >= 2,
    staleTime: 5 * 60 * 1000 // 5 minutes
  });
};
```

## Performance Optimization

### Caching Strategy

1. **Redis Distributed Cache**
   - Search results: 1 hour TTL
   - Autocomplete: 2 hours TTL
   - Location details: 6 hours TTL
   - Popular destinations: 12 hours TTL

2. **Database Indexes**
   - GIN indexes for full-text search vectors
   - GiST indexes for spatial queries
   - Trigram indexes for fuzzy matching
   - Compound indexes for common query patterns

3. **Search Optimization**
   - Language-aware search vectors
   - Popularity-based ranking
   - Population-weighted results
   - Distance-based relevance (when user location provided)

### Expected Performance

- **Cache Hit**: ~125ms response time
- **Cache Miss**: ~560ms response time
- **Search Throughput**: 1000+ requests/minute
- **Database Storage**: 10MB for Spain configuration

## Expansion Path

### Phase-based Rollout

**Phase 1**: Spain + 4 languages (current)
- Countries: ES
- Languages: en, nl, es, ca
- Data: ~8,000 locations
- Storage: ~10MB

**Phase 2**: Add France
```bash
GEONAMES_COUNTRIES="ES,FR"
GEONAMES_LANGUAGES="en,nl,es,ca,fr"
```

**Phase 3**: Add Italy
```bash
GEONAMES_COUNTRIES="ES,FR,IT"
GEONAMES_LANGUAGES="en,nl,es,ca,fr,it"
```

**Phase 4**: Full European Coverage
```bash
GEONAMES_COUNTRIES="ES,FR,IT,PT,DE,NL,BE,AT,CH"
GEONAMES_LANGUAGES="en,nl,es,ca,fr,it,pt,de"
```

### Scaling Considerations

- **Data Growth**: Linear scaling (10MB per country average)
- **Sync Time**: 5-10 minutes per country
- **Search Performance**: Maintained through proper indexing
- **Cache Distribution**: Redis cluster for high-traffic scenarios

## Monitoring and Maintenance

### Health Checks

```bash
# System health
curl /api/public/geonames/admin/sync/status

# Database connectivity
curl /api/public/geonames/config

# Search functionality
curl "/api/public/geonames/search?q=test"
```

### Maintenance Tasks

1. **Weekly**: Monitor sync logs and performance metrics
2. **Monthly**: Update GeoNames data via incremental sync
3. **Quarterly**: Review and update language configurations
4. **Annually**: Evaluate expansion to new countries

### Troubleshooting

Common issues and solutions:

1. **Sync Failures**
   - Check GeoNames API limits
   - Verify database connectivity
   - Review environment variables

2. **Search Performance**
   - Monitor cache hit rates
   - Check database index usage
   - Review query patterns

3. **Data Quality**
   - Validate population thresholds
   - Review feature code filtering
   - Check language coverage

## Security Considerations

1. **API Rate Limiting**: Implement rate limits for search endpoints
2. **Input Validation**: Sanitize all search queries
3. **Access Control**: Protect admin sync endpoints
4. **Data Privacy**: Ensure GDPR compliance for location data

## Testing Strategy

### Unit Tests
- Configuration system
- Data parsing and filtering
- Search algorithm logic
- API response formatting

### Integration Tests
- Database migration scripts
- Sync service operations
- API endpoint functionality
- Cache integration

### Performance Tests
- Search response times
- Concurrent user handling
- Database query optimization
- Cache effectiveness

### System Tests
- End-to-end search workflows
- Multi-language functionality
- Error handling and recovery
- Sync process reliability

## Support and Documentation

- **API Documentation**: Available at `/api/public/geonames/config`
- **Health Status**: Monitor at `/api/public/geonames/admin/sync/status`
- **Configuration**: Review current settings via API
- **Logs**: Comprehensive logging for all operations

This implementation provides a robust, scalable foundation for location search that can grow with your platform's needs while maintaining excellent performance and user experience.