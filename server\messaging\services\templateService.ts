import { supabase } from '../../supabase';
import { Logger } from '../../utils/logger';

export interface MessageTemplate {
  id: string;
  host_id: string;
  name: string;
  content: string;
  category: string;
  dynamic_fields: Record<string, string>;
  usage_count: number;
  is_active: boolean;
  created_at: string;
}

export interface CreateTemplateData {
  hostId: string;
  name: string;
  content: string;
  category: 'inquiry' | 'booking' | 'checkin' | 'checkout' | 'support' | 'custom';
  dynamicFields?: Record<string, string>;
}

export class TemplateService {
  
  /**
   * Get templates for a host
   */
  async getTemplates(
    hostId: string,
    category?: string,
    activeOnly: boolean = true
  ): Promise<MessageTemplate[]> {
    try {
      let query = supabase
        .from('message_templates')
        .select('*')
        .eq('host_id', hostId)
        .order('usage_count', { ascending: false });
      
      if (activeOnly) {
        query = query.eq('is_active', true);
      }
      
      if (category) {
        query = query.eq('category', category);
      }
      
      const { data: templates, error } = await query;
      
      if (error) {
        Logger.error('Error fetching templates:', error);
        throw new Error(`Failed to fetch templates: ${error.message}`);
      }
      
      return templates || [];
    } catch (error) {
      Logger.error('TemplateService.getTemplates error:', error);
      throw error;
    }
  }
  
  /**
   * Create a new template
   */
  async createTemplate(data: CreateTemplateData): Promise<MessageTemplate> {
    try {
      const { data: template, error } = await supabase
        .from('message_templates')
        .insert({
          host_id: data.hostId,
          name: data.name,
          content: data.content,
          category: data.category,
          dynamic_fields: data.dynamicFields || {},
          usage_count: 0,
          is_active: true,
          created_at: new Date().toISOString()
        })
        .select()
        .single();
      
      if (error) {
        Logger.error('Error creating template:', error);
        throw new Error(`Failed to create template: ${error.message}`);
      }
      
      Logger.info(`Template created: ${template.id} for host ${data.hostId}`);
      return template;
    } catch (error) {
      Logger.error('TemplateService.createTemplate error:', error);
      throw error;
    }
  }
  
  /**
   * Update template
   */
  async updateTemplate(
    templateId: string,
    hostId: string,
    updates: Partial<CreateTemplateData>
  ): Promise<MessageTemplate | null> {
    try {
      const { data: template, error } = await supabase
        .from('message_templates')
        .update({
          ...updates,
          dynamic_fields: updates.dynamicFields
        })
        .eq('id', templateId)
        .eq('host_id', hostId)
        .select()
        .single();
      
      if (error) {
        Logger.error('Error updating template:', error);
        throw new Error(`Failed to update template: ${error.message}`);     
      }
      
      Logger.info(`Template updated: ${templateId}`);
      return template;
    } catch (error) {
      Logger.error('TemplateService.updateTemplate error:', error);
      throw error;
    }
  }
  
  /**
   * Delete template
   */
  async deleteTemplate(templateId: string, hostId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('message_templates')
        .delete()
        .eq('id', templateId)
        .eq('host_id', hostId);
      
      if (error) {
        Logger.error('Error deleting template:', error);
        return false;
      }
      
      Logger.info(`Template deleted: ${templateId}`);
      return true;
    } catch (error) {
      Logger.error('TemplateService.deleteTemplate error:', error);
      return false;
    }
  }
  
  /**
   * Process template with dynamic fields
   */
  async processTemplate(
    templateId: string,
    hostId: string,
    variables: Record<string, string>
  ): Promise<{ content: string; template: MessageTemplate }> {
    try {
      const { data: template, error } = await supabase
        .from('message_templates')
        .select('*')
        .eq('id', templateId)
        .eq('host_id', hostId)
        .eq('is_active', true)
        .single();
      
      if (error || !template) {
        throw new Error('Template not found or inactive');
      }
      
      // Replace dynamic fields in content
      let processedContent = template.content;
      
      // Replace standard variables like {guest_name}, {property_name}, etc.
      Object.entries(variables).forEach(([key, value]) => {
        const regex = new RegExp(`\\{${key}\\}`, 'g');
        processedContent = processedContent.replace(regex, value);
      });
      
      // Update usage count
      await supabase
        .from('message_templates')
        .update({ usage_count: template.usage_count + 1 })
        .eq('id', templateId);
      
      return {
        content: processedContent,
        template
      };
    } catch (error) {
      Logger.error('TemplateService.processTemplate error:', error);
      throw error;
    }
  }
  
  /**
   * Get default templates for new hosts
   */
  async getDefaultTemplates(): Promise<Omit<CreateTemplateData, 'hostId'>[]> {
    return [
      {
        name: "Welcome Inquiry Response",
        content: "Hi {guest_name}! Thank you for your interest in {property_name}. I'd be happy to help you with any questions you have about the property.",
        category: "inquiry",
        dynamicFields: { guest_name: "Guest Name", property_name: "Property Name" }
      },
      {
        name: "Booking Confirmation",
        content: "Hello {guest_name}! Your booking for {property_name} from {check_in_date} to {check_out_date} has been confirmed. I'll send you check-in details closer to your arrival date.",
        category: "booking",
        dynamicFields: { 
          guest_name: "Guest Name", 
          property_name: "Property Name",
          check_in_date: "Check-in Date",
          check_out_date: "Check-out Date"
        }
      },
      {
        name: "Check-in Instructions",
        content: "Hi {guest_name}! Looking forward to welcoming you tomorrow. Here are your check-in details for {property_name}: [Add your specific instructions here]",
        category: "checkin",
        dynamicFields: { guest_name: "Guest Name", property_name: "Property Name" }
      },
      {
        name: "Check-out Reminder",
        content: "Hi {guest_name}! Hope you enjoyed your stay at {property_name}. Just a friendly reminder that check-out is at 11 AM. Please leave the keys as instructed. Thank you!",
        category: "checkout",
        dynamicFields: { guest_name: "Guest Name", property_name: "Property Name" }
      },
      {
        name: "General Support",
        content: "Hi {guest_name}! I received your message and I'm here to help. Let me get back to you with a solution as soon as possible.",
        category: "support",
        dynamicFields: { guest_name: "Guest Name" }
      }
    ];
  }
  
  /**
   * Create default templates for a new host
   */
  async createDefaultTemplatesForHost(hostId: string): Promise<MessageTemplate[]> {
    try {
      const defaultTemplates = await this.getDefaultTemplates();
      const createdTemplates: MessageTemplate[] = [];
      
      for (const templateData of defaultTemplates) {
        const template = await this.createTemplate({
          ...templateData,
          hostId
        });
        createdTemplates.push(template);
      }
      
      Logger.info(`Created ${createdTemplates.length} default templates for host ${hostId}`);
      return createdTemplates;
    } catch (error) {
      Logger.error('TemplateService.createDefaultTemplatesForHost error:', error);
      throw error;
    }
  }
}

export const templateService = new TemplateService();