import { useState } from 'react';
import { Calendar, Users, Check } from 'lucide-react';
import { useTranslations } from '@/lib/translations';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { DateRangePicker } from '@/components/property-searcher/DateRangePicker';
import { GuestSelector } from '@/components/property-searcher/GuestSelector';
import type { PropertyDetails } from '../types';

interface BookingCardProps {
  property: PropertyDetails;
}

export function BookingCard({ property }: BookingCardProps) {
  const t = useTranslations('propertyDetails');
  const [dateRange, setDateRange] = useState<{ from: Date; to: Date } | undefined>();
  const [guests, setGuests] = useState({
    adults: 1,
    children: 0,
    infants: 0,
    pets: 0
  });

  const totalGuests = guests.adults + guests.children;
  const pricePerNight = property.pricePerNight;
  const nights = dateRange ? Math.ceil((dateRange.to.getTime() - dateRange.from.getTime()) / (1000 * 60 * 60 * 24)) : 0;
  const subtotal = nights * pricePerNight;
  const serviceFee = Math.round(subtotal * 0.14);
  const total = subtotal + serviceFee;

  return (
    <Card className="lg:sticky lg:top-6 shadow-lg border-2 mx-0">
      <CardHeader className="pb-3 px-4 lg:px-6">
        <div className="flex items-baseline space-x-2">
          <span className="text-xl lg:text-2xl font-bold">€ {pricePerNight},-</span>
          <span className="text-gray-600 text-sm lg:text-base">{t('perNight')}</span>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-3 lg:space-y-4 px-4 lg:px-6">
        {/* Date Selection */}
        <div className="border rounded-lg">
          <DateRangePicker
            onDateRangeChange={setDateRange}
            placeholder={t('selectDates')}
          />
        </div>

        {/* Guest Selection */}
        <div className="border rounded-lg">
          <GuestSelector
            guests={guests}
            onChange={setGuests}
            placeholder={t('addGuests')}
          />
        </div>

        {/* Reserve Button */}
        <Button 
          className="w-full bg-rose-600 hover:bg-rose-700 text-white font-medium py-2.5 lg:py-3 text-sm lg:text-base"
          disabled={!dateRange || totalGuests === 0}
        >
          {t('reserve')}
        </Button>

        <p className="text-center text-xs lg:text-sm text-gray-600">
          {t('noChargeYet')}
        </p>

        {/* Price Breakdown */}
        {dateRange && nights > 0 && (
          <div className="space-y-3 pt-4 border-t">
            <div className="flex justify-between items-center">
              <span className="underline text-sm">€ {pricePerNight} x {nights} {t('nights')}</span>
              <span className="font-medium">€ {subtotal}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="underline text-sm">{t('serviceFee')}</span>
              <span className="font-medium">€ {serviceFee}</span>
            </div>
            <div className="flex justify-between items-center font-medium text-lg border-t pt-3">
              <span>{t('total')}</span>
              <span>€ {total}</span>
            </div>
          </div>
        )}

        {/* Trust Indicators */}
        {property.trustIndicators && (
          <div className="space-y-2 pt-4 border-t">
            {property.trustIndicators.freeBookingChanges && (
              <div className="flex items-start space-x-2 text-sm">
                <Check className="h-4 w-4 text-green-600 flex-shrink-0 mt-0.5" />
                <span className="leading-relaxed">{t('trustIndicators.freeBookingChanges')}</span>
              </div>
            )}
            
            {property.trustIndicators.directCommunication && (
              <div className="flex items-start space-x-2 text-sm">
                <Check className="h-4 w-4 text-green-600 flex-shrink-0 mt-0.5" />
                <span className="leading-relaxed">{t('trustIndicators.directCommunication')}</span>
              </div>
            )}
            
            {property.trustIndicators.trustedReviews && (
              <div className="flex items-start space-x-2 text-sm">
                <Check className="h-4 w-4 text-green-600 flex-shrink-0 mt-0.5" />
                <span className="leading-relaxed">{t('trustIndicators.trustedReviews')}</span>
              </div>
            )}
            
            {property.trustIndicators.securePayment && (
              <div className="flex items-start space-x-2 text-sm">
                <Check className="h-4 w-4 text-green-600 flex-shrink-0 mt-0.5" />
                <span className="leading-relaxed">{t('trustIndicators.securePayment')}</span>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}