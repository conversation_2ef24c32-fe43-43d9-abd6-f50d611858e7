/**
 * GeoNames Data Migration Seeder
 * 
 * Updates existing geonames_locations to reference the new code-based system.
 * Links country_code strings to country_code_id and admin1_code to region_code_id.
 */

export default {
  name: '05_geonames_migration',
  description: 'Migrate existing GeoNames data to code-based system',
  environment: 'shared' as const,
  order: 6,

  async execute(supabase: any): Promise<void> {
    console.log('   🔄 Migrating existing GeoNames data...');

    // Update country_code_id based on existing country_code
    console.log('   📍 Linking country codes...');
    
    const countryUpdateQuery = `
      UPDATE geonames_locations 
      SET country_code_id = country_codes.id
      FROM country_codes 
      WHERE geonames_locations.country_code = country_codes.iso_alpha2
        AND geonames_locations.country_code_id IS NULL
    `;

    const { error: countryError } = await supabase.rpc('execute_sql', { 
      sql: countryUpdateQuery 
    });

    if (countryError) {
      console.warn('   ⚠️ Could not execute country code linking (may need manual SQL execution)');
      console.log('   📝 Manual SQL:', countryUpdateQuery);
    } else {
      console.log('   ✅ Linked country codes');
    }

    // Update region_code_id based on existing admin1_code
    console.log('   🏛️ Linking region codes...');
    
    const regionUpdateQuery = `
      UPDATE geonames_locations 
      SET region_code_id = region_codes.id
      FROM region_codes 
      WHERE geonames_locations.admin1_code = region_codes.geonames_admin1_code
        AND geonames_locations.country_code = 'ES'
        AND geonames_locations.region_code_id IS NULL
    `;

    const { error: regionError } = await supabase.rpc('execute_sql', { 
      sql: regionUpdateQuery 
    });

    if (regionError) {
      console.warn('   ⚠️ Could not execute region code linking (may need manual SQL execution)');
      console.log('   📝 Manual SQL:', regionUpdateQuery);
    } else {
      console.log('   ✅ Linked region codes');
    }

    // Update tourism_region_code_id based on coordinate boundaries
    console.log('   🏖️ Linking tourism regions...');
    
    // Costa Blanca (Valencia region)
    const costaBlancaregionQuery = `
      UPDATE geonames_locations 
      SET tourism_region_code_id = 3
      WHERE country_code = 'ES'
        AND admin1_code = 'VC'
        AND admin2_code = 'A'
        AND latitude BETWEEN 37.8 AND 38.8
        AND longitude BETWEEN -0.8 AND -0.1
        AND tourism_region_code_id IS NULL
    `;

    // Costa Brava (Catalonia region)
    const costaBravaQuery = `
      UPDATE geonames_locations 
      SET tourism_region_code_id = 1
      WHERE country_code = 'ES'
        AND admin1_code = 'CT'
        AND admin2_code = 'GI'
        AND latitude BETWEEN 41.6 AND 42.3
        AND longitude BETWEEN 2.8 AND 3.3
        AND tourism_region_code_id IS NULL
    `;

    // Costa del Sol (Andalusia region)
    const costaDelSolQuery = `
      UPDATE geonames_locations 
      SET tourism_region_code_id = 2
      WHERE country_code = 'ES'
        AND admin1_code = 'AN'
        AND admin2_code = 'MA'
        AND latitude BETWEEN 36.3 AND 36.8
        AND longitude BETWEEN -5.2 AND -3.8
        AND tourism_region_code_id IS NULL
    `;

    // Balearic Islands
    const balearicQuery = `
      UPDATE geonames_locations 
      SET tourism_region_code_id = 6
      WHERE country_code = 'ES'
        AND admin1_code = 'IB'
        AND latitude BETWEEN 39.2 AND 40.1
        AND longitude BETWEEN 2.3 AND 4.3
        AND tourism_region_code_id IS NULL
    `;

    // Canary Islands
    const canaryQuery = `
      UPDATE geonames_locations 
      SET tourism_region_code_id = 7
      WHERE country_code = 'ES'
        AND admin1_code = 'CN'
        AND latitude BETWEEN 27.6 AND 29.4
        AND longitude BETWEEN -18.2 AND -13.4
        AND tourism_region_code_id IS NULL
    `;

    const tourismQueries = [
      { name: 'Costa Blanca', query: costaBlancaregionQuery },
      { name: 'Costa Brava', query: costaBravaQuery },
      { name: 'Costa del Sol', query: costaDelSolQuery },
      { name: 'Balearic Islands', query: balearicQuery },
      { name: 'Canary Islands', query: canaryQuery }
    ];

    for (const { name, query } of tourismQueries) {
      try {
        const { error } = await supabase.rpc('execute_sql', { sql: query });
        if (error) {
          console.warn(`   ⚠️ Could not link ${name} tourism region`);
          console.log(`   📝 Manual SQL for ${name}:`, query);
        } else {
          console.log(`   ✅ Linked ${name} tourism region`);
        }
      } catch (error) {
        console.warn(`   ⚠️ Could not execute ${name} tourism region linking`);
      }
    }

    // Get statistics
    try {
      const { data: stats } = await supabase.rpc('execute_sql', { 
        sql: `
          SELECT 
            COUNT(*) as total_locations,
            COUNT(country_code_id) as linked_countries,
            COUNT(region_code_id) as linked_regions,
            COUNT(tourism_region_code_id) as linked_tourism_regions
          FROM geonames_locations 
          WHERE country_code = 'ES'
        `
      });

      if (stats && stats.length > 0) {
        const stat = stats[0];
        console.log(`   📊 Migration Statistics:`);
        console.log(`      Total Spanish locations: ${stat.total_locations}`);
        console.log(`      Linked to countries: ${stat.linked_countries}`);
        console.log(`      Linked to regions: ${stat.linked_regions}`);
        console.log(`      Linked to tourism regions: ${stat.linked_tourism_regions}`);
      }
    } catch (error) {
      console.log('   📊 Could not retrieve migration statistics');
    }

    console.log('   ✅ GeoNames data migration completed');
  },

  async rollback(supabase: any): Promise<void> {
    // Reset the code references
    const resetQuery = `
      UPDATE geonames_locations 
      SET 
        country_code_id = NULL,
        region_code_id = NULL,
        tourism_region_code_id = NULL
      WHERE country_code_id IS NOT NULL 
         OR region_code_id IS NOT NULL 
         OR tourism_region_code_id IS NOT NULL
    `;

    try {
      await supabase.rpc('execute_sql', { sql: resetQuery });
      console.log('   🧹 Reset GeoNames code references');
    } catch (error) {
      console.log('   📝 Manual rollback SQL:', resetQuery);
    }
  }
};
