#!/bin/bash
echo "🧹 Starting Git repository cleanup..."

# Step 1: Remove large files from Git cache
echo "Removing large files from Git index..."
git rm --cached --ignore-unmatch data/geonames/alternateNamesV2.txt
git rm --cached --ignore-unmatch data/geonames/ES.txt
git rm --cached --ignore-unmatch data/geonames/*.zip

# Step 2: Add clean changes
echo "Adding clean changes..."
git add .gitignore scripts/ replit.md server/

# Step 3: Create clean commit
echo "Creating clean commit..."
git commit -m "Clean repository: Remove large data files

- Remove 724MB alternateNamesV2.txt from Git tracking
- Tourism region system uses on-demand downloads  
- Reduces repository size for successful push"

# Step 4: Aggressive cleanup
echo "Cleaning Git objects..."
git gc --aggressive --prune=now

# Step 5: Check size
echo "Repository size after cleanup:"
du -sh .git

echo "✅ Cleanup completed! Now try: git push origin main"