import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useUser } from '@/features/shared/auth/hooks/useAuth';

// Guest API hooks for dashboard data integration
export const useGuestProfile = () => {
  const { data: user } = useUser();
  
  return useQuery({
    queryKey: ['guest-profile', user?.id],
    queryFn: async () => {
      if (!user?.id) return null;
      const response = await fetch(`/api/guest/profile/${user.id}`);
      if (!response.ok) {
        if (response.status === 404) return null;
        throw new Error('Failed to fetch guest profile');
      }
      return response.json();
    },
    enabled: !!user?.id
  });
};

export const useGuestBookings = () => {
  const { data: user } = useUser();
  
  return useQuery({
    queryKey: ['guest-bookings', user?.id],
    queryFn: async () => {
      if (!user?.id) return [];
      const response = await fetch(`/api/guest/bookings/${user.id}`);
      if (!response.ok) {
        throw new Error('Failed to fetch guest bookings');
      }
      return response.json();
    },
    enabled: !!user?.id
  });
};

export const useGuestWishlists = () => {
  const { data: user } = useUser();
  
  return useQuery({
    queryKey: ['guest-wishlists', user?.id],
    queryFn: async () => {
      if (!user?.id) return [];
      const response = await fetch(`/api/guest/wishlists/${user.id}`);
      if (!response.ok) {
        throw new Error('Failed to fetch guest wishlists');
      }
      return response.json();
    },
    enabled: !!user?.id
  });
};

export const useGuestReviews = () => {
  const { data: user } = useUser();
  
  return useQuery({
    queryKey: ['guest-reviews', user?.id],
    queryFn: async () => {
      if (!user?.id) return [];
      const response = await fetch(`/api/guest/reviews/${user.id}`);
      if (!response.ok) {
        throw new Error('Failed to fetch guest reviews');
      }
      return response.json();
    },
    enabled: !!user?.id
  });
};

export const useGuestMessages = () => {
  const { data: user } = useUser();
  
  return useQuery({
    queryKey: ['guest-messages', user?.id],
    queryFn: async () => {
      if (!user?.id) return [];
      const response = await fetch(`/api/guest/messages/${user.id}`);
      if (!response.ok) {
        throw new Error('Failed to fetch guest messages');
      }
      return response.json();
    },
    enabled: !!user?.id
  });
};

export const useGuestHelpArticles = () => {
  return useQuery({
    queryKey: ['guest-help-articles'],
    queryFn: async () => {
      const response = await fetch('/api/guest/help/articles');
      if (!response.ok) {
        throw new Error('Failed to fetch help articles');
      }
      return response.json();
    }
  });
};

// Mutation hooks for guest actions
export const useCreateGuestWishlist = () => {
  const queryClient = useQueryClient();
  const { data: user } = useUser();
  
  return useMutation({
    mutationFn: async (wishlistData: { name: string; description?: string }) => {
      if (!user?.id) throw new Error('User not authenticated');
      
      const response = await fetch('/api/guest/wishlists', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...wishlistData,
          guest_id: user.id
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to create wishlist');
      }
      
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['guest-wishlists', user?.id] });
    }
  });
};

export const useUpdateGuestProfile = () => {
  const queryClient = useQueryClient();
  const { data: user } = useUser();
  
  return useMutation({
    mutationFn: async (profileData: any) => {
      if (!user?.id) throw new Error('User not authenticated');
      
      const response = await fetch(`/api/guest/profile/${user.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(profileData),
      });
      
      if (!response.ok) {
        throw new Error('Failed to update profile');
      }
      
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['guest-profile', user?.id] });
    }
  });
};