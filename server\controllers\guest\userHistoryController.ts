import { Request, Response } from 'express';
import { Logger } from '../../utils/logger';
import { supabase } from '../../supabase';
import { cacheBackend } from '../../dal/cache/redisCache';

export interface SearchHistoryItem {
  id: string;
  name: string;
  type: 'country' | 'city' | 'village' | 'beach' | 'region';
  country: string;
  region?: string;
  searchedAt: number;
  // Complete search state (optional for backward compatibility)
  checkIn?: string;
  checkOut?: string;
  guests?: {
    adults: number;
    children: number;
    infants: number;
    pets: number;
  };
}

export class UserHistoryController {

  async getSearchHistory(req: Request, res: Response) {
    const startTime = Date.now();
    try {
      const userId = (req as any).session?.user?.id || null;
      const sessionId = userId ? null : 'anonymous'; // Use session ID for anonymous users
      
      Logger.info(`Search history requested for user: ${userId || 'anonymous'}`);
      
      // Check cache first
      const cacheKey = `user-history:search:${userId || sessionId}`;
      const cached = await cacheBackend.get(cacheKey);
      if (cached) {
        Logger.api('GET', '/api/user/search-history', 200, Date.now() - startTime);
        return res.json(cached);
      }
      
      // Get search history from database
      if (!supabase) {
        Logger.warn('Supabase client not available, returning empty search history');
        return res.json([]);
      }
      
      let query = supabase.from('guest_search_history').select('*');
      
      if (userId) {
        query = query.eq('user_id', userId);
      } else {
        query = query.eq('session_id', sessionId);
      }
      
      const { data: historyData, error } = await query
        .order('created_at', { ascending: false })
        .limit(5);
      
      if (error) {
        throw error;
      }
      
      // Transform database format to frontend format
      const history = (historyData || []).map(item => ({
        id: `${item.location}-${Date.parse(item.created_at || '')}`,
        name: item.location,
        type: 'city' as const,
        country: 'Spain', // Default for Costa Blanca properties
        region: 'Costa Blanca',
        searchedAt: Date.parse(item.created_at || ''),
        checkIn: item.check_in,
        checkOut: item.check_out,
        guests: item.guests
      }));
      
      // Cache for 5 minutes (search history changes frequently)
      await cacheBackend.set(cacheKey, history, 300);
      
      Logger.api('GET', '/api/user/search-history', 200, Date.now() - startTime);
      res.json(history);
    } catch (error) {
      Logger.error('Error fetching search history', error);
      Logger.api('GET', '/api/user/search-history', 500, Date.now() - startTime);
      res.status(500).json({ error: 'Failed to fetch search history' });
    }
  }

  async addToSearchHistory(req: Request, res: Response) {
    const startTime = Date.now();
    try {
      const userId = (req as any).session?.user?.id || null;
      const sessionId = userId ? null : 'anonymous';
      const historyItem: SearchHistoryItem = req.body;
      
      // Add to database
      if (!supabase) {
        Logger.warn('Supabase client not available, cannot add to search history');
        return res.status(500).json({ error: 'Database service unavailable' });
      }
      
      const { error } = await supabase
        .from('guest_search_history')
        .insert({
          user_id: userId,
          session_id: sessionId,
          location: historyItem.name,
          check_in: historyItem.checkIn,
          check_out: historyItem.checkOut,
          guests: historyItem.guests
        });
      
      if (error) {
        throw error;
      }
      
      // Clear cache to force refresh on next request
      const cacheKey = `user-history:search:${userId || sessionId}`;
      await cacheBackend.delete(cacheKey);

      Logger.info(`Search history item added for user: ${userId || 'anonymous'}`, { item: historyItem.name });
      Logger.api('POST', '/api/user/search-history', 200, Date.now() - startTime);
      
      res.json({ success: true });
    } catch (error) {
      Logger.error('Error adding to search history', error);
      Logger.api('POST', '/api/user/search-history', 500, Date.now() - startTime);
      res.status(500).json({ error: 'Failed to add to search history' });
    }
  }

  async clearSearchHistory(req: Request, res: Response) {
    const startTime = Date.now();
    try {
      const userId = (req as any).session?.user?.id || null;
      const sessionId = userId ? null : 'anonymous';
      
      // Clear from database
      if (!supabase) {
        Logger.warn('Supabase client not available, cannot clear search history');
        return res.status(500).json({ error: 'Database service unavailable' });
      }
      
      let query = supabase.from('guest_search_history').delete();
      
      if (userId) {
        query = query.eq('user_id', userId);
      } else {
        query = query.eq('session_id', sessionId);
      }
      
      const { error } = await query;
      
      if (error) {
        throw error;
      }
      
      // Clear cache
      const cacheKey = `user-history:search:${userId || sessionId}`;
      await cacheBackend.delete(cacheKey);

      Logger.info(`Search history cleared for user: ${userId || 'anonymous'}`);
      Logger.api('DELETE', '/api/user/search-history', 200, Date.now() - startTime);
      
      res.json({ success: true });
    } catch (error) {
      Logger.error('Error clearing search history', error);
      Logger.api('DELETE', '/api/user/search-history', 500, Date.now() - startTime);
      res.status(500).json({ error: 'Failed to clear search history' });
    }
  }
}

export const userHistoryController = new UserHistoryController();