import { 
  supabase, 
  supabase<PERSON>dmin,
  User, 
  InsertUser, 
  HostProperty, 
  InsertHostProperty, 
  GuestBooking, 
  InsertGuestBooking, 
  GuestReview, 
  InsertGuestReview, 
  GuestSearchHistory, 
  InsertGuestSearchHistory,
  GuestProfile,
  InsertGuestProfile,
  GuestWishlist,
  InsertGuestWishlist,
  GuestWishlistProperty,
  InsertGuestWishlistProperty,
  CommConversation,
  InsertCommConversation,
  CommMessage,
  InsertCommMessage,
  HelpArticle,
  InsertHelpArticle,
} from "./supabase";

export interface IStorage {
  // User methods
  getUser(id: string): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  getUserByEmail(email: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  updateUser(id: string, updates: Partial<User>): Promise<User | undefined>;
  
  // Host property methods (renamed from property methods)
  getHostProperty(id: string): Promise<HostProperty | undefined>;
  getHostPropertiesByHost(hostId: string): Promise<HostProperty[]>;
  getHostPropertiesByLocation(city: string, country: string): Promise<HostProperty[]>;
  searchHostProperties(filters: {
    location?: string;
    checkIn?: Date;
    checkOut?: Date;
    maxGuests?: number;
    minPrice?: number;
    maxPrice?: number;
  }): Promise<HostProperty[]>;
  createHostProperty(property: InsertHostProperty): Promise<HostProperty>;
  updateHostProperty(id: string, updates: Partial<HostProperty>): Promise<HostProperty | undefined>;
  
  // Property draft methods
  getPropertyDraft(id: string): Promise<any | undefined>;
  createPropertyDraft(draft: any): Promise<any>;
  updatePropertyDraft(id: string, updates: any): Promise<any | undefined>;
  deletePropertyDraft(id: string): Promise<void>;
  
  // Guest booking methods (renamed from booking methods)
  getGuestBooking(id: string): Promise<GuestBooking | undefined>;
  getGuestBookingsByGuest(guestId: string): Promise<GuestBooking[]>;
  getGuestBookingsByProperty(propertyId: string): Promise<GuestBooking[]>;
  getGuestBookingsByHost(hostId: string): Promise<GuestBooking[]>;
  createGuestBooking(booking: InsertGuestBooking): Promise<GuestBooking>;
  updateGuestBooking(id: string, updates: Partial<GuestBooking>): Promise<GuestBooking | undefined>;
  
  // Guest review methods (renamed from review methods)
  getGuestReviewsByProperty(propertyId: string): Promise<GuestReview[]>;
  getGuestReviewsByGuest(guestId: string): Promise<GuestReview[]>;
  createGuestReview(review: InsertGuestReview): Promise<GuestReview>;
  
  // Guest search history methods
  getGuestSearchHistory(userId?: string, sessionId?: string): Promise<GuestSearchHistory[]>;
  addGuestSearchHistory(search: InsertGuestSearchHistory): Promise<GuestSearchHistory>;
  clearGuestSearchHistory(userId?: string, sessionId?: string): Promise<void>;
  
  // Guest profile methods
  getGuestProfile(userId: string): Promise<GuestProfile | undefined>;
  createGuestProfile(profile: InsertGuestProfile): Promise<GuestProfile>;
  updateGuestProfile(userId: string, updates: Partial<GuestProfile>): Promise<GuestProfile | undefined>;
  
  // Guest wishlist methods
  getGuestWishlists(guestId: string): Promise<GuestWishlist[]>;
  getGuestWishlist(id: string): Promise<GuestWishlist | undefined>;
  createGuestWishlist(wishlist: InsertGuestWishlist): Promise<GuestWishlist>;
  updateGuestWishlist(id: string, updates: Partial<GuestWishlist>): Promise<GuestWishlist | undefined>;
  deleteGuestWishlist(id: string): Promise<void>;
  addPropertyToWishlist(wishlistProperty: InsertGuestWishlistProperty): Promise<GuestWishlistProperty>;
  removePropertyFromWishlist(wishlistId: string, propertyId: string): Promise<void>;
  getWishlistProperties(wishlistId: string): Promise<GuestWishlistProperty[]>;
  
  // Communication methods
  getConversations(userId: string, userType: 'guest' | 'host'): Promise<CommConversation[]>;
  getConversation(id: string): Promise<CommConversation | undefined>;
  createConversation(conversation: InsertCommConversation): Promise<CommConversation>;
  getMessages(conversationId: string): Promise<CommMessage[]>;
  sendMessage(message: InsertCommMessage): Promise<CommMessage>;
  markMessagesAsRead(conversationId: string, userId: string): Promise<void>;
  
  // Help methods
  getHelpArticles(category?: string): Promise<HelpArticle[]>;
  getHelpArticle(id: string): Promise<HelpArticle | undefined>;
  getFAQs(): Promise<HelpArticle[]>;
  createHelpArticle(article: InsertHelpArticle): Promise<HelpArticle>;
  updateHelpArticle(id: string, updates: Partial<HelpArticle>): Promise<HelpArticle | undefined>;
  deleteHelpArticle(id: string): Promise<void>;
}

// Supabase Storage Implementation
class SupabaseStorage implements IStorage {
  // In-memory storage for property drafts (temporary solution)
  private propertyDrafts: Map<string, any> = new Map();
  // User methods
  async getUser(id: string): Promise<User | undefined> {
    console.log('🔍 SupabaseStorage.getUser called with ID:', id);
    
    if (!supabaseAdmin) {
      console.error('❌ Supabase admin client not available in getUser');
      return undefined;
    }
    
    console.log('✅ Supabase admin client available, querying database...');
    
    const { data, error } = await supabaseAdmin
      .from('users')
      .select('*')
      .eq('id', id);
    
    console.log('📊 Database query result:');
    console.log('  Data:', data);
    console.log('  Error:', error);
    console.log('  Data length:', data?.length || 0);
    
    if (error) {
      console.error('❌ Database error in getUser:', error);
      return undefined;
    }
    
    // Handle array response - return first user or undefined
    const user = data && data.length > 0 ? data[0] : undefined;
    console.log('✅ User found:', user?.email || 'No user found');
    return user;
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    if (!supabaseAdmin) {
      console.warn('Supabase admin client not available');
      return undefined;
    }
    
    const { data, error } = await supabaseAdmin
      .from('users')
      .select('*')
      .eq('username', username);
    
    if (error) return undefined;
    // Handle array response - return first user or undefined
    return data && data.length > 0 ? data[0] : undefined;
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    console.log('🔍 SupabaseStorage.getUserByEmail called with email:', email);
    
    if (!supabaseAdmin) {
      console.error('❌ Supabase admin client not available in getUserByEmail');
      return undefined;
    }
    
    console.log('✅ Supabase admin client available, querying database by email...');
    
    const { data, error } = await supabaseAdmin
      .from('users')
      .select('*')
      .eq('email', email);
    
    console.log('📊 Database query result (by email):');
    console.log('  Data:', data);
    console.log('  Error:', error);
    console.log('  Data length:', data?.length || 0);
    
    if (error) {
      console.error('❌ Database error in getUserByEmail:', error);
      return undefined;
    }
    
    // Handle array response - return first user or undefined
    const user = data && data.length > 0 ? data[0] : undefined;
    console.log('✅ User found by email:', user?.email || 'No user found');
    return user;
  }

  async createUser(user: InsertUser): Promise<User> {
    // Use admin client for user creation to bypass RLS
    const client = supabaseAdmin || supabase;
    
    if (!client) {
      throw new Error('Supabase client not available');
    }
    
    console.log('🔧 Creating user with client type:', client === supabaseAdmin ? 'admin' : 'regular');
    console.log('🔧 User data:', { email: user.email, username: user.username });
    
    const { data, error } = await client
      .from('users')
      .insert([user])
      .select()
      .single();
    
    if (error) {
      console.error('User creation error:', error);
      throw new Error(`Failed to create user: ${error.message}`);
    }
    
    console.log('✅ User created successfully:', data.email);
    return data;
  }

  async updateUser(id: string, updates: Partial<User>): Promise<User | undefined> {
    // Use admin client for user updates to bypass RLS
    const client = supabaseAdmin || supabase;
    
    if (!client) {
      console.warn('Supabase client not available');
      return undefined;
    }
    
    console.log('🔧 Updating user with admin client:', { id, updates });
    
    const { data, error } = await client
      .from('users')
      .update(updates)
      .eq('id', id)
      .select()
      .single();
    
    if (error) {
      console.error('❌ User update error:', error);
      return undefined;
    }
    
    console.log('✅ User updated successfully:', data.email);
    return data;
  }

  // Host property methods
  async getHostProperty(id: string): Promise<HostProperty | undefined> {
    if (!supabase) {
      console.warn('Supabase client not available');
      return undefined;
    }
    
    const { data, error } = await supabase
      .from('host_properties')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) return undefined;
    return data;
  }

  async getHostPropertiesByHost(hostId: string): Promise<HostProperty[]> {
    if (!supabase) {
      console.warn('Supabase client not available');
      return [];
    }
    
    const { data, error } = await supabase
      .from('host_properties')
      .select('*')
      .eq('host_id', hostId)
      .order('created_at', { ascending: false });
    
    if (error) return [];
    return data || [];
  }

  async getHostPropertiesByLocation(city: string, country: string): Promise<HostProperty[]> {
    if (!supabase) {
      console.warn('Supabase client not available');
      return [];
    }
    
    const { data, error } = await supabase
      .from('host_properties')
      .select('*')
      .eq('city', city)
      .eq('country', country)
      .eq('is_active', true)
      .order('created_at', { ascending: false });
    
    if (error) return [];
    return data || [];
  }

  async searchHostProperties(filters: {
    location?: string;
    checkIn?: Date;
    checkOut?: Date;
    maxGuests?: number;
    minPrice?: number;
    maxPrice?: number;
  }): Promise<HostProperty[]> {
    if (!supabase) {
      console.warn('Supabase client not available');
      return [];
    }
    
    let query = supabase
      .from('host_properties')
      .select('*')
      .eq('is_active', true);
    
    if (filters.location) {
      query = query.or(`city.ilike.%${filters.location}%,location.ilike.%${filters.location}%`);
    }
    
    if (filters.maxGuests) {
      query = query.gte('max_guests', filters.maxGuests);
    }
    
    if (filters.minPrice) {
      query = query.gte('price_per_night', filters.minPrice);
    }
    
    if (filters.maxPrice) {
      query = query.lte('price_per_night', filters.maxPrice);
    }
    
    const { data, error } = await query.order('created_at', { ascending: false });
    
    if (error) return [];
    return data || [];
  }

  async createHostProperty(property: InsertHostProperty): Promise<HostProperty> {
    if (!supabase) {
      throw new Error('Supabase client not available');
    }
    
    const { data, error } = await supabase
      .from('host_properties')
      .insert([property])
      .select()
      .single();
    
    if (error) throw new Error(`Failed to create host property: ${error.message}`);
    return data;
  }

  async updateHostProperty(id: string, updates: Partial<HostProperty>): Promise<HostProperty | undefined> {
    if (!supabase) {
      console.warn('Supabase client not available');
      return undefined;
    }
    
    const { data, error } = await supabase
      .from('host_properties')
      .update(updates)
      .eq('id', id)
      .select()
      .single();
    
    if (error) return undefined;
    return data;
  }

  // Property draft methods (in-memory storage)
  async getPropertyDraft(id: string): Promise<any | undefined> {
    return this.propertyDrafts.get(id);
  }

  async createPropertyDraft(draft: any): Promise<any> {
    this.propertyDrafts.set(draft.id, draft);
    return draft;
  }

  async updatePropertyDraft(id: string, updates: any): Promise<any | undefined> {
    const existingDraft = this.propertyDrafts.get(id);
    if (!existingDraft) return undefined;
    
    const updatedDraft = { ...existingDraft, ...updates };
    this.propertyDrafts.set(id, updatedDraft);
    return updatedDraft;
  }

  async deletePropertyDraft(id: string): Promise<void> {
    this.propertyDrafts.delete(id);
  }

  // Guest booking methods
  async getGuestBooking(id: string): Promise<GuestBooking | undefined> {
    if (!supabase) {
      console.warn('Supabase client not available');
      return undefined;
    }
    
    const { data, error } = await supabase
      .from('guest_bookings')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) return undefined;
    return data;
  }

  async getGuestBookingsByGuest(guestId: string): Promise<GuestBooking[]> {
    if (!supabase) {
      console.warn('Supabase client not available');
      return [];
    }
    
    const { data, error } = await supabase
      .from('guest_bookings')
      .select('*')
      .eq('guest_id', guestId)
      .order('created_at', { ascending: false });
    
    if (error) return [];
    return data || [];
  }

  async getGuestBookingsByProperty(propertyId: string): Promise<GuestBooking[]> {
    if (!supabase) {
      console.warn('Supabase client not available');
      return [];
    }
    
    const { data, error } = await supabase
      .from('guest_bookings')
      .select('*')
      .eq('host_property_id', propertyId)
      .order('created_at', { ascending: false });
    
    if (error) return [];
    return data || [];
  }

  async getGuestBookingsByHost(hostId: string): Promise<GuestBooking[]> {
    if (!supabase) {
      console.warn('Supabase client not available');
      return [];
    }
    
    const { data, error } = await supabase
      .from('guest_bookings')
      .select('*, host_properties!inner(host_id)')
      .eq('host_properties.host_id', hostId)
      .order('created_at', { ascending: false });
    
    if (error) return [];
    return data || [];
  }

  async createGuestBooking(booking: InsertGuestBooking): Promise<GuestBooking> {
    if (!supabase) {
      throw new Error('Supabase client not available');
    }
    
    const { data, error } = await supabase
      .from('guest_bookings')
      .insert([booking])
      .select()
      .single();
    
    if (error) throw new Error(`Failed to create guest booking: ${error.message}`);
    return data;
  }

  async updateGuestBooking(id: string, updates: Partial<GuestBooking>): Promise<GuestBooking | undefined> {
    if (!supabase) {
      console.warn('Supabase client not available');
      return undefined;
    }
    
    const { data, error } = await supabase
      .from('guest_bookings')
      .update(updates)
      .eq('id', id)
      .select()
      .single();
    
    if (error) return undefined;
    return data;
  }

  // Guest review methods
  async getGuestReviewsByProperty(propertyId: string): Promise<GuestReview[]> {
    if (!supabase) {
      console.warn('Supabase client not available');
      return [];
    }
    
    const { data, error } = await supabase
      .from('guest_reviews')
      .select('*')
      .eq('host_property_id', propertyId)
      .order('created_at', { ascending: false });
    
    if (error) return [];
    return data || [];
  }

  async getGuestReviewsByGuest(guestId: string): Promise<GuestReview[]> {
    if (!supabase) {
      console.warn('Supabase client not available');
      return [];
    }
    
    const { data, error } = await supabase
      .from('guest_reviews')
      .select('*')
      .eq('guest_id', guestId)
      .order('created_at', { ascending: false });
    
    if (error) return [];
    return data || [];
  }

  async createGuestReview(review: InsertGuestReview): Promise<GuestReview> {
    if (!supabase) {
      throw new Error('Supabase client not available');
    }
    
    const { data, error } = await supabase
      .from('guest_reviews')
      .insert([review])
      .select()
      .single();
    
    if (error) throw new Error(`Failed to create guest review: ${error.message}`);
    return data;
  }

  // Guest search history methods
  async getGuestSearchHistory(userId?: string, sessionId?: string): Promise<GuestSearchHistory[]> {
    if (!supabase) {
      console.warn('Supabase client not available');
      return [];
    }
    
    let query = supabase
      .from('guest_search_history')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(10);

    if (userId) {
      query = query.eq('user_id', userId);
    } else if (sessionId) {
      query = query.eq('session_id', sessionId);
    }

    const { data, error } = await query;
    
    if (error) return [];
    return data || [];
  }

  async addGuestSearchHistory(search: InsertGuestSearchHistory): Promise<GuestSearchHistory> {
    if (!supabase) {
      throw new Error('Supabase client not available');
    }
    
    const searchData = {
      ...search,
      check_in: search.check_in?.toISOString() || null,
      check_out: search.check_out?.toISOString() || null
    };

    const { data, error } = await supabase
      .from('guest_search_history')
      .insert([searchData])
      .select()
      .single();
    
    if (error) throw new Error(`Failed to add guest search history: ${error.message}`);
    return data;
  }

  async clearGuestSearchHistory(userId?: string, sessionId?: string): Promise<void> {
    if (!supabase) {
      throw new Error('Supabase client not available');
    }
    
    let query = supabase.from('guest_search_history').delete();

    if (userId) {
      query = query.eq('user_id', userId);
    } else if (sessionId) {
      query = query.eq('session_id', sessionId);
    }

    const { error } = await query;
    if (error) throw new Error(`Failed to clear guest search history: ${error.message}`);
  }

  // Guest profile methods
  async getGuestProfile(userId: string): Promise<GuestProfile | undefined> {
    if (!supabase) {
      console.warn('Supabase client not available');
      return undefined;
    }
    
    const { data, error } = await supabase
      .from('guest_profiles')
      .select('*')
      .eq('user_id', userId)
      .single();
    
    if (error) return undefined;
    return data;
  }

  async createGuestProfile(profile: InsertGuestProfile): Promise<GuestProfile> {
    if (!supabase) {
      throw new Error('Supabase client not available');
    }
    
    const { data, error } = await supabase
      .from('guest_profiles')
      .insert([profile])
      .select()
      .single();
    
    if (error) throw new Error(`Failed to create guest profile: ${error.message}`);
    return data;
  }

  async updateGuestProfile(userId: string, updates: Partial<GuestProfile>): Promise<GuestProfile | undefined> {
    if (!supabase) {
      console.warn('Supabase client not available');
      return undefined;
    }
    
    const { data, error } = await supabase
      .from('guest_profiles')
      .update(updates)
      .eq('user_id', userId)
      .select()
      .single();
    
    if (error) return undefined;
    return data;
  }

  // Guest wishlist methods
  async getGuestWishlists(guestId: string): Promise<GuestWishlist[]> {
    if (!supabase) {
      console.warn('Supabase client not available');
      return [];
    }
    
    const { data, error } = await supabase
      .from('guest_wishlists')
      .select('*')
      .eq('guest_id', guestId)
      .order('created_at', { ascending: false });
    
    if (error) return [];
    return data || [];
  }

  async getGuestWishlist(id: string): Promise<GuestWishlist | undefined> {
    if (!supabase) {
      console.warn('Supabase client not available');
      return undefined;
    }
    
    const { data, error } = await supabase
      .from('guest_wishlists')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) return undefined;
    return data;
  }

  async createGuestWishlist(wishlist: InsertGuestWishlist): Promise<GuestWishlist> {
    if (!supabase) {
      throw new Error('Supabase client not available');
    }
    
    const { data, error } = await supabase
      .from('guest_wishlists')
      .insert([wishlist])
      .select()
      .single();
    
    if (error) throw new Error(`Failed to create guest wishlist: ${error.message}`);
    return data;
  }

  async updateGuestWishlist(id: string, updates: Partial<GuestWishlist>): Promise<GuestWishlist | undefined> {
    if (!supabase) {
      console.warn('Supabase client not available');
      return undefined;
    }
    
    const { data, error } = await supabase
      .from('guest_wishlists')
      .update(updates)
      .eq('id', id)
      .select()
      .single();
    
    if (error) return undefined;
    return data;
  }

  async deleteGuestWishlist(id: string): Promise<void> {
    if (!supabase) {
      console.warn('Supabase client not available');
      return;
    }
    
    const { error } = await supabase
      .from('guest_wishlists')
      .delete()
      .eq('id', id);
    
    if (error) console.warn('Failed to delete guest wishlist:', error.message);
  }

  async addPropertyToWishlist(wishlistProperty: InsertGuestWishlistProperty): Promise<GuestWishlistProperty> {
    if (!supabase) {
      throw new Error('Supabase client not available');
    }
    
    const { data, error } = await supabase
      .from('guest_wishlist_properties')
      .insert([wishlistProperty])
      .select()
      .single();
    
    if (error) throw new Error(`Failed to add property to wishlist: ${error.message}`);
    return data;
  }

  async removePropertyFromWishlist(wishlistId: string, propertyId: string): Promise<void> {
    if (!supabase) {
      console.warn('Supabase client not available');
      return;
    }
    
    const { error } = await supabase
      .from('guest_wishlist_properties')
      .delete()
      .eq('wishlist_id', wishlistId)
      .eq('host_property_id', propertyId);
    
    if (error) console.warn('Failed to remove property from wishlist:', error.message);
  }

  async getWishlistProperties(wishlistId: string): Promise<GuestWishlistProperty[]> {
    if (!supabase) {
      console.warn('Supabase client not available');
      return [];
    }
    
    const { data, error } = await supabase
      .from('guest_wishlist_properties')
      .select('*')
      .eq('wishlist_id', wishlistId)
      .order('added_at', { ascending: false });
    
    if (error) return [];
    return data || [];
  }

  // Communication methods
  async getConversations(userId: string, userType: 'guest' | 'host'): Promise<CommConversation[]> {
    if (!supabase) {
      console.warn('Supabase client not available');
      return [];
    }
    
    const column = userType === 'guest' ? 'guest_id' : 'host_id';
    
    const { data, error } = await supabase
      .from('comm_conversations')
      .select('*')
      .eq(column, userId)
      .order('updated_at', { ascending: false });
    
    if (error) return [];
    return data || [];
  }

  async getConversation(id: string): Promise<CommConversation | undefined> {
    if (!supabase) {
      console.warn('Supabase client not available');
      return undefined;
    }
    
    const { data, error } = await supabase
      .from('comm_conversations')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) return undefined;
    return data;
  }

  async createConversation(conversation: InsertCommConversation): Promise<CommConversation> {
    if (!supabase) {
      throw new Error('Supabase client not available');
    }
    
    const { data, error } = await supabase
      .from('comm_conversations')
      .insert([conversation])
      .select()
      .single();
    
    if (error) throw new Error(`Failed to create conversation: ${error.message}`);
    return data;
  }

  async getMessages(conversationId: string): Promise<CommMessage[]> {
    if (!supabase) {
      console.warn('Supabase client not available');
      return [];
    }
    
    const { data, error } = await supabase
      .from('comm_messages')
      .select('*')
      .eq('conversation_id', conversationId)
      .order('created_at', { ascending: true });
    
    if (error) return [];
    return data || [];
  }

  async sendMessage(message: InsertCommMessage): Promise<CommMessage> {
    if (!supabase) {
      throw new Error('Supabase client not available');
    }
    
    const { data, error } = await supabase
      .from('comm_messages')
      .insert([message])
      .select()
      .single();
    
    if (error) throw new Error(`Failed to send message: ${error.message}`);
    return data;
  }

  async markMessagesAsRead(conversationId: string, userId: string): Promise<void> {
    if (!supabase) {
      console.warn('Supabase client not available');
      return;
    }
    
    const { error } = await supabase
      .from('comm_messages')
      .update({ is_read: true })
      .eq('conversation_id', conversationId)
      .neq('sender_id', userId);
    
    if (error) console.warn('Failed to mark messages as read:', error.message);
  }

  // Help methods
  async getHelpArticles(category?: string): Promise<HelpArticle[]> {
    if (!supabase) {
      console.warn('Supabase client not available');
      return [];
    }
    
    let query = supabase
      .from('help_articles')
      .select('*')
      .eq('is_active', true)
      .order('display_order', { ascending: true });
    
    if (category) {
      query = query.eq('category', category);
    }
    
    const { data, error } = await query;
    
    if (error) return [];
    return data || [];
  }

  async getHelpArticle(id: string): Promise<HelpArticle | undefined> {
    if (!supabase) {
      console.warn('Supabase client not available');
      return undefined;
    }
    
    const { data, error } = await supabase
      .from('help_articles')
      .select('*')
      .eq('id', id)
      .eq('is_active', true)
      .single();
    
    if (error) return undefined;
    return data;
  }

  async getFAQs(): Promise<HelpArticle[]> {
    if (!supabase) {
      console.warn('Supabase client not available');
      return [];
    }
    
    const { data, error } = await supabase
      .from('help_articles')
      .select('*')
      .eq('is_faq', true)
      .eq('is_active', true)
      .order('display_order', { ascending: true });
    
    if (error) return [];
    return data || [];
  }

  async createHelpArticle(article: InsertHelpArticle): Promise<HelpArticle> {
    if (!supabase) {
      throw new Error('Supabase client not available');
    }
    
    const { data, error } = await supabase
      .from('help_articles')
      .insert([article])
      .select()
      .single();
    
    if (error) throw new Error(`Failed to create help article: ${error.message}`);
    return data;
  }

  async updateHelpArticle(id: string, updates: Partial<HelpArticle>): Promise<HelpArticle | undefined> {
    if (!supabase) {
      console.warn('Supabase client not available');
      return undefined;
    }
    
    const { data, error } = await supabase
      .from('help_articles')
      .update(updates)
      .eq('id', id)
      .select()
      .single();
    
    if (error) return undefined;
    return data;
  }

  async deleteHelpArticle(id: string): Promise<void> {
    if (!supabase) {
      console.warn('Supabase client not available');
      return;
    }
    
    const { error } = await supabase
      .from('help_articles')
      .delete()
      .eq('id', id);
    
    if (error) console.warn('Failed to delete help article:', error.message);
  }
}

// Always use Supabase storage - no fallback to memory storage
export const storage = new SupabaseStorage();