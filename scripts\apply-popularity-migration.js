import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase credentials');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function applyMigration() {
  try {
    console.log('🚀 Applying popularity-weighted fuzzy search migration...');
    
    const migrationPath = path.join(__dirname, '../database/migrations/popularity-weighted-fuzzy-search.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    // Split migration into individual statements
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    console.log(`📝 Executing ${statements.length} SQL statements...`);
    
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      if (statement.trim()) {
        console.log(`   ${i + 1}/${statements.length}: ${statement.substring(0, 60)}...`);
        
        const { error } = await supabase.rpc('execute_sql', { 
          sql_query: statement + ';' 
        });
        
        if (error) {
          console.error(`   ❌ Failed: ${error.message}`);
          // Continue with other statements
        } else {
          console.log(`   ✅ Success`);
        }
      }
    }
    
    console.log('🎉 Migration application completed');
    
    // Test the new function
    console.log('\n🧪 Testing popularity-weighted fuzzy search...');
    const { data: testResults, error: testError } = await supabase
      .rpc('fuzzy_search_with_popularity', {
        search_query: 'torrev',
        min_similarity: 0.25,
        result_limit: 5
      });
    
    if (testError) {
      console.error('❌ Test failed:', testError);
    } else {
      console.log('✅ Test successful:', testResults?.length || 0, 'results');
      if (testResults?.length > 0) {
        console.log('🏆 Top result:', testResults[0].name, 'Score:', testResults[0].final_score);
      }
    }
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

applyMigration();