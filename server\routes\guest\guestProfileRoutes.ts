import { Router } from 'express';
import { guestProfileController } from '../../controllers/guest/guestProfileController';

const router = Router();

// Guest profile routes
router.get('/profile/:userId', guestProfileController.getProfile.bind(guestProfileController));
router.post('/profile', guestProfileController.createProfile.bind(guestProfileController));
router.put('/profile/:userId', guestProfileController.updateProfile.bind(guestProfileController));

export default router;