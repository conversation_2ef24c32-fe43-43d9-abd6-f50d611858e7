#!/usr/bin/env tsx
/**
 * Database Migration System
 * 
 * Modern migration runner with proper versioning and rollback support.
 * Supports both Supabase and local PostgreSQL instances.
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

interface Migration {
  version: string;
  name: string;
  filepath: string;
  up: string;
  down?: string;
}

interface MigrationRecord {
  version: string;
  name: string;
  executed_at: string;
  checksum: string;
}

class DatabaseMigrator {
  private supabase: any;
  private migrationsDir: string;

  constructor() {
    // Initialize Supabase client
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY;

    if (!supabaseUrl || !supabaseKey) {
      throw new Error('Missing SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY environment variables');
    }

    this.supabase = createClient(supabaseUrl, supabaseKey);
    this.migrationsDir = path.resolve(__dirname, '../migrations');
  }

  /**
   * Initialize migration tracking table
   */
  async initMigrationTable(): Promise<void> {
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS _migrations (
        version VARCHAR(20) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        executed_at TIMESTAMPTZ DEFAULT NOW(),
        checksum VARCHAR(64) NOT NULL
      );
      
      CREATE INDEX IF NOT EXISTS idx_migrations_executed_at 
        ON _migrations(executed_at);
    `;

    // Check if table exists by trying to query it
    const { error: tableError } = await this.supabase
      .from('_migrations')
      .select('version')
      .limit(1);
    if (tableError && tableError.code === 'PGRST116') {
      throw new Error('Migration table not found. Please create it manually in your database.');
    }
  }

  /**
   * Get all available migrations from filesystem
   */
  async getAvailableMigrations(): Promise<Migration[]> {
    try {
      const files = await fs.readdir(this.migrationsDir);
      const migrationFiles = files
        .filter(f => f.match(/^v\d{3}_.*\.sql$/))
        .sort();

      const migrations: Migration[] = [];

      for (const file of migrationFiles) {
        const filepath = path.join(this.migrationsDir, file);
        const content = await fs.readFile(filepath, 'utf-8');
        
        // Parse version and name from filename
        const match = file.match(/^v(\d{3})_(.*)\.sql$/);
        if (!match) continue;

        const version = `v${match[1]}`;
        const name = match[2].replace(/[_-]/g, ' ');

        // Split UP and DOWN sections
        const sections = this.parseMigrationContent(content);

        migrations.push({
          version,
          name,
          filepath,
          up: sections.up,
          down: sections.down
        });
      }

      return migrations;
    } catch (error) {
      throw new Error(`Failed to read migrations directory: ${error}`);
    }
  }

  /**
   * Parse migration file content into UP and DOWN sections
   */
  private parseMigrationContent(content: string): { up: string; down?: string } {
    const lines = content.split('\n');
    let currentSection = 'up';
    let upContent: string[] = [];
    let downContent: string[] = [];

    for (const line of lines) {
      const trimmed = line.trim().toLowerCase();
      
      if (trimmed.startsWith('-- down') || trimmed.startsWith('-- rollback')) {
        currentSection = 'down';
        continue;
      }
      
      if (trimmed.startsWith('-- up') || trimmed.startsWith('-- migration')) {
        currentSection = 'up';
        continue;
      }

      if (currentSection === 'up') {
        upContent.push(line);
      } else if (currentSection === 'down') {
        downContent.push(line);
      }
    }

    return {
      up: upContent.join('\n').trim(),
      down: downContent.length > 0 ? downContent.join('\n').trim() : undefined
    };
  }

  /**
   * Get executed migrations from database
   */
  async getExecutedMigrations(): Promise<MigrationRecord[]> {
    const { data, error } = await this.supabase
      .from('_migrations')
      .select('*')
      .order('version');

    if (error) {
      throw new Error(`Failed to fetch executed migrations: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Calculate checksum for migration content
   */
  private calculateChecksum(content: string): string {
    const crypto = require('crypto');
    return crypto.createHash('sha256').update(content).digest('hex').substring(0, 16);
  }

  /**
   * Execute a single migration
   */
  async executeMigration(migration: Migration): Promise<void> {
    console.log(`🚀 Executing migration ${migration.version}: ${migration.name}`);
    
    const checksum = this.calculateChecksum(migration.up);

    try {
      // For now, we'll use SQL execution tool
      console.log('⚠️  Please execute this migration SQL manually in your database:');
      console.log(migration.up);
      console.log('Then run the command again to record the migration.');
      return;

      if (execError) {
        throw new Error(`Migration execution failed: ${execError.message}`);
      }

      // Record successful migration
      const { error: recordError } = await this.supabase
        .from('_migrations')
        .insert({
          version: migration.version,
          name: migration.name,
          checksum
        });

      if (recordError) {
        throw new Error(`Failed to record migration: ${recordError.message}`);
      }

      console.log(`✅ Migration ${migration.version} completed successfully`);

    } catch (error) {
      console.error(`❌ Migration ${migration.version} failed:`, error);
      throw error;
    }
  }

  /**
   * Rollback a migration
   */
  async rollbackMigration(migration: Migration): Promise<void> {
    if (!migration.down) {
      throw new Error(`Migration ${migration.version} has no rollback defined`);
    }

    console.log(`🔄 Rolling back migration ${migration.version}: ${migration.name}`);

    try {
      // For now, we'll use SQL execution tool  
      console.log('⚠️  Please execute this rollback SQL manually in your database:');
      console.log(migration.down);
      console.log('Then run the command again to record the rollback.');
      return;

      if (execError) {
        throw new Error(`Rollback execution failed: ${execError.message}`);
      }

      // Remove migration record
      const { error: deleteError } = await this.supabase
        .from('_migrations')
        .delete()
        .eq('version', migration.version);

      if (deleteError) {
        throw new Error(`Failed to remove migration record: ${deleteError.message}`);
      }

      console.log(`✅ Migration ${migration.version} rolled back successfully`);

    } catch (error) {
      console.error(`❌ Rollback ${migration.version} failed:`, error);
      throw error;
    }
  }

  /**
   * Run all pending migrations
   */
  async migrate(targetVersion?: string): Promise<void> {
    console.log('🔍 Checking migration status...');

    await this.initMigrationTable();

    const available = await this.getAvailableMigrations();
    const executed = await this.getExecutedMigrations();
    const executedVersions = new Set(executed.map(m => m.version));

    const pending = available.filter(m => {
      if (executedVersions.has(m.version)) return false;
      if (targetVersion && m.version > targetVersion) return false;
      return true;
    });

    if (pending.length === 0) {
      console.log('✅ No pending migrations');
      return;
    }

    console.log(`📋 Found ${pending.length} pending migrations:`);
    pending.forEach(m => console.log(`   - ${m.version}: ${m.name}`));

    for (const migration of pending) {
      await this.executeMigration(migration);
    }

    console.log(`🎉 Successfully executed ${pending.length} migrations`);
  }

  /**
   * Rollback the last migration
   */
  async rollback(): Promise<void> {
    console.log('🔍 Finding last migration to rollback...');

    await this.initMigrationTable();

    const executed = await this.getExecutedMigrations();
    if (executed.length === 0) {
      console.log('✅ No migrations to rollback');
      return;
    }

    const lastMigration = executed[executed.length - 1];
    const available = await this.getAvailableMigrations();
    const migration = available.find(m => m.version === lastMigration.version);

    if (!migration) {
      throw new Error(`Migration file not found for version ${lastMigration.version}`);
    }

    await this.rollbackMigration(migration);
    console.log('🎉 Rollback completed successfully');
  }

  /**
   * Show migration status
   */
  async status(): Promise<void> {
    await this.initMigrationTable();

    const available = await this.getAvailableMigrations();
    const executed = await this.getExecutedMigrations();
    const executedVersions = new Set(executed.map(m => m.version));

    console.log('\n📊 Migration Status:\n');
    console.log('Version   | Status    | Name');
    console.log('----------|-----------|----------------------------------');

    for (const migration of available) {
      const status = executedVersions.has(migration.version) ? '✅ Applied' : '⏳ Pending';
      console.log(`${migration.version.padEnd(9)} | ${status.padEnd(9)} | ${migration.name}`);
    }

    const pending = available.filter(m => !executedVersions.has(m.version));
    console.log(`\n📈 Summary: ${executed.length} applied, ${pending.length} pending\n`);
  }
}

// CLI Interface
async function main() {
  const command = process.argv[2];
  const migrator = new DatabaseMigrator();

  try {
    switch (command) {
      case 'migrate':
        const targetVersion = process.argv[3];
        await migrator.migrate(targetVersion);
        break;

      case 'rollback':
        await migrator.rollback();
        break;

      case 'status':
        await migrator.status();
        break;

      default:
        console.log(`
Database Migration Tool

Usage:
  tsx database/management/migrator.ts <command>

Commands:
  migrate [version]  Run pending migrations (optionally up to version)
  rollback          Rollback the last migration
  status            Show migration status

Examples:
  tsx database/management/migrator.ts migrate
  tsx database/management/migrator.ts migrate v003
  tsx database/management/migrator.ts rollback
  tsx database/management/migrator.ts status
        `);
        process.exit(1);
    }
  } catch (error) {
    console.error('💥 Migration failed:', error);
    process.exit(1);
  }
}

// Only run if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { DatabaseMigrator };