# Hardcoded Data Migration Summary

This document summarizes the migration of hardcoded location data to the database-driven translation system.

## 🎯 Migration Overview

The migration moves all hardcoded geographical data from TypeScript files to database tables with a code-based translation system.

## 📁 Files Created

### Database Migration
- `v004_translation_system.sql` - Creates new tables for code-based translations

### Seed Scripts
- `01_country_codes.ts` - ISO 3166-1 country codes and translations
- `02_spanish_regions.ts` - Spanish administrative regions with translations  
- `03_tourism_regions.ts` - Tourism regions with coordinate boundaries
- `04_popular_locations.ts` - Popular Costa Blanca locations for autocomplete
- `05_geonames_migration.ts` - Links existing GeoNames data to new code system

## 🔄 Hardcoded Data Migrated

### 1. Country Name Mappings
**Source:** `server/config/geonames-scope.ts` - `getCountryName()` function
**Destination:** `country_codes` + `translations` tables

**Before (Hardcoded):**
```typescript
private getCountryName(code: string): string {
  const names: Record<string, string> = {
    'ES': 'Spain',
    'FR': 'France', 
    'IT': 'Italy',
    // ... hardcoded for each country
  };
  return names[code] || code;
}
```

**After (Database-driven):**
- Country codes: `724` (Spain), `250` (France), `380` (Italy)
- Translations: Multiple languages per country in `translations` table
- API: `TranslationService.getTranslation('country', 724, 'en')` → 'Spain'

### 2. Popular Locations Array
**Source:** `client/src/features/host/properties/components/wizard/LocationStep.tsx`
**Destination:** `popular_locations` table

**Before (Hardcoded):**
```typescript
const POPULAR_LOCATIONS = [
  { city: 'Benidorm', region: 'Alicante', coordinates: { lat: 38.5385, lng: -0.1313 } },
  { city: 'Javea', region: 'Alicante', coordinates: { lat: 38.7914, lng: 0.1616 } },
  // ... 10 hardcoded locations
];
```

**After (Database-driven):**
- All locations stored in `popular_locations` table
- Linked to country/region codes
- API: `getPopularDestinations(country: 'ES', language: 'en')`

### 3. Spanish Tourism Regions
**Source:** `server/services/geonames/tourism-data.ts` - `SPANISH_TOURISM_REGIONS` array
**Destination:** `tourism_region_codes` + `translations` tables

**Before (Hardcoded):**
```typescript
export const SPANISH_TOURISM_REGIONS: TourismRegionData[] = [
  {
    name: 'Costa Brava',
    name_en: 'Costa Brava',
    name_es: 'Costa Brava',
    name_ca: 'Costa Brava',
    type: 'coastal',
    // ... 12+ hardcoded regions
  }
];
```

**After (Database-driven):**
- Tourism regions: Numeric IDs (1=Costa Brava, 2=Costa del Sol, 3=Costa Blanca)
- Coordinate boundaries stored as JSONB
- Translations in multiple languages
- API: `TranslationService.getTranslation('tourism_region', 1, 'en')` → 'Costa Brava'

### 4. Location Tourism Mappings
**Source:** `server/services/geonames/tourism-data.ts` - `LOCATION_TOURISM_MAPPINGS` array
**Destination:** Coordinate-based queries in `geonames_locations` table

**Before (Hardcoded):**
```typescript
export const LOCATION_TOURISM_MAPPINGS: LocationTourismMapping[] = [
  {
    tourism_region: 'Costa Brava',
    criteria: {
      admin1_code: 'CT',
      admin2_code: 'GI',
      coordinate_bounds: { lat_min: 41.6, lat_max: 42.3, lng_min: 2.8, lng_max: 3.3 }
    }
  }
];
```

**After (Database-driven):**
- Coordinate boundaries stored in `tourism_region_codes.coordinate_bounds`
- Automatic linking via SQL queries based on coordinates
- Dynamic assignment instead of hardcoded mappings

### 5. Regional Translations
**Source:** Multiple service files with hardcoded country name lookups
**Destination:** Centralized `translations` table

**Before (Scattered hardcoded):**
```typescript
// In location-search.ts
const countryNames: { [key: string]: string } = {
  'ES': 'España', 'FR': 'France', 'IT': 'Italia'
};

// In search-service.ts  
const countryNames: { [key: string]: string } = {
  'ES': 'España', 'FR': 'France', 'IT': 'Italia'
};

// In locationController.ts
const countryNames: { [key: string]: string } = {
  'ES': 'España', 'FR': 'France', 'IT': 'Italia'
};
```

**After (Centralized):**
- Single `translations` table for all geographic entities
- Consistent translations across all services
- Easy to add new languages without code changes

## 🗄️ New Database Schema

### Core Tables
```sql
country_codes (id, iso_alpha2, iso_alpha3)
region_codes (id, country_id, iso_code, geonames_admin1_code)  
tourism_region_codes (id, country_id, region_type, coordinate_bounds)
translations (entity_type, entity_id, language_code, text)
popular_locations (id, name, country_id, region_id, coordinates)
```

### Updated Tables
```sql
geonames_locations (
  -- existing columns +
  country_code_id REFERENCES country_codes(id),
  region_code_id REFERENCES region_codes(id), 
  tourism_region_code_id REFERENCES tourism_region_codes(id)
)
```

## 🚀 Benefits Achieved

1. **Single Source of Truth**: All translations in one table
2. **Numeric Efficiency**: Countries = 724 (Spain), Regions = 1,2,3...
3. **Language Scalability**: Add new languages without schema changes  
4. **Performance**: Cached numeric lookups vs JSON parsing
5. **Consistency**: Same translation system for UI and location data
6. **Maintainability**: No hardcoded translation data in code

## 📋 Next Steps

1. **Run Migration**: Execute `v004_translation_system.sql`
2. **Run Seeds**: Execute seed scripts in order (01-05)
3. **Update Services**: Modify services to use `TranslationService`
4. **Update Frontend**: Replace hardcoded arrays with API calls
5. **Remove Hardcoded Data**: Clean up old hardcoded arrays and mappings

## 🔧 Manual Steps Required

Some operations may require manual SQL execution if the seeder's `execute_sql` function is not available:

1. **Country Code Linking**:
```sql
UPDATE geonames_locations 
SET country_code_id = country_codes.id
FROM country_codes 
WHERE geonames_locations.country_code = country_codes.iso_alpha2;
```

2. **Region Code Linking**:
```sql  
UPDATE geonames_locations 
SET region_code_id = region_codes.id
FROM region_codes 
WHERE geonames_locations.admin1_code = region_codes.geonames_admin1_code
  AND geonames_locations.country_code = 'ES';
```

3. **Tourism Region Linking**: See individual queries in `05_geonames_migration.ts`

## ✅ Verification

After migration, verify:
- [ ] All country codes have translations in target languages
- [ ] Popular locations are linked to country/region codes  
- [ ] Tourism regions have coordinate boundaries
- [ ] Existing GeoNames data is linked to new code system
- [ ] Translation API returns correct values for all entity types
