import { Router } from 'express';
import { guestCommunicationController } from '../../controllers/guest/guestCommunicationController';

const router = Router();

// Guest communication routes
router.get('/conversations/:guestId', guestCommunicationController.getConversations.bind(guestCommunicationController));
router.get('/conversation/:conversationId', guestCommunicationController.getConversation.bind(guestCommunicationController));
router.post('/conversation', guestCommunicationController.createConversation.bind(guestCommunicationController));

// Message routes
router.get('/conversation/:conversationId/messages', guestCommunicationController.getMessages.bind(guestCommunicationController));
router.post('/conversation/:conversationId/messages', guestCommunicationController.sendMessage.bind(guestCommunicationController));
router.put('/conversation/:conversationId/read', guestCommunicationController.markMessagesAsRead.bind(guestCommunicationController));

export default router;