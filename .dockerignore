# Git
.git
.gitignore

# Node modules
node_modules
npm-debug.log*

# Environment files
.env
.env.local
.env.*.local

# Development files
*.md
*.log

# Documentation
docs/
README.md
CHANGELOG.md

# Test files
test/
tests/
**/*.test.js
**/*.test.ts
**/*.spec.js
**/*.spec.ts

# Coverage
coverage/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
tmp/
temp/

# Build artifacts (only for development Dockerfile)
dist/

# Docker files (don't copy into container)
Dockerfile*
docker-compose*.yml
.dockerignore

# Scripts not needed in production
scripts/windows/