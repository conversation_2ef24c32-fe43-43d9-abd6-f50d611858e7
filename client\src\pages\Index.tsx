
import { Header } from "../components/Header";
import { SearchBar, OwnerCTA } from "../features/public/home";
import { Footer } from "../components/Footer";
import { InspirationSection } from "../features/public/inspiration";

import { useTranslations } from "@/lib/translations";
import {
  ExplorationSection,
  usePopularInSpain,
  useNewInFrance,
  useGuestFavorites,
} from "../features/public/exploration";

export default function Index() {
  const t = useTranslations("indexPage");

  // Use the new hooks for data fetching
  const { data: popularInSpain = [] } = usePopularInSpain();
  const { data: newInFrance = [] } = useNewInFrance();
  const { data: guestFavorites = [] } = useGuestFavorites();

  return (
    <div className="min-h-screen relative bg-background">
      {/* Purple gradient background */}
      <div className="absolute inset-0 bg-gradient-to-b from-purple-100/50 via-purple-50/30 to-transparent pointer-events-none" />

      <div className="relative z-10">
        <Header />

        <main>
          <SearchBar />

          <ExplorationSection
            title={t("popularInSpain")}
            properties={popularInSpain}
          />

          <ExplorationSection
            title={t("newInFrance")}
            properties={newInFrance}
          />

          <ExplorationSection
            title={t("guestFavorites")}
            properties={guestFavorites}
          />

          <InspirationSection />

          <OwnerCTA />


        </main>

        <Footer />
      </div>
    </div>
  );
}
