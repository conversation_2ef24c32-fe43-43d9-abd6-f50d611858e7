# 🏗️ VillaWise Database Management System

**Status**: ✅ **Restructured & Modernized** - Ready for Generic Geonames Implementation

Professional database management system with proper versioning, country-agnostic architecture, and enterprise-grade tooling.

## 📊 Current Status

✅ **Migration table created** (`_migrations`)  
⏳ **v001**: Initial schema (pending execution)  
⏳ **v002**: Generic geonames system (pending execution)  
🗂️ **Legacy files removed** (clean slate established)

## 🏗️ New Structure

```
database/
├── management/               # 🔧 Professional database tools
│   ├── migrator.ts          # Version-controlled migration runner
│   ├── seeder.ts            # Environment-aware seed management  
│   └── schema-generator-simple.ts  # Schema documentation tool
├── migrations/              # 📝 Version-controlled migrations
│   ├── v001_initial_schema.sql     # Complete base schema
│   └── v002_geonames_system.sql    # Country-agnostic geonames
└── seeds/                   # 🌱 Organized seed data
    ├── shared/              # Production-safe data (countries, etc.)
    └── development/         # Test data only
```

## Quick Start

### 1. Run Latest Migrations
```bash
npm run db:migrate
```

### 2. Seed Development Data
```bash
npm run db:seed:dev
```

### 3. Generate Current Schema
```bash
npm run db:schema:generate
```

## Migration System

### Creating Migrations
1. Create new file: `migrations/v{NUMBER}_{description}.sql`
2. Follow naming convention: `v001`, `v002`, etc.
3. Include both UP and DOWN operations
4. Test locally before committing

### Running Migrations
```bash
# Run all pending migrations
npm run db:migrate

# Run specific migration
npm run db:migrate --version=v003

# Rollback last migration
npm run db:rollback
```

## Environment Configuration

Required environment variables:
```env
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

## Best Practices

1. **Migrations**: Always reversible, test locally first
2. **Seeds**: Idempotent (safe to run multiple times)
3. **Functions**: Version controlled, documented
4. **Schema**: Generated from migrations, never edit directly

## Commands

| Command | Description |
|---------|-------------|
| `npm run db:migrate` | Run pending migrations |
| `npm run db:rollback` | Rollback last migration |
| `npm run db:seed:dev` | Seed development data |
| `npm run db:seed:prod` | Seed production-safe data |
| `npm run db:schema:generate` | Generate current schema |
| `npm run db:status` | Check migration status |
| `npm run db:reset` | Reset database (dev only) |