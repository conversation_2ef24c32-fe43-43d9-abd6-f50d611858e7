#!/bin/bash
# Test script for Docker build process
# This script validates that Docker build works correctly

set -e  # Exit on any error

echo "🚀 Testing VillaWise Docker Build Process"
echo "========================================"

# Check if .env.example exists
if [ ! -f ".env.example" ]; then
    echo "❌ .env.example file not found!"
    exit 1
fi

# Create a test .env file
echo "📝 Creating test .env file..."
cp .env.example .env.test
cat >> .env.test << EOF

# Test values for Docker build
SUPABASE_URL=https://test-project.supabase.co
SUPABASE_ANON_KEY=test-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=test-service-role-key-here
USE_REDIS_CACHE=false
EOF

echo "✅ Test environment file created"

# Test TypeScript compilation
echo "🔍 Testing TypeScript compilation..."
npx tsc --noEmit --skipLibCheck
if [ $? -eq 0 ]; then
    echo "✅ TypeScript compilation successful"
else
    echo "❌ TypeScript compilation failed"
    exit 1
fi

# Test build process (simulate what <PERSON><PERSON> would do)
echo "🏗️ Testing build process..."
npm run build > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ Build process successful"
    echo "📦 Build artifacts created in dist/"
    ls -la dist/ | head -5
else
    echo "❌ Build process failed"
    exit 1
fi

# Cleanup
rm -f .env.test
rm -rf dist/

echo ""
echo "✅ All Docker build tests passed!"
echo "🐳 Docker build should work correctly with these configurations:"
echo "   - Dockerfile (production build)"
echo "   - Dockerfile.dev (development build)"
echo "   - docker-compose.yml (production deployment)"
echo "   - docker-compose.dev.yml (development with hot reload)"