#!/usr/bin/env node
import { build } from 'esbuild';
import { fileURLToPath } from 'url';
import path from 'path';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

try {
  // Build the server
  await build({
    entryPoints: ['server/index.ts'],
    bundle: true,
    platform: 'node',
    target: 'node20',
    format: 'esm',
    outdir: 'dist',
    packages: 'external',
    define: {
      // Replace import.meta.dirname with a cross-platform solution
      'import.meta.dirname': JSON.stringify(process.cwd()),
    },
    inject: ['./esbuild-shim.js'],
    banner: {
      js: `
        import { fileURLToPath } from 'url';
        import path from 'path';
        const __dirname = path.dirname(fileURLToPath(import.meta.url));
        const __filename = fileURLToPath(import.meta.url);
      `,
    },
  });

  console.log('Server build completed successfully');
} catch (error) {
  console.error('Build failed:', error);
  process.exit(1);
}