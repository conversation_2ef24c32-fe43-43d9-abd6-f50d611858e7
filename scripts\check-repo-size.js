#!/usr/bin/env node
/**
 * Repository Size and Large File Checker
 * Prevents accidental commits of large GeoNames files
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';

// Configuration
const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
const DANGEROUS_EXTENSIONS = ['.zip', '.dump', '.sql.gz'];
const DANGEROUS_PATTERNS = ['alternateNames', 'ES.txt', 'IT.txt', 'FR.txt', 'DE.txt'];
const DANGEROUS_TXT_PATTERNS = ['alternateNames', 'ES.txt', 'IT.txt', 'FR.txt', 'DE.txt'];
const SAFE_DIRECTORIES = ['attached_assets', 'client/public', 'docs', 'scripts'];

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function checkFileSize(filePath) {
  try {
    const stats = fs.statSync(filePath);
    return stats.size;
  } catch {
    return 0;
  }
}

function findLargeFiles() {
  const largeFiles = [];
  
  function walkDir(dir) {
    if (dir.includes('node_modules') || dir.includes('.git') || dir.includes('data/')) {
      return; // Skip excluded directories
    }
    
    try {
      const files = fs.readdirSync(dir);
      for (const file of files) {
        const filePath = path.join(dir, file);
        const stats = fs.statSync(filePath);
        
        if (stats.isDirectory()) {
          walkDir(filePath);
        } else if (stats.size > MAX_FILE_SIZE) {
          largeFiles.push({
            path: filePath,
            size: stats.size,
            extension: path.extname(file)
          });
        }
      }
    } catch (error) {
      // Skip directories we can't read
    }
  }
  
  walkDir('.');
  return largeFiles;
}

function checkDangerousFiles() {
  const dangerousFiles = [];
  
  function walkDir(dir) {
    if (dir.includes('node_modules') || dir.includes('.git') || dir.includes('data/')) {
      return;
    }
    
    try {
      const files = fs.readdirSync(dir);
      for (const file of files) {
        const filePath = path.join(dir, file);
        const stats = fs.statSync(filePath);
        
        if (stats.isDirectory()) {
          walkDir(filePath);
        } else {
          const ext = path.extname(file).toLowerCase();
          const basename = path.basename(file);
          const isInSafeDir = SAFE_DIRECTORIES.some(safeDir => filePath.includes(safeDir));
          
          // Check for dangerous extensions (always flagged)
          if (DANGEROUS_EXTENSIONS.includes(ext)) {
            dangerousFiles.push({
              path: filePath,
              size: stats.size,
              reason: `Dangerous extension: ${ext}`
            });
          }
          // Check for dangerous TXT files (only if not in safe directory and matches GeoNames pattern)
          else if (ext === '.txt' && !isInSafeDir && 
                   DANGEROUS_TXT_PATTERNS.some(pattern => basename.includes(pattern))) {
            dangerousFiles.push({
              path: filePath,
              size: stats.size,
              reason: `GeoNames data file pattern: ${basename}`
            });
          }
          // Check for other dangerous patterns
          else if (DANGEROUS_PATTERNS.some(pattern => basename.includes(pattern))) {
            dangerousFiles.push({
              path: filePath,
              size: stats.size,
              reason: `Matches dangerous pattern`
            });
          }
        }
      }
    } catch (error) {
      // Skip directories we can't read
    }
  }
  
  walkDir('.');
  return dangerousFiles;
}

function checkGitStatus() {
  try {
    const staged = execSync('git diff --cached --name-only', { encoding: 'utf8' }).trim();
    const untracked = execSync('git ls-files --others --exclude-standard', { encoding: 'utf8' }).trim();
    
    const stagedFiles = staged ? staged.split('\n') : [];
    const untrackedFiles = untracked ? untracked.split('\n') : [];
    
    return { staged: stagedFiles, untracked: untrackedFiles };
  } catch {
    return { staged: [], untracked: [] };
  }
}

function main() {
  console.log('🔍 VillaWise Repository Size Check\n');
  
  // Check for large files
  console.log('1. Checking for large files (>10MB)...');
  const largeFiles = findLargeFiles();
  if (largeFiles.length === 0) {
    console.log('   ✅ No large files found');
  } else {
    console.log('   ⚠️  Large files detected:');
    largeFiles.forEach(file => {
      console.log(`      ${file.path} (${formatBytes(file.size)})`);
    });
  }
  
  // Check for dangerous file patterns
  console.log('\n2. Checking for dangerous file patterns...');
  const dangerousFiles = checkDangerousFiles();
  if (dangerousFiles.length === 0) {
    console.log('   ✅ No dangerous files found');
  } else {
    console.log('   ⚠️  Dangerous files detected:');
    dangerousFiles.forEach(file => {
      console.log(`      ${file.path} (${formatBytes(file.size)}) - ${file.reason}`);
    });
  }
  
  // Check Git status
  console.log('\n3. Checking Git status...');
  const gitStatus = checkGitStatus();
  const problemFiles = [...gitStatus.staged, ...gitStatus.untracked].filter(file => {
    const ext = path.extname(file).toLowerCase();
    const basename = path.basename(file);
    const isInSafeDir = SAFE_DIRECTORIES.some(safeDir => file.includes(safeDir));
    
    // Always flag dangerous extensions
    if (DANGEROUS_EXTENSIONS.includes(ext)) return true;
    
    // Flag large files
    if (checkFileSize(file) > MAX_FILE_SIZE) return true;
    
    // Flag GeoNames TXT files if not in safe directory
    if (ext === '.txt' && !isInSafeDir && 
        DANGEROUS_TXT_PATTERNS.some(pattern => basename.includes(pattern))) return true;
    
    // Flag other dangerous patterns
    if (DANGEROUS_PATTERNS.some(pattern => basename.includes(pattern))) return true;
    
    return false;
  });
  
  if (problemFiles.length === 0) {
    console.log('   ✅ No problematic files in Git');
  } else {
    console.log('   ❌ Problematic files in Git:');
    problemFiles.forEach(file => {
      const size = checkFileSize(file);
      console.log(`      ${file} (${formatBytes(size)})`);
    });
  }
  
  // Check data directory
  console.log('\n4. Checking data directory setup...');
  if (fs.existsSync('data')) {
    if (fs.existsSync('data/.gitkeep')) {
      console.log('   ✅ Data directory properly configured');
    } else {
      console.log('   ⚠️  Data directory missing .gitkeep file');
    }
  } else {
    console.log('   ⚠️  Data directory not found');
  }
  
  // Final verdict
  const hasIssues = largeFiles.length > 0 || dangerousFiles.length > 0 || problemFiles.length > 0;
  
  console.log('\n' + '='.repeat(50));
  if (hasIssues) {
    console.log('❌ ISSUES DETECTED - Review files before committing');
    console.log('\nRecommended actions:');
    console.log('- Move large files to data/ directory');
    console.log('- Use git rm --cached to unstage problematic files');
    console.log('- Check .gitignore rules are working');
    process.exit(1);
  } else {
    console.log('✅ REPOSITORY CLEAN - Safe to commit');
  }
}

// Run if this is the main module
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { findLargeFiles, checkDangerousFiles, checkGitStatus };