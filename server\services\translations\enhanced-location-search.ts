// Enhanced location search with comprehensive multilingual translation support
// Integrates multiple translation sources for maximum coverage

import { LocationResult } from '../geonames/types';
import { multilingualLocationService, MultilingualLocationResult } from './multilingual-location-service.js';
import { getRegionTranslation, getCountryTranslation } from '../geonames/regional-translations.js';

export interface EnhancedLocationResult extends LocationResult {
  localizedName: string;
  displayName: string;
  translationSource: 'nominatim' | 'regional' | 'fallback';
  translationConfidence: number;
}

/**
 * Enhanced location search service with comprehensive multilingual support
 * Wraps the existing location search and adds robust translation capabilities
 */
export class EnhancedLocationSearchService {
  
  /**
   * Search locations with multilingual translation support
   */
  async searchLocationsWithTranslations(
    query: string,
    locale: string = 'en',
    limit: number = 10,
    minSimilarity: number = 0.25
  ): Promise<EnhancedLocationResult[]> {
    // Import the existing location search service
    const { locationSearchService } = await import('../geonames/location-search.js');
    
    // Get base search results
    const baseResults = await locationSearchService.fuzzyLocationSearch(query, limit, minSimilarity);
    
    // Enhance results with translations
    const enhancedResults: EnhancedLocationResult[] = [];
    
    for (const result of baseResults) {
      const enhanced = await this.enhanceLocationWithTranslation(result, locale);
      enhancedResults.push(enhanced);
    }
    
    return enhancedResults;
  }

  /**
   * Enhance a single location result with translation
   */
  private async enhanceLocationWithTranslation(
    location: LocationResult,
    locale: string
  ): Promise<EnhancedLocationResult> {
    
    // Get multilingual translation for the location
    const translation = await multilingualLocationService.getLocationTranslation(
      location.name,
      location.country_code,
      this.getRegionName(location.admin1_code || ''),
      locale,
      {
        lat: location.latitude,
        lng: location.longitude
      }
    );

    return {
      ...location,
      localizedName: translation.localizedName,
      displayName: translation.displayName,
      translationSource: translation.translationSource as 'nominatim' | 'regional' | 'fallback',
      translationConfidence: translation.confidence
    };
  }

  /**
   * Get autocomplete suggestions with translations
   */
  async getAutocompleteWithTranslations(
    query: string,
    locale: string = 'en',
    limit: number = 5,
    minSimilarity: number = 0.3
  ): Promise<EnhancedLocationResult[]> {
    return this.searchLocationsWithTranslations(query, locale, limit, minSimilarity);
  }

  /**
   * Get popular destinations with translations
   */
  async getPopularDestinationsWithTranslations(
    countryCode: string = 'ES',
    locale: string = 'en',
    limit: number = 10
  ): Promise<EnhancedLocationResult[]> {
    const { locationSearchService } = await import('../geonames/location-search.js');
    
    // Get popular destinations via fuzzy search (fallback approach)
    const popularResults = await locationSearchService.fuzzyLocationSearch('', limit, 0.1);
    
    // Enhance with translations
    const enhancedResults: EnhancedLocationResult[] = [];
    
    for (const result of popularResults) {
      const enhanced = await this.enhanceLocationWithTranslation(result, locale);
      enhancedResults.push(enhanced);
    }
    
    return enhancedResults;
  }

  /**
   * Get region name from admin1 code
   */
  private getRegionName(admin1Code: string): string {
    const spanishRegions: { [key: string]: string } = {
      'AN': 'Andalucía', 'AR': 'Aragón', 'AS': 'Asturias', 'IB': 'Baleares',
      'PV': 'País Vasco', 'CN': 'Canarias', 'CB': 'Cantabria', 'CM': 'Castilla-La Mancha',
      'CL': 'Castilla y León', 'CT': 'Cataluña', 'EX': 'Extremadura', 'GA': 'Galicia',
      'MD': 'Madrid', 'MC': 'Murcia', 'NC': 'Navarra', 'RI': 'La Rioja',
      'VC': 'Valencia', 'CE': 'Ceuta', 'ML': 'Melilla'
    };
    return spanishRegions[admin1Code] || admin1Code || '';
  }

  /**
   * Batch process locations for translation caching
   * Useful for pre-warming cache with popular locations
   */
  async preWarmTranslationCache(
    locales: string[] = ['en', 'nl', 'fr', 'de', 'it', 'es'],
    limit: number = 50
  ): Promise<void> {
    console.log('[TRANSLATION-CACHE] Starting pre-warming for', locales.length, 'locales');
    
    const { locationSearchService } = await import('../geonames/location-search.js');
    
    // Get top popular destinations via fuzzy search (fallback approach)
    const popularLocations = await locationSearchService.fuzzyLocationSearch('', limit, 0.1);
    
    for (const locale of locales) {
      console.log(`[TRANSLATION-CACHE] Pre-warming cache for locale: ${locale}`);
      
      for (const location of popularLocations) {
        try {
          await multilingualLocationService.getLocationTranslation(
            location.name,
            location.country_code,
            this.getRegionName(location.admin1_code || ''),
            locale,
            {
              lat: location.latitude,
              lng: location.longitude
            }
          );
          
          // Small delay to respect API rate limits
          await new Promise(resolve => setTimeout(resolve, 100));
        } catch (error) {
          console.warn(`[TRANSLATION-CACHE] Failed to cache ${location.name} in ${locale}:`, error);
        }
      }
    }
    
    console.log('[TRANSLATION-CACHE] Pre-warming completed');
  }

  /**
   * Get translation health status
   */
  async getTranslationHealthStatus(): Promise<{
    status: 'healthy' | 'degraded' | 'error';
    sources: {
      nominatim: boolean;
      regional: boolean;
      cache: boolean;
    };
    metrics: {
      cachedTranslations: number;
      supportedLocales: number;
    };
  }> {
    try {
      const health = await multilingualLocationService.getHealthStatus();
      
      return {
        status: health.nominatimAvailable ? 'healthy' : 'degraded',
        sources: {
          nominatim: health.nominatimAvailable,
          regional: true, // Always available
          cache: health.cacheAvailable
        },
        metrics: {
          cachedTranslations: 0, // Could be enhanced with actual count
          supportedLocales: 8 // en, nl, fr, de, it, es, ca, eu
        }
      };
    } catch (error) {
      return {
        status: 'error',
        sources: {
          nominatim: false,
          regional: true,
          cache: false
        },
        metrics: {
          cachedTranslations: 0,
          supportedLocales: 8
        }
      };
    }
  }
}

// Export singleton instance
export const enhancedLocationSearchService = new EnhancedLocationSearchService();