# Production Docker Compose for VillaWise
version: '3.8'

services:
  villawise:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=production
      - PORT=5000
      # Default to memory cache - add Redis credentials to enable Redis caching
      - USE_REDIS_CACHE=false
      # Uncomment and set these to enable Redis caching:
      # - UPSTASH_REDIS_REST_URL=https://your-redis-url
      # - UPSTASH_REDIS_REST_TOKEN=your-redis-token
      
      # Add your Supabase credentials here
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:5000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s

