# VillaWise Deployment Guide

This comprehensive guide covers all deployment scenarios for VillaWise including local development, Docker deployment, and Railway production deployment with Redis cache integration.

## Quick Start

VillaWise uses **memory cache by default** - no Redis setup required for basic functionality.

### Local Development (All Platforms)

1. **Copy environment file:**
   ```bash
   cp .env.example .env
   ```

2. **Add your Supabase credentials to `.env`:**
   ```env
   SUPABASE_URL=https://your-project-ref.supabase.co
   SUPABASE_ANON_KEY=your-anon-key-here
   SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here
   USE_REDIS_CACHE=false
   NODE_ENV=development
   PORT=5000
   ```

3. **Install dependencies and run:**
   ```bash
   npm install
   npm run dev
   ```

### Windows Users

Use the provided batch scripts:
- `scripts/windows/start-dev.bat` - Start development server with validation
- `scripts/windows/docker-build.bat` - Build Docker image
- `scripts/windows/docker-run.bat` - Run Docker container

## Docker Deployment

### Architecture Decision: Monolith + Docker

**✅ Recommended: Single Container Deployment**
- **Simplified Operations**: One deployment, one service to monitor
- **Better Performance**: No network latency between services
- **Cost Effective**: Lower infrastructure costs
- **Easier Debugging**: Single log stream, simpler troubleshooting
- **Team Size**: Optimal for small-to-medium teams

### Option 1: Docker Build & Run

```bash
# Build the image
docker build -t villawise:latest .

# Run with environment file
docker run -d \
  --name villawise-container \
  -p 5000:5000 \
  --env-file .env \
  --restart unless-stopped \
  villawise:latest
```

### Option 2: Docker Compose (Recommended)

```bash
# Production deployment
docker-compose up -d

# Development with hot reload
docker-compose -f docker-compose.dev.yml up
```

### Docker File Structure

```
deployment/
├── railway/
│   ├── Dockerfile.railway       # Railway-optimized Dockerfile
│   └── railway.toml            # Railway configuration
├── docker-compose.yml          # Production deployment
├── docker-compose.dev.yml      # Development environment
├── Dockerfile                  # Main production Dockerfile
└── Dockerfile.dev              # Development Dockerfile
```

## Railway Production Deployment

VillaWise is optimized for Railway deployment with automatic GitHub integration.

### Automatic Deployment

1. **Connect your GitHub repository to Railway**

2. **Set environment variables in Railway dashboard:**
   ```env
   NODE_ENV=production
   SUPABASE_URL=your-supabase-url
   SUPABASE_ANON_KEY=your-anon-key
   SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
   USE_REDIS_CACHE=false  # or true with Redis credentials
   ```

3. **Deploy** - Railway will automatically build and deploy

### Manual Railway Deployment

```bash
# Using Railway CLI
railway login
railway link your-project-id
railway up

# Using Docker for Railway
docker build -f deployment/railway/Dockerfile.railway -t villawise .
docker run -p 3000:3000 --env-file .env villawise
```

## Caching Configuration

### Default: Memory Cache
- **No configuration needed**
- Works out of the box
- Suitable for single-instance deployments
- Data persists only while application is running

### Optional: Upstash Redis Cache
Enable distributed caching for multi-instance deployments:

1. **Get Upstash Redis credentials:**
   - Sign up at [console.upstash.com](https://console.upstash.com/)
   - Create a new Redis database
   - Copy the REST URL and REST TOKEN

2. **Update your `.env` file:**
   ```env
   USE_REDIS_CACHE=true
   UPSTASH_REDIS_REST_URL=https://your-redis-url.upstash.io
   UPSTASH_REDIS_REST_TOKEN=your-redis-token
   ```

3. **Restart your application**

### Cache Architecture
- **Primary Cache**: Upstash Redis REST API for distributed caching
- **Fallback**: In-memory cache when Redis unavailable
- **Auto-Detection**: System automatically enables Redis when credentials present
- **Performance**: 99.5% improvement on cache hits

## Environment Variables Reference

### Required Variables
```env
# Database
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Application
NODE_ENV=production
PORT=5000
```

### Optional Variables
```env
# Caching
USE_REDIS_CACHE=true
UPSTASH_REDIS_REST_URL=https://your-redis-url.upstash.io
UPSTASH_REDIS_REST_TOKEN=your-redis-token

# Development
DEBUG_MODE=false
LOG_LEVEL=info
```

## Health Monitoring

### Health Check Endpoint
```bash
# Check application health
curl http://localhost:5000/api/health
curl https://your-domain.com/api/health
```

### Monitoring Commands
```bash
# View Railway logs
railway logs

# Check database connection
npm run db:status

# Test authentication
curl -H "Authorization: Bearer token" https://your-domain.com/api/auth/me
```

## Troubleshooting

### Common Issues

1. **Build Failures**
   - Check TypeScript compilation: `npx tsc --noEmit --skipLibCheck`
   - Verify all dependencies installed
   - Review build logs for specific errors
   - **Docker Production Build**: Ensure server directories are properly copied

2. **Database Connection Issues**
   - Verify Supabase credentials
   - Check network connectivity
   - Review RLS policies

3. **Authentication Problems**
   - Verify OAuth redirect URLs
   - Check Supabase Auth configuration
   - Review JWT secret configuration

4. **Performance Issues**
   - Monitor memory usage
   - Check database query performance
   - Review application logs
   - Enable Redis caching for better performance

5. **Redis Cache Issues**
   - **Connection Failures**: System automatically falls back to memory cache
   - **Missing Credentials**: Set `USE_REDIS_CACHE=false` for memory-only mode
   - **Upstash Setup**: Verify REST URL and TOKEN are correct

### Docker-Specific Issues

#### Vite Import Errors in Production
**Error**: `Cannot find package 'vite' imported from /app/dist/index.js`
**Solution**: Production builds use conditional imports - vite is only loaded in development

#### Missing Server Directories  
**Error**: Server directories not found in Docker container
**Solution**: Dockerfile now properly copies all server subdirectories:
```dockerfile
COPY --from=builder /app/server/dal ./server/dal
COPY --from=builder /app/server/controllers ./server/controllers
COPY --from=builder /app/server/services ./server/services
```

#### Static File Serving Issues
**Error**: SPA routing not working in production
**Solution**: Production static server includes fallback to index.html for SPA routes

### Rollback Procedures

#### Railway Rollback
```bash
# List deployments
railway deployments

# Rollback to specific deployment
railway rollback <deployment-id>

# Rollback to previous deployment
railway rollback --previous
```

#### Docker Rollback
```bash
# Stop current container
docker stop villawise-container

# Run previous image version
docker run -d \
  --name villawise-container \
  -p 5000:5000 \
  --env-file .env \
  villawise:previous-tag
```

## Cost Optimization

### Railway Pricing
- **Hobby Plan**: $5/month (production use)
- **Pro Plan**: $20/month (advanced features)
- **Custom Plans**: Enterprise pricing

### Upstash Redis Pricing
- **Free tier**: 10,000 requests/day
- **Pay-as-you-scale**: Global edge locations

## Security Considerations

- Environment variables never exposed in logs
- Supabase Row Level Security (RLS) enabled
- OAuth authentication with secure JWT tokens
- Regular security dependency updates
- Non-root Docker container user

## Performance Optimizations

- Multi-stage Docker builds for smaller images
- Static asset caching
- Database query optimization
- Distributed Redis caching
- CDN integration ready

## Support and Maintenance

### Regular Tasks
- **Weekly**: Review application logs and performance
- **Monthly**: Update dependencies and security patches  
- **Quarterly**: Review and optimize database queries
- **Annually**: Review and update OAuth credentials

### Emergency Procedures
- **Outage Response**: Health check monitoring and alerts
- **Security Incidents**: Immediate credential rotation
- **Data Loss**: Backup restoration procedures via Supabase
- **Performance Issues**: Scaling and optimization

This deployment guide provides comprehensive instructions for deploying VillaWise to production with Railway.app, including all necessary configurations, monitoring, and maintenance procedures.