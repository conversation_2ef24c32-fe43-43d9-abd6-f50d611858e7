import { Router } from 'express';
import { Request, Response } from 'express';
import { storage } from '../../storage';
import { requireAuth } from '../../middleware/auth';

const router = Router();

// Upgrade user to host (requires authentication)
router.post('/upgrade-to-host', requireAuth, async (req: Request, res: Response) => {
  try {
    // Get user ID from authenticated user
    const userId = (req as any).userId;
    
    if (!userId) {
      return res.status(400).json({
        success: false,
        message: "Authentication required"
      });
    }

    // Update user to host status
    const updatedUser = await storage.updateUser(userId, {
      is_host: true
    });

    if (!updatedUser) {
      return res.status(404).json({
        success: false,
        message: "User not found"
      });
    }

    res.json({
      success: true,
      message: "Successfully upgraded to host",
      user: updatedUser
    });

  } catch (error) {
    console.error("Host upgrade error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error"
    });
  }
});

export { router as hostUpgradeRoutes };