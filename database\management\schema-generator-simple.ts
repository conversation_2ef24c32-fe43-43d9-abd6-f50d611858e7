#!/usr/bin/env tsx
/**
 * Simple Schema Generator
 * 
 * Basic schema documentation tool
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class SimpleSchemaGenerator {
  private supabase: any;

  constructor() {
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY;

    if (!supabaseUrl || !supabaseKey) {
      throw new Error('Missing SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY environment variables');
    }

    this.supabase = createClient(supabaseUrl, supabaseKey);
  }

  async showStatus(): Promise<void> {
    console.log('\n📊 Database Status:\n');

    try {
      // Get tables
      const { data: tables, error } = await this.supabase
        .from('information_schema.tables')
        .select('table_name')
        .eq('table_schema', 'public')
        .not('table_name', 'like', '_%');

      if (error) {
        console.error('Could not fetch tables:', error.message);
        return;
      }

      console.log(`📋 Tables: ${tables?.length || 0}`);
      tables?.forEach((table: any) => console.log(`   - ${table.table_name}`));

      console.log('\n✅ Database connection successful');
    } catch (error) {
      console.error('Database connection failed:', error);
    }
  }
}

// CLI Interface
async function main() {
  const generator = new SimpleSchemaGenerator();

  try {
    await generator.showStatus();
  } catch (error) {
    console.error('💥 Schema check failed:', error);
    process.exit(1);
  }
}

// Only run if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { SimpleSchemaGenerator };