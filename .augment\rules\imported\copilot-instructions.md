---
type: "agent_requested"
description: "Example description"
---
# GitHub Copilot Instructions for VillaWise

## Project Overview

VillaWise is a sophisticated vacation rental platform featuring 100+ authentic Spanish properties in Costa Blanca region. Built with React 18 + TypeScript + Tailwind CSS frontend, Express.js + Supabase backend, with OAuth authentication, property management, and booking systems.

**Current Status:** Production-ready with modular dashboard architecture, comprehensive security audit pipeline, and multi-language support.

## 🚨 CRITICAL REQUIREMENTS - NEVER SKIP THESE

### 1. TypeScript Check (MANDATORY)

- **ALWAYS run:** `timeout 30s npx tsc --noEmit --skipLibCheck` before ANY commits
- **MUST fix ALL TypeScript errors before proceeding**
- Available as VS Code task: "TypeScript Check"

### 2. Translation Implementation (MANDATORY)

- **ALWAYS implement translations for ALL new components/pages**
- **Use `useTranslations(namespace)` hook from `client/src/lib/translations.ts`**
- **Add translation keys to `server/controllers/shared/translationController.ts`**
- **Support both English (en) and Dutch (nl) languages**
- **Namespace pattern:** Component/page name as namespace (e.g., 'userProfile', 'searchResults')

### 3. Feature-Based Architecture (MANDATORY)

- Follow existing feature-based structure in `client/src/features/`
- Each feature includes: components/, hooks/, services/, types/, utils/
- Use established patterns for authentication, property management, dashboards

## Architecture Patterns

### Tech Stack

- **Frontend:** React 18 + TypeScript + Tailwind CSS + shadcn/ui + Vite
- **Backend:** Express.js + Supabase PostgreSQL (11 tables) + TypeScript
- **Authentication:** Supabase Auth (JWT + OAuth with Google/Facebook)
- **Maps:** Leaflet + OpenStreetMap with clustering for 100+ properties
- **Internationalization:** Custom API-based system with advanced caching
- **Deployment:** Docker + Railway.app with health monitoring

### Project Structure

```
├── client/
│   ├── src/
│   │   ├── components/        # Shared UI components (Header, Footer, auth, map, etc.)
│   │   ├── features/          # Feature-based modules
│   │   │   ├── guest/         # Guest-specific features
│   │   │   │   ├── dashboard/ # Guest dashboard (overview, bookings, wishlists)
│   │   │   │   ├── exploration/ # Property discovery
│   │   │   │   ├── inspiration/ # Travel content
│   │   │   │   ├── property-details/ # Property viewing
│   │   │   │   └── search/    # Property search with maps
│   │   │   ├── host/          # Host-specific features
│   │   │   │   ├── dashboard/ # Host dashboard (overview, bookings, properties)
│   │   │   │   ├── properties/ # Property management
│   │   │   │   └── components/ # Host-specific components
│   │   │   ├── shared/        # Shared features across user types
│   │   │   │   ├── auth/      # Authentication system
│   │   │   │   ├── home/      # Landing page
│   │   │   │   ├── messaging/ # Communication system
│   │   │   │   └── navigation/ # Navigation components
│   │   │   └── admin/         # Admin panel features
│   │   ├── pages/             # Route-based page components
│   │   ├── hooks/             # Shared React hooks
│   │   ├── lib/               # Utility libraries and configurations
│   │   └── types/             # TypeScript type definitions
├── server/
│   ├── controllers/           # Feature-organized API controllers
│   │   ├── guest/             # Guest-specific endpoints
│   │   ├── host/              # Host-specific endpoints
│   │   └── shared/            # Shared endpoints (auth, translations)
│   ├── dal/                   # Data Access Layer (5 components)
│   │   ├── entities/          # Database operations
│   │   ├── dto/               # Data transformation
│   │   ├── auth/              # Session management
│   │   ├── cache/             # Memory caching
│   │   └── aggregators/       # Complex data aggregation
│   ├── routes/                # Express route definitions
│   ├── middleware/            # Authentication and validation
│   ├── services/              # Business logic services
│   └── types/                 # Backend type definitions
├── database/                  # Database schema, migrations, seeds
├── shared/                    # Shared types and schemas (schema.ts)
├── scripts/                   # Build and deployment scripts
├── docs/                      # Comprehensive documentation
├── deployment/                # Docker and Railway configurations
└── configs/                   # Configuration files (components.json)
```

### Data Access Layer (DAL) Architecture

Located in `server/dal/` with 5 key components:

1. **Entity Layer** (`entities/`) - Direct database operations
2. **DTO Layer** (`dto/`) - Safe data transformation for client consumption
3. **Authentication Layer** (`auth/`) - JWT verification and session management
4. **Cache Layer** (`cache/`) - Memory caching with TTL for performance
5. **Aggregator Layer** (`aggregators/`) - Complex multi-entity data aggregation

## Authentication & Security

### OAuth Implementation

- **OAuth Providers:** Google, Facebook via Supabase Auth
- **OAuth Flow:** Backend initiates → Frontend handles callback → Token storage
- **OAuth Routes:** `/api/auth/google`, `/api/auth/facebook`, `/api/auth/callback`
- **Token Management:** JWT tokens with automatic refresh, stored in cookies + localStorage

### Security Requirements

- **Row Level Security (RLS)** enabled for all Supabase tables
- **Input validation** with Zod schemas on all API endpoints
- **CORS protection** with specific allowed origins
- **Rate limiting** on authentication endpoints
- **Security audit pipeline** fails CI/CD for moderate+ vulnerabilities

### Authentication Patterns

```typescript
// Frontend: Use auth hook
const { user, isAuthenticated, login, logout } = useAuth();

// Backend: Use auth middleware
import { requireAuth } from "../middleware/auth";
router.get("/protected", requireAuth, controller.method);

// Database: Access user session
const { user } = await getSessionUser(authHeader);
```

## Translation System

### Implementation Pattern

```typescript
// Frontend hook usage
const t = useTranslations('namespace');
const text = t('key', { variable: 'value' });

// Controller registration
// In server/controllers/shared/translationController.ts
[language]: {
  namespace: {
    key: 'Translation text with {variable}'
  }
}
```

### Translation Architecture

- **API Endpoint:** `/api/translations/:locale` with version-based cache busting
- **Multi-layer Caching:** Memory + localStorage with build version invalidation
- **Supported Languages:** English (en), Dutch (nl)
- **Fallback:** Returns translation key if translation not found

## Frontend Patterns

### Component Organization

```typescript
// Feature component structure
features/[feature]/
├── components/
│   ├── [Feature]Form.tsx
│   ├── [Feature]List.tsx
│   └── [Feature]Card.tsx
├── hooks/
│   ├── use[Feature].ts
│   └── use[Feature]Mutations.ts
├── services/
│   └── [feature]Api.ts
├── types/
│   └── [feature].types.ts
└── utils/
    └── [feature]Utils.ts
```

### State Management

- **TanStack Query:** API state management with caching (5min default)
- **React Hook Form:** Form state with Zod validation
- **Local Storage:** Search filters, user preferences
- **Supabase Auth:** Authentication state management

### UI Standards

- **Design System:** shadcn/ui + Radix UI components
- **Color Scheme:** Yellow accent theme (`hsl(45, 93%, 58%)`)
- **Responsive:** Mobile-first with Tailwind CSS breakpoints
- **Accessibility:** WCAG compliant via Radix UI primitives

## Backend Patterns

### API Controller Structure

```typescript
// Standard controller pattern
export const controller = {
  async method(req: Request, res: Response) {
    try {
      const authHeader = req.headers.authorization;
      const result = await dalMethod(authHeader, params);

      res.json({
        success: true,
        data: result,
      });
    } catch (error) {
      console.error("Controller error:", error);
      res.status(401).json({
        success: false,
        message: error.message,
      });
    }
  },
};
```

### Database Patterns

```typescript
// Entity layer pattern
export async function getEntities(authHeader: string, filters?: any) {
  const { user } = await getSessionUser(authHeader);

  const { data, error } = await supabase
    .from("table")
    .select("*")
    .eq("user_id", user.id);

  if (error) throw new Error(error.message);

  return data.map(transformToDTO);
}
```

## Property Management

### Search Implementation

- **Location Autocomplete:** 49 Costa Blanca locations with database-first approach
- **Map Integration:** Leaflet with clustering for performance (100+ properties)
- **Filtering:** Price, bedrooms, amenities, availability with localStorage persistence
- **Caching:** TanStack Query with 5-minute cache duration

### Property Features

- **Image Management:** Supabase Storage with optimized lazy loading
- **Booking System:** Availability calendar with real-time updates
- **Reviews System:** Host-guest review exchange with moderation
- **Messaging:** Unified host-guest communication system

## Dashboard Architecture

### Host Dashboard Modules

Located in `client/src/features/host/dashboard/`:

- **overview/** - Dashboard main overview and metrics
- **bookings/** - Booking management and calendar
- **properties/** - Property CRUD and management
- **components/** - Shared dashboard UI components
- **hooks/** - Dashboard-specific React hooks
- **services/** - Dashboard API services
- **types/** - Dashboard TypeScript definitions

### Guest Dashboard Modules

Located in `client/src/features/guest/dashboard/`:

- **overview/** - Guest dashboard main view
- **bookings/** - Guest booking history and management
- **wishlists/** - Saved properties and favorites
- **components/** - Guest dashboard UI components

### Current Implementation Status

- **Host Dashboard:** Fully modular with services, hooks, and types architecture
- **Guest Dashboard:** Basic module structure with core features
- **Shared Components:** Common dashboard patterns and layouts

### Dashboard Patterns

```typescript
// Dashboard module structure
export function DashboardModule() {
  const { data, isLoading, error } = useDashboardData("module");
  const t = useTranslations("dashboardModule");

  if (isLoading) return <ModuleSkeleton />;
  if (error) return <ErrorDisplay error={error} />;

  return (
    <div className="dashboard-module">
      <h2>{t("title")}</h2>
      <ModuleContent data={data} />
    </div>
  );
}
```

## Development Environment

### Required Environment Variables

```bash
# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Application
NODE_ENV=development
PORT=5000

# Frontend (VITE_ prefix required)
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key
```

### Development Commands

```bash
# Start development server
npm run dev                 # Backend + Frontend on port 5000

# Type checking (MANDATORY before commits)
timeout 30s npx tsc --noEmit --skipLibCheck

# Code quality
npx eslint . --ext .ts,.tsx,.js,.jsx
npm run test
npm run audit:security

# Database operations
npm run db:push            # Push schema changes
```

## Deployment

### Railway.app Configuration

- **Docker Deployment:** Uses `deployment/railway/Dockerfile.railway`
- **Health Check:** `/api/health` endpoint with 300s timeout
- **Auto-scaling:** Configured for traffic-based scaling
- **Domain Patterns:** `*.up.railway.app`, `villa-wise*.up.railway.app`

### Production Requirements

- **Build Validation:** TypeScript check + ESLint in CI/CD
- **Security Audit:** Fails pipeline for moderate+ vulnerabilities
- **Environment:** Production-optimized with minification and tree shaking
- **Monitoring:** Health checks and performance metrics

## Known Issues & Troubleshooting

### OAuth Redirect Issue

**Problem:** Hardcoded redirect to Railway domain when running localhost
**Location:** `server/controllers/shared/authController.ts` - `getRedirectUrl()` method
**Solution:** Check `OAUTH_REDIRECT_URL` environment variable configuration

### Common Patterns to Follow

1. **Always use TypeScript strict mode** with proper null safety
2. **Implement error boundaries** for React components
3. **Use consistent API response format** with success/error structure
4. **Cache frequently accessed data** with appropriate TTL
5. **Follow REST conventions** for API endpoints
6. **Implement proper loading states** and error handling
7. **Use semantic HTML** with proper accessibility attributes

## Code Quality Standards

### TypeScript Guidelines

- Use strict typing with interfaces over `any`
- Implement proper null safety for Supabase clients
- Use discriminated unions for state management
- Export types from dedicated `.types.ts` files

### React Guidelines

- Use functional components with hooks
- Implement proper cleanup in useEffect
- Use React.memo for performance optimization
- Follow single responsibility principle

### API Guidelines

- Validate all inputs with Zod schemas
- Implement consistent error handling
- Use appropriate HTTP status codes
- Include request logging for debugging

This document serves as the comprehensive guide for maintaining and extending VillaWise following established patterns and critical requirements.
