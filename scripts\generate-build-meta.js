#!/usr/bin/env node

/**
 * Generate build metadata for cache invalidation
 * This script creates a meta.json file with version and build info
 */

const fs = require('fs');
const path = require('path');

try {
  // Read package.json for version
  const packageJsonPath = path.join(__dirname, '../package.json');
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  // Generate build metadata
  const buildMeta = {
    version: packageJson.version,
    buildTime: new Date().toISOString(),
    buildHash: Date.now().toString(36), // Simple hash based on timestamp
    env: process.env.NODE_ENV || 'development'
  };
  
  // Write meta.json to public directory
  const metaPath = path.join(__dirname, '../client/public/meta.json');
  
  // Ensure public directory exists
  const publicDir = path.dirname(metaPath);
  if (!fs.existsSync(publicDir)) {
    fs.mkdirSync(publicDir, { recursive: true });
  }
  
  fs.writeFileSync(metaPath, JSON.stringify(buildMeta, null, 2));
  
  console.log('✅ Build metadata generated successfully');
  console.log(`📦 Version: ${buildMeta.version}`);
  console.log(`🕐 Build Time: ${buildMeta.buildTime}`);
  console.log(`🔗 Build Hash: ${buildMeta.buildHash}`);
  
} catch (error) {
  console.error('❌ Error generating build metadata:', error);
  process.exit(1);
}