import React from "react";
import { <PERSON> } from "wouter";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";

interface AuthLayoutProps {
  children: React.ReactNode;
  title: string;
  subtitle?: string;
  showBackButton?: boolean;
  rightSideContent?: React.ReactNode;
}

export function AuthLayout({ 
  children, 
  title, 
  subtitle, 
  showBackButton = true,
  rightSideContent 
}: AuthLayoutProps) {
  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Left Side - Form */}
      <div className="flex-1 flex flex-col justify-center px-4 sm:px-6 lg:px-8">
        <div className="w-full max-w-md mx-auto">
          {showBackButton && (
            <div className="mb-6">
              <Link href="/">
                <Button variant="ghost" className="flex items-center gap-2 p-0 h-auto">
                  <ArrowLeft className="w-4 h-4" />
                  Back to VillaWise
                </Button>
              </Link>
            </div>
          )}
          
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">{title}</h1>
            {subtitle && (
              <p className="text-gray-600">{subtitle}</p>
            )}
          </div>
          
          {children}
        </div>
      </div>
      
      {/* Right Side - Content */}
      <div className="hidden lg:flex lg:flex-1 lg:flex-col lg:justify-center lg:px-8 bg-primary/5">
        {rightSideContent || (
          <div className="max-w-md mx-auto text-center">
            <div className="mb-8">
              <div className="w-24 h-24 bg-primary/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-12 h-12 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                </svg>
              </div>
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                Find Your Perfect Villa
              </h2>
              <p className="text-gray-600 mb-6">
                Join thousands of travelers who have discovered their ideal vacation rental through VillaWise.
              </p>
              <ul className="text-left space-y-3 text-gray-600">
                <li className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-primary rounded-full"></div>
                  Access to exclusive villa collections
                </li>
                <li className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-primary rounded-full"></div>
                  Verified property reviews and ratings
                </li>
                <li className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-primary rounded-full"></div>
                  Direct booking with property owners
                </li>
                <li className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-primary rounded-full"></div>
                  24/7 customer support
                </li>
              </ul>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}