
import { supabase } from '../../supabase'
import { getCurrentUser, requireAuth, requireRole } from '../auth/session'
import { BookingDTO, BookingData, BookingFilters, BookingStats } from '../dto/booking.dto'
import { memoryCache } from '../cache/memoryCache'

export const getBookings = async (
  authHeader: string,
  userType: 'host' | 'guest',
  filters?: BookingFilters
): Promise<BookingDTO[]> => {
  const user = await requireAuth(authHeader)
  
  const cacheKey = `bookings:${userType}:${user.userId}:${JSON.stringify(filters)}`
  const cached = memoryCache.get(cacheKey) as BookingDTO[] | null
  if (cached) return cached
  
  try {
    let query = supabase
      .from('bookings')
      .select(`
        *,
        properties:property_id (
          id,
          title,
          location,
          images
        ),
        guest:guest_id (
          id,
          full_name,
          email,
          profile_picture
        ),
        host:host_id (
          id,
          full_name,
          email,
          profile_picture
        )
      `)
    
    if (userType === 'host') {
      query = query.eq('host_id', user.userId)
    } else {
      query = query.eq('guest_id', user.userId)
    }
    
    if (filters?.status && filters.status.length > 0) {
      query = query.in('status', filters.status)
    }
    
    const { data: bookings, error } = await query.order('created_at', { ascending: false })
    
    if (error) {
      throw new Error(`Failed to fetch bookings: ${error.message}`)
    }
    
    const result = BookingDTO.fromArray(bookings || [], user.role, user.userId)
    memoryCache.set(cacheKey, result, 120)
    return result
  } catch (error) {
    console.error('Error fetching bookings:', error)
    throw error
  }
}

export const createBooking = async (
  authHeader: string,
  bookingData: {
    propertyId: string
    checkInDate: string
    checkOutDate: string
    guestCount: number
    specialRequests?: string
  }
): Promise<BookingDTO> => {
  const user = await requireAuth(authHeader)
  
  try {
    const { data: property, error: propertyError } = await supabase
      .from('properties')
      .select('id, host_id, price_per_night, max_guests, status')
      .eq('id', bookingData.propertyId)
      .single()
    
    if (propertyError || !property) {
      throw new Error('Property not found')
    }
    
    if (property.status !== 'active') {
      throw new Error('Property is not available for booking')
    }
    
    const checkIn = new Date(bookingData.checkInDate)
    const checkOut = new Date(bookingData.checkOutDate)
    const nights = Math.ceil((checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60 * 24))
    const totalAmount = nights * property.price_per_night
    
    const { data: booking, error } = await supabase
      .from('bookings')
      .insert({
        property_id: bookingData.propertyId,
        guest_id: user.userId,
        host_id: property.host_id,
        check_in_date: bookingData.checkInDate,
        check_out_date: bookingData.checkOutDate,
        guest_count: bookingData.guestCount,
        total_amount: totalAmount,
        special_requests: bookingData.specialRequests,
        status: 'pending',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select(`
        *,
        properties:property_id (
          id,
          title,
          location,
          images
        )
      `)
      .single()
    
    if (error) {
      throw new Error(`Failed to create booking: ${error.message}`)
    }
    
    memoryCache.invalidatePattern(`bookings:guest:${user.userId}:*`)
    memoryCache.invalidatePattern(`bookings:host:${property.host_id}:*`)
    
    return new BookingDTO(booking, user.role, user.userId)
  } catch (error) {
    console.error('Error creating booking:', error)
    throw error
  }
}

export const getBookingStats = async (
  authHeader: string,
  userType: 'host' | 'guest'
): Promise<BookingStats> => {
  const user = await requireAuth(authHeader)
  
  const cacheKey = `booking-stats:${userType}:${user.userId}`
  const cached = memoryCache.get(cacheKey) as BookingStats | null
  if (cached) return cached
  
  try {
    let query = supabase
      .from('bookings')
      .select('status, total_amount, created_at')
    
    if (userType === 'host') {
      query = query.eq('host_id', user.userId)
    } else {
      query = query.eq('guest_id', user.userId)
    }
    
    const { data: bookings, error } = await query
    
    if (error) {
      throw new Error(`Failed to fetch booking stats: ${error.message}`)
    }
    
    const stats: BookingStats = {
      total: bookings?.length || 0,
      confirmed: bookings?.filter(b => b.status === 'confirmed').length || 0,
      pending: bookings?.filter(b => b.status === 'pending').length || 0,
      cancelled: bookings?.filter(b => b.status === 'cancelled').length || 0,
      completed: bookings?.filter(b => b.status === 'completed').length || 0,
      totalRevenue: bookings?.reduce((sum, b) => sum + (b.total_amount || 0), 0) || 0
    }
    
    memoryCache.set(cacheKey, stats, 600)
    return stats
  } catch (error) {
    console.error('Error fetching booking stats:', error)
    throw error
  }
}

export const updateBookingStatus = async (
  authHeader: string,
  bookingId: string,
  status: string
): Promise<BookingDTO> => {
  const user = await requireAuth(authHeader)
  
  try {
    const { data: booking, error } = await supabase
      .from('bookings')
      .update({
        status,
        updated_at: new Date().toISOString()
      })
      .eq('id', bookingId)
      .or(`host_id.eq.${user.userId},guest_id.eq.${user.userId}`)
      .select(`
        *,
        properties:property_id (
          id,
          title,
          location,
          images
        )
      `)
      .single()
    
    if (error) {
      throw new Error(`Failed to update booking status: ${error.message}`)
    }
    
    memoryCache.invalidatePattern(`bookings:*:${user.userId}:*`)
    
    return new BookingDTO(booking, user.role, user.userId)
  } catch (error) {
    console.error('Error updating booking status:', error)
    throw error
  }
}