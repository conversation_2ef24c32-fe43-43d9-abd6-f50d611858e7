import { useState, useEffect } from 'react';
import { useIsMobile } from './use-mobile';
import type { ViewMode } from '@/types/property';

export function useViewMode(defaultView: ViewMode = 'list') {
  const isMobile = useIsMobile();
  const [viewMode, setViewMode] = useState<ViewMode>(defaultView);

  // Load saved view mode from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('villawise-view-mode');
    if (saved && ['list', 'split', 'map'].includes(saved)) {
      // On mobile, force list view for split mode
      if (isMobile && saved === 'split') {
        setViewMode('list');
      } else {
        setViewMode(saved as ViewMode);
      }
    }
  }, [isMobile]);

  // Save view mode to localStorage
  const changeViewMode = (mode: ViewMode) => {
    // On mobile, convert split to list
    const actualMode = isMobile && mode === 'split' ? 'list' : mode;
    setViewMode(actualMode);
    localStorage.setItem('villawise-view-mode', actualMode);
  };

  return { viewMode, setViewMode: changeViewMode };
}