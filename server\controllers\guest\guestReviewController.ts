import { Request, Response } from 'express';
import { Logger } from '../../utils/logger';
import { storage } from '../../storage';
import { insertGuestReviewSchema } from '../../../shared/schema';
import { z } from 'zod';

export class GuestReviewController {
  
  async getReviewsByGuest(req: Request, res: Response) {
    const startTime = Date.now();
    try {
      const guestId = req.params.guestId;
      
      if (!guestId) {
        return res.status(400).json({ error: 'Guest ID is required' });
      }
      
      Logger.info(`Guest reviews requested for guest: ${guestId}`);
      
      const reviews = await storage.getGuestReviewsByGuest(guestId);
      
      Logger.api('GET', `/api/guest/reviews/${guestId}`, 200, Date.now() - startTime);
      res.json(reviews);
    } catch (error) {
      Logger.error('Error fetching guest reviews', error);
      Logger.api('GET', `/api/guest/reviews/${req.params.guestId}`, 500, Date.now() - startTime);
      res.status(500).json({ error: 'Failed to fetch guest reviews' });
    }
  }

  async getReviewsByProperty(req: Request, res: Response) {
    const startTime = Date.now();
    try {
      const propertyId = req.params.propertyId;
      
      if (!propertyId) {
        return res.status(400).json({ error: 'Property ID is required' });
      }
      
      Logger.info(`Guest reviews requested for property: ${propertyId}`);
      
      const reviews = await storage.getGuestReviewsByProperty(propertyId);
      
      Logger.api('GET', `/api/guest/reviews/property/${propertyId}`, 200, Date.now() - startTime);
      res.json(reviews);
    } catch (error) {
      Logger.error('Error fetching property reviews', error);
      Logger.api('GET', `/api/guest/reviews/property/${req.params.propertyId}`, 500, Date.now() - startTime);
      res.status(500).json({ error: 'Failed to fetch property reviews' });
    }
  }

  async createReview(req: Request, res: Response) {
    const startTime = Date.now();
    try {
      const validation = insertGuestReviewSchema.safeParse(req.body);
      
      if (!validation.success) {
        return res.status(400).json({ 
          error: 'Invalid review data', 
          details: validation.error.errors 
        });
      }
      
      const reviewData = validation.data;
      Logger.info(`Creating guest review for property: ${reviewData.host_property_id}`);
      
      const review = await storage.createGuestReview(reviewData);
      
      Logger.api('POST', '/api/guest/review', 201, Date.now() - startTime);
      res.status(201).json(review);
    } catch (error) {
      Logger.error('Error creating guest review', error);
      Logger.api('POST', '/api/guest/review', 500, Date.now() - startTime);
      res.status(500).json({ error: 'Failed to create guest review' });
    }
  }
}

export const guestReviewController = new GuestReviewController();