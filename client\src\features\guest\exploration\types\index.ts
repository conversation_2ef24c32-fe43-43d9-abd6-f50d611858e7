import { z } from 'zod';

// Property schema for exploration section
export const explorationPropertySchema = z.object({
  id: z.string(),
  title: z.string(),
  hostType: z.string(),
  price: z.string(),
  rating: z.number(),
  reviewCount: z.number(),
  images: z.array(z.string()),
  isGuest: z.boolean().optional(),
  badge: z.string().optional(),
});

// Type exports
export type ExplorationProperty = z.infer<typeof explorationPropertySchema>;

export interface ExplorationSectionProps {
  title: string;
  properties: ExplorationProperty[];
}