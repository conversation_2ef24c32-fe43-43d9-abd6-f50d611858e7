import React from 'react';
import { LucideIcon } from 'lucide-react';

interface PlaceholderContentProps {
  icon: LucideIcon;
  title: string;
  description: string;
}

const PlaceholderContent: React.FC<PlaceholderContentProps> = ({
  icon: Icon,
  title,
  description
}) => {
  return (
    <div className="p-6">
      <div className="text-center py-12">
        <Icon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h2 className="text-xl font-semibold text-gray-900 mb-2">{title}</h2>
        <p className="text-gray-600">{description}</p>
      </div>
    </div>
  );
};

export default PlaceholderContent;