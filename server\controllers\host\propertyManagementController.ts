import { Request, Response } from "express";
import { storage } from "../../storage";
import { insertPropertySchema, insertBookingSchema, insertReviewSchema } from "@shared/schema";
import { z } from "zod";

export class PropertyManagementController {
  // Get all properties with filtering (HOST-ONLY with authentication)
  async getProperties(req: Request, res: Response) {
    try {
      const requestingUserId = (req as any).userId; // From auth middleware
      
      if (!requestingUserId) {
        return res.status(401).json({
          success: false,
          error: "Authentication required"
        });
      }
      
      // For host dashboard, only return properties owned by the authenticated user
      const properties = await storage.getHostPropertiesByHost(requestingUserId);

      res.json({
        success: true,
        properties,
        count: properties.length
      });
    } catch (error) {
      console.error("Error fetching host properties:", error);
      res.status(500).json({
        success: false,
        error: "Failed to fetch host properties"
      });
    }
  }

  // Get single property by ID (HOST-ONLY with ownership validation)
  async getProperty(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const requestingUserId = (req as any).userId; // From auth middleware
      
      if (!requestingUserId) {
        return res.status(401).json({
          success: false,
          error: "Authentication required"
        });
      }
      
      const property = await storage.getHostProperty(id);
      
      if (!property) {
        return res.status(404).json({
          success: false,
          error: "Property not found"
        });
      }

      // RLS-style check: Hosts can only access their own properties
      if (property.host_id !== requestingUserId) {
        return res.status(403).json({
          success: false,
          error: "Access denied - You can only access your own properties"
        });
      }

      // Get reviews for this property
      const reviews = await storage.getGuestReviewsByProperty(id);
      
      res.json({
        success: true,
        property: {
          ...property,
          reviews
        }
      });
    } catch (error) {
      console.error("Error fetching property:", error);
      res.status(500).json({
        success: false,
        error: "Failed to fetch property"
      });
    }
  }

  // Create new property (HOST-ONLY with authentication)
  async createProperty(req: Request, res: Response) {
    try {
      const requestingUserId = (req as any).userId; // From auth middleware
      
      if (!requestingUserId) {
        return res.status(401).json({
          success: false,
          error: "Authentication required"
        });
      }
      
      const validatedData = insertPropertySchema.parse(req.body);
      
      // Force host_id to be the authenticated user ID for security
      const propertyData = {
        ...validatedData,
        host_id: requestingUserId
      };

      const property = await storage.createHostProperty(propertyData);
      
      res.status(201).json({
        success: true,
        property
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          success: false,
          error: "Invalid property data",
          details: error.errors
        });
      }
      
      console.error("Error creating property:", error);
      res.status(500).json({
        success: false,
        error: "Failed to create property"
      });
    }
  }

  // Update property (HOST-ONLY with ownership validation)
  async updateProperty(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const requestingUserId = (req as any).userId; // From auth middleware
      const updates = req.body;
      
      if (!requestingUserId) {
        return res.status(401).json({
          success: false,
          error: "Authentication required"
        });
      }
      
      // First, verify the property exists and belongs to the authenticated user
      const existingProperty = await storage.getHostProperty(id);
      if (!existingProperty) {
        return res.status(404).json({
          success: false,
          error: "Property not found"
        });
      }
      
      // RLS-style check: Hosts can only update their own properties
      if (existingProperty.host_id !== requestingUserId) {
        return res.status(403).json({
          success: false,
          error: "Access denied - You can only update your own properties"
        });
      }
      
      // Prevent host_id from being changed in updates for security
      const { host_id, ...safeUpdates } = updates;
      
      const property = await storage.updateHostProperty(id, safeUpdates);
      
      if (!property) {
        return res.status(404).json({
          success: false,
          error: "Property not found"
        });
      }

      res.json({
        success: true,
        property
      });
    } catch (error) {
      console.error("Error updating property:", error);
      res.status(500).json({
        success: false,
        error: "Failed to update property"
      });
    }
  }

  // Create booking
  async createBooking(req: Request, res: Response) {
    try {
      const validatedData = insertBookingSchema.parse(req.body);
      
      // For now, use a default guest ID since we don't have auth
      const bookingData = {
        ...validatedData,
        guestId: 1, // Default guest ID
        check_in: new Date(validatedData.check_in),
        check_out: new Date(validatedData.check_out)
      };

      const booking = await storage.createGuestBooking(bookingData);
      
      res.status(201).json({
        success: true,
        booking
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          success: false,
          error: "Invalid booking data",
          details: error.errors
        });
      }
      
      console.error("Error creating booking:", error);
      res.status(500).json({
        success: false,
        error: "Failed to create booking"
      });
    }
  }

  // Get bookings (HOST-ONLY with authentication)
  async getBookings(req: Request, res: Response) {
    try {
      const requestingUserId = (req as any).userId; // From auth middleware
      
      if (!requestingUserId) {
        return res.status(401).json({
          success: false,
          error: "Authentication required"
        });
      }
      
      // For host dashboard, only return bookings for properties owned by the authenticated host
      const bookings = await storage.getGuestBookingsByHost(requestingUserId);

      res.json({
        success: true,
        bookings,
        count: bookings.length
      });
    } catch (error) {
      console.error("Error fetching host bookings:", error);
      res.status(500).json({
        success: false,
        error: "Failed to fetch host bookings"
      });
    }
  }

  // Update booking status (HOST-ONLY with authorization)
  async updateBooking(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const requestingUserId = (req as any).userId; // From auth middleware
      const updates = req.body;
      
      if (!requestingUserId) {
        return res.status(401).json({
          success: false,
          error: "Authentication required"
        });
      }
      
      // First, verify the booking exists and belongs to a property owned by the authenticated host
      const existingBooking = await storage.getGuestBooking(id);
      if (!existingBooking) {
        return res.status(404).json({
          success: false,
          error: "Booking not found"
        });
      }
      
      // Get the property to verify ownership
      const property = await storage.getHostProperty(existingBooking.host_property_id);
      if (!property) {
        return res.status(404).json({
          success: false,
          error: "Property not found"
        });
      }
      
      // RLS-style check: Hosts can only update bookings for their own properties
      if (property.host_id !== requestingUserId) {
        return res.status(403).json({
          success: false,
          error: "Access denied - You can only update bookings for your own properties"
        });
      }
      
      const booking = await storage.updateGuestBooking(id, updates);
      
      if (!booking) {
        return res.status(404).json({
          success: false,
          error: "Booking not found"
        });
      }

      res.json({
        success: true,
        booking
      });
    } catch (error) {
      console.error("Error updating booking:", error);
      res.status(500).json({
        success: false,
        error: "Failed to update booking"
      });
    }
  }

  // Create review
  async createReview(req: Request, res: Response) {
    try {
      const validatedData = insertReviewSchema.parse(req.body);
      
      const review = await storage.createGuestReview(validatedData);
      
      res.status(201).json({
        success: true,
        review
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          success: false,
          error: "Invalid review data",
          details: error.errors
        });
      }
      
      console.error("Error creating review:", error);
      res.status(500).json({
        success: false,
        error: "Failed to create review"
      });
    }
  }

  // Get reviews
  async getReviews(req: Request, res: Response) {
    try {
      const { propertyId, guestId } = req.query;
      
      let reviews: any[] = [];
      
      if (propertyId) {
        reviews = await storage.getGuestReviewsByProperty(propertyId as string);
      } else if (guestId) {
        reviews = await storage.getGuestReviewsByGuest(guestId as string);
      } else {
        reviews = [];
      }

      res.json({
        success: true,
        reviews,
        count: reviews.length
      });
    } catch (error) {
      console.error("Error fetching reviews:", error);
      res.status(500).json({
        success: false,
        error: "Failed to fetch reviews"
      });
    }
  }

  // Get host earnings summary (HOST-ONLY with authentication)
  async getOwnerEarnings(req: Request, res: Response) {
    try {
      const requestingUserId = (req as any).userId; // From auth middleware
      
      if (!requestingUserId) {
        return res.status(401).json({
          success: false,
          error: "Authentication required"
        });
      }
      
      // Get real earnings data from host's bookings
      const hostBookings = await storage.getGuestBookingsByHost(requestingUserId);
      
      // Calculate real earnings from completed bookings
      const completedBookings = hostBookings.filter((booking: any) => booking.status === 'completed');
      const totalEarnings = completedBookings.reduce((sum: any, booking: any) => sum + parseFloat(booking.total_price), 0);
      
      // Calculate this month's earnings
      const currentMonth = new Date().getMonth();
      const currentYear = new Date().getFullYear();
      const thisMonthBookings = completedBookings.filter((booking: any) => {
        const bookingDate = new Date(booking.created_at);
        return bookingDate.getMonth() === currentMonth && bookingDate.getFullYear() === currentYear;
      });
      const thisMonth = thisMonthBookings.reduce((sum: any, booking: any) => sum + parseFloat(booking.total_price), 0);
      
      // Calculate last month's earnings
      const lastMonth = currentMonth === 0 ? 11 : currentMonth - 1;
      const lastMonthYear = currentMonth === 0 ? currentYear - 1 : currentYear;
      const lastMonthBookings = completedBookings.filter((booking: any) => {
        const bookingDate = new Date(booking.created_at);
        return bookingDate.getMonth() === lastMonth && bookingDate.getFullYear() === lastMonthYear;
      });
      const lastMonthEarnings = lastMonthBookings.reduce((sum: any, booking: any) => sum + parseFloat(booking.total_price), 0);
      
      // Calculate pending payouts
      const pendingBookings = hostBookings.filter((booking: any) => booking.status === 'confirmed');
      const pendingPayouts = pendingBookings.reduce((sum: any, booking: any) => sum + parseFloat(booking.total_price), 0);
      
      // Calculate average nightly rate
      const averageNightlyRate = totalEarnings / Math.max(completedBookings.length, 1);
      
      const earnings = {
        totalEarnings,
        thisMonth,
        lastMonth: lastMonthEarnings,
        pendingPayouts,
        completedBookings: completedBookings.length,
        averageNightlyRate
      };

      res.json({
        success: true,
        earnings
      });
    } catch (error) {
      console.error("Error fetching owner earnings:", error);
      res.status(500).json({
        success: false,
        error: "Failed to fetch earnings"
      });
    }
  }

  // Get property performance metrics (HOST-ONLY with authentication)
  async getPropertyMetrics(req: Request, res: Response) {
    try {
      const requestingUserId = (req as any).userId; // From auth middleware
      
      if (!requestingUserId) {
        return res.status(401).json({
          success: false,
          error: "Authentication required"
        });
      }
      
      // Get real metrics from host's properties
      const hostProperties = await storage.getHostPropertiesByHost(requestingUserId);
      const hostBookings = await storage.getGuestBookingsByHost(requestingUserId);
      
      // Calculate aggregate metrics
      const totalProperties = hostProperties.length;
      const totalBookings = hostBookings.length;
      const completedBookings = hostBookings.filter((booking: any) => booking.status === 'completed').length;
      
      // Calculate average rating across all properties
      const averageRating = totalProperties > 0 
        ? hostProperties.reduce((sum: any, prop: any) => sum + (prop.rating || 0), 0) / totalProperties
        : 0;
      
      // Calculate total review count
      const reviewCount = hostProperties.reduce((sum: any, prop: any) => sum + (prop.review_count || 0), 0);
      
      // Calculate occupancy rate (simplified - based on bookings vs available days)
      const occupancyRate = totalBookings > 0 ? (completedBookings / totalBookings) * 100 : 0;
      
      // Calculate conversion rate (bookings vs views - simplified)
      const conversionRate = totalBookings > 0 ? (completedBookings / totalBookings) * 100 : 0;
      
      const metrics = {
        totalViews: totalBookings * 10, // Simplified - assume 10 views per booking
        viewsThisMonth: totalBookings * 3, // Simplified - assume 30% of views this month
        conversionRate,
        averageRating,
        reviewCount,
        occupancyRate
      };

      res.json({
        success: true,
        metrics
      });
    } catch (error) {
      console.error("Error fetching property metrics:", error);
      res.status(500).json({
        success: false,
        error: "Failed to fetch metrics"
      });
    }
  }

  // Create property draft
  async createPropertyDraft(req: Request, res: Response) {
    try {
      console.log('🔍 CreatePropertyDraft called');
      console.log('🔍 Request userId:', (req as any).userId);
      console.log('🔍 Request body:', req.body);
      
      const requestingUserId = (req as any).userId;
      
      if (!requestingUserId) {
        console.log('❌ No userId found, checking development mode');
        // In development mode, allow creating drafts with a mock user
        if (process.env.NODE_ENV === 'development') {
          console.log('🔧 Development mode: Creating draft with mock user');
          (req as any).userId = 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11';
        } else {
          console.log('❌ Not development mode, returning 401');
          return res.status(401).json({
            success: false,
            error: "Authentication required"
          });
        }
      }

      const { data = {}, currentStep = 0 } = req.body;
      
      // Create a simple draft object with ID
      const draftId = `draft_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const draft = {
        id: draftId,
        hostId: requestingUserId,
        data,
        currentStep,
        lastSaved: new Date(),
        isPublished: false
      };

      // Store in memory storage for now (in a real app, this would go to database)
      await storage.createPropertyDraft(draft);

      const response = {
        success: true,
        data: draft
      };
      
      console.log('✅ Sending response:', JSON.stringify(response, null, 2));
      res.status(201).json(response);
    } catch (error) {
      console.error("Error creating property draft:", error);
      res.status(500).json({
        success: false,
        error: "Failed to create property draft"
      });
    }
  }

  // Get property draft
  async getPropertyDraft(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const requestingUserId = (req as any).userId;
      
      if (!requestingUserId) {
        return res.status(401).json({
          success: false,
          error: "Authentication required"
        });
      }

      const draft = await storage.getPropertyDraft(id);
      
      if (!draft) {
        return res.status(404).json({
          success: false,
          error: "Draft not found"
        });
      }

      // Ensure user can only access their own drafts
      if (draft.hostId !== requestingUserId) {
        return res.status(403).json({
          success: false,
          error: "Access denied"
        });
      }

      res.json({
        success: true,
        data: draft
      });
    } catch (error) {
      console.error("Error fetching property draft:", error);
      res.status(500).json({
        success: false,
        error: "Failed to fetch property draft"
      });
    }
  }

  // Update property draft
  async updatePropertyDraft(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const requestingUserId = (req as any).userId;
      
      if (!requestingUserId) {
        return res.status(401).json({
          success: false,
          error: "Authentication required"
        });
      }

      const existingDraft = await storage.getPropertyDraft(id);
      
      if (!existingDraft) {
        return res.status(404).json({
          success: false,
          error: "Draft not found"
        });
      }

      // Ensure user can only update their own drafts
      if (existingDraft.hostId !== requestingUserId) {
        return res.status(403).json({
          success: false,
          error: "Access denied"
        });
      }

      const updates = req.body;
      const updatedDraft = {
        ...existingDraft,
        ...updates,
        lastSaved: new Date()
      };

      await storage.updatePropertyDraft(id, updatedDraft);

      res.json({
        success: true,
        data: updatedDraft
      });
    } catch (error) {
      console.error("Error updating property draft:", error);
      res.status(500).json({
        success: false,
        error: "Failed to update property draft"
      });
    }
  }

  // Delete property draft
  async deletePropertyDraft(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const requestingUserId = (req as any).userId;
      
      if (!requestingUserId) {
        return res.status(401).json({
          success: false,
          error: "Authentication required"
        });
      }

      const existingDraft = await storage.getPropertyDraft(id);
      
      if (!existingDraft) {
        return res.status(404).json({
          success: false,
          error: "Draft not found"
        });
      }

      // Ensure user can only delete their own drafts
      if (existingDraft.hostId !== requestingUserId) {
        return res.status(403).json({
          success: false,
          error: "Access denied"
        });
      }

      await storage.deletePropertyDraft(id);

      res.json({
        success: true,
        message: "Draft deleted successfully"
      });
    } catch (error) {
      console.error("Error deleting property draft:", error);
      res.status(500).json({
        success: false,
        error: "Failed to delete property draft"
      });
    }
  }

  // Publish property draft
  async publishPropertyDraft(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const requestingUserId = (req as any).userId;
      
      if (!requestingUserId) {
        return res.status(401).json({
          success: false,
          error: "Authentication required"
        });
      }

      const draft = await storage.getPropertyDraft(id);
      
      if (!draft) {
        return res.status(404).json({
          success: false,
          error: "Draft not found"
        });
      }

      // Ensure user can only publish their own drafts
      if (draft.hostId !== requestingUserId) {
        return res.status(403).json({
          success: false,
          error: "Access denied"
        });
      }

      // Validate draft data before publishing
      try {
        const propertyData = {
          ...draft.data,
          host_id: requestingUserId,
          status: 'active'
        };
        
        const validatedData = insertPropertySchema.parse(propertyData);
        const property = await storage.createHostProperty(validatedData);
        
        // Delete the draft after successful publishing
        await storage.deletePropertyDraft(id);
        
        res.status(201).json({
          success: true,
          property,
          message: "Property published successfully"
        });
      } catch (validationError) {
        if (validationError instanceof z.ZodError) {
          return res.status(400).json({
            success: false,
            error: "Invalid property data",
            details: validationError.errors
          });
        }
        throw validationError;
      }
    } catch (error) {
      console.error("Error publishing property draft:", error);
      res.status(500).json({
        success: false,
        error: "Failed to publish property"
      });
    }
  }
}

export const propertyManagementController = new PropertyManagementController();