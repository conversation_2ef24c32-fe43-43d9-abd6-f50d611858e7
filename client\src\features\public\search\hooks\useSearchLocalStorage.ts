import { useLocalStorage } from './useLocalStorage';
import { useEffect } from 'react';

export interface RecentSearchLocation {
  id: string;
  name: string;
  type: string;
  country: string;
  region?: string;
  lastUsed: number;
}

export interface SearchDefaults {
  guests: {
    adults: number;
    children: number;
    infants: number;
    pets: number;
  };
  recentLocations: RecentSearchLocation[];
  lastSearch?: {
    location: string;
    dateRange?: {
      from: string;
      to: string;
    };
    dateFlexibility?: number | "exact" | null;
    guests: {
      adults: number;
      children: number;
      infants: number;
      pets: number;
    };
    timestamp: number;
  };
}

const defaultSearchDefaults: SearchDefaults = {
  guests: {
    adults: 2,
    children: 0,
    infants: 0,
    pets: 0,
  },
  recentLocations: [],
  lastSearch: undefined,
};

export function useSearchLocalStorage() {
  const [searchDefaults, setSearchDefaults] = useLocalStorage<SearchDefaults>(
    'villawise-search-defaults',
    defaultSearchDefaults
  );

  // Migrate old localStorage data to include lastSearch field
  useEffect(() => {
    if (searchDefaults && !('lastSearch' in searchDefaults)) {
      console.log('🔄 Migrating localStorage to include lastSearch field');
      setSearchDefaults(prev => ({
        ...prev,
        lastSearch: undefined,
      }));
    }
  }, [searchDefaults, setSearchDefaults]);

  // Migrate existing lastSearch data to include dateFlexibility field
  useEffect(() => {
    if (searchDefaults?.lastSearch && !('dateFlexibility' in searchDefaults.lastSearch)) {
      console.log('🔄 Migrating lastSearch to include dateFlexibility field');
      setSearchDefaults(prev => ({
        ...prev,
        lastSearch: prev.lastSearch ? {
          ...prev.lastSearch,
          dateFlexibility: null,
        } : undefined,
      }));
    }
  }, [searchDefaults, setSearchDefaults]);

  // Simplified - we only track recent locations for backward compatibility
  const addRecentLocation = (location: Omit<RecentSearchLocation, 'lastUsed'>) => {
    setSearchDefaults(prev => {
      // Remove existing entry for this location
      const filtered = prev.recentLocations.filter(item => item.id !== location.id);
      
      // Add new entry at the beginning
      const newLocation: RecentSearchLocation = {
        ...location,
        lastUsed: Date.now(),
      };
      
      // Keep only the 5 most recent locations
      const updated = [newLocation, ...filtered].slice(0, 5);
      
      return {
        ...prev,
        recentLocations: updated,
      };
    });
  };

  const updateDefaultGuests = (guests: SearchDefaults['guests']) => {
    setSearchDefaults(prev => ({
      ...prev,
      guests,
    }));
  };

  const saveLastSearch = (searchParams: {
    location: string;
    dateRange?: { from: Date; to: Date };
    dateFlexibility?: number | "exact" | null;
    guests: {
      adults: number;
      children: number;
      infants: number;
      pets: number;
    };
  }) => {
    const lastSearch = {
      location: searchParams.location,
      dateRange: searchParams.dateRange ? {
        from: searchParams.dateRange.from.toISOString(),
        to: searchParams.dateRange.to.toISOString(),
      } : undefined,
      dateFlexibility: searchParams.dateFlexibility,
      guests: searchParams.guests,
      timestamp: Date.now(),
    };
    
    console.log('💾 saveLastSearch: Saving to localStorage:', lastSearch);
    
    setSearchDefaults(prev => {
      const updated = {
        ...prev,
        lastSearch,
      };
      console.log('💾 saveLastSearch: Updated searchDefaults:', updated);
      return updated;
    });
  };

  const getLastSearch = () => {
    console.log('🔍 getLastSearch: searchDefaults.lastSearch:', searchDefaults.lastSearch);
    
    if (!searchDefaults.lastSearch) {
      console.log('🔍 getLastSearch: No lastSearch found');
      return null;
    }

    const lastSearch = searchDefaults.lastSearch;
    
    // Return null if last search is older than 30 days
    if (Date.now() - lastSearch.timestamp > 30 * 24 * 60 * 60 * 1000) {
      console.log('🔍 getLastSearch: Search too old, clearing');
      return null;
    }

    const result = {
      location: lastSearch.location,
      dateRange: lastSearch.dateRange ? {
        from: new Date(lastSearch.dateRange.from),
        to: new Date(lastSearch.dateRange.to),
      } : undefined,
      dateFlexibility: lastSearch.dateFlexibility,
      guests: lastSearch.guests,
    };
    
    console.log('🔍 getLastSearch: Returning:', result);
    return result;
  };

  const clearLastSearch = () => {
    setSearchDefaults(prev => ({
      ...prev,
      lastSearch: undefined,
    }));
  };

  // Keep for backward compatibility with LocationAutocomplete
  const getRecentLocations = () => {
    return searchDefaults.recentLocations
      .sort((a, b) => b.lastUsed - a.lastUsed);
  };

  return {
    searchDefaults,
    addRecentLocation,
    updateDefaultGuests,
    getRecentLocations,
    saveLastSearch,
    getLastSearch,
    clearLastSearch,
  };
}