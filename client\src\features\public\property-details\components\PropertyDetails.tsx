import { ArrowLeft } from 'lucide-react';
import { useTranslations } from '@/lib/translations';
import { Button } from '@/components/ui/button';
import { usePropertyDetails } from '../hooks/usePropertyDetails';
import { PropertyHero } from './PropertyHero';
import { PropertyHeader } from './PropertyHeader';
import { PropertyDescription } from './PropertyDescription';
import { BookingCard } from './BookingCard';
import { HostProfile } from './HostProfile';
import { ReviewsSection } from './ReviewsSection';
import { ErrorBoundary } from '@/components/ErrorBoundary';

interface PropertyDetailsProps {
  propertyId: string;
  onBack?: () => void;
}

export function PropertyDetails({ propertyId, onBack }: PropertyDetailsProps) {
  const t = useTranslations('propertyDetails');
  const { data: property, isLoading, error } = usePropertyDetails(propertyId);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse space-y-8">
            <div className="h-8 bg-gray-200 rounded w-1/4"></div>
            <div className="h-96 bg-gray-200 rounded-xl"></div>
            <div className="space-y-4">
              <div className="h-6 bg-gray-200 rounded w-3/4"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !property) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center space-y-4">
          <h2 className="text-2xl font-bold text-gray-900">{t('propertyNotFound')}</h2>
          <p className="text-gray-600">{t('propertyNotFoundDescription')}</p>
          <Button onClick={onBack} variant="outline">
            <ArrowLeft className="h-4 w-4 mr-2" />
            {t('goBack')}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-3 sm:px-4 lg:px-8 py-3 sm:py-8">
        {/* Back Button */}
        {onBack && (
          <Button
            variant="ghost"
            onClick={onBack}
            className="mb-4 sm:mb-6 -ml-2"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            {t('back')}
          </Button>
        )}

        {/* Hero Image */}
        <ErrorBoundary>
          <PropertyHero property={property} />
        </ErrorBoundary>

        {/* Main Content */}
        <div className="grid lg:grid-cols-3 gap-4 lg:gap-8 mt-4 lg:mt-8">
          {/* Left Column - Property Info */}
          <div className="lg:col-span-2 space-y-4 lg:space-y-8 order-2 lg:order-1">
            <ErrorBoundary>
              <PropertyHeader property={property} />
            </ErrorBoundary>

            <div className="border-t pt-4 lg:pt-8">
              <ErrorBoundary>
                <HostProfile property={property} />
              </ErrorBoundary>
            </div>

            <div className="border-t pt-4 lg:pt-8">
              <ErrorBoundary>
                <PropertyDescription property={property} />
              </ErrorBoundary>
            </div>

            <div className="border-t pt-4 lg:pt-8">
              <ErrorBoundary>
                <ReviewsSection property={property} />
              </ErrorBoundary>
            </div>
          </div>

          {/* Right Column - Booking Card */}
          <div className="lg:col-span-1 order-1 lg:order-2">
            <div className="lg:sticky lg:top-8">
              <ErrorBoundary>
                <BookingCard property={property} />
              </ErrorBoundary>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}