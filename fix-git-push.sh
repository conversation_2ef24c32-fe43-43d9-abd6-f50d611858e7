#!/bin/bash
set -e

echo "🔧 Fixing Git repository for successful push..."
echo "Problem: Large GeoNames files are blocking GitHub push"
echo ""

# Check current repository size
echo "📊 Current repository size:"
du -sh .git

echo ""
echo "🗑️ Step 1: Remove large files from Git tracking..."

# Remove large files from Git index (ignore if they don't exist)
git rm --cached --ignore-unmatch "data/geonames/alternateNamesV2.txt" || true
git rm --cached --ignore-unmatch "data/geonames/alternateNamesV2.zip" || true
git rm --cached --ignore-unmatch "data/geonames/ES.txt" || true
git rm --cached --ignore-unmatch "data/geonames/ES.zip" || true
git rm --cached --ignore-unmatch "data/geonames/*.zip" || true
git rm --cached --ignore-unmatch "data/geonames/*.txt" || true

echo "✅ Large files removed from Git index"

echo ""
echo "🧹 Step 2: Remove files from working directory..."
rm -f data/geonames/alternateNamesV2.txt
rm -f data/geonames/alternateNamesV2.zip  
rm -f data/geonames/ES.txt
rm -f data/geonames/ES.zip
rm -f data/geonames/*.zip

echo "✅ Large files removed from working directory"

echo ""
echo "📝 Step 3: Stage clean changes..."
git add .gitignore scripts/ replit.md server/ || true

echo ""
echo "💾 Step 4: Create clean commit..."
git commit -m "Fix repository: Remove large GeoNames data files

- Remove 723MB alternateNamesV2.txt and 188MB zip files
- Tourism region system uses on-demand data downloads
- GeoNames data downloaded automatically when needed
- Repository now under GitHub's 100MB file size limit" || echo "Nothing to commit"

echo ""
echo "🧹 Step 5: Aggressive Git cleanup..."
git gc --aggressive --prune=now

echo ""
echo "📊 Repository size after cleanup:"
du -sh .git

echo ""
echo "🚀 Step 6: Attempting push..."
if git push origin main; then
    echo "✅ SUCCESS! Repository pushed successfully"
    echo "🎉 Your tourism region system is now deployed!"
else
    echo "❌ Push failed. Repository may still be too large."
    echo "💡 Try Git LFS or more aggressive history cleanup."
fi

echo ""
echo "📋 Summary:"
echo "- Large data files removed from Git"
echo "- System uses on-demand data downloads"
echo "- Tourism region functionality preserved"
echo "- All code changes maintained"