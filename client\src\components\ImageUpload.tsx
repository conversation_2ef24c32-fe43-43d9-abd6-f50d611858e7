import React, { useState, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Upload, X, GripVertical, Trash2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useTranslations } from '@/lib/translations';

interface ImageUploadProps {
  propertyId?: string;
  images?: string[];
  onImagesChange: (images: string[]) => void;
  maxImages?: number;
  className?: string;
}

interface UploadResponse {
  success: boolean;
  imageUrl?: string;
  message: string;
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  propertyId,
  images = [],
  onImagesChange,
  maxImages = 10,
  className = ''
}) => {
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const t = useTranslations('imageUpload');

  // Upload mutation
  const uploadMutation = useMutation({
    mutationFn: async (file: File): Promise<UploadResponse> => {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = async () => {
          try {
            const response = await fetch(`/api/images/properties/${propertyId}/images`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                file: reader.result as string,
                fileName: file.name,
              }),
            });

            if (!response.ok) {
              throw new Error('Upload failed');
            }

            const data = await response.json();
            resolve(data);
          } catch (error) {
            reject(error);
          }
        };
        reader.onerror = reject;
        reader.readAsDataURL(file);
      });
    },
    onSuccess: (data) => {
      if (data.success && data.imageUrl) {
        onImagesChange([...images, data.imageUrl]);
        toast({
          title: t('uploadSuccess'),
          description: t('uploadSuccessDescription'),
        });
      }
    },
    onError: (error) => {
      console.error('Upload error:', error);
      toast({
        title: t('uploadFailed'),
        description: t('uploadFailedDescription'),
        variant: "destructive",
      });
    },
  });

  // Delete mutation
  const deleteMutation = useMutation({
    mutationFn: async (imageUrl: string) => {
      const response = await fetch(`/api/images/properties/${propertyId}/images`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ imageUrl }),
      });

      if (!response.ok) {
        throw new Error('Delete failed');
      }

      return response.json();
    },
    onSuccess: (_, imageUrl) => {
      onImagesChange(images.filter(img => img !== imageUrl));
      toast({
        title: t('imageDeleted'),
        description: t('imageDeletedDescription'),
      });
    },
    onError: (error) => {
      console.error('Delete error:', error);
      toast({
        title: t('deleteFailed'),
        description: t('deleteFailedDescription'),
        variant: "destructive",
      });
    },
  });

  // Reorder mutation
  const reorderMutation = useMutation({
    mutationFn: async (newOrder: string[]) => {
      const response = await fetch(`/api/images/properties/${propertyId}/images/reorder`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ imageUrls: newOrder }),
      });

      if (!response.ok) {
        throw new Error('Reorder failed');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['property', propertyId] });
    },
    onError: (error) => {
      console.error('Reorder error:', error);
      toast({
        title: t('reorderFailed'),
        description: t('reorderFailedDescription'),
        variant: "destructive",
      });
    },
  });

  const handleFileSelect = useCallback((files: FileList | null) => {
    if (!files || files.length === 0) return;

    const file = files[0];
    
    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast({
        title: t('invalidFileType'),
        description: t('invalidFileTypeDescription'),
        variant: "destructive",
      });
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast({
        title: t('fileTooLarge'),
        description: t('fileTooLargeDescription'),
        variant: "destructive",
      });
      return;
    }

    // Check max images limit
    if (images.length >= maxImages) {
      toast({
        title: t('maxImagesReached'),
        description: t('maxImagesReachedDescription', { maxImages: maxImages.toString() }),
        variant: "destructive",
      });
      return;
    }

    uploadMutation.mutate(file);
  }, [images.length, maxImages, uploadMutation, toast]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback(() => {
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    handleFileSelect(e.dataTransfer.files);
  }, [handleFileSelect]);

  const handleImageDragStart = useCallback((e: React.DragEvent, index: number) => {
    setDraggedIndex(index);
    e.dataTransfer.effectAllowed = 'move';
  }, []);

  const handleImageDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  }, []);

  const handleImageDrop = useCallback((e: React.DragEvent, dropIndex: number) => {
    e.preventDefault();
    
    if (draggedIndex === null || draggedIndex === dropIndex) {
      setDraggedIndex(null);
      return;
    }

    const newImages = [...images];
    const draggedImage = newImages[draggedIndex];
    newImages.splice(draggedIndex, 1);
    newImages.splice(dropIndex, 0, draggedImage);
    
    onImagesChange(newImages);
    reorderMutation.mutate(newImages);
    setDraggedIndex(null);
  }, [draggedIndex, images, onImagesChange, reorderMutation]);

  const handleDeleteImage = useCallback((imageUrl: string) => {
    deleteMutation.mutate(imageUrl);
  }, [deleteMutation]);

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Upload Area */}
      <Card
        className={`border-2 border-dashed transition-colors ${
          isDragging ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <CardContent className="p-8 text-center">
          <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-semibold mb-2">Upload Property Images</h3>
          <p className="text-gray-600 mb-4">
            Drag and drop images here, or click to select files
          </p>
          <Button
            onClick={() => fileInputRef.current?.click()}
            disabled={uploadMutation.isPending || images.length >= maxImages}
            className="mb-2"
          >
            {uploadMutation.isPending ? 'Uploading...' : 'Select Images'}
          </Button>
          <p className="text-sm text-gray-500">
            {images.length} / {maxImages} images uploaded
          </p>
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={(e) => handleFileSelect(e.target.files)}
            className="hidden"
          />
        </CardContent>
      </Card>

      {/* Image Gallery */}
      {images.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Property Images</h3>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {images.map((image, index) => (
              <Card
                key={image}
                className="relative group cursor-move"
                draggable
                onDragStart={(e) => handleImageDragStart(e, index)}
                onDragOver={handleImageDragOver}
                onDrop={(e) => handleImageDrop(e, index)}
              >
                <CardContent className="p-2">
                  <div className="relative aspect-square overflow-hidden rounded-lg">
                    <img
                      src={image}
                      alt={`Property image ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                    
                    {/* Drag Handle */}
                    <div className="absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity">
                      <GripVertical className="h-4 w-4 text-white bg-black bg-opacity-50 rounded p-0.5" />
                    </div>
                    
                    {/* Delete Button */}
                    <button
                      type="button"
                      onClick={() => handleDeleteImage(image)}
                      disabled={deleteMutation.isPending}
                      className="absolute top-2 right-2 p-1 bg-red-500 hover:bg-red-600 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                    
                    {/* Main Image Badge */}
                    {index === 0 && (
                      <div className="absolute bottom-2 left-2 bg-blue-500 text-white text-xs px-2 py-1 rounded">
                        Main Image
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
          
          <p className="text-sm text-gray-600">
            Drag images to reorder them. The first image will be the main image shown on property cards.
          </p>
        </div>
      )}
    </div>
  );
};

export default ImageUpload;