export class Logger {
  private static formatTimestamp(): string {
    return new Date().toLocaleTimeString('en-US', { 
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  }

  static info(message: string, data?: any): void {
    const timestamp = this.formatTimestamp();
    console.log(`[${timestamp}] [INFO] ${message}`);
    if (data) {
      console.log(`[${timestamp}] [DATA]`, JSON.stringify(data, null, 2));
    }
  }

  static warn(message: string, data?: any): void {
    const timestamp = this.formatTimestamp();
    console.warn(`[${timestamp}] [WARN] ${message}`);
    if (data) {
      console.warn(`[${timestamp}] [DATA]`, JSON.stringify(data, null, 2));
    }
  }

  static error(message: string, error?: any): void {
    const timestamp = this.formatTimestamp();
    console.error(`[${timestamp}] [ERROR] ${message}`);
    if (error) {
      console.error(`[${timestamp}] [ERROR_DETAILS]`, error);
    }
  }

  static search(query: string, resultsCount: number): void {
    const timestamp = this.formatTimestamp();
    console.log(`[${timestamp}] [SEARCH] Query: "${query}" | Results: ${resultsCount}`);
  }

  static api(method: string, endpoint: string, statusCode: number, responseTime?: number): void {
    const timestamp = this.formatTimestamp();
    const timeStr = responseTime ? ` | ${responseTime}ms` : '';
    console.log(`[${timestamp}] [API] ${method} ${endpoint} ${statusCode}${timeStr}`);
  }
}