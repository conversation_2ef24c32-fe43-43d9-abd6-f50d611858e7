import { useState } from 'react';
import { <PERSON>evronLeft, ChevronRight, Share, Heart, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import type { PropertyDetails } from '../types';

interface PropertyHeroProps {
  property: PropertyDetails;
}

export function PropertyHero({ property }: PropertyHeroProps) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [showGallery, setShowGallery] = useState(false);
  const [isFavorite, setIsFavorite] = useState(false);

  // Convert Unsplash photo IDs to proper URLs
  const getImageUrl = (photoId: string, width = 800, height = 600) => {
    try {
      if (!photoId || typeof photoId !== 'string') return '';
      if (photoId.startsWith('photo-')) {
        const unsplashId = photoId.replace('photo-', '');
        const imageUrl = `https://picsum.photos/id/${Math.abs(parseInt(unsplashId.slice(-6), 16) % 1000)}/${width}/${height}`;
        return imageUrl;
      }
      return photoId;
    } catch (error) {
      console.error('Error processing image URL:', error);
      return `https://picsum.photos/${width}/${height}`;
    }
  };

  const mainImage = getImageUrl(property.gallery.main, 1200, 800);
  const thumbnailImages = property.gallery.thumbnails.map(img => getImageUrl(img, 400, 300));

  const nextImage = () => {
    const totalImages = [property.gallery.main, ...property.gallery.thumbnails].length;
    setCurrentImageIndex((prev) => (prev + 1) % totalImages);
  };

  const prevImage = () => {
    const totalImages = [property.gallery.main, ...property.gallery.thumbnails].length;
    setCurrentImageIndex((prev) => (prev - 1 + totalImages) % totalImages);
  };

  const allImages = [mainImage, ...thumbnailImages];

  return (
    <>
      {/* Main Hero Section */}
      <div className="relative w-full h-[30vh] min-h-[200px] bg-gray-100 overflow-hidden rounded-xl">
        <img
          src={mainImage}
          alt={property.title}
          className="w-full h-full object-cover"
          onError={(e) => {
            const target = e.target as HTMLImageElement;
            target.src = 'https://picsum.photos/1200/800';
          }}
        />

        {/* Overlay Controls */}
        <div className="absolute top-4 left-4 right-4 flex justify-between items-start">
          {/* Badge */}
          {property.badge && (
            <Badge variant="secondary" className="bg-white/90 text-gray-900 font-medium">
              {property.badge}
            </Badge>
          )}

          {/* Action Buttons */}
          <div className="flex space-x-1 sm:space-x-2">
            <Button
              variant="ghost"
              size="sm"
              className="bg-white/90 hover:bg-white text-gray-900 px-2 sm:px-3"
              onClick={() => {/* Share functionality */}}
            >
              <Share className="h-4 w-4 sm:mr-2" />
              <span className="hidden sm:inline">Share</span>
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="bg-white/90 hover:bg-white text-gray-900 px-2 sm:px-3"
              onClick={() => setIsFavorite(!isFavorite)}
            >
              <Heart className={`h-4 w-4 sm:mr-2 ${isFavorite ? 'fill-red-500 text-red-500' : ''}`} />
              <span className="hidden sm:inline">Save</span>
            </Button>
          </div>
        </div>

        {/* Thumbnail Grid Overlay - Hidden on mobile */}
        <div className="absolute bottom-4 right-4">
          <div className="hidden md:grid grid-cols-2 gap-1">
            {thumbnailImages.slice(0, 4).map((img, index) => (
              <div
                key={index}
                className="w-16 h-12 bg-gray-200 rounded-sm overflow-hidden cursor-pointer hover:opacity-80 transition-opacity"
                onClick={() => setShowGallery(true)}
              >
                <img
                  src={img}
                  alt={`Thumbnail ${index + 1}`}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = 'https://picsum.photos/400/300';
                  }}
                />
              </div>
            ))}
          </div>
          {/* Show all photos button - always visible */}
          <Button
            variant="outline"
            size="sm"
            className="bg-white/90 hover:bg-white text-gray-900 md:mt-2"
            onClick={() => setShowGallery(true)}
          >
            Show all photos
          </Button>
        </div>
      </div>

      {/* Full Screen Gallery Modal */}
      {showGallery && (
        <div className="fixed inset-0 bg-black z-50 flex items-center justify-center">
          {/* Close Button */}
          <Button
            variant="ghost"
            size="sm"
            className="absolute top-4 right-4 z-10 text-white hover:bg-white/20"
            onClick={() => setShowGallery(false)}
          >
            <X className="h-6 w-6" />
          </Button>

          {/* Navigation Arrows */}
          <Button
            variant="ghost"
            size="sm"
            className="absolute left-4 top-1/2 -translate-y-1/2 z-10 text-white hover:bg-white/20"
            onClick={prevImage}
          >
            <ChevronLeft className="h-8 w-8" />
          </Button>

          <Button
            variant="ghost"
            size="sm"
            className="absolute right-4 top-1/2 -translate-y-1/2 z-10 text-white hover:bg-white/20"
            onClick={nextImage}
          >
            <ChevronRight className="h-8 w-8" />
          </Button>

          {/* Current Image */}
          <div className="w-full h-full flex items-center justify-center p-8">
            <img
              src={allImages[currentImageIndex]}
              alt={`${property.title} - Image ${currentImageIndex + 1}`}
              className="max-w-full max-h-full object-contain"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.src = 'https://picsum.photos/1200/800';
              }}
            />
          </div>

          {/* Image Counter */}
          <div className="absolute bottom-4 left-1/2 -translate-x-1/2 bg-black/50 text-white px-3 py-1 rounded-full text-sm">
            {currentImageIndex + 1} / {allImages.length}
          </div>

          {/* Thumbnail Strip */}
          <div className="absolute bottom-16 left-1/2 -translate-x-1/2 flex space-x-2 max-w-md overflow-x-auto">
            {allImages.map((img, index) => (
              <div
                key={index}
                className={`flex-shrink-0 w-16 h-12 bg-gray-200 rounded-sm overflow-hidden cursor-pointer transition-opacity ${
                  index === currentImageIndex ? 'ring-2 ring-white' : 'opacity-60 hover:opacity-80'
                }`}
                onClick={() => setCurrentImageIndex(index)}
              >
                <img
                  src={img}
                  alt={`Thumbnail ${index + 1}`}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = 'https://picsum.photos/400/300';
                  }}
                />
              </div>
            ))}
          </div>
        </div>
      )}
    </>
  );
}