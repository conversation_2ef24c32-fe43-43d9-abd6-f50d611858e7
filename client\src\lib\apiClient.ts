// src/lib/apiClient.ts

// Property type for exploration cards
export type Property = {
  id: string;
  title: string;
  hostType: string;
  price: string;
  rating: number;
  reviewCount: number;
  images: string[];
  badge?: string;
  badges?: string[];
  location?: {
    city: string;
    region: string;
    country: string;
  };
  maxGuests?: number;
  bedrooms?: number;
  bathrooms?: number;
};

export type PropertyDetails = {
  id: string;
  title: string;
  subtitle: string;
  hostType: string;
  price: string;
  pricePerNight: number;
  rating: number;
  reviewCount: number;
  images: string[];
  gallery: {
    main: string;
    thumbnails: string[];
  };
  badge?: string;
  location: {
    city: string;
    region: string;
    country: string;
    address: string;
    coordinates?: {
      lat: number;
      lng: number;
    };
  };
  maxGuests: number;
  bedrooms: number;
  bathrooms: number;
  amenities: {
    wifi: boolean;
    parking: boolean;
    pool: boolean;
    kitchen: boolean;
    airConditioning: boolean;
    heating: boolean;
    tv: boolean;
    washer: boolean;
    balcony: boolean;
    garden: boolean;
  };
  description: string;
  houseRules: string[];
  host: {
    id: string;
    name: string;
    avatar: string;
    joinedDate: string;
    isVerified: boolean;
    isSuperhost: boolean;
    responseRate: number;
    responseTime: string;
    bio?: string;
  };
  reviews: {
    id: string;
    guestName: string;
    guestAvatar: string;
    rating: number;
    comment: string;
    date: string;
  }[];
  availability: {
    [date: string]: {
      available: boolean;
      price?: number;
      minStay?: number;
    };
  };
  bookingRules: {
    checkIn: string;
    checkOut: string;
    cancellationPolicy: string;
    minimumStay: number;
    instantBook: boolean;
  };
  trustIndicators: {
    freeBookingChanges: boolean;
    directCommunication: boolean;
    trustedReviews: boolean;
    securePayment: boolean;
  };
};

export type InspirationItem = {
  image: string;
  alt: string;
  title: string;
  desc: string;
};

export type InspirationData = {
  header: string;
  items: InspirationItem[];
};

export type Location = {
  id: string;
  name: string;
  type: 'country' | 'city' | 'village' | 'beach' | 'region';
  country: string;
  region?: string;
  searchTerms: string[];
};

export type SuggestionCategory = {
  titleKey: string;
  icon: string;
  locations: Location[];
};

// API client functions that fetch from server
export async function getPopularInSpain(): Promise<Property[]> {
  const response = await fetch('/api/public/content/popular-spain');
  if (!response.ok) {
    throw new Error('Failed to fetch popular properties in Spain');
  }
  return response.json();
}

export async function getNewInFrance(): Promise<Property[]> {
  const response = await fetch('/api/public/content/new-france');
  if (!response.ok) {
    throw new Error('Failed to fetch new properties in France');
  }
  return response.json();
}

export async function getGuestFavorites(): Promise<Property[]> {
  const response = await fetch('/api/public/content/guest-favorites');
  if (!response.ok) {
    throw new Error('Failed to fetch guest favorite properties');
  }
  return response.json();
}

export async function getInspirations(): Promise<InspirationData> {
  const response = await fetch('/api/public/content/inspirations');
  if (!response.ok) {
    throw new Error('Failed to fetch inspiration data');
  }
  return response.json();
}

export async function searchLocations(query: string, locale?: string): Promise<Location[]> {
  if (!query.trim()) return [];
  
  // Detect browser language if not provided
  const searchLocale = locale || navigator.language.split('-')[0] || 'en';
  
  // Enable fuzzy search for better user experience with typos and incomplete queries
  const response = await fetch(`/api/public/locations/autocomplete?q=${encodeURIComponent(query)}&locale=${searchLocale}&fuzzy=true&fuzzyThreshold=0.3&limit=10`);
  if (!response.ok) {
    throw new Error('Failed to search locations');
  }
  const result = await response.json();
  return result.data || [];
}

export async function getPopularSuggestions(): Promise<SuggestionCategory[]> {
  const response = await fetch('/api/public/locations/suggestions');
  if (!response.ok) {
    throw new Error('Failed to fetch popular suggestions');
  }
  const result = await response.json();
  
  // Transform the API response to match the expected SuggestionCategory format
  if (result.data) {
    return [
      {
        titleKey: 'popularDestinations',
        icon: 'TrendingUp',
        locations: result.data
      }
    ];
  }
  
  return [];
}

export async function getRandomSuggestion(): Promise<Location> {
  const response = await fetch('/api/public/locations/random');
  if (!response.ok) {
    throw new Error('Failed to fetch random suggestion');
  }
  return response.json();
}

export async function getAllLocations(): Promise<Location[]> {
  const response = await fetch('/api/public/locations');
  if (!response.ok) {
    throw new Error('Failed to fetch all locations');
  }
  return response.json();
}

export async function getPropertyDetails(id: string): Promise<PropertyDetails> {
  const response = await fetch(`/api/public/properties/${id}`);
  if (!response.ok) {
    throw new Error('Failed to fetch property details');
  }
  return response.json();
}
