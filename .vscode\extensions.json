{"recommendations": ["ms-vscode.vscode-typescript-next", "bradlc.vscode-tailwindcss", "esbenp.prettier-vscode", "dbaeumer.vscode-eslint", "ms-vscode.vscode-json", "formulahendry.auto-rename-tag", "christian-kohler.path-intellisense", "ms-vscode.vscode-css-codelens", "ms-vscode.hexeditor", "yzhang.markdown-all-in-one", "ms-vscode.live-server", "ms-vscode.vscode-node-azure-pack", "github.copilot", "github.copilot-chat", "ms-vscode.vscode-docker", "ms-vscode.remote-containers", "mikestead.dotenv", "humao.rest-client", "rangav.vscode-thunder-client", "wix.vscode-import-cost", "ms-vscode.vscode-npm-script", "ms-vscode.vscode-react-native", "ms-vscode.vscode-yaml", "redhat.vscode-yaml", "ms-vscode.vscode-text-color", "shd101wyy.markdown-preview-enhanced", "ms-vscode.vscode-intellicode", "ms-vscode.vscode-remote-extensionpack", "ms-vsliveshare.vsliveshare", "ms-vscode.vscode-text-table", "alefragnani.bookmarks", "gruntfuggly.todo-tree", "streetsidesoftware.code-spell-checker", "ms-vscode.vscode-icons", "pkief.material-icon-theme"], "unwantedRecommendations": ["ms-vscode.vscode-typescript", "ms-vscode.vscode-json-languageserver"]}