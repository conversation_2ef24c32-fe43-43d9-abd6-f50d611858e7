import { Star, Shield, Clock } from 'lucide-react';
import { useTranslations } from '@/lib/translations';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import type { PropertyDetails } from '../types';

interface HostProfileProps {
  property: PropertyDetails;
}

export function HostProfile({ property }: HostProfileProps) {
  const t = useTranslations('propertyDetails');
  const { host } = property;

  return (
    <Card>
      <CardContent className="p-4 lg:p-6">
        <div className="flex flex-col sm:flex-row items-start space-y-4 sm:space-y-0 sm:space-x-4">
          {/* Host Avatar */}
          <div className="relative flex-shrink-0">
            <img
              src={host.avatar}
              alt={host.name}
              className="w-16 h-16 rounded-full object-cover"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.src = 'https://picsum.photos/100/100';
              }}
            />
            {host.isVerified && (
              <div className="absolute -bottom-1 -right-1 bg-white rounded-full p-1">
                <Shield className="h-4 w-4 text-blue-600" />
              </div>
            )}
          </div>

          {/* Host Info */}
          <div className="flex-1 min-w-0 w-full">
            <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-2 mb-1">
              <h3 className="text-lg font-semibold truncate">{t('hostedBy')} {host.name}</h3>
              {host.isSuperhost && (
                <Badge variant="outline" className="text-xs self-start">
                  <Star className="h-3 w-3 mr-1 fill-current" />
                  {t('superhost')}
                </Badge>
              )}
            </div>
            
            <p className="text-gray-600 text-sm mb-3">
              {t('memberSince')} {host.joinedDate}
            </p>

            {/* Host Stats */}
            <div className="flex flex-col sm:flex-row sm:items-center space-y-1 sm:space-y-0 sm:space-x-4 text-sm text-gray-600 mb-4">
              <div className="flex items-center space-x-1">
                <Clock className="h-4 w-4 flex-shrink-0" />
                <span className="truncate">{t('responseTime')}: {host.responseTime}</span>
              </div>
              <span className="hidden sm:inline">·</span>
              <span className="truncate">{t('responseRate')}: {host.responseRate}%</span>
            </div>

            {/* Host Bio */}
            {host.bio && (
              <p className="text-gray-700 text-sm mb-4 leading-relaxed">
                {host.bio}
              </p>
            )}

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
              <Button variant="outline" size="sm" className="w-full sm:w-auto">
                {t('contactHost')}
              </Button>
              <Button variant="ghost" size="sm" className="text-gray-600 w-full sm:w-auto">
                {t('viewProfile')}
              </Button>
            </div>
          </div>
        </div>

        {/* Host Verification Badges */}
        {host.isVerified && (
          <div className="mt-6 pt-6 border-t">
            <h4 className="font-medium mb-3">{t('hostVerifications')}</h4>
            <div className="flex flex-wrap gap-2">
              <div className="flex items-center space-x-2 text-sm">
                <Shield className="h-4 w-4 text-blue-600" />
                <span>{t('identityVerified')}</span>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}