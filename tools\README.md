# Development Tools

This directory contains development automation scripts for VillaWise MVP project management.

## Structure

```
tools/
├── github/                 # GitHub automation
│   ├── create-github-board.ps1    # PowerShell script for GitHub board creation
│   └── create-issues-batch.json   # Complete host-centric MVP issues (18 issues)
├── check-build-status.js   # GitHub Actions build checker
└── README.md              # This file
```

## GitHub MVP Board Tool

**Single PowerShell script** that loads all issues from JSON for creating host-centric project board focused on Spanish villa owner onboarding.

### Host-Centric MVP Strategy
The GitHub board creation tool implements our comprehensive MVP strategy with **18 complete issues**:

**Phase 1 (Critical):** Host Onboarding & Property Registration
- Multi-step registration wizard with Spanish compliance
- Property listing wizard with AI assistance
- Photo upload and amenity selection systems
- Host verification and profile creation

**Phase 2 (High):** Property Management & Availability
- Calendar integration and pricing management
- AI-powered optimization and recommendations
- Spanish compliance validation

**Key Features:**
- Spanish market compliance (tourist registration numbers)
- AI-powered property optimization suggestions
- Professional photo upload and management
- Mobile-first responsive design approach
- Complete Spanish language support

### Usage
1. Install GitHub CLI: https://cli.github.com/
2. Run: `gh auth login`
3. **Test setup first**: `./tools/github/test-github-setup.ps1 [RepoOwner] [RepoName]`
4. Execute: `./tools/github/create-github-board.ps1 [RepoOwner] [RepoName]`
5. Example: `./tools/github/create-github-board.ps1 johndoe VillaWise`

### Troubleshooting
If no issues are created but no errors shown:
1. Run the test script first: `test-github-setup.ps1` 
2. Check repository permissions (need push access)
3. Verify GitHub CLI authentication: `gh auth status`
4. Test with organization repository if personal repo fails

### JSON Structure
The `create-issues-batch.json` contains 18 comprehensive issues organized by epics:
- **Host Onboarding:** 5 issues (registration, compliance, verification)
- **Property Registration:** 5 issues (listing wizard, photos, amenities)
- **AI Assistance:** 2 issues (suggestions, optimization)  
- **Calendar Management:** 2 issues (availability, pricing)
- **Infrastructure:** 4 issues (database, APIs, validation)

## Build Status Tool

- `check-build-status.js` - GitHub Actions build status checker for monitoring CI/CD pipeline

## Notes

- All GitHub issues follow user story format with clear acceptance criteria
- Epic labels align with MVP phase priorities
- Success metrics defined for each sprint
- Spanish market requirements integrated throughout

**For database operations:** Use scripts in `/database/` directory instead.