import { useState, useEffect, useCallback } from 'react';
import { useSearch, useLocation } from 'wouter';
import { SearchFilters } from '../types';

const SEARCH_STATE_KEY = 'villawise-search-state';

interface SearchState {
  filters: SearchFilters;
  results: any[];
  totalResults: number;
  lastSearch: number;
}

const defaultSearchState: SearchState = {
  filters: {
    location: '',
    checkIn: '',
    checkOut: '',
    dateFlexibility: null,
    guests: {
      adults: 1,
      children: 0,
      infants: 0,
      pets: 0,
    },
  },
  results: [],
  totalResults: 0,
  lastSearch: 0,
};

export function useSearchState() {
  const searchString = useSearch();
  const [location] = useLocation();
  const [searchState, setSearchState] = useState<SearchState>(defaultSearchState);

  // Load state from localStorage on mount
  useEffect(() => {
    const stored = localStorage.getItem(SEARCH_STATE_KEY);
    if (stored) {
      try {
        const parsed = JSON.parse(stored);
        // Only restore if search was recent (within 1 hour)
        if (Date.now() - parsed.lastSearch < 3600000) {
          setSearchState(parsed);
          return;
        }
      } catch (error) {
        console.error('Failed to parse stored search state:', error);
      }
    }

    // Initialize from URL parameters if no valid stored state
    const searchParams = new URLSearchParams(searchString);
    if (searchParams.get('location')) {
      const flexibilityParam = searchParams.get("dateFlexibility");
      const dateFlexibility = flexibilityParam ? 
        (flexibilityParam === "exact" ? "exact" : parseInt(flexibilityParam)) : 
        null;
      
      setSearchState({
        filters: {
          location: searchParams.get('location') || '',
          checkIn: searchParams.get('checkIn') || '',
          checkOut: searchParams.get('checkOut') || '',
          dateFlexibility,
          guests: {
            adults: parseInt(searchParams.get('adults') || '1'),
            children: parseInt(searchParams.get('children') || '0'),
            infants: parseInt(searchParams.get('infants') || '0'),
            pets: parseInt(searchParams.get('pets') || '0'),
          },
        },
        results: [],
        totalResults: 0,
        lastSearch: Date.now(),
      });
    }
  }, [searchString]); // Add searchString as dependency to prevent double initialization

  // Save state to localStorage whenever it changes
  useEffect(() => {
    if (searchState.lastSearch > 0) {
      localStorage.setItem(SEARCH_STATE_KEY, JSON.stringify(searchState));
    }
  }, [searchState]);

  const updateFilters = useCallback((filters: SearchFilters) => {
    setSearchState(prev => ({
      ...prev,
      filters,
      lastSearch: Date.now(),
    }));
  }, []);

  const updateResults = useCallback((results: any[], totalResults: number) => {
    setSearchState(prev => ({
      ...prev,
      results,
      totalResults,
      lastSearch: Date.now(),
    }));
  }, []);

  const clearSearchState = useCallback(() => {
    setSearchState(defaultSearchState);
    localStorage.removeItem(SEARCH_STATE_KEY);
  }, []);

  const hasActiveSearch = () => {
    return Boolean(searchState.filters && searchState.filters.location && searchState.filters.location.length > 0);
  };

  return {
    searchState,
    updateFilters,
    updateResults,
    clearSearchState,
    hasActiveSearch,
  };
}