-- VillaWise Messaging System Migration
-- Version: v003
-- Description: Complete messaging system with conversations, messages, templates, and analytics
-- Date: 2025-07-26

-- Enable required extensions for messaging features
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Create conversations table
CREATE TABLE IF NOT EXISTS conversations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    guest_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    host_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    property_id UUID REFERENCES properties(id) ON DELETE SET NULL,
    booking_id UUID REFERENCES bookings(id) ON DELETE SET NULL,
    subject TEXT,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'archived', 'closed')),
    last_message_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Performance indexes
    UNIQUE(guest_id, host_id, property_id),
    INDEX idx_conversations_guest_id ON conversations(guest_id),
    INDEX idx_conversations_host_id ON conversations(host_id),
    INDEX idx_conversations_property_id ON conversations(property_id),
    INDEX idx_conversations_status ON conversations(status),
    INDEX idx_conversations_last_message_at ON conversations(last_message_at DESC)
);

-- Create messages table with comprehensive features
CREATE TABLE IF NOT EXISTS messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    conversation_id UUID NOT NULL REFERENCES conversations(id) ON DELETE CASCADE,
    sender_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    sender_type VARCHAR(10) NOT NULL CHECK (sender_type IN ('guest', 'host')),
    content TEXT NOT NULL,
    message_type VARCHAR(20) DEFAULT 'text' CHECK (message_type IN ('text', 'template', 'system', 'automated')),
    message_status VARCHAR(20) DEFAULT 'sent' CHECK (message_status IN ('sent', 'delivered', 'read')),
    template_id UUID REFERENCES message_templates(id) ON DELETE SET NULL,
    reply_to_id UUID REFERENCES messages(id) ON DELETE SET NULL,
    edited_at TIMESTAMP WITH TIME ZONE,
    is_flagged BOOLEAN DEFAULT FALSE,
    flagged_reason TEXT,
    flagged_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Performance indexes
    INDEX idx_messages_conversation_id ON messages(conversation_id),
    INDEX idx_messages_sender_id ON messages(sender_id),
    INDEX idx_messages_created_at ON messages(created_at DESC),
    INDEX idx_messages_status ON messages(message_status),
    INDEX idx_messages_flagged ON messages(is_flagged)
);

-- Create message templates table for hosts
CREATE TABLE IF NOT EXISTS message_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    host_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    category VARCHAR(50) NOT NULL CHECK (category IN ('inquiry', 'booking', 'checkin', 'checkout', 'support', 'custom')),
    dynamic_fields JSONB DEFAULT '{}',
    usage_count INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Performance indexes
    INDEX idx_templates_host_id ON message_templates(host_id),
    INDEX idx_templates_category ON message_templates(category),
    INDEX idx_templates_is_active ON message_templates(is_active),
    INDEX idx_templates_usage_count ON message_templates(usage_count DESC)
);

-- Create host response metrics for analytics
CREATE TABLE IF NOT EXISTS host_response_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    host_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    conversation_id UUID NOT NULL REFERENCES conversations(id) ON DELETE CASCADE,
    message_id UUID NOT NULL REFERENCES messages(id) ON DELETE CASCADE,
    response_time_minutes INTEGER,
    is_first_response BOOLEAN DEFAULT FALSE,
    responded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Performance indexes
    INDEX idx_response_metrics_host_id ON host_response_metrics(host_id),
    INDEX idx_response_metrics_conversation_id ON host_response_metrics(conversation_id),
    INDEX idx_response_metrics_responded_at ON host_response_metrics(responded_at DESC)
);

-- Create message reports table for safety/moderation
CREATE TABLE IF NOT EXISTS message_reports (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    message_id UUID NOT NULL REFERENCES messages(id) ON DELETE CASCADE,
    reporter_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    reason VARCHAR(100) NOT NULL CHECK (reason IN ('inappropriate', 'spam', 'harassment', 'other')),
    description TEXT,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'reviewed', 'resolved', 'dismissed')),
    reviewed_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    reviewed_at TIMESTAMP WITH TIME ZONE,
    resolution_notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Prevent duplicate reports
    UNIQUE(message_id, reporter_id),
    
    -- Performance indexes
    INDEX idx_message_reports_message_id ON message_reports(message_id),
    INDEX idx_message_reports_reporter_id ON message_reports(reporter_id),
    INDEX idx_message_reports_status ON message_reports(status)
);

-- Create conversation participants table for future group messaging
CREATE TABLE IF NOT EXISTS conversation_participants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    conversation_id UUID NOT NULL REFERENCES conversations(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    participant_type VARCHAR(20) NOT NULL CHECK (participant_type IN ('guest', 'host', 'admin')),
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    left_at TIMESTAMP WITH TIME ZONE,
    unread_count INTEGER DEFAULT 0,
    last_read_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Prevent duplicate participants
    UNIQUE(conversation_id, user_id),
    
    -- Performance indexes
    INDEX idx_participants_conversation_id ON conversation_participants(conversation_id),
    INDEX idx_participants_user_id ON conversation_participants(user_id),
    INDEX idx_participants_unread_count ON conversation_participants(unread_count)
);

-- Create triggers for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply timestamp triggers
CREATE TRIGGER update_conversations_updated_at 
    BEFORE UPDATE ON conversations 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_messages_updated_at 
    BEFORE UPDATE ON messages 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_templates_updated_at 
    BEFORE UPDATE ON message_templates 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_reports_updated_at 
    BEFORE UPDATE ON message_reports 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create trigger for updating conversation last_message_at
CREATE OR REPLACE FUNCTION update_conversation_last_message()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE conversations 
    SET last_message_at = NEW.created_at 
    WHERE id = NEW.conversation_id;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_conversation_last_message_trigger 
    AFTER INSERT ON messages 
    FOR EACH ROW EXECUTE FUNCTION update_conversation_last_message();

-- Create trigger for updating template usage count
CREATE OR REPLACE FUNCTION increment_template_usage()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.template_id IS NOT NULL THEN
        UPDATE message_templates 
        SET usage_count = usage_count + 1 
        WHERE id = NEW.template_id;
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER increment_template_usage_trigger 
    AFTER INSERT ON messages 
    FOR EACH ROW EXECUTE FUNCTION increment_template_usage();

-- Create search indexes for message content
CREATE INDEX IF NOT EXISTS idx_messages_content_search 
    ON messages USING gin(to_tsvector('english', content));

CREATE INDEX IF NOT EXISTS idx_templates_search 
    ON message_templates USING gin(to_tsvector('english', name || ' ' || content));

-- Insert default message templates for hosts
INSERT INTO message_templates (host_id, name, content, category, dynamic_fields) 
SELECT 
    u.id as host_id,
    'Welcome Inquiry Response',
    'Hello {guest_name}! Thank you for your interest in {property_name}. I''d be happy to help answer any questions you have about the property or the area. The property is located in {property_location} and offers {property_features}. Please let me know if you''d like to book or need any additional information!',
    'inquiry',
    '{"guest_name": "Guest name", "property_name": "Property title", "property_location": "Property location", "property_features": "Property highlights"}'::jsonb
FROM auth.users u 
WHERE u.user_metadata->>'role' = 'host'
ON CONFLICT DO NOTHING;

INSERT INTO message_templates (host_id, name, content, category, dynamic_fields)
SELECT 
    u.id as host_id,
    'Booking Confirmation',
    'Congratulations {guest_name}! Your booking for {property_name} from {check_in_date} to {check_out_date} has been confirmed. Your booking reference is {booking_reference}. I''ll send you check-in details closer to your arrival date. Looking forward to hosting you!',
    'booking',
    '{"guest_name": "Guest name", "property_name": "Property title", "check_in_date": "Check-in date", "check_out_date": "Check-out date", "booking_reference": "Booking ID"}'::jsonb
FROM auth.users u 
WHERE u.user_metadata->>'role' = 'host'
ON CONFLICT DO NOTHING;

INSERT INTO message_templates (host_id, name, content, category, dynamic_fields)
SELECT 
    u.id as host_id,
    'Check-in Instructions',
    'Hi {guest_name}! Your check-in for {property_name} is tomorrow at {check_in_time}. The property address is {property_address}. Access instructions: {access_instructions}. WiFi: {wifi_details}. House rules: {house_rules}. I''m available if you need anything during your stay!',
    'checkin',
    '{"guest_name": "Guest name", "property_name": "Property title", "check_in_time": "Check-in time", "property_address": "Full address", "access_instructions": "Key/access details", "wifi_details": "WiFi credentials", "house_rules": "Important rules"}'::jsonb
FROM auth.users u 
WHERE u.user_metadata->>'role' = 'host'
ON CONFLICT DO NOTHING;

INSERT INTO message_templates (host_id, name, content, category, dynamic_fields)
SELECT 
    u.id as host_id,
    'Check-out Reminder',
    'Hello {guest_name}! I hope you''ve enjoyed your stay at {property_name}. Just a friendly reminder that check-out is at {check_out_time} today. Please {checkout_instructions}. Thank you for being a wonderful guest! Don''t forget to leave a review about your experience.',
    'checkout',
    '{"guest_name": "Guest name", "property_name": "Property title", "check_out_time": "Check-out time", "checkout_instructions": "Check-out procedures"}'::jsonb
FROM auth.users u 
WHERE u.user_metadata->>'role' = 'host'
ON CONFLICT DO NOTHING;

-- Add Row Level Security (RLS) policies
ALTER TABLE conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE message_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE host_response_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE message_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE conversation_participants ENABLE ROW LEVEL SECURITY;

-- RLS Policies for conversations
CREATE POLICY "Users can view their own conversations" ON conversations
    FOR SELECT USING (auth.uid() = guest_id OR auth.uid() = host_id);

CREATE POLICY "Users can create conversations" ON conversations
    FOR INSERT WITH CHECK (auth.uid() = guest_id OR auth.uid() = host_id);

CREATE POLICY "Users can update their own conversations" ON conversations
    FOR UPDATE USING (auth.uid() = guest_id OR auth.uid() = host_id);

-- RLS Policies for messages
CREATE POLICY "Users can view messages in their conversations" ON messages
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM conversations c 
            WHERE c.id = conversation_id 
            AND (c.guest_id = auth.uid() OR c.host_id = auth.uid())
        )
    );

CREATE POLICY "Users can send messages in their conversations" ON messages
    FOR INSERT WITH CHECK (
        auth.uid() = sender_id AND
        EXISTS (
            SELECT 1 FROM conversations c 
            WHERE c.id = conversation_id 
            AND (c.guest_id = auth.uid() OR c.host_id = auth.uid())
        )
    );

CREATE POLICY "Users can update their own messages" ON messages
    FOR UPDATE USING (auth.uid() = sender_id);

-- RLS Policies for templates
CREATE POLICY "Hosts can manage their own templates" ON message_templates
    FOR ALL USING (auth.uid() = host_id);

-- RLS Policies for response metrics
CREATE POLICY "Hosts can view their own metrics" ON host_response_metrics
    FOR SELECT USING (auth.uid() = host_id);

-- RLS Policies for message reports
CREATE POLICY "Users can view their own reports" ON message_reports
    FOR SELECT USING (auth.uid() = reporter_id);

CREATE POLICY "Users can create reports" ON message_reports
    FOR INSERT WITH CHECK (auth.uid() = reporter_id);

-- RLS Policies for participants
CREATE POLICY "Users can view their own participations" ON conversation_participants
    FOR SELECT USING (auth.uid() = user_id);

-- Create views for dashboard analytics
CREATE OR REPLACE VIEW host_messaging_stats AS
SELECT 
    h.id as host_id,
    COUNT(DISTINCT c.id) as total_conversations,
    COUNT(DISTINCT CASE WHEN c.status = 'active' THEN c.id END) as active_conversations,
    COUNT(DISTINCT m.id) as total_messages_sent,
    ROUND(AVG(hrm.response_time_minutes), 2) as avg_response_time_minutes,
    COUNT(DISTINCT CASE WHEN hrm.is_first_response = TRUE THEN hrm.id END) as first_responses,
    MAX(c.last_message_at) as last_activity_at
FROM auth.users h
LEFT JOIN conversations c ON h.id = c.host_id
LEFT JOIN messages m ON c.id = m.conversation_id AND m.sender_id = h.id
LEFT JOIN host_response_metrics hrm ON h.id = hrm.host_id
WHERE h.user_metadata->>'role' = 'host'
GROUP BY h.id;

-- Create notification helper function
CREATE OR REPLACE FUNCTION get_unread_message_count(user_id UUID)
RETURNS INTEGER AS $$
BEGIN
    RETURN (
        SELECT COUNT(*)
        FROM messages m
        JOIN conversations c ON m.conversation_id = c.id
        WHERE (c.guest_id = user_id OR c.host_id = user_id)
        AND m.sender_id != user_id
        AND m.message_status != 'read'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant appropriate permissions
GRANT SELECT, INSERT, UPDATE ON conversations TO authenticated;
GRANT SELECT, INSERT, UPDATE ON messages TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON message_templates TO authenticated;
GRANT SELECT, INSERT ON host_response_metrics TO authenticated;
GRANT SELECT, INSERT, UPDATE ON message_reports TO authenticated;
GRANT SELECT, INSERT, UPDATE ON conversation_participants TO authenticated;

-- Grant usage on sequences
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- Add helpful comments
COMMENT ON TABLE conversations IS 'Property-linked conversations between guests and hosts';
COMMENT ON TABLE messages IS 'Individual messages within conversations with full edit history';
COMMENT ON TABLE message_templates IS 'Pre-defined message templates for hosts to save time';
COMMENT ON TABLE host_response_metrics IS 'Analytics for host response times and engagement';
COMMENT ON TABLE message_reports IS 'Safety reporting system for inappropriate messages';
COMMENT ON TABLE conversation_participants IS 'Future-ready participant tracking for group messaging';

-- Migration completed successfully
SELECT 'VillaWise Messaging System v003 migration completed successfully' as status;