#!/usr/bin/env node

/**
 * Test script to emulate CI/CD backend build without Supabase credentials
 * This simulates the GitHub Actions environment where Supabase env vars are not available
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🧪 Testing backend CI build without Supabase credentials...');

// Save existing environment variables
const originalSupabaseUrl = process.env.SUPABASE_URL;
const originalSupabaseAnonKey = process.env.SUPABASE_ANON_KEY;

// Clear Supabase environment variables to simulate CI/CD environment
delete process.env.SUPABASE_URL;
delete process.env.SUPABASE_ANON_KEY;

async function runCommand(command, args = [], options = {}) {
  return new Promise((resolve, reject) => {
    const child = spawn(command, args, { 
      stdio: ['pipe', 'pipe', 'pipe'],
      ...options 
    });
    
    let stdout = '';
    let stderr = '';
    
    child.stdout.on('data', (data) => {
      stdout += data.toString();
    });
    
    child.stderr.on('data', (data) => {
      stderr += data.toString();
    });
    
    child.on('close', (code) => {
      if (code === 0) {
        resolve({ stdout, stderr });
      } else {
        reject({ code, stdout, stderr });
      }
    });
  });
}

async function testHealthEndpoint() {
  const maxRetries = 5;
  const retryDelay = 1000;
  
  for (let i = 0; i < maxRetries; i++) {
    try {
      const { stdout } = await runCommand('curl', ['-s', '-o', '/dev/null', '-w', '%{http_code}', 'http://localhost:5000/api/health']);
      const httpStatus = stdout.trim();
      
      if (httpStatus === '200') {
        console.log('✅ Health endpoint responding successfully');
        
        // Get health response
        const { stdout: healthResponse } = await runCommand('curl', ['-s', 'http://localhost:5000/api/health']);
        console.log('Health response:', healthResponse.trim());
        return true;
      }
    } catch (error) {
      // Retry on error
    }
    
    if (i < maxRetries - 1) {
      console.log(`Retrying health check in ${retryDelay}ms... (${i + 1}/${maxRetries})`);
      await new Promise(resolve => setTimeout(resolve, retryDelay));
    }
  }
  
  console.log('❌ Health endpoint failed after max retries');
  return false;
}

async function main() {
  let appProcess;
  
  try {
    // Check if build directory exists
    console.log('📦 Checking build...');
    if (!fs.existsSync('dist')) {
      console.log('Building application...');
      try {
        await runCommand('npm', ['run', 'build']);
        console.log('✅ Build successful');
      } catch (error) {
        console.log('❌ Build failed:', error.stderr);
        process.exit(1);
      }
    } else {
      console.log('✅ Build directory exists');
    }
    
    // Start the application in background
    console.log('🚀 Starting application...');
    appProcess = spawn('node', ['dist/index.js'], { 
      env: { ...process.env, NODE_ENV: 'production' },
      stdio: ['pipe', 'pipe', 'pipe'],
      detached: false
    });
    
    let appOutput = '';
    appProcess.stdout.on('data', (data) => {
      appOutput += data.toString();
      // Log server start message
      if (data.toString().includes('serving on port')) {
        console.log('✅ Application started successfully');
      }
    });
    
    appProcess.stderr.on('data', (data) => {
      const errorText = data.toString();
      // Only show warnings, not the normal Supabase fallback message
      if (errorText.includes('warn') && !errorText.includes('Using in-memory storage')) {
        console.log('⚠️  Warning:', errorText.trim());
      }
    });
    
    // Wait for application to start
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Test health endpoint
    console.log('🩺 Testing health endpoint...');
    const healthCheckPassed = await testHealthEndpoint();
    
    if (healthCheckPassed) {
      console.log('✅ Backend CI test completed successfully');
      process.exit(0);
    } else {
      console.log('❌ Backend CI test failed');
      process.exit(1);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  } finally {
    // Clean up
    if (appProcess && !appProcess.killed) {
      appProcess.kill();
    }
    
    // Restore original environment variables
    if (originalSupabaseUrl) {
      process.env.SUPABASE_URL = originalSupabaseUrl;
    }
    if (originalSupabaseAnonKey) {
      process.env.SUPABASE_ANON_KEY = originalSupabaseAnonKey;
    }
  }
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n🛑 Test interrupted');
  process.exit(1);
});

main().catch(error => {
  console.error('💥 Unexpected error:', error);
  process.exit(1);
});