# Frontend-Backend Communication Architecture

## Overview

This document details how VillaWise frontend pages communicate with the backend, including specific page flows, authorization patterns, caching strategies, and data retrieval mechanisms.

## Page-Specific Communication Flows

### 1. Search Page Communication Flow

```mermaid
sequenceDiagram
    participant User as User
    participant SearchPage as Search Page
    participant SearchHook as usePropertySearch
    participant QueryClient as TanStack Query
    participant API as /api/properties/search
    participant DAL as Properties Entity
    participant Cache as Memory Cache
    participant DB as Supabase DB
    
    User->>SearchPage: Enter search criteria
    SearchPage->>SearchHook: usePropertySearch(filters)
    SearchHook->>QueryClient: useQuery(['search', filters])
    
    QueryClient->>QueryClient: Check React Query cache
    alt Query Cache Hit (5min)
        QueryClient-->>SearchHook: Return cached data
        SearchHook-->>SearchPage: Properties data
        SearchPage-->>User: Display results
    else Query Cache Miss
        QueryClient->>API: GET /api/properties/search?filters=...
        API->>DAL: getProperties(filters)
        
        DAL->>Cache: Check memory cache
        alt Memory Cache Hit (5min)
            Cache-->>DAL: Cached PropertyDTO[]
        else Memory Cache Miss
            DAL->>DB: Execute property query
            DB-->>DAL: Raw property data
            DAL->>DAL: Transform to PropertyDTO
            DAL->>Cache: Store (TTL: 5min)
        end
        
        DAL-->>API: PropertyDTO[]
        API-->>QueryClient: JSON response
        QueryClient-->>SearchHook: Properties data
        SearchHook-->>SearchPage: Properties data
        SearchPage-->>User: Display results
    end
```

#### Search Page Implementation

```typescript
// client/src/features/search/SearchPage.tsx
export function SearchPage() {
  const [filters, setFilters] = useState<PropertySearchFilters>()
  
  // TanStack Query hook with automatic caching
  const { data: searchResult, isLoading, error } = usePropertySearch(filters)
  
  return (
    <div>
      <SearchForm onSearch={setFilters} />
      {isLoading && <SearchSkeleton />}
      {error && <ErrorDisplay error={error} />}
      {searchResult && (
        <>
          <PropertyGrid properties={searchResult.properties} />
          <MapView properties={searchResult.properties} />
        </>
      )}
    </div>
  )
}

// client/src/features/search/hooks/usePropertySearch.ts
export function usePropertySearch(filters?: PropertySearchFilters) {
  return useQuery({
    queryKey: ['/api/properties/search', filters],
    enabled: !!filters?.location,
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
    gcTime: 10 * 60 * 1000,   // Keep in memory for 10 minutes
  })
}
```

### 2. Host Dashboard Communication Flow

```mermaid
sequenceDiagram
    participant User as Host User
    participant Dashboard as Host Dashboard
    participant AuthHook as useAuth
    participant DashHook as useDashboardData
    participant QueryClient as TanStack Query
    participant AuthAPI as /api/auth/me
    participant DashAPI as /api/dashboard/host
    participant AuthDAL as Session Auth
    participant AggDAL as Dashboard Aggregator
    participant Cache as Memory Cache
    participant DB as Supabase DB
    
    User->>Dashboard: Navigate to dashboard
    Dashboard->>AuthHook: Check authentication
    AuthHook->>QueryClient: useQuery(['/api/auth/me'])
    
    QueryClient->>AuthAPI: GET /api/auth/me
    Note over QueryClient,AuthAPI: Headers: Authorization: Bearer {token}
    AuthAPI->>AuthDAL: getCurrentUser(authHeader)
    AuthDAL->>Cache: Check session cache
    
    alt Session Valid
        Cache-->>AuthDAL: UserSession
        AuthDAL-->>AuthAPI: UserSession
        AuthAPI-->>QueryClient: User data
        QueryClient-->>AuthHook: User data
        AuthHook-->>Dashboard: Authenticated user
        
        Dashboard->>DashHook: useDashboardData()
        DashHook->>QueryClient: useQuery(['/api/dashboard/host'])
        QueryClient->>DashAPI: GET /api/dashboard/host
        Note over QueryClient,DashAPI: Headers: Authorization: Bearer {token}
        
        DashAPI->>AggDAL: getHostDashboardData(authHeader)
        AggDAL->>AggDAL: Parallel data fetching
        
        par Profile Data
            AggDAL->>DB: Get user profile
        and Properties Data
            AggDAL->>DB: Get host properties
        and Bookings Data
            AggDAL->>DB: Get recent bookings
        and Messages Data
            AggDAL->>DB: Get unread messages
        and Stats Data
            AggDAL->>DB: Get booking stats
        end
        
        AggDAL->>Cache: Cache aggregated data
        AggDAL-->>DashAPI: Dashboard data
        DashAPI-->>QueryClient: Dashboard response
        QueryClient-->>DashHook: Dashboard data
        DashHook-->>Dashboard: Render dashboard
        Dashboard-->>User: Display dashboard
        
    else Session Invalid
        AuthDAL-->>AuthAPI: null
        AuthAPI-->>QueryClient: 401 Unauthorized
        QueryClient-->>AuthHook: Authentication error
        AuthHook-->>Dashboard: Redirect to login
    end
```

#### Host Dashboard Implementation

```typescript
// client/src/features/host/dashboard/HostDashboard.tsx
export function HostDashboard() {
  const { user, isLoading: authLoading } = useAuth()
  const { data: dashboardData, isLoading: dashLoading } = useHostDashboardData()
  
  if (authLoading || dashLoading) return <DashboardSkeleton />
  if (!user) return <Navigate to="/login" />
  
  return (
    <MainLayout>
      <DashboardOverview data={dashboardData?.overview} />
      <RecentBookings bookings={dashboardData?.recentBookings} />
      <PropertyStats properties={dashboardData?.properties} />
      <MessageNotifications messages={dashboardData?.unreadMessages} />
    </MainLayout>
  )
}

// client/src/features/host/dashboard/hooks/useHostDashboardData.ts
export function useHostDashboardData() {
  return useQuery({
    queryKey: ['/api/dashboard/host'],
    staleTime: 2 * 60 * 1000, // 2 minutes for dashboard data
    refetchInterval: 5 * 60 * 1000, // Auto-refresh every 5 minutes
  })
}
```

### 3. Property Management Flow

```mermaid
sequenceDiagram
    participant User as Host
    participant PropertyMgmt as Property Management
    participant CreateHook as useCreateProperty
    participant UpdateHook as useUpdateProperty
    participant QueryClient as TanStack Query
    participant API as /api/properties
    participant DAL as Properties Entity
    participant AuthDAL as Session Auth
    participant Cache as Memory Cache
    participant DB as Supabase DB
    
    User->>PropertyMgmt: Create new property
    PropertyMgmt->>CreateHook: mutate(propertyData)
    CreateHook->>QueryClient: useMutation()
    
    QueryClient->>API: POST /api/properties
    Note over QueryClient,API: Headers: Authorization: Bearer {token}<br/>Body: PropertyData
    
    API->>AuthDAL: requireRole('host', authHeader)
    AuthDAL->>Cache: Verify session + role
    
    alt Authorized Host
        AuthDAL-->>API: UserSession (role: host)
        API->>DAL: createProperty(data, authHeader)
        DAL->>DB: Insert property
        DB-->>DAL: Created property
        DAL->>DAL: Transform to PropertyDTO
        DAL->>Cache: Invalidate property caches
        DAL-->>API: PropertyDTO
        API-->>QueryClient: Success response
        
        QueryClient->>QueryClient: onSuccess callback
        QueryClient->>QueryClient: Invalidate related queries
        Note over QueryClient: Invalidates:<br/>- /api/properties<br/>- /api/dashboard/host<br/>- /api/properties/host
        
        QueryClient-->>CreateHook: Success
        CreateHook-->>PropertyMgmt: Show success message
        PropertyMgmt->>PropertyMgmt: Refresh property list
        PropertyMgmt-->>User: Updated UI
        
    else Unauthorized
        AuthDAL-->>API: 403 Forbidden
        API-->>QueryClient: Error response
        QueryClient-->>CreateHook: Error
        CreateHook-->>PropertyMgmt: Show error
        PropertyMgmt-->>User: Error message
    end
```

#### Property Management Implementation

```typescript
// client/src/features/host/properties/PropertyManagement.tsx
export function PropertyManagement() {
  const createProperty = useCreateProperty()
  const updateProperty = useUpdateProperty()
  const deleteProperty = useDeleteProperty()
  
  const handleCreate = (data: PropertyFormData) => {
    createProperty.mutate(data, {
      onSuccess: () => {
        toast.success('Property created successfully')
        setShowCreateForm(false)
      },
      onError: (error) => {
        toast.error(`Failed to create property: ${error.message}`)
      }
    })
  }
  
  return (
    <div>
      <PropertyCreateForm 
        onSubmit={handleCreate}
        isLoading={createProperty.isPending}
      />
      <PropertyList 
        onUpdate={updateProperty.mutate}
        onDelete={deleteProperty.mutate}
      />
    </div>
  )
}

// client/src/features/host/properties/hooks/usePropertyMutations.ts
export function useCreateProperty() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (data: PropertyFormData) =>
      apiRequest('/api/properties', {
        method: 'POST',
        body: JSON.stringify(data)
      }),
    onSuccess: () => {
      // Invalidate and refetch related queries
      queryClient.invalidateQueries({ queryKey: ['/api/properties'] })
      queryClient.invalidateQueries({ queryKey: ['/api/dashboard/host'] })
      queryClient.invalidateQueries({ queryKey: ['/api/properties/host'] })
    }
  })
}
```

### 4. Guest Booking Flow

```mermaid
sequenceDiagram
    participant User as Guest
    participant PropertyPage as Property Detail
    participant BookingHook as useCreateBooking
    participant QueryClient as TanStack Query
    participant BookingAPI as /api/bookings
    participant BookingDAL as Bookings Entity
    participant AuthDAL as Session Auth
    participant Cache as Memory Cache
    participant DB as Supabase DB
    
    User->>PropertyPage: Select dates & book
    PropertyPage->>BookingHook: mutate(bookingData)
    BookingHook->>QueryClient: useMutation()
    
    QueryClient->>BookingAPI: POST /api/bookings
    Note over QueryClient,BookingAPI: Headers: Authorization: Bearer {token}<br/>Body: BookingData
    
    BookingAPI->>AuthDAL: requireAuth(authHeader)
    AuthDAL->>Cache: Verify user session
    
    alt Authenticated User
        AuthDAL-->>BookingAPI: UserSession
        BookingAPI->>BookingDAL: createBooking(data, authHeader)
        
        BookingDAL->>DB: Check availability
        DB-->>BookingDAL: Availability status
        
        alt Available
            BookingDAL->>DB: Create booking
            BookingDAL->>DB: Update property availability
            DB-->>BookingDAL: Created booking
            BookingDAL->>Cache: Invalidate booking caches
            BookingDAL-->>BookingAPI: BookingDTO
            BookingAPI-->>QueryClient: Success response
            
            QueryClient->>QueryClient: Invalidate queries
            Note over QueryClient: Invalidates:<br/>- /api/bookings<br/>- /api/dashboard/guest<br/>- /api/properties/{id}/availability
            
            QueryClient-->>BookingHook: Success
            BookingHook-->>PropertyPage: Booking confirmed
            PropertyPage-->>User: Confirmation page
            
        else Not Available
            BookingDAL-->>BookingAPI: 409 Conflict
            BookingAPI-->>QueryClient: Availability error
            QueryClient-->>BookingHook: Error
            BookingHook-->>PropertyPage: Show availability error
            PropertyPage-->>User: Update calendar
        end
        
    else Unauthorized
        AuthDAL-->>BookingAPI: 401 Unauthorized
        BookingAPI-->>QueryClient: Auth error
        QueryClient-->>BookingHook: Error
        BookingHook-->>PropertyPage: Redirect to login
    end
```

## Authentication Integration

### JWT Token Management

```mermaid
graph TB
    subgraph "Frontend Token Flow"
        A[User Login]
        B[Supabase Auth]
        C[localStorage Token]
        D[Query Client Headers]
        E[API Requests]
    end
    
    subgraph "Backend Token Flow"
        F[Express Middleware]
        G[Session Auth]
        H[Token Verification]
        I[User Session]
        J[Route Handler]
    end
    
    A --> B
    B --> C
    C --> D
    D --> E
    E --> F
    F --> G
    G --> H
    H --> I
    I --> J
    
    style C fill:#e1f5fe
    style I fill:#e8f5e8
```

### Authentication Hook Implementation

```typescript
// client/src/hooks/useAuth.ts
export function useAuth() {
  const queryClient = useQueryClient()
  
  const { data: user, isLoading, error } = useQuery({
    queryKey: ['/api/auth/me'],
    retry: false,
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
  
  const login = useMutation({
    mutationFn: async (credentials: LoginCredentials) => {
      const { user, session } = await supabase.auth.signInWithPassword(credentials)
      if (session?.access_token) {
        localStorage.setItem('sb_access_token', session.access_token)
      }
      return user
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/auth/me'] })
    }
  })
  
  const logout = useMutation({
    mutationFn: async () => {
      await supabase.auth.signOut()
      localStorage.removeItem('sb_access_token')
    },
    onSuccess: () => {
      queryClient.clear() // Clear all cached data
    }
  })
  
  return {
    user: user?.data,
    isLoading,
    isAuthenticated: !!user?.data,
    login: login.mutate,
    logout: logout.mutate,
    isLoginPending: login.isPending,
  }
}
```

## Caching Strategy Across Layers

### Multi-Layer Cache Architecture

```mermaid
graph TB
    subgraph "Frontend Caching (Client-Side)"
        A[TanStack Query Cache]
        B[Browser localStorage]
        C[Component State]
    end
    
    subgraph "API Route Caching (Server-Side)"
        D[Express Route Cache]
        E[Response Compression]
    end
    
    subgraph "DAL Caching (Server-Side)"
        F[Memory Cache]
        G[Session Cache]
        H[Entity Cache]
    end
    
    subgraph "Database Layer"
        I[Supabase Connection Pool]
        J[PostgreSQL Query Cache]
    end
    
    A --> D
    B --> D
    D --> F
    E --> F
    F --> I
    G --> I
    H --> I
    
    style A fill:#e1f5fe
    style F fill:#e8f5e8
    style I fill:#fff3e0
```

### Cache Invalidation Patterns

```typescript
// Coordinated cache invalidation example
export function useCreateProperty() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: createPropertyAPI,
    onSuccess: (newProperty) => {
      // Optimistic update
      queryClient.setQueryData(
        ['/api/properties/host'], 
        (old: PropertyDTO[]) => [...old, newProperty]
      )
      
      // Invalidate related caches
      queryClient.invalidateQueries({ 
        queryKey: ['/api/properties'] 
      })
      queryClient.invalidateQueries({ 
        queryKey: ['/api/dashboard/host'] 
      })
      
      // Prefetch property details
      queryClient.prefetchQuery({
        queryKey: ['/api/properties', newProperty.id],
        queryFn: () => getPropertyById(newProperty.id)
      })
    }
  })
}
```

## Error Handling Patterns

### Centralized Error Handling

```mermaid
graph TB
    subgraph "Frontend Error Handling"
        A[API Error]
        B[React Query Error]
        C[Error Boundary]
        D[Toast Notification]
        E[Retry Logic]
    end
    
    subgraph "Backend Error Handling"
        F[Route Error Handler]
        G[DAL Error Handler]
        H[Database Error]
        I[Auth Error]
    end
    
    A --> B
    B --> C
    B --> D
    B --> E
    
    F --> A
    G --> F
    H --> G
    I --> G
    
    style C fill:#ffebee
    style F fill:#fff3e0
```

### Error Handling Implementation

```typescript
// client/src/components/ErrorBoundary.tsx
export function QueryErrorBoundary({ children }: { children: React.ReactNode }) {
  return (
    <QueryErrorResetBoundary>
      {({ reset }) => (
        <ErrorBoundary
          onReset={reset}
          fallbackRender={({ error, resetErrorBoundary }) => (
            <div className="error-container">
              <h2>Something went wrong</h2>
              <pre>{error.message}</pre>
              <button onClick={resetErrorBoundary}>Try again</button>
            </div>
          )}
        >
          {children}
        </ErrorBoundary>
      )}
    </QueryErrorResetBoundary>
  )
}

// Global error handling for queries
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: (failureCount, error: any) => {
        // Don't retry on 401/403 errors
        if (error?.status === 401 || error?.status === 403) {
          return false
        }
        // Retry up to 3 times for other errors
        return failureCount < 3
      },
      onError: (error: any) => {
        // Handle auth errors globally
        if (error?.status === 401) {
          // Redirect to login
          window.location.href = '/login'
        } else {
          // Show error toast
          toast.error(error?.message || 'Something went wrong')
        }
      }
    }
  }
})
```

## Real-Time Features

### WebSocket Integration for Messages

```mermaid
sequenceDiagram
    participant UI as Message Interface
    participant WS as WebSocket Hook
    participant Server as WebSocket Server
    participant DB as Database
    participant Cache as Cache Layer
    
    UI->>WS: Subscribe to messages
    WS->>Server: Connect WebSocket
    Server-->>WS: Connection established
    
    loop Real-time Updates
        DB->>Server: New message notification
        Server->>Cache: Update conversation cache
        Server->>WS: Broadcast message
        WS->>UI: Update message list
    end
    
    UI->>WS: Send new message
    WS->>Server: WebSocket message
    Server->>DB: Store message
    Server->>Cache: Invalidate caches
    Server->>WS: Confirm sent
    WS->>UI: Show sent status
```

## Performance Optimization Strategies

### Query Optimization Patterns

1. **Parallel Data Fetching**
   ```typescript
   // Load dashboard data in parallel
   const queries = useQueries([
     { queryKey: ['/api/dashboard/overview'], },
     { queryKey: ['/api/dashboard/bookings'], },
     { queryKey: ['/api/dashboard/messages'], },
   ])
   ```

2. **Prefetching**
   ```typescript
   // Prefetch property details on hover
   const onPropertyHover = (propertyId: string) => {
     queryClient.prefetchQuery({
       queryKey: ['/api/properties', propertyId],
       staleTime: 5 * 60 * 1000
     })
   }
   ```

3. **Infinite Queries**
   ```typescript
   // Paginated property search
   const {
     data,
     fetchNextPage,
     hasNextPage,
     isFetchingNextPage
   } = useInfiniteQuery({
     queryKey: ['/api/properties/search', filters],
     queryFn: ({ pageParam = 1 }) => 
       searchProperties(filters, pageParam),
     getNextPageParam: (lastPage) => lastPage.nextPage
   })
   ```

### Memory Management

```typescript
// Aggressive garbage collection for large datasets
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000,      // 5 minutes
      gcTime: 10 * 60 * 1000,        // 10 minutes in memory
    },
  },
})

// Cleanup on route changes
useEffect(() => {
  return () => {
    queryClient.removeQueries({ 
      queryKey: ['/api/properties/search'],
      exact: false 
    })
  }
}, [location.pathname])
```

## API Response Standards

### Consistent Response Format

```typescript
// Success Response
interface APIResponse<T> {
  success: true
  data: T
  meta?: {
    total?: number
    page?: number
    limit?: number
    hasMore?: boolean
  }
}

// Error Response
interface APIError {
  success: false
  message: string
  error?: string
  statusCode: number
  details?: Record<string, any>
}
```

### Response Examples

```typescript
// Property search response
{
  "success": true,
  "data": {
    "properties": [
      {
        "id": "prop_123",
        "title": "Luxury Villa in Costa Blanca",
        "pricePerNight": 250,
        "location": "Calpe, Spain",
        "images": ["https://..."],
        "rating": 4.8,
        "reviewsCount": 42
      }
    ],
    "total": 150,
    "page": 1,
    "limit": 20,
    "hasMore": true
  },
  "meta": {
    "searchTime": "45ms",
    "filters": {
      "location": "Costa Blanca",
      "maxPrice": 300
    }
  }
}

// Error response
{
  "success": false,
  "message": "Property not found",
  "statusCode": 404,
  "details": {
    "propertyId": "prop_invalid",
    "suggestion": "Check the property ID and try again"
  }
}
```

This architecture ensures robust, performant, and maintainable communication between the VillaWise frontend and backend, with comprehensive error handling, caching, and security considerations.