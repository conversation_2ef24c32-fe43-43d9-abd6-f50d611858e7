import { useTranslations } from "@/lib/translations";
import { PropertySearcher } from "@/components/property-searcher";

export function SearchBar() {
  const t = useTranslations("indexPage");

  return (
    <section 
      className="relative py-14 lg:py-20 bg-cover bg-center bg-no-repeat"
      style={{
        backgroundImage: `url(https://bapymeimutdxrngejohd.supabase.co/storage/v1/object/public/public-index//istockphoto-94279168-2048x2048.jpg)`,
        minHeight: '510px'
      }}
    >
      {/* Overlay for better text readability */}
      <div className="absolute inset-0 bg-black/20"></div>
      
      <div className="relative z-10 max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl lg:text-6xl font-bold text-white mb-6 leading-tight drop-shadow-lg">
            {t("hero.title")}
          </h1>
          <p className="text-xl lg:text-2xl text-white/90 max-w-3xl mx-auto drop-shadow-md">
            {t("hero.subtitle")}
          </p>
        </div>

        <PropertySearcher variant="hero" showHomeIcon={false} />
      </div>
    </section>
  );
}
