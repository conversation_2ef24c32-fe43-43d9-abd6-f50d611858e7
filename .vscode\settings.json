{"typescript.preferences.quoteStyle": "double", "typescript.preferences.includePackageJsonAutoImports": "on", "typescript.suggest.autoImports": true, "typescript.updateImportsOnFileMove.enabled": "always", "typescript.inlayHints.parameterNames.enabled": "literals", "typescript.inlayHints.parameterTypes.enabled": true, "typescript.inlayHints.variableTypes.enabled": true, "typescript.inlayHints.propertyDeclarationTypes.enabled": true, "typescript.inlayHints.functionLikeReturnTypes.enabled": true, "typescript.inlayHints.enumMemberValues.enabled": true, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "eslint.workingDirectories": ["./"], "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "tailwindCSS.includeLanguages": {"typescript": "javascript", "typescriptreact": "javascript"}, "tailwindCSS.experimental.configFile": "./tailwind.config.ts", "tailwindCSS.experimental.classRegex": [["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"], ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]], "files.associations": {"*.css": "tailwindcss", "*.tsx": "typescriptreact", "*.ts": "typescript"}, "emmet.includeLanguages": {"typescript": "html", "typescriptreact": "html"}, "search.exclude": {"**/node_modules": true, "**/dist": true, "**/build": true, "**/.git": true, "**/.vscode": false, "**/coverage": true}, "files.watcherExclude": {"**/node_modules/**": true, "**/dist/**": true, "**/build/**": true}, "javascript.preferences.quoteStyle": "double", "javascript.suggest.autoImports": true, "javascript.updateImportsOnFileMove.enabled": "always", "css.validate": false, "less.validate": false, "scss.validate": false, "editor.quickSuggestions": {"strings": true}, "files.exclude": {"**/.git": true, "**/.svn": true, "**/.hg": true, "**/CVS": true, "**/.DS_Store": true, "**/node_modules": true, "**/dist": true, "**/build": true}, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.tabSize": 2, "editor.insertSpaces": true, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "[javascriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "[css]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "git.autofetch": true, "git.enableSmartCommit": true, "git.confirmSync": false, "explorer.compactFolders": false, "explorer.fileNesting.enabled": true, "explorer.fileNesting.expand": false, "explorer.fileNesting.patterns": {"*.ts": "${capture}.js", "*.tsx": "${capture}.js", "package.json": "package-lock.json,yarn.lock,pnpm-lock.yaml", "tailwind.config.ts": "tailwind.config.js,postcss.config.js", "vite.config.ts": "vite.config.js", "tsconfig.json": "tsconfig.*.json", "README.md": "README.*.md", "docker-compose.yml": "docker-compose.*.yml,<PERSON>er<PERSON>le*", ".env": ".env.*", ".eslintrc.js": ".eslintrc.*.js,.eslint<PERSON>ore", ".gitignore": ".gitattributes,.gitkeep"}, "terminal.integrated.defaultProfile.linux": "bash", "terminal.integrated.env.linux": {"NODE_ENV": "development"}, "debug.internalConsoleOptions": "neverOpen", "debug.console.fontSize": 12, "workbench.colorTheme": "Default Dark+", "workbench.iconTheme": "vs-seti", "workbench.tree.indent": 20, "workbench.editor.enablePreview": false, "breadcrumbs.enabled": true, "breadcrumbs.showFiles": true, "breadcrumbs.showSymbols": true, "problems.decorations.enabled": true, "problems.showCurrentInStatus": true, "extensions.ignoreRecommendations": false, "chat.editor.fontSize": 14, "chat.editor.fontFamily": "Consolas, 'Courier New', monospace", "chat.editor.wordWrap": "on", "chat.editor.lineNumbers": "on", "scm.defaultViewMode": "tree", "scm.diffDecorations": "all", "json.schemas": [{"fileMatch": ["package.json"], "url": "https://json.schemastore.org/package.json"}, {"fileMatch": ["tsconfig.json", "tsconfig.*.json"], "url": "https://json.schemastore.org/tsconfig.json"}], "chat.mcp.serverSampling": {"villa-wise-explorer/.vscode/mcp.json: supabase": {"allowedModels": ["github.copilot-chat/gpt-4.1", "github.copilot-chat/claude-3.7-sonnet", "github.copilot-chat/claude-sonnet-4", "github.copilot-chat/gemini-2.0-flash-001", "github.copilot-chat/gemini-2.5-pro"]}}}