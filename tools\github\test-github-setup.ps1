# GitHub Setup Test Script for VillaWise
# Run this to test your GitHub CLI setup before creating the project board

param(
    [Parameter(Mandatory=$true)]
    [string]$RepoOwner,
    
    [Parameter(Mandatory=$true)]
    [string]$RepoName
)

$REPO = "$RepoOwner/$RepoName"

Write-Host "🧪 Testing GitHub CLI Setup for VillaWise..." -ForegroundColor Cyan
Write-Host "🎯 Target repository: $REPO" -ForegroundColor Cyan
Write-Host ""

# Test 1: GitHub CLI Installation
Write-Host "1. Testing GitHub CLI installation..." -ForegroundColor Yellow
try {
    $ghVersion = gh --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "   ✅ GitHub CLI is installed" -ForegroundColor Green
        Write-Host "   📋 Version: $($ghVersion.Split("`n")[0])" -ForegroundColor Gray
    } else {
        Write-Host "   ❌ GitHub CLI not found" -ForegroundColor Red
        Write-Host "   💡 Install from: https://cli.github.com/" -ForegroundColor Yellow
        exit 1
    }
} catch {
    Write-Host "   ❌ Error checking GitHub CLI: $_" -ForegroundColor Red
    exit 1
}

# Test 2: Authentication
Write-Host "2. Testing GitHub authentication..." -ForegroundColor Yellow
try {
    $authStatus = gh auth status 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "   ✅ GitHub CLI is authenticated" -ForegroundColor Green
        Write-Host "   📋 Status: $($authStatus.Split("`n")[0])" -ForegroundColor Gray
    } else {
        Write-Host "   ❌ Not authenticated" -ForegroundColor Red
        Write-Host "   💡 Run: gh auth login" -ForegroundColor Yellow
        Write-Host "   📋 Error: $authStatus" -ForegroundColor Gray
        exit 1
    }
} catch {
    Write-Host "   ❌ Error checking auth: $_" -ForegroundColor Red
    exit 1
}

# Test 3: Repository Access
Write-Host "3. Testing repository access..." -ForegroundColor Yellow
try {
    $repoInfo = gh repo view $REPO --json name,visibility,permissions 2>&1
    if ($LASTEXITCODE -eq 0) {
        $repoData = $repoInfo | ConvertFrom-Json
        Write-Host "   ✅ Repository found: $($repoData.name)" -ForegroundColor Green
        Write-Host "   📋 Visibility: $($repoData.visibility)" -ForegroundColor Gray
        Write-Host "   📋 Permissions: admin=$($repoData.permissions.admin), push=$($repoData.permissions.push)" -ForegroundColor Gray
        
        if (-not $repoData.permissions.push) {
            Write-Host "   ⚠️  Warning: No push permissions - issues may fail to create" -ForegroundColor Yellow
        }
    } else {
        Write-Host "   ❌ Repository not found or no access" -ForegroundColor Red
        Write-Host "   📋 Error: $repoInfo" -ForegroundColor Gray
        Write-Host "   💡 Check repository name and permissions" -ForegroundColor Yellow
        exit 1
    }
} catch {
    Write-Host "   ❌ Error checking repository: $_" -ForegroundColor Red
    exit 1
}

# Test 4: JSON File
Write-Host "4. Testing JSON file..." -ForegroundColor Yellow
$jsonPath = Join-Path $PSScriptRoot "create-issues-batch.json"
if (Test-Path $jsonPath) {
    try {
        $jsonContent = Get-Content $jsonPath -Raw
        $issues = $jsonContent | ConvertFrom-Json
        Write-Host "   ✅ JSON file found and valid" -ForegroundColor Green
        Write-Host "   📋 Issues count: $($issues.Count)" -ForegroundColor Gray
        Write-Host "   📋 Sample issue: $($issues[0].title)" -ForegroundColor Gray
    } catch {
        Write-Host "   ❌ JSON file invalid: $_" -ForegroundColor Red
        exit 1
    }
} else {
    Write-Host "   ❌ JSON file not found at: $jsonPath" -ForegroundColor Red
    Write-Host "   💡 Ensure create-issues-batch.json is in the same directory" -ForegroundColor Yellow
    exit 1
}

# Test 5: Test Issue Creation (dry run)
Write-Host "5. Testing issue creation permissions..." -ForegroundColor Yellow
try {
    # Try to create a test label first (safer than creating an issue)
    $testResult = gh label create "test-label-villawise" --color "ff0000" --description "Test label for VillaWise setup" --repo $REPO 2>&1
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "   ✅ Issue creation permissions confirmed" -ForegroundColor Green
        
        # Clean up test label
        gh label delete "test-label-villawise" --repo $REPO --yes 2>$null
        Write-Host "   🧹 Test label cleaned up" -ForegroundColor Gray
    } else {
        Write-Host "   ❌ No permission to create issues/labels" -ForegroundColor Red
        Write-Host "   📋 Error: $testResult" -ForegroundColor Gray
        Write-Host "   💡 Check repository permissions or try with organization repo" -ForegroundColor Yellow
    }
} catch {
    Write-Host "   ❌ Error testing permissions: $_" -ForegroundColor Red
}

Write-Host ""
Write-Host "🎉 GitHub CLI setup test complete!" -ForegroundColor Green
Write-Host "💡 If all tests passed, run:" -ForegroundColor Cyan
Write-Host "   ./tools/github/create-github-board.ps1 $RepoOwner $RepoName" -ForegroundColor White
Write-Host ""