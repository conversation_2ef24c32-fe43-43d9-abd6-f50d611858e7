// Standalone sync runner for Railway deployment
import { GeoNamesSyncService } from './sync-service';

async function runSync(): Promise<void> {
  console.log('[SYNC-RUNNER] Starting GeoNames sync process...');
  console.log('[SYNC-RUNNER] Environment:', {
    nodeEnv: process.env.NODE_ENV,
    serviceType: process.env.SERVICE_TYPE,
    countries: process.env.GEONAMES_COUNTRIES || 'ES',
    languages: process.env.GEONAMES_LANGUAGES || 'en,nl,es,ca'
  });

  const syncService = new GeoNamesSyncService();

  try {
    // Check health before starting
    console.log('[SYNC-RUNNER] Checking health status...');
    const health = await syncService.getHealthStatus();
    
    if (health.status === 'error') {
      console.error('[SYNC-RUNNER] ❌ Health check failed:', health.checks);
      process.exit(1);
    }

    console.log('[SYNC-RUNNER] Health status:', health.status);

    // Perform full sync
    const result = await syncService.performFullSync();
    
    console.log('[SYNC-RUNNER] ✅ Sync completed successfully');
    console.log('[SYNC-RUNNER] Results:', {
      countries: result.countries,
      languages: result.languages,
      locations: result.locationsProcessed,
      names: result.alternateNamesProcessed,
      duration: `${Math.round(result.processingTime / 1000)}s`
    });

    // In development, exit gracefully
    // In production, keep running for health checks
    if (process.env.NODE_ENV === 'development') {
      console.log('[SYNC-RUNNER] Development mode - exiting after sync');
      process.exit(0);
    } else {
      console.log('[SYNC-RUNNER] Production mode - staying alive for health checks');
      // Keep the process alive for Railway health checks
      setInterval(() => {
        console.log('[SYNC-RUNNER] Health check ping');
      }, 30000);
    }

  } catch (error) {
    console.error('[SYNC-RUNNER] ❌ Sync failed:', error);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGTERM', () => {
  console.log('[SYNC-RUNNER] Received SIGTERM, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('[SYNC-RUNNER] Received SIGINT, shutting down gracefully');
  process.exit(0);
});

// Start sync
runSync().catch((error) => {
  console.error('[SYNC-RUNNER] Fatal error:', error);
  process.exit(1);
});