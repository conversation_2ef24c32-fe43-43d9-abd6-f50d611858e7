export interface Property {
  id: number;
  title: string;
  location: string;
  pricePerNight: number;
  status: 'active' | 'inactive' | 'pending';
  bookings: number;
  revenue: number;
  views: number;
  rating: number;
  images: string[];
}

export interface Booking {
  id: number;
  propertyTitle: string;
  guestName: string;
  checkIn: string;
  checkOut: string;
  status: 'confirmed' | 'pending' | 'cancelled' | 'completed';
  totalPrice: number;
  guests: number;
}

export interface PropertyFormData {
  title: string;
  description: string;
  location: string;
  city: string;
  country: string;
  propertyType: 'villa' | 'apartment' | 'house' | 'cottage' | 'loft' | 'penthouse' | 'studio' | 'townhouse' | 'castle' | 'farm';
  pricePerNight: number;
  maxGuests: number;
  bedrooms: number;
  bathrooms: number;
  amenities?: string[];
  coordinates: {
    lat: number;
    lng: number;
  };
}

export interface PropertyManagementData {
  properties: Property[];
  bookings: Booking[];
  totalRevenue: number;
  totalBookings: number;
  averageRating: number;
}