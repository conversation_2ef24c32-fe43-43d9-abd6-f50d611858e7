import React, { useEffect } from 'react';
import { useLocation } from 'wouter';
import { useToast } from '@/hooks/use-toast';
import { useQueryClient } from '@tanstack/react-query';
import { useTranslations } from '@/lib/translations';

// Helper function to get cookie value
function getCookie(name: string): string | null {
  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) {
    return parts.pop()?.split(';').shift() || null;
  }
  return null;
}

// Helper function to delete cookie
function deleteCookie(name: string) {
  document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
}

export function OAuthCallback() {
  const [, navigate] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const t = useTranslations('oauthCallback');
  
  // Check if translations are loaded
  const isTranslationsLoaded = t('processingLogin') !== 'oauthCallback.processingLogin';

  useEffect(() => {
    const handleOAuthCallback = async () => {
      console.log('OAuthCallback: Starting OAuth callback processing');
      console.log('OAuthCallback: Current URL:', window.location.href);
      console.log('OAuthCallback: Hash:', window.location.hash);
      
      // Check if authentication is already complete (tokens in localStorage)
      const existingToken = localStorage.getItem('sb_access_token');
      if (existingToken) {
        console.log('OAuthCallback: Authentication already complete, redirecting...');
        window.history.replaceState({}, document.title, '/');
        navigate('/');
        return;
      }
      
      // Check URL parameters for OAuth results (query params)
      const urlParams = new URLSearchParams(window.location.search);
      const loginSuccess = urlParams.get('login');
      const error = urlParams.get('error');

      // Check URL hash fragment for OAuth tokens (Supabase returns tokens in hash)
      const hashParams = new URLSearchParams(window.location.hash.substring(1));
      const accessToken = hashParams.get('access_token');
      const refreshToken = hashParams.get('refresh_token');
      const expiresIn = hashParams.get('expires_in');
      
      // Also check for tokens in cookies (server-side OAuth flow)
      const cookieAccessToken = getCookie('sb_access_token');
      const cookieRefreshToken = getCookie('sb_refresh_token');
      
      console.log('OAuthCallback: Hash content:', window.location.hash);
      console.log('OAuthCallback: All hash params:', Array.from(hashParams.entries()));
      console.log('OAuthCallback: Document cookies:', document.cookie);
      console.log('OAuthCallback: Found tokens:', {
        accessToken: accessToken ? accessToken.substring(0, 20) + '...' : 'missing',
        refreshToken: refreshToken ? refreshToken.substring(0, 20) + '...' : 'missing',
        cookieAccessToken: cookieAccessToken ? cookieAccessToken.substring(0, 20) + '...' : 'missing',
        cookieRefreshToken: cookieRefreshToken ? cookieRefreshToken.substring(0, 20) + '...' : 'missing',
        loginSuccess,
        error
      });
      
      // Helper function to complete OAuth login
      const completeOAuthLogin = async (token: string) => {
        try {
          console.log('OAuthCallback: Starting token validation with backend...');
          console.log('OAuthCallback: Token length:', token.length);
          console.log('OAuthCallback: Token prefix:', token.substring(0, 20) + '...');
          
          // Validate token with backend
          const response = await fetch('/api/auth/oauth-user', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          });

          console.log('OAuthCallback: Backend validation response status:', response.status);
          
          if (response.ok) {
            const userData = await response.json();
            console.log('OAuthCallback: User profile validated successfully:', userData.user?.email);
            console.log('OAuthCallback: User data received:', {
              id: userData.user?.id,
              email: userData.user?.email,
              username: userData.user?.username,
              first_name: userData.user?.first_name,
              is_host: userData.user?.is_host
            });
            
            // Immediately set user data in cache after successful validation
            queryClient.setQueryData(['auth', 'user'], userData.user);
            
            // Invalidate all auth-related queries to ensure consistency
            queryClient.invalidateQueries({ queryKey: ['auth'] });
            
            // Show subtle success message
            console.log('OAuthCallback: OAuth login completed successfully!');
            toast({
              title: t('loginSuccessful'),
              description: t('welcomeBack', { 
                name: userData.user?.first_name || userData.user?.username || t('user') 
              }),
              variant: "default",
            });
            
            // Clean up URL and redirect
            window.history.replaceState({}, document.title, '/');
            
            // Small delay for UI update then navigate without full reload
            setTimeout(() => {
              // Use navigate instead of reload to maintain app state
              navigate('/');
            }, 800);
            
            return true;
          } else {
            const errorData = await response.json().catch(() => ({}));
            console.error('OAuthCallback: Failed to validate user profile');
            console.error('OAuthCallback: Response status:', response.status);
            console.error('OAuthCallback: Error data:', errorData);
            
            // Show error message to user
            toast({
              title: t('loginFailed'),
              description: errorData.message || t('loginValidationError'),
              variant: "destructive",
            });
            
            return false;
          }
        } catch (error) {
          console.error('OAuthCallback: Error validating user profile:', error);
          
          // Show error message to user
          toast({
            title: t('loginError'),
            description: t('networkError'),
            variant: "destructive",
          });
          
          return false;
        }
      };

      if (error) {
        // Handle OAuth errors
        let errorMessage = t('authenticationFailed');
        
        switch (error) {
          case 'auth_failed':
            errorMessage = t('authFailed');
            break;
          case 'user_not_found':
            errorMessage = t('userNotFound');
            break;
          case 'profile_creation_failed':
            errorMessage = t('profileCreationFailed');
            break;
          case 'callback_failed':
            errorMessage = t('callbackFailed');
            break;
        }

        toast({
          title: t('authenticationError'),
          description: errorMessage,
          variant: "destructive",
        });

        // Clean up URL and redirect to home
        window.history.replaceState({}, document.title, '/');
        return;
      }

      // Priority 1: Handle OAuth tokens from URL hash (primary Supabase OAuth flow)
      if (accessToken && refreshToken) {
        console.log('OAuthCallback: Processing OAuth tokens from hash');
        
        // Store tokens in localStorage for frontend auth
        localStorage.setItem('sb_access_token', accessToken);
        localStorage.setItem('sb_refresh_token', refreshToken);
        
        console.log('OAuthCallback: Tokens stored in localStorage');

        // Force immediate cache invalidation and refetch
        queryClient.invalidateQueries({ queryKey: ['auth', 'user'] });
        
        // Complete OAuth login with validation
        const success = await completeOAuthLogin(accessToken);
        if (!success) {
          // Clean up and redirect to home
          localStorage.removeItem('sb_access_token');
          localStorage.removeItem('sb_refresh_token');
          window.history.replaceState({}, document.title, '/');
        }
        
        return;
      }

      // Priority 2: Handle OAuth tokens from cookies (server-side OAuth flow)
      if (cookieAccessToken && cookieRefreshToken) {
        console.log('OAuthCallback: Processing OAuth tokens from cookies');
        
        // Transfer tokens from cookies to localStorage for consistency
        localStorage.setItem('sb_access_token', cookieAccessToken);
        localStorage.setItem('sb_refresh_token', cookieRefreshToken);

        // Clean up cookies
        deleteCookie('sb_access_token');
        deleteCookie('sb_refresh_token');

        console.log('OAuthCallback: Tokens transferred from cookies to localStorage');

        // Force immediate cache invalidation and refetch
        queryClient.invalidateQueries({ queryKey: ['auth', 'user'] });
        
        // Complete OAuth login with validation
        const success = await completeOAuthLogin(cookieAccessToken);
        if (!success) {
          // Clean up and redirect to home
          localStorage.removeItem('sb_access_token');
          localStorage.removeItem('sb_refresh_token');
          window.history.replaceState({}, document.title, '/');
        }
        
        return;
      }

      // Priority 3: Handle delayed cookie setting (sometimes OAuth providers delay cookie setting)
      if (window.location.pathname === '/auth/callback') {
        console.log('OAuthCallback: On callback page, checking for delayed tokens');
        console.log('OAuthCallback: URL search params:', window.location.search);
        console.log('OAuthCallback: Document cookies:', document.cookie);
        
        // Track if we've already processed authentication to avoid duplicate error messages
        let hasShownError = false;
        
        // Wait a moment and try again in case cookies are being set asynchronously
        setTimeout(async () => {
          // Check if authentication was already successful (user might have been redirected)
          const currentToken = localStorage.getItem('sb_access_token');
          if (currentToken) {
            console.log('OAuthCallback: Authentication already completed, skipping retry');
            return;
          }
          
          const retryAccessToken = getCookie('sb_access_token');
          const retryRefreshToken = getCookie('sb_refresh_token');
          
          console.log('OAuthCallback: Retry - checking for tokens after delay');
          console.log('OAuthCallback: Retry access token:', retryAccessToken ? 'found' : 'missing');
          console.log('OAuthCallback: Retry refresh token:', retryRefreshToken ? 'found' : 'missing');
          
          if (retryAccessToken && retryRefreshToken) {
            console.log('OAuthCallback: Found tokens on retry, processing now');
            
            // Store tokens in localStorage
            localStorage.setItem('sb_access_token', retryAccessToken);
            localStorage.setItem('sb_refresh_token', retryRefreshToken);
            
            // Clean up cookies
            deleteCookie('sb_access_token');
            deleteCookie('sb_refresh_token');
            
            // Force immediate cache invalidation and refetch
            queryClient.invalidateQueries({ queryKey: ['auth', 'user'] });
            
            // Complete OAuth login with validation
            const success = await completeOAuthLogin(retryAccessToken);
            if (!success) {
              // Clean up and redirect to home
              localStorage.removeItem('sb_access_token');
              localStorage.removeItem('sb_refresh_token');
              window.history.replaceState({}, document.title, '/');
            }
          } else {
            // Check one more time if authentication completed while we were waiting
            const finalToken = localStorage.getItem('sb_access_token');
            if (!finalToken && !hasShownError) {
              // Still no tokens found - show error only if we haven't shown it yet
              console.log('OAuthCallback: No tokens found after retry');
              hasShownError = true;
              toast({
                title: t('authenticationError'),
                description: t('noTokensReceived'),
                variant: "destructive",
              });
              
              // Clean up URL and redirect to home
              window.history.replaceState({}, document.title, '/');
            }
          }
        }, 1500); // Increased delay to 1.5 seconds to allow more time for cookie setting
      }
    };

    // Run on component mount
    handleOAuthCallback();
  }, [toast, navigate, queryClient]);

  // Render a loading page while processing OAuth callback
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto mb-4"></div>
        <h2 className="text-2xl font-semibold mb-2">
          {isTranslationsLoaded ? t('processingLogin') : 'Processing Login...'}
        </h2>
        <p className="text-gray-600">
          {isTranslationsLoaded ? t('pleaseWait') : 'Please wait while we complete your authentication.'}
        </p>
      </div>
    </div>
  );
}

export default OAuthCallback;