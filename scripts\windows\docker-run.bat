@echo off
echo Running VillaWise in Docker...

REM Check if .env file exists
if not exist .env (
    echo WARNING: .env file not found!
    echo Please copy .env.example to .env and configure your environment variables.
    pause
    exit /b 1
)

REM Check if Docker image exists
docker image inspect villawise:latest >nul 2>&1
if errorlevel 1 (
    echo Docker image not found. Building it now...
    call scripts\windows\docker-build.bat
    if errorlevel 1 exit /b 1
)

REM Stop and remove existing container if running
docker stop villawise-container >nul 2>&1
docker rm villawise-container >nul 2>&1

echo Starting VillaWise container...
docker run -d ^
    --name villawise-container ^
    -p 5000:5000 ^
    --env-file .env ^
    --restart unless-stopped ^
    villawise:latest

if errorlevel 1 (
    echo ERROR: Failed to start container!
    echo.
    echo Common solutions:
    echo - Make sure Docker Desktop is running
    echo - Check that port 5000 is not already in use
    echo - Verify your .env file has correct Supabase credentials
    pause
    exit /b 1
)

echo SUCCESS: VillaWise is running at http://localhost:5000
echo Container name: villawise-container
echo.
echo The application uses memory cache by default (no Redis required)
echo.
echo Commands:
echo   View logs: docker logs -f villawise-container
echo   Stop: docker stop villawise-container
echo   Remove: docker rm villawise-container
echo.
echo To enable Redis cache (optional):
echo   1. Get Upstash Redis credentials from console.upstash.com
echo   2. Add to .env: USE_REDIS_CACHE=true
echo   3. Add to .env: UPSTASH_REDIS_REST_URL=your-url
echo   4. Add to .env: UPSTASH_REDIS_REST_TOKEN=your-token
echo   5. Restart the container
pause