import { useState, useEffect } from 'react';
import { Check, Save } from 'lucide-react';
import { cn } from '@/lib/utils';

interface SearchPersistenceIndicatorProps {
  isSaving?: boolean;
  className?: string;
}

export function SearchPersistenceIndicator({ 
  isSaving = false, 
  className 
}: SearchPersistenceIndicatorProps) {
  const [showSaved, setShowSaved] = useState(false);

  useEffect(() => {
    if (!isSaving && showSaved) {
      const timer = setTimeout(() => setShowSaved(false), 2000);
      return () => clearTimeout(timer);
    }
    if (isSaving) {
      setShowSaved(true);
    }
  }, [isSaving, showSaved]);

  if (!isSaving && !showSaved) {
    return null;
  }

  return (
    <div className={cn(
      "inline-flex items-center space-x-1 text-xs text-muted-foreground transition-opacity",
      className
    )}>
      {isSaving ? (
        <>
          <Save className="h-3 w-3 animate-pulse" />
          <span>Saving...</span>
        </>
      ) : (
        <>
          <Check className="h-3 w-3 text-green-500" />
          <span className="text-green-600">Saved</span>
        </>
      )}
    </div>
  );
}