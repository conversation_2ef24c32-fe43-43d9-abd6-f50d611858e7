import { useState, useEffect } from 'react';
import { useLocale } from './i18n';
import { translationCache } from './translationCache';

type TranslationValues = Record<string, any>;
type TranslationFunction = (key: string, values?: TranslationValues) => string;

// Get nested value from object using dot notation
function getNestedValue(obj: any, path: string): any {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined;
  }, obj);
}

// Format translation with values
function formatTranslation(template: string, values?: TranslationValues): string {
  if (!values) return template;
  
  return template.replace(/\{(\w+)\}/g, (match, key) => {
    return values[key] !== undefined ? String(values[key]) : match;
  });
}

// Load translations from API with enhanced caching
async function loadTranslations(locale: string): Promise<Record<string, any>> {
  try {
    // Check if we have valid cached translations
    const cachedTranslations = await translationCache.get(locale);
    if (cachedTranslations) {
      console.log(`Using cached translations for locale: ${locale}`);
      return cachedTranslations;
    }

    console.log(`Fetching fresh translations for locale: ${locale}`);
    
    // Fetch from API with simpler cache-busting
    const response = await fetch(`/api/translations/${locale}`, {
      headers: {
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to load translations for ${locale}: ${response.status}`);
    }
    
    const translations = await response.json();
    
    // Store in enhanced cache
    await translationCache.set(locale, translations);
    
    return translations;
  } catch (error) {
    console.error('Failed to load translations:', error);
    
    // Try to get any cached version as fallback
    const fallbackCache = await translationCache.get(locale);
    if (fallbackCache) {
      console.warn(`Using potentially stale cache for locale: ${locale}`);
      return fallbackCache;
    }
    
    // Return empty object as last resort
    return {};
  }
}

// Hook to get translation function for a specific namespace
export function useTranslations(namespace?: string): TranslationFunction {
  const { locale } = useLocale();
  const [translations, setTranslations] = useState<Record<string, any>>({});
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadTranslationData = async () => {
      setIsLoading(true);
      try {
        const translationData = await loadTranslations(locale);
        setTranslations(translationData);
      } catch (error) {
        console.error('Error loading translations:', error);
        setTranslations({});
      } finally {
        setIsLoading(false);
      }
    };

    loadTranslationData();
  }, [locale]);

  const t: TranslationFunction = (key: string, values?: TranslationValues) => {
    if (isLoading) return key; // Return key while loading
    
    // Build the full key path
    const fullKey = namespace ? `${namespace}.${key}` : key;
    
    // Get the translation
    const translation = getNestedValue(translations, fullKey);
    
    // Return formatted translation or fallback to key
    if (translation !== undefined) {
      return formatTranslation(String(translation), values);
    }
    
    // Fallback to key if translation not found
    return key;
  };

  return t;
}

// Direct translation function (for non-hook usage)
export async function getTranslation(key: string, locale: string = 'nl', namespace?: string): Promise<string> {
  const translations = await loadTranslations(locale);
  const fullKey = namespace ? `${namespace}.${key}` : key;
  const translation = getNestedValue(translations, fullKey);
  return translation !== undefined ? String(translation) : key;
}

// Clear translation cache (useful for testing and locale changes)
export async function clearTranslationCache() {
  await translationCache.clear();
}

// Clear cache for specific locale (useful for locale switching)
export async function clearLocaleCache(locale: string) {
  await translationCache.clearLocale(locale);
}

// Force refresh translations (useful for development)
export async function refreshTranslations(locale: string): Promise<Record<string, any>> {
  await translationCache.clearLocale(locale);
  return loadTranslations(locale);
}

// Get build version info (useful for debugging)
export async function getBuildInfo() {
  return translationCache.getCurrentVersion();
}