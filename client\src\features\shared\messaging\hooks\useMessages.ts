import { useUser } from "@/features/shared/auth/hooks/useAuth";
import { useQuery } from "@tanstack/react-query";

// Message interface for frontend usage
export interface Message {
  id: string;
  conversation_id?: string;
  sender_id: string;
  receiver_id?: string;
  content: string;
  message_type:
    | "text"
    | "booking_inquiry"
    | "booking_update"
    | "system"
    | "template"
    | "automated";
  message_status?: "sent" | "delivered" | "read";
  read: boolean;
  created_at: string;
  updated_at?: string;
  sender?: {
    id: string;
    name: string;
    avatar_url?: string;
    user_type?: string;
  };
  receiver?: {
    id: string;
    name: string;
    avatar_url?: string;
  };
  property?: {
    id: string;
    title: string;
    location?: string;
  };
  booking?: {
    id: string;
    check_in_date: string;
    check_out_date: string;
    status: string;
  };
}

export const useMessages = (userType: "host" | "guest") => {
  const { data: user } = useUser();

  const {
    data: messages = [],
    isLoading,
    error,
  } = useQuery({
    queryKey: [`/api/messages/${userType}`],
    enabled: !!user,
  });

  // Type the messages array properly
  const messagesArray = messages as Message[];

  // Filter messages by read status
  const unreadMessages = messagesArray.filter((m: Message) => !m.read);
  const readMessages = messagesArray.filter((m: Message) => m.read);

  // Sort messages by latest first
  const sortedMessages = [...messagesArray].sort(
    (a: Message, b: Message) =>
      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
  );

  return {
    messages: sortedMessages,
    unreadMessages,
    readMessages,
    isLoading,
    error,
    messageCounts: {
      total: messagesArray.length,
      unread: unreadMessages.length,
      read: readMessages.length,
    },
  };
};
