import { useState, useEffect, useCallback } from 'react';
import { useLocation } from 'wouter';
import { useSearch } from 'wouter';

// Utility function to format date in local timezone (prevents timezone shifts)
const formatLocalDate = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

export interface SearchQueryState {
  location: string;
  checkIn?: string;
  checkOut?: string;
  dateFlexibility?: string;
  adults: number;
  children: number;
  infants: number;
  pets: number;
}

export interface SearchQueryParams {
  location: string;
  dateRange?: { from: Date; to: Date };
  dateFlexibility?: number | "exact" | null;
  guests: {
    adults: number;
    children: number;
    infants: number;
    pets: number;
  };
}

/**
 * Custom hook to manage search state synchronization with URL parameters
 * Provides URL-based state management for search functionality
 */
export function useSearchQuery() {
  const [, navigate] = useLocation();
  const searchString = useSearch();

  // Parse current URL search parameters
  const parseSearchParams = useCallback((): SearchQueryParams => {
    const params = new URLSearchParams(searchString);
    
    return {
      location: params.get('location') || '',
      dateRange: params.get('checkIn') && params.get('checkOut') ? {
        from: new Date(params.get('checkIn')!),
        to: new Date(params.get('checkOut')!)
      } : undefined,
      dateFlexibility: params.get('dateFlexibility') ? 
        (params.get('dateFlexibility') === 'exact' ? 'exact' : parseInt(params.get('dateFlexibility')!)) : null,
      guests: {
        adults: parseInt(params.get('adults') || '2'),
        children: parseInt(params.get('children') || '0'),
        infants: parseInt(params.get('infants') || '0'),
        pets: parseInt(params.get('pets') || '0'),
      }
    };
  }, [searchString]);

  // Current search state from URL
  const [searchState, setSearchState] = useState<SearchQueryParams>(parseSearchParams);

  // Update state when URL changes
  useEffect(() => {
    setSearchState(parseSearchParams());
  }, [parseSearchParams]);

  // Function to update search parameters in URL
  const updateSearch = useCallback((params: Partial<SearchQueryParams>, replace: boolean = false) => {
    const currentParams = new URLSearchParams(searchString);
    
    // Update location
    if (params.location !== undefined) {
      if (params.location) {
        currentParams.set('location', params.location);
      } else {
        currentParams.delete('location');
      }
    }

    // Update date range
    if (params.dateRange !== undefined) {
      if (params.dateRange) {
        currentParams.set('checkIn', formatLocalDate(params.dateRange.from));
        currentParams.set('checkOut', formatLocalDate(params.dateRange.to));
      } else {
        currentParams.delete('checkIn');
        currentParams.delete('checkOut');
      }
    }

    // Update date flexibility
    if (params.dateFlexibility !== undefined) {
      if (params.dateFlexibility !== null) {
        currentParams.set('dateFlexibility', params.dateFlexibility.toString());
      } else {
        currentParams.delete('dateFlexibility');
      }
    }

    // Update guests
    if (params.guests) {
      currentParams.set('adults', params.guests.adults.toString());
      currentParams.set('children', params.guests.children.toString());
      currentParams.set('infants', params.guests.infants.toString());
      currentParams.set('pets', params.guests.pets.toString());
    }

    const newUrl = `${window.location.pathname}?${currentParams.toString()}`;
    
    if (replace) {
      window.history.replaceState({}, '', newUrl);
    } else {
      window.history.pushState({}, '', newUrl);
    }

    // Update local state
    setSearchState(parseSearchParams());
  }, [searchString, parseSearchParams]);

  // Function to navigate to search page with parameters
  const navigateToSearch = useCallback((params: SearchQueryParams) => {
    const urlParams = new URLSearchParams({
      location: params.location,
      ...(params.dateRange?.from && {
        checkIn: formatLocalDate(params.dateRange.from),
      }),
      ...(params.dateRange?.to && {
        checkOut: formatLocalDate(params.dateRange.to),
      }),
      ...(params.dateFlexibility !== null && params.dateFlexibility !== undefined && {
        dateFlexibility: params.dateFlexibility.toString(),
      }),
      adults: params.guests.adults.toString(),
      children: params.guests.children.toString(),
      infants: params.guests.infants.toString(),
      pets: params.guests.pets.toString(),
    });

    navigate(`/search?${urlParams.toString()}`);
  }, [navigate]);

  // Function to clear all search parameters
  const clearSearch = useCallback(() => {
    const newUrl = window.location.pathname;
    window.history.replaceState({}, '', newUrl);
    setSearchState(parseSearchParams());
  }, [parseSearchParams]);

  return {
    searchState,
    updateSearch,
    navigateToSearch,
    clearSearch,
    hasSearchParams: Boolean(searchString)
  };
}