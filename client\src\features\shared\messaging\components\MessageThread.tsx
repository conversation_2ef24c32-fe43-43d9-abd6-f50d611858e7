import { useState, useRef, useEffect } from 'react';
import { format, isToday, isYesterday } from 'date-fns';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { 
  Send, 
  MapPin, 
  MoreVertical, 
  Archive, 
  Flag,
  Edit2,
  Trash2,
  FileText,
  Smile
} from 'lucide-react';
import { useRealTimeMessages } from '../hooks/useRealTimeMessages';
import { useMessageTemplates } from '../hooks/useMessageTemplates';
// import { MessageBubble } from './MessageBubble';
// import { TypingIndicator } from './TypingIndicator';
// import { TemplateSelector } from './TemplateSelector';
import { cn } from '@/lib/utils';

interface MessageThreadProps {
  conversation: {
    id: string;
    guest_id: string;
    host_id: string;
    property_id?: string;
    subject?: string;
    status: string;
    participant?: {
      id: string;
      name: string;
      avatar_url?: string;
    };
    property?: {
      id: string;
      title: string;
      location: string;
    };
  };
  currentUserId?: string;
  userType: 'guest' | 'host';
}

export const MessageThread = ({ conversation, currentUserId, userType }: MessageThreadProps) => {
  const [messageText, setMessageText] = useState('');
  const [showTemplates, setShowTemplates] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  
  // Real-time messaging
  const {
    messages,
    typingUsers,
    onlineUsers,
    isConnected,
    isLoading,
    sendMessage,
    sendTypingIndicator,
    markAsRead,
    editMessage,
    deleteMessage
  } = useRealTimeMessages(conversation.id, currentUserId);
  
  // Templates (for hosts)
  const { templates } = useMessageTemplates();
  
  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);
  
  // Mark messages as read when conversation opens
  useEffect(() => {
    if (conversation.id && currentUserId) {
      markAsRead();
    }
  }, [conversation.id, currentUserId, markAsRead]);
  
  // Handle typing indicator
  useEffect(() => {
    let typingTimeout: NodeJS.Timeout;
    
    if (messageText && !isTyping) {
      setIsTyping(true);
      sendTypingIndicator(true);
    }
    
    if (messageText) {
      typingTimeout = setTimeout(() => {
        setIsTyping(false);
        sendTypingIndicator(false);
      }, 1000);
    }
    
    return () => {
      if (typingTimeout) {
        clearTimeout(typingTimeout);
      }
    };
  }, [messageText, isTyping, sendTypingIndicator]);
  
  // Handle send message
  const handleSendMessage = async () => {
    if (!messageText.trim()) return;
    
    try {
      await sendMessage(messageText.trim());
      setMessageText('');
      setIsTyping(false);
      sendTypingIndicator(false);
      
      // Focus back to textarea
      textareaRef.current?.focus();
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  };
  
  // Handle template selection
  const handleTemplateSelect = async (templateId: string, variables: Record<string, string>) => {
    try {
      await sendMessage('', {
        messageType: 'template',
        templateId,
        variables
      });
      setShowTemplates(false);
    } catch (error) {
      console.error('Failed to send template message:', error);
    }
  };
  
  // Handle Enter key
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };
  
  // Group messages by date
  const groupMessagesByDate = (messages: any[]) => {
    const groups: { [key: string]: any[] } = {};
    
    messages.forEach(message => {
      const date = new Date(message.created_at);
      let dateKey: string;
      
      if (isToday(date)) {
        dateKey = 'Today';
      } else if (isYesterday(date)) {
        dateKey = 'Yesterday';
      } else {
        dateKey = format(date, 'MMMM d, yyyy');
      }
      
      if (!groups[dateKey]) {
        groups[dateKey] = [];
      }
      groups[dateKey].push(message);
    });
    
    return groups;
  };
  
  const messageGroups = groupMessagesByDate(messages);
  const isOnline = onlineUsers.some(user => user.status === 'online');
  
  if (isLoading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-pulse">Loading messages...</div>
        </div>
      </div>
    );
  }
  
  return (
    <div className="flex-1 flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b bg-background">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Avatar className="h-10 w-10">
              <AvatarImage src={conversation.participant?.avatar_url} />
              <AvatarFallback className="bg-primary text-primary-foreground">
                {conversation.participant?.name?.charAt(0) || '?'}
              </AvatarFallback>
            </Avatar>
            
            <div>
              <div className="flex items-center gap-2">
                <h3 className="font-semibold">
                  {conversation.participant?.name || 'Unknown User'}
                </h3>
                {isOnline && (
                  <Badge variant="secondary" className="text-xs">
                    Online
                  </Badge>
                )}
                {!isConnected && (
                  <Badge variant="outline" className="text-xs">
                    Offline
                  </Badge>
                )}
              </div>
              
              {conversation.property && (
                <div className="flex items-center gap-1 text-sm text-muted-foreground">
                  <MapPin className="h-3 w-3" />
                  <span>{conversation.property.title}</span>
                </div>
              )}
            </div>
          </div>
          
          <div className="flex items-center gap-1">
            <Button variant="ghost" size="sm">
              <Archive className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm">
              <MoreVertical className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
      
      {/* Messages */}
      <ScrollArea className="flex-1 p-4">
        <div className="space-y-4">
          {Object.entries(messageGroups).map(([dateKey, dayMessages]) => (
            <div key={dateKey}>
              {/* Date Separator */}
              <div className="flex items-center gap-3 my-4">
                <Separator className="flex-1" />
                <span className="text-xs text-muted-foreground bg-background px-2">
                  {dateKey}
                </span>
                <Separator className="flex-1" />
              </div>
              
              {/* Messages */}
              <div className="space-y-2">
                {dayMessages.map((message: any, index: number) => (
                  <div key={message.id} className="bg-muted p-2 rounded">
                    <p className="text-sm">{message.content}</p>
                    <span className="text-xs text-muted-foreground">
                      {format(new Date(message.created_at), 'HH:mm')}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          ))}
          
          {/* Typing Indicator */}
          {typingUsers.length > 0 && (
            <div className="bg-muted/50 p-2 rounded text-sm text-muted-foreground">
              {conversation.participant?.name} is typing...
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>
      
      {/* Message Input */}
      <div className="p-4 border-t bg-background">
        <div className="flex items-end gap-2">
          {/* Template Button (Host only) */}
          {userType === 'host' && templates.length > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowTemplates(!showTemplates)}
              className={cn(showTemplates && "bg-accent")}
            >
              <FileText className="h-4 w-4" />
            </Button>
          )}
          
          {/* Message Input */}
          <div className="flex-1">
            <Textarea
              ref={textareaRef}
              placeholder="Type your message..."
              value={messageText}
              onChange={(e) => setMessageText(e.target.value)}
              onKeyDown={handleKeyDown}
              rows={1}
              className="min-h-[40px] max-h-32 resize-none"
            />
          </div>
          
          {/* Send Button */}
          <Button
            onClick={handleSendMessage}
            disabled={!messageText.trim() || !isConnected}
            size="sm"
            className="h-10"
          >
            <Send className="h-4 w-4" />
            <span className="sr-only">Send message</span>
          </Button>
        </div>
        
        {/* Templates Panel */}
        {showTemplates && userType === 'host' && (
          <div className="mt-2 p-3 bg-muted rounded border">
            <p className="text-sm text-muted-foreground mb-2">Message Templates</p>
            <div className="space-y-2">
              {templates.map((template: any) => (
                <div 
                  key={template.id}
                  className="p-2 bg-background rounded cursor-pointer hover:bg-accent"
                  onClick={() => handleTemplateSelect(template.id, {})}
                >
                  <p className="font-medium text-sm">{template.name}</p>
                  <p className="text-xs text-muted-foreground truncate">{template.content}</p>
                </div>
              ))}
            </div>
            <Button 
              variant="ghost" 
              size="sm" 
              className="mt-2"
              onClick={() => setShowTemplates(false)}
            >
              Close
            </Button>
          </div>
        )}
        
        {/* Connection Status */}
        <div className="flex items-center justify-between mt-2 text-xs text-muted-foreground">
          <div className="flex items-center gap-1">
            <div className={cn(
              "w-2 h-2 rounded-full",
              isConnected ? "bg-green-500" : "bg-red-500"
            )} />
            {isConnected ? 'Connected' : 'Reconnecting...'}
          </div>
          
          {typingUsers.length > 0 && (
            <span>{conversation.participant?.name} is typing...</span>
          )}
        </div>
      </div>
    </div>
  );
};