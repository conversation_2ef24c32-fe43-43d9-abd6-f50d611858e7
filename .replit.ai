# VillaWise Architecture Instructions

## Project Overview
VillaWise is a vacation rental application built with React, Express, and TypeScript following a feature-based modular architecture.

## Architecture Principles

### Feature-Based Organization
- Organize code by business domains in `/client/src/features/`
- Each feature contains: components, hooks, services, types
- Keep features self-contained and loosely coupled

### Component Structure
- `/client/src/components/ui/` - Reusable UI primitives (shadcn/ui)
- `/client/src/components/` - Shared app-level components
- `/client/src/features/*/components/` - Feature-specific components

### Technology Stack
- Frontend: React 18, TypeScript, Tailwind CSS, shadcn/ui
- Backend: Express.js, TypeScript
- State Management: TanStack Query for server state
- Routing: Wouter for client-side routing
- Internationalization: next-intl for Dutch/English support

### Code Standards

#### React Components
- Use functional components with hooks
- Prefer composition over inheritance
- Extract custom hooks for reusable logic
- Use TypeScript interfaces for props

#### Styling
- Use Tailwind CSS classes
- Follow Airbnb design patterns
- Implement responsive design (mobile-first)
- Use shadcn/ui components as building blocks

#### Internationalization
- All user-facing text must use translation keys
- Store translations in `/client/src/messages/[locale].json`
- Use `useTranslations()` hook from next-intl
- Support Dutch (nl) and English (en) locales

#### API Integration
- Use TanStack Query for data fetching
- Implement proper loading states
- Handle errors gracefully
- Use TypeScript for API response types

#### File Naming
- Use PascalCase for component files
- Use camelCase for utility files
- Use kebab-case for page routes
- Export components as named exports

#### Component Naming Best Practices
- Use simple, descriptive names without redundant prefixes
- Let folder structure provide context instead of component name prefixes
- Examples: Use `Layout.tsx` in `mobile/` folder, not `MobileDashboardLayout.tsx`
- Use import aliases when importing same-named components from different folders
- Pattern: `import { Layout as MobileLayout } from './mobile/Layout'`
- Avoid redundant naming like `DesktopDashboardContent.tsx` in a `desktop/` folder

### Folder Structure
```
client/src/
├── components/
│   ├── ui/           # Reusable UI components
│   ├── Header.tsx    # App-level components
│   └── Footer.tsx
├── features/
│   ├── home/         # Landing page features
│   ├── search/       # Search functionality
│   ├── auth/         # Authentication
│   └── inspiration/  # Travel inspiration
├── pages/            # Route components
├── lib/              # Utilities and configuration
├── hooks/            # Shared custom hooks
└── messages/         # Translation files
```

### Development Guidelines
- Write clean, readable code with meaningful names
- Add proper TypeScript types for all data structures
- Implement proper error boundaries
- Use consistent spacing and formatting
- Follow the existing code patterns in the project

### Performance
- Implement code splitting at route level
- Optimize images with proper sizing
- Use React.memo for expensive components
- Implement proper loading states

### Testing Considerations
- Structure code for testability
- Separate business logic from UI components
- Use dependency injection for services
- Keep components pure and predictable