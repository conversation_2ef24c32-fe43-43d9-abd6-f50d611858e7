import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';

export interface MessageTemplate {
  id: string;
  host_id: string;
  name: string;
  content: string;
  category: 'inquiry' | 'booking' | 'checkin' | 'checkout' | 'support' | 'custom';
  dynamic_fields: Record<string, string>;
  usage_count: number;
  is_active: boolean;
  created_at: string;
}

export interface CreateTemplateData {
  name: string;
  content: string;
  category: 'inquiry' | 'booking' | 'checkin' | 'checkout' | 'support' | 'custom';
  dynamicFields?: Record<string, string>;
}

/**
 * Hook for managing message templates (host only)
 */
export const useMessageTemplates = (category?: string) => {
  const queryClient = useQueryClient();
  
  // Get templates
  const { data: templates, isLoading, error } = useQuery({
    queryKey: ['/api/messaging/templates', { category }],
    queryFn: () => apiRequest('/api/messaging/templates', {
      method: 'GET'
    })
  });
  
  // Create template mutation
  const createTemplate = useMutation({
    mutationFn: (templateData: CreateTemplateData) =>
      apiRequest('/api/messaging/templates', {
        method: 'POST',
        body: JSON.stringify(templateData)
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/messaging/templates'] });
    }
  });
  
  // Update template mutation
  const updateTemplate = useMutation({
    mutationFn: ({ templateId, updates }: { templateId: string; updates: Partial<CreateTemplateData> }) =>
      apiRequest(`/api/messaging/templates/${templateId}`, {
        method: 'PUT',
        body: JSON.stringify(updates)
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/messaging/templates'] });
    }
  });
  
  // Delete template mutation
  const deleteTemplate = useMutation({
    mutationFn: (templateId: string) =>
      apiRequest(`/api/messaging/templates/${templateId}`, {
        method: 'DELETE'
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/messaging/templates'] });
    }
  });
  
  // Process template for preview
  const processTemplate = useMutation({
    mutationFn: ({ templateId, variables }: { templateId: string; variables: Record<string, string> }) =>
      apiRequest(`/api/messaging/templates/${templateId}/process`, {
        method: 'POST',
        body: JSON.stringify({ variables })
      })
  });
  
  return {
    // Data
    templates: templates?.data || [],
    isLoading,
    error,
    
    // Mutations
    createTemplate,
    updateTemplate,
    deleteTemplate,
    processTemplate,
    
    // Helper functions
    getTemplatesByCategory: (cat: string) => 
      (templates?.data || []).filter((t: MessageTemplate) => t.category === cat),
    
    getMostUsedTemplates: (limit = 5) =>
      (templates?.data || [])
        .sort((a: MessageTemplate, b: MessageTemplate) => b.usage_count - a.usage_count)
        .slice(0, limit)
  };
};

/**
 * Hook for default templates
 */
export const useDefaultTemplates = () => {
  const { data: defaultTemplates, isLoading } = useQuery({
    queryKey: ['/api/messaging/templates/default'],
    queryFn: () => apiRequest('/api/messaging/templates/default')
  });
  
  return {
    defaultTemplates: defaultTemplates?.data || [],
    isLoading
  };
};

/**
 * Template categories with labels
 */
export const TEMPLATE_CATEGORIES = [
  { value: 'inquiry', label: 'Property Inquiries', description: 'Responses to initial property questions' },
  { value: 'booking', label: 'Booking Confirmations', description: 'Messages for confirmed bookings' },
  { value: 'checkin', label: 'Check-in Instructions', description: 'Pre-arrival and check-in details' },
  { value: 'checkout', label: 'Check-out Reminders', description: 'Departure instructions and reminders' },
  { value: 'support', label: 'Guest Support', description: 'Responses to guest questions and issues' },
  { value: 'custom', label: 'Custom Templates', description: 'Personalized templates for specific needs' }
] as const;

/**
 * Common dynamic fields available in templates
 */
export const TEMPLATE_VARIABLES = [
  { key: 'guest_name', label: 'Guest Name', example: 'John Smith' },
  { key: 'property_name', label: 'Property Name', example: 'Villa Sunshine' },
  { key: 'check_in_date', label: 'Check-in Date', example: 'March 15, 2024' },
  { key: 'check_out_date', label: 'Check-out Date', example: 'March 22, 2024' },
  { key: 'booking_reference', label: 'Booking Reference', example: 'VW-12345' },
  { key: 'host_name', label: 'Host Name', example: 'Maria Garcia' },
  { key: 'property_address', label: 'Property Address', example: 'Calle del Sol 123, Valencia' },
  { key: 'guest_count', label: 'Number of Guests', example: '4 guests' },
  { key: 'total_nights', label: 'Total Nights', example: '7 nights' },
  { key: 'house_rules', label: 'House Rules', example: 'No smoking, No pets' }
] as const;