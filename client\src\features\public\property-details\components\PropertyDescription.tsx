import { useTranslations } from '@/lib/translations';
import { Wifi, Car, Waves, UtensilsCrossed, Snowflake, Thermometer, Tv, WashingMachine, Building, Trees } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import type { PropertyDetails } from '../types';

interface PropertyDescriptionProps {
  property: PropertyDetails;
}

export function PropertyDescription({ property }: PropertyDescriptionProps) {
  const t = useTranslations('propertyDetails');

  const amenityIcons = {
    wifi: Wifi,
    parking: Car,
    pool: Waves,
    kitchen: UtensilsCrossed,
    airConditioning: Snowflake,
    heating: Thermometer,
    tv: Tv,
    washer: WashingMachine,
    balcony: Building,
    garden: Trees,
  };

  const availableAmenities = Object.entries(property.amenities)
    .filter(([_, available]) => available)
    .map(([amenity]) => amenity as keyof typeof amenityIcons);

  return (
    <div className="space-y-4 lg:space-y-8">
      {/* Description */}
      <Card>
        <CardHeader className="px-4 lg:px-6">
          <CardTitle className="text-lg lg:text-xl">{t('aboutProperty')}</CardTitle>
        </CardHeader>
        <CardContent className="px-4 lg:px-6">
          <p className="text-gray-700 text-sm lg:text-base leading-relaxed whitespace-pre-line">
            {property.description}
          </p>
        </CardContent>
      </Card>

      {/* Amenities */}
      <Card>
        <CardHeader className="px-4 lg:px-6">
          <CardTitle className="text-lg lg:text-xl">{t('amenitiesTitle')}</CardTitle>
        </CardHeader>
        <CardContent className="px-4 lg:px-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 lg:gap-4">
            {availableAmenities.map((amenity) => {
              const IconComponent = amenityIcons[amenity];
              return (
                <div key={amenity} className="flex items-center space-x-2 min-w-0">
                  <IconComponent className="h-5 w-5 text-gray-600 flex-shrink-0" />
                  <span className="text-gray-800 text-sm truncate">{t(`amenities.${amenity}`)}</span>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* House Rules */}
      <Card>
        <CardHeader>
          <CardTitle>{t('houseRules')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {property.houseRules.map((rule, index) => (
              <div key={index} className="flex items-start space-x-3">
                <div className="w-1.5 h-1.5 bg-gray-400 rounded-full mt-2 flex-shrink-0" />
                <span className="text-gray-700">{rule}</span>
              </div>
            ))}
          </div>
          
          <div className="mt-6 pt-6 border-t space-y-2 text-sm text-gray-600">
            <div className="flex justify-between">
              <span>{t('checkIn')}</span>
              <span>{property.bookingRules.checkIn}</span>
            </div>
            <div className="flex justify-between">
              <span>{t('checkOut')}</span>
              <span>{property.bookingRules.checkOut}</span>
            </div>
            <div className="flex justify-between">
              <span>{t('minimumStay')}</span>
              <span>{property.bookingRules.minimumStay} {t('nights')}</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}