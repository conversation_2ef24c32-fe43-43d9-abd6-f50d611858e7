import { useTranslations } from '@/lib/translations';
import { Settings2, ArrowUpDown, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { PropertySearcher, type SearchParams } from '@/components/property-searcher';

interface SearchHeaderProps {
  searchParams: SearchParams;
  totalResults: number;
  filters: any;
  onFiltersChange: (filters: any) => void;
  onSearchRefine: (params: SearchParams) => void;
}

export const SearchHeader: React.FC<SearchHeaderProps> = ({
  searchParams,
  totalResults,
  filters,
  onFiltersChange,
  onSearchRefine
}) => {
  const t = useTranslations('searchPage');

  // Handle breadcrumb clicks
  const handleBreadcrumbClick = (location: string) => {
    onSearchRefine({
      ...searchParams,
      location: location
    });
  };

  // Handle filter tag removal
  const handleRemoveFilter = (filterType: 'location' | 'dateRange' | 'guests') => {
    const updatedParams = { ...searchParams };
    
    switch (filterType) {
      case 'location':
        updatedParams.location = '';
        break;
      case 'dateRange':
        updatedParams.dateRange = undefined;
        break;
      case 'guests':
        updatedParams.guests = { adults: 2, children: 0, infants: 0, pets: 0 };
        break;
    }
    
    onSearchRefine(updatedParams);
  };

  return (
    <div className="space-y-3">
      {/* Compact Search Bar */}
      <PropertySearcher 
        variant="compact" 
        initialValues={searchParams}
        onSearch={onSearchRefine}
        showHomeIcon={true}
      />

      {/* Breadcrumb with results count */}
      <div className="space-y-3">
        {/* Top row: Breadcrumb */}
        <nav className="flex items-center space-x-2 text-sm">
          <button 
            onClick={() => handleBreadcrumbClick('')}
            className="text-muted-foreground hover:text-foreground transition-colors cursor-pointer"
          >
            {t('breadcrumb.home')}
          </button>
          <span className="text-muted-foreground">›</span>
          <button 
            onClick={() => handleBreadcrumbClick('Costa Brava')}
            className="text-muted-foreground hover:text-foreground transition-colors cursor-pointer"
          >
            {t('breadcrumb.spain')}
          </button>
          {searchParams.location && (
            <>
              <span className="text-muted-foreground">›</span>
              <span className="text-foreground font-medium">{searchParams.location}</span>
            </>
          )}
          <span className="ml-4 px-2 py-1 bg-muted rounded-md text-xs font-medium">
            {totalResults.toLocaleString()} {t('title').toLowerCase()}
          </span>
        </nav>

        {/* Bottom row: Controls */}
        <div className="flex items-center justify-between gap-2">
          {/* Left side: Price toggle (hidden on mobile) */}
          <Button variant="ghost" size="sm" className="text-sm px-3 hidden sm:flex">
            {t('showPricesPerNight')}
          </Button>

          {/* Right side: Filters and Sort */}
          <div className="flex items-center gap-2 ml-auto">
            <Button variant="outline" size="sm" className="flex items-center space-x-1.5 px-2 sm:px-3">
              <Settings2 className="h-4 w-4" />
              <span className="hidden sm:inline">{t('filters')}</span>
            </Button>

            <Select defaultValue="recommended">
              <SelectTrigger className="w-auto min-w-[120px] sm:w-44">
                <div className="flex items-center space-x-1.5">
                  <ArrowUpDown className="h-4 w-4" />
                  <div className="flex-1 text-left">
                    <SelectValue />
                  </div>
                </div>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="recommended">{t('sortOptions.recommended')}</SelectItem>
                <SelectItem value="price-low">{t('sortOptions.priceLow')}</SelectItem>
                <SelectItem value="price-high">{t('sortOptions.priceHigh')}</SelectItem>
                <SelectItem value="rating">{t('sortOptions.rating')}</SelectItem>
                <SelectItem value="newest">{t('sortOptions.newest')}</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {/* Active filters with delete options */}
      <div className="flex items-center space-x-2 flex-wrap">
        {searchParams.location && (
          <Badge variant="secondary" className="px-3 py-1 flex items-center gap-1 group">
            <span>{searchParams.location}</span>
            <button
              onClick={() => handleRemoveFilter('location')}
              className="ml-1 hover:bg-muted-foreground/20 rounded-full p-0.5 transition-colors"
              aria-label="Remove location filter"
            >
              <X className="h-3 w-3" />
            </button>
          </Badge>
        )}
        {searchParams.dateRange && (
          <Badge variant="secondary" className="px-3 py-1 flex items-center gap-1 group">
            <span>{searchParams.dateRange.from.toLocaleDateString()} - {searchParams.dateRange.to.toLocaleDateString()}</span>
            <button
              onClick={() => handleRemoveFilter('dateRange')}
              className="ml-1 hover:bg-muted-foreground/20 rounded-full p-0.5 transition-colors"
              aria-label="Remove date filter"
            >
              <X className="h-3 w-3" />
            </button>
          </Badge>
        )}
        {(searchParams.guests.adults + searchParams.guests.children) !== 2 && (
          <Badge variant="secondary" className="px-3 py-1 flex items-center gap-1 group">
            <span>{searchParams.guests.adults + searchParams.guests.children} {searchParams.guests.adults + searchParams.guests.children === 1 ? t('guest') : t('guests')}</span>
            <button
              onClick={() => handleRemoveFilter('guests')}
              className="ml-1 hover:bg-muted-foreground/20 rounded-full p-0.5 transition-colors"
              aria-label="Remove guest filter"
            >
              <X className="h-3 w-3" />
            </button>
          </Badge>
        )}
      </div>
    </div>
  );
};