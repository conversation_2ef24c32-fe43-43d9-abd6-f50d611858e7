import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { useTranslations } from "@/lib/translations";
import {
  Building,
  Castle,
  Home,
  Mountain,
  TreePine,
  Waves,
} from "lucide-react";
import { PropertyWizardData } from "../../types/property";

interface PropertyTypeStepProps {
  data: Partial<PropertyWizardData>;
  onUpdate: (data: Partial<PropertyWizardData>) => void;
}

const PROPERTY_TYPES = [
  {
    id: "house",
    icon: Home,
    popular: true,
  },
  {
    id: "apartment",
    icon: Building,
    popular: true,
  },
  {
    id: "villa",
    icon: Castle,
    popular: true,
  },
  {
    id: "townhouse",
    icon: Home,
    popular: false,
  },
  {
    id: "cottage",
    icon: TreePine,
    popular: false,
  },
  {
    id: "penthouse",
    icon: Building,
    popular: false,
  },
  {
    id: "beachhouse",
    icon: Waves,
    popular: false,
  },
  {
    id: "mountain_cabin",
    icon: Mountain,
    popular: false,
  },
];

const SPACE_TYPES: Array<{
  id: "entire_place" | "private_room" | "shared_room";
  recommended: boolean;
}> = [
  {
    id: "entire_place",
    recommended: true,
  },
  {
    id: "private_room",
    recommended: false,
  },
  {
    id: "shared_room",
    recommended: false,
  },
];

export const PropertyTypeStep = ({ data, onUpdate }: PropertyTypeStepProps) => {
  const t = useTranslations("hostOnboarding.propertyType");

  const handlePropertyTypeSelect = (propertyType: string) => {
    onUpdate({ propertyType });
  };

  const handleSpaceTypeSelect = (
    spaceType: "entire_place" | "private_room" | "shared_room"
  ) => {
    onUpdate({ spaceType });
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          {t("title")}
        </h2>
        <p className="text-gray-600 dark:text-gray-400">{t("description")}</p>
      </div>

      {/* Property Type Selection */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          {t("propertyTypeTitle")}
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {PROPERTY_TYPES.map((type) => (
            <Card
              key={type.id}
              className={`cursor-pointer transition-all hover:shadow-md ${
                data.propertyType === type.id
                  ? "ring-2 ring-primary border-primary"
                  : "border-gray-200 dark:border-gray-700"
              }`}
              onClick={() => handlePropertyTypeSelect(type.id)}
            >
              <CardContent className="p-4">
                <div className="flex items-start justify-between mb-3">
                  <type.icon className="h-6 w-6 text-primary" />
                  {type.popular && (
                    <Badge variant="secondary" className="text-xs">
                      {t("labels.popular")}
                    </Badge>
                  )}
                </div>
                <h4 className="font-medium text-gray-900 dark:text-white mb-1">
                  {t(`types.${type.id}.name`)}
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {t(`types.${type.id}.description`)}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Space Type Selection */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          {t("spaceTypeTitle")}
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {SPACE_TYPES.map((type) => (
            <Card
              key={type.id}
              className={`cursor-pointer transition-all hover:shadow-md ${
                data.spaceType === type.id
                  ? "ring-2 ring-primary border-primary"
                  : "border-gray-200 dark:border-gray-700"
              }`}
              onClick={() => handleSpaceTypeSelect(type.id)}
            >
              <CardContent className="p-4">
                <div className="flex items-start justify-between mb-3">
                  <h4 className="font-medium text-gray-900 dark:text-white">
                    {t(`spaceTypes.${type.id}.name`)}
                  </h4>
                  {type.recommended && (
                    <Badge variant="default" className="text-xs bg-primary">
                      {t("labels.recommended")}
                    </Badge>
                  )}
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {t(`spaceTypes.${type.id}.description`)}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Illustration */}
      <div className="text-center py-8">
        <div className="inline-flex items-center justify-center w-32 h-32 bg-primary/10 rounded-full mb-4">
          <Home className="h-16 w-16 text-primary" />
        </div>
        <p className="text-sm text-gray-600 dark:text-gray-400 max-w-md mx-auto">
          {t("illustrationText")}
        </p>
      </div>
    </div>
  );
};
