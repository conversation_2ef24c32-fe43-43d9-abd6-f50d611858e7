-- Migration v002: GeoNames Location System
-- Creates comprehensive location search infrastructure with multi-language support

-- UP Migration
BEGIN;

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS pg_trgm;
CREATE EXTENSION IF NOT EXISTS unaccent;

-- Create geonames locations table
CREATE TABLE IF NOT EXISTS geonames_locations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    geonames_id BIGINT UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    ascii_name VARCHAR(255),
    country_code CHAR(2) NOT NULL,
    admin1_code VARCHAR(20),
    admin2_code VARCHAR(20),
    feature_class CHAR(1) NOT NULL,
    feature_code VARCHAR(10) NOT NULL,
    latitude DECIMAL(10,8) NOT NULL,
    longitude DECIMAL(11,8) NOT NULL,
    population BIGINT DEFAULT 0,
    elevation INTEGER,
    timezone VARCHAR(50),
    popularity_score INTEGER DEFAULT 50,
    property_count INTEGER DEFAULT 0,
    search_frequency INTEGER DEFAULT 0,
    last_searched_at TIMESTAMPTZ,
    destination_type VARCHAR(20) DEFAULT 'town',
    tourism_region VARCHAR(100),
    tourism_region_type VARCHAR(50),
    is_tourism_region BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create location names table for multi-language support
CREATE TABLE IF NOT EXISTS geonames_location_names (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    location_id UUID REFERENCES geonames_locations(id) ON DELETE CASCADE,
    geonames_id BIGINT REFERENCES geonames_locations(geonames_id) ON DELETE CASCADE,
    language_code VARCHAR(7) NOT NULL,
    name VARCHAR(400) NOT NULL,
    is_preferred BOOLEAN DEFAULT FALSE,
    is_short BOOLEAN DEFAULT FALSE,
    is_colloquial BOOLEAN DEFAULT FALSE,
    is_historic BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for optimal performance
CREATE INDEX IF NOT EXISTS idx_geonames_locations_geonames_id ON geonames_locations(geonames_id);
CREATE INDEX IF NOT EXISTS idx_geonames_locations_country ON geonames_locations(country_code);
CREATE INDEX IF NOT EXISTS idx_geonames_locations_feature ON geonames_locations(feature_class, feature_code);
CREATE INDEX IF NOT EXISTS idx_geonames_locations_population ON geonames_locations(population DESC);
CREATE INDEX IF NOT EXISTS idx_geonames_locations_popularity ON geonames_locations(popularity_score DESC);
CREATE INDEX IF NOT EXISTS idx_geonames_locations_search_frequency ON geonames_locations(search_frequency DESC);
CREATE INDEX IF NOT EXISTS idx_geonames_locations_composite_score ON geonames_locations(popularity_score DESC, property_count DESC, search_frequency DESC);

-- Trigram indexes for fuzzy search
CREATE INDEX IF NOT EXISTS idx_geonames_locations_name_trgm ON geonames_locations USING GIN(LOWER(name) gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_geonames_locations_ascii_name_trgm ON geonames_locations USING GIN(LOWER(ascii_name) gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_geonames_locations_tourism_region_trgm ON geonames_locations USING GIN(LOWER(tourism_region) gin_trgm_ops);

-- Indexes for location names
CREATE INDEX IF NOT EXISTS idx_geonames_location_names_geonames_id ON geonames_location_names(geonames_id);
CREATE INDEX IF NOT EXISTS idx_geonames_location_names_location_id ON geonames_location_names(location_id);
CREATE INDEX IF NOT EXISTS idx_geonames_location_names_language ON geonames_location_names(language_code);
CREATE INDEX IF NOT EXISTS idx_geonames_location_names_name_trgm ON geonames_location_names USING GIN(LOWER(name) gin_trgm_ops);

-- Create generic fuzzy search function (country-agnostic)
CREATE OR REPLACE FUNCTION fuzzy_location_search(
    search_query TEXT,
    country_filter TEXT DEFAULT NULL,
    min_similarity FLOAT DEFAULT 0.25,
    result_limit INT DEFAULT 10
) RETURNS TABLE (
    id UUID,
    geonames_id BIGINT,
    name TEXT,
    ascii_name TEXT,
    country_code TEXT,
    admin1_code TEXT,
    admin2_code TEXT,
    feature_class TEXT,
    feature_code TEXT,
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    population BIGINT,
    elevation INTEGER,
    timezone TEXT,
    popularity_score INTEGER,
    property_count INTEGER,
    tourism_region TEXT,
    tourism_region_type TEXT,
    is_tourism_region BOOLEAN,
    similarity_score FLOAT,
    match_type TEXT
) LANGUAGE SQL AS $$
WITH similarity_search AS (
    SELECT 
        gl.*,
        GREATEST(
            similarity(LOWER(gl.name), LOWER(search_query)),
            similarity(LOWER(gl.ascii_name), LOWER(search_query)),
            CASE 
                WHEN gl.tourism_region IS NOT NULL 
                THEN similarity(LOWER(gl.tourism_region), LOWER(search_query))
                ELSE 0.0
            END
        ) as sim_score,
        CASE
            WHEN LOWER(gl.name) = LOWER(search_query) OR LOWER(gl.ascii_name) = LOWER(search_query) THEN 'exact'
            WHEN LOWER(gl.name) LIKE LOWER(search_query) || '%' OR LOWER(gl.ascii_name) LIKE LOWER(search_query) || '%' THEN 'prefix'
            WHEN similarity(LOWER(gl.name), LOWER(search_query)) >= min_similarity 
                 OR similarity(LOWER(gl.ascii_name), LOWER(search_query)) >= min_similarity THEN 'fuzzy'
            ELSE 'none'
        END as match_category
    FROM geonames_locations gl
    WHERE (country_filter IS NULL OR gl.country_code = UPPER(country_filter))
        AND (
            similarity(LOWER(gl.name), LOWER(search_query)) >= min_similarity
            OR similarity(LOWER(gl.ascii_name), LOWER(search_query)) >= min_similarity
            OR (gl.tourism_region IS NOT NULL AND similarity(LOWER(gl.tourism_region), LOWER(search_query)) >= min_similarity)
            OR LOWER(gl.name) LIKE '%' || LOWER(search_query) || '%'
            OR LOWER(gl.ascii_name) LIKE '%' || LOWER(search_query) || '%'
        )
)
SELECT 
    ss.id,
    ss.geonames_id,
    ss.name,
    ss.ascii_name,
    ss.country_code,
    ss.admin1_code,
    ss.admin2_code,
    ss.feature_class,
    ss.feature_code,
    ss.latitude,
    ss.longitude,
    ss.population,
    ss.elevation,
    ss.timezone,
    ss.popularity_score,
    ss.property_count,
    ss.tourism_region,
    ss.tourism_region_type,
    ss.is_tourism_region,
    ss.sim_score as similarity_score,
    ss.match_category as match_type
FROM similarity_search ss
WHERE ss.match_category != 'none'
ORDER BY 
    CASE ss.match_category
        WHEN 'exact' THEN 1
        WHEN 'prefix' THEN 2
        WHEN 'fuzzy' THEN 3
        ELSE 4
    END,
    ss.sim_score DESC,
    ss.popularity_score DESC,
    ss.population DESC
LIMIT result_limit;
$$;

-- Create multilingual fuzzy search function
CREATE OR REPLACE FUNCTION fuzzy_location_search_i18n(
    search_query TEXT,
    language_code TEXT DEFAULT 'en',
    country_filter TEXT DEFAULT NULL,
    min_similarity FLOAT DEFAULT 0.25,
    result_limit INT DEFAULT 10
) RETURNS TABLE (
    id UUID,
    geonames_id BIGINT,
    name TEXT,
    display_name TEXT,
    ascii_name TEXT,
    country_code TEXT,
    admin1_code TEXT,
    admin2_code TEXT,
    feature_class TEXT,
    feature_code TEXT,
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    population BIGINT,
    elevation INTEGER,
    timezone TEXT,
    popularity_score INTEGER,
    property_count INTEGER,
    tourism_region TEXT,
    tourism_region_type TEXT,
    is_tourism_region BOOLEAN,
    similarity_score FLOAT,
    match_type TEXT,
    coordinates JSONB
) LANGUAGE SQL AS $$
WITH location_matches AS (
    SELECT 
        gl.*,
        -- Check for matches in alternate names
        COALESCE(
            (SELECT gln.name 
             FROM geonames_location_names gln 
             WHERE gln.geonames_id = gl.geonames_id 
             AND gln.language_code = language_code
             AND gln.is_preferred = true
             LIMIT 1),
            gl.name
        ) as localized_name,
        GREATEST(
            similarity(LOWER(gl.name), LOWER(search_query)),
            similarity(LOWER(gl.ascii_name), LOWER(search_query)),
            COALESCE(
                (SELECT MAX(similarity(LOWER(gln.name), LOWER(search_query)))
                 FROM geonames_location_names gln 
                 WHERE gln.geonames_id = gl.geonames_id),
                0.0
            ),
            CASE 
                WHEN gl.tourism_region IS NOT NULL 
                THEN similarity(LOWER(gl.tourism_region), LOWER(search_query))
                ELSE 0.0
            END
        ) as best_similarity,
        CASE
            WHEN LOWER(gl.name) = LOWER(search_query) OR LOWER(gl.ascii_name) = LOWER(search_query) THEN 'exact'
            WHEN EXISTS(
                SELECT 1 FROM geonames_location_names gln 
                WHERE gln.geonames_id = gl.geonames_id 
                AND LOWER(gln.name) = LOWER(search_query)
            ) THEN 'exact'
            WHEN LOWER(gl.name) LIKE LOWER(search_query) || '%' OR LOWER(gl.ascii_name) LIKE LOWER(search_query) || '%' THEN 'prefix'
            WHEN EXISTS(
                SELECT 1 FROM geonames_location_names gln 
                WHERE gln.geonames_id = gl.geonames_id 
                AND LOWER(gln.name) LIKE LOWER(search_query) || '%'
            ) THEN 'prefix'
            ELSE 'fuzzy'
        END as match_category
    FROM geonames_locations gl
    WHERE (country_filter IS NULL OR gl.country_code = UPPER(country_filter))
        AND (
            similarity(LOWER(gl.name), LOWER(search_query)) >= min_similarity
            OR similarity(LOWER(gl.ascii_name), LOWER(search_query)) >= min_similarity
            OR EXISTS(
                SELECT 1 FROM geonames_location_names gln 
                WHERE gln.geonames_id = gl.geonames_id 
                AND similarity(LOWER(gln.name), LOWER(search_query)) >= min_similarity
            )
            OR (gl.tourism_region IS NOT NULL AND similarity(LOWER(gl.tourism_region), LOWER(search_query)) >= min_similarity)
            OR LOWER(gl.name) LIKE '%' || LOWER(search_query) || '%'
            OR LOWER(gl.ascii_name) LIKE '%' || LOWER(search_query) || '%'
        )
)
SELECT 
    lm.id,
    lm.geonames_id,
    lm.name,
    lm.localized_name as display_name,
    lm.ascii_name,
    lm.country_code,
    lm.admin1_code,
    lm.admin2_code,
    lm.feature_class,
    lm.feature_code,
    lm.latitude,
    lm.longitude,
    lm.population,
    lm.elevation,
    lm.timezone,
    lm.popularity_score,
    lm.property_count,
    lm.tourism_region,
    lm.tourism_region_type,
    lm.is_tourism_region,
    lm.best_similarity as similarity_score,
    lm.match_category as match_type,
    jsonb_build_object('lat', lm.latitude, 'lng', lm.longitude) as coordinates
FROM location_matches lm
ORDER BY 
    CASE lm.match_category
        WHEN 'exact' THEN 1
        WHEN 'prefix' THEN 2
        WHEN 'fuzzy' THEN 3
        ELSE 4
    END,
    lm.best_similarity DESC,
    lm.popularity_score DESC,
    lm.population DESC
LIMIT result_limit;
$$;

-- Create function to update search frequency
CREATE OR REPLACE FUNCTION update_search_frequency(location_name TEXT)
RETURNS VOID
LANGUAGE plpgsql
AS $$
BEGIN
    UPDATE geonames_locations 
    SET 
        search_frequency = COALESCE(search_frequency, 0) + 1,
        last_searched_at = NOW()
    WHERE LOWER(name) = LOWER(location_name)
       OR similarity(name, location_name) >= 0.8;
END;
$$;

-- Enable Row Level Security
ALTER TABLE geonames_locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE geonames_location_names ENABLE ROW LEVEL SECURITY;

-- Allow public read access for search functionality
CREATE POLICY "Allow public read access to geonames_locations" 
  ON geonames_locations FOR SELECT 
  USING (true);

CREATE POLICY "Allow public read access to geonames_location_names" 
  ON geonames_location_names FOR SELECT 
  USING (true);

-- Add comments for documentation
COMMENT ON TABLE geonames_locations IS 'GeoNames location data with enhanced search and tourism features';
COMMENT ON TABLE geonames_location_names IS 'Multi-language names for GeoNames locations';
COMMENT ON FUNCTION fuzzy_location_search IS 'Generic fuzzy search for locations with country filtering';
COMMENT ON FUNCTION fuzzy_location_search_i18n IS 'Multilingual fuzzy search with localized display names';
COMMENT ON FUNCTION update_search_frequency IS 'Tracks search frequency for improving future search rankings';

COMMIT;

-- DOWN Migration (Rollback)
-- Note: This will remove all geonames tables and functions

-- BEGIN;
-- DROP FUNCTION IF EXISTS update_search_frequency(TEXT);
-- DROP FUNCTION IF EXISTS fuzzy_location_search_i18n(TEXT, TEXT, TEXT, FLOAT, INT);
-- DROP FUNCTION IF EXISTS fuzzy_location_search(TEXT, TEXT, FLOAT, INT);
-- DROP TABLE IF EXISTS geonames_location_names CASCADE;
-- DROP TABLE IF EXISTS geonames_locations CASCADE;
-- COMMIT;