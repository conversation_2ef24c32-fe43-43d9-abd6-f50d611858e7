import { apiRequest } from "@/lib/queryClient";
import { PropertyFormData } from "../types";

export const propertyManagementService = {
  async createProperty(data: PropertyFormData) {
    return apiRequest('/api/properties', {
      method: 'POST',
      body: JSON.stringify(data)
    });
  },

  async updateProperty(id: number, data: Partial<PropertyFormData>) {
    return apiRequest(`/api/properties/${id}`, {
      method: 'PATCH',
      body: JSON.stringify(data)
    });
  },

  async deleteProperty(id: number) {
    return apiRequest(`/api/properties/${id}`, {
      method: 'DELETE'
    });
  },

  async getOwnerProperties() {
    return apiRequest('/api/user/properties');
  },

  async getOwnerBookings() {
    return apiRequest('/api/host/bookings');
  },

  async getOwnerAnalytics() {
    return apiRequest('/api/host/analytics');
  }
};