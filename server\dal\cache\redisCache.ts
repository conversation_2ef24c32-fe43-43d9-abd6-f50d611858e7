import { Redis } from "@upstash/redis";

export interface CacheBackend {
  get<T = unknown>(key: string): Promise<T | null>;
  set<T = unknown>(key: string, value: T, ttl?: number): Promise<void>;
  delete(key: string): Promise<void>;
  clear?(): Promise<void>;
  invalidatePattern?(pattern: string): Promise<void>;
  getStats?(): Promise<Record<string, unknown>>;
}

export class UpstashRedisCache implements CacheBackend {
  private redis: Redis;
  private isConfigured: boolean;

  constructor() {
    const url = process.env.UPSTASH_REDIS_REST_URL;
    const token = process.env.UPSTASH_REDIS_REST_TOKEN;
    
    console.log('[REDIS] Cache initialization starting...');
    console.log('[REDIS] Environment check:');
    console.log(`  - UPSTASH_REDIS_REST_URL: ${url ? 'Present (' + url.slice(0, 30) + '...)' : 'Missing'}`);
    console.log(`  - UPSTASH_REDIS_REST_TOKEN: ${token ? 'Present (' + token.slice(0, 20) + '...)' : 'Missing'}`);
    
    this.isConfigured = Boolean(url && token);
    
    if (this.isConfigured) {
      this.redis = new Redis({
        url: url!,
        token: token!,
      });
      console.log('[REDIS] ✅ Upstash Redis configured successfully');
    } else {
      console.warn('[REDIS] ⚠️  Upstash Redis credentials not configured. Add UPSTASH_REDIS_REST_URL and UPSTASH_REDIS_REST_TOKEN for Redis caching.');
      // Create dummy redis instance to prevent errors
      this.redis = null as any;
    }
  }

  async get<T = unknown>(key: string): Promise<T | null> {
    if (!this.isConfigured) {
      console.log(`[REDIS] Cache get skipped (not configured): ${key}`);
      return null;
    }
    
    try {
      console.log(`[REDIS] 🔍 Cache GET: ${key}`);
      const data = await this.redis.get(key);
      if (!data) {
        console.log(`[REDIS] ❌ Cache MISS: ${key}`);
        return null;
      }

      if (typeof data === "string") {
        try {
          const parsed = JSON.parse(data) as T;
          console.log(`[REDIS] ✅ Cache HIT: ${key} (parsed JSON)`);
          return parsed;
        } catch {
          console.log(`[REDIS] ⚠️  Cache HIT but JSON parse failed: ${key}`);
          return null; // Return null if JSON parsing fails
        }
      }

      console.log(`[REDIS] ✅ Cache HIT: ${key} (direct value)`);
      return data as T;
    } catch (error) {
      console.error(`[REDIS] ❌ Cache GET error for ${key}:`, error);
      return null;
    }
  }

  async set<T = unknown>(
    key: string,
    value: T,
    ttl: number = 3600
  ): Promise<void> {
    if (!this.isConfigured) {
      console.log(`[REDIS] Cache set skipped (not configured): ${key}`);
      return;
    }
    
    try {
      console.log(`[REDIS] 💾 Cache SET: ${key} (TTL: ${ttl}s)`);
      await this.redis.set(key, JSON.stringify(value), { ex: ttl });
      console.log(`[REDIS] ✅ Cache SET successful: ${key}`);
    } catch (error) {
      console.error(`[REDIS] ❌ Cache SET error for ${key}:`, error);
    }
  }

  async delete(key: string): Promise<void> {
    if (!this.isConfigured) return;
    
    try {
      await this.redis.del(key);
    } catch (error) {
      console.error('Redis delete error:', error);
    }
  }

  async clear(): Promise<void> {
    if (!this.isConfigured) return;
    
    try {
      // Not recommended in production, but for dev/testing:
      const keys = await this.redis.keys("*");
      if (keys.length) await this.redis.del(...keys);
    } catch (error) {
      console.error('Redis clear error:', error);
    }
  }

  async invalidatePattern(pattern: string): Promise<void> {
    if (!this.isConfigured) return;
    
    try {
      const regex = new RegExp(pattern.replace("*", ".*"));
      const keys = await this.redis.keys("*");
      const toDelete = keys.filter((k: string) => regex.test(k));
      if (toDelete.length) await this.redis.del(...toDelete);
    } catch (error) {
      console.error('Redis invalidatePattern error:', error);
    }
  }

  async getStats(): Promise<Record<string, unknown>> {
    if (!this.isConfigured) return { keyCount: 0, configured: false };
    
    try {
      // Upstash REST API does not expose memory stats, so return key count
      const keys = await this.redis.keys("*");
      return { keyCount: keys.length, configured: true };
    } catch (error) {
      console.error('Redis getStats error:', error);
      return { keyCount: 0, configured: false, error: String(error) };
    }
  }
}

// Singleton instance
export const redisCache = new UpstashRedisCache();

// Pluggable cache selector (memory or redis)
import { memoryCache } from "./memoryCache";
// Adapter for synchronous memoryCache to async CacheBackend
const memoryCacheAdapter: CacheBackend = {
  async get<T = unknown>(key: string): Promise<T | null> {
    console.log(`[MEMORY] 🔍 Cache GET: ${key}`);
    const result = memoryCache.get(key) as T | null;
    if (result) {
      console.log(`[MEMORY] ✅ Cache HIT: ${key}`);
    } else {
      console.log(`[MEMORY] ❌ Cache MISS: ${key}`);
    }
    return result;
  },
  async set<T = unknown>(key: string, value: T, ttl = 3600): Promise<void> {
    console.log(`[MEMORY] 💾 Cache SET: ${key} (TTL: ${ttl}s)`);
    memoryCache.set(key, value, ttl);
    console.log(`[MEMORY] ✅ Cache SET successful: ${key}`);
  },
  async delete(key: string): Promise<void> {
    console.log(`[MEMORY] 🗑️ Cache DELETE: ${key}`);
    memoryCache.delete(key);
  },
  async clear(): Promise<void> {
    console.log(`[MEMORY] 🧹 Cache CLEAR: all entries`);
    memoryCache.clear();
  },
  async invalidatePattern(pattern: string): Promise<void> {
    console.log(`[MEMORY] 🔄 Cache INVALIDATE pattern: ${pattern}`);
    memoryCache.invalidatePattern(pattern);
  },
  async getStats(): Promise<Record<string, unknown>> {
    const stats = memoryCache.getStats();
    console.log(`[MEMORY] 📊 Cache STATS:`, stats);
    return stats;
  },
};

// Cache backend selection with detailed logging
console.log('[CACHE] Backend selection starting...');
console.log('[CACHE] Environment variables:');
console.log(`  - USE_REDIS_CACHE: "${process.env.USE_REDIS_CACHE}"`);
console.log(`  - NODE_ENV: "${process.env.NODE_ENV}"`);

const useRedisCache = process.env.USE_REDIS_CACHE === "true";
console.log(`[CACHE] Cache backend decision: ${useRedisCache ? 'Redis' : 'Memory'}`);

export const cacheBackend: CacheBackend = useRedisCache ? redisCache : memoryCacheAdapter;

// Log which backend was selected
if (useRedisCache) {
  console.log('[CACHE] ✅ Redis cache backend selected');
} else {
  console.log('[CACHE] 📝 Memory cache backend selected (Redis disabled)');
}

// Cache wrapper for DAL methods (async)
export function withCacheAsync<T>(key: string, ttl: number = 3600) {
  return function (
    target: object,
    propertyName: string,
    descriptor: TypedPropertyDescriptor<(...args: unknown[]) => Promise<T>>
  ): TypedPropertyDescriptor<(...args: unknown[]) => Promise<T>> {
    const originalMethod = descriptor.value!;
    descriptor.value = async function (...args: unknown[]): Promise<T> {
      const cacheKey = `${key}:${JSON.stringify(args)}`;
      const cached = await cacheBackend.get<T>(cacheKey);
      if (cached !== null) return cached;
      const result = await originalMethod.apply(this, args);
      await cacheBackend.set(cacheKey, result, ttl);
      return result;
    };
    return descriptor;
  };
}
