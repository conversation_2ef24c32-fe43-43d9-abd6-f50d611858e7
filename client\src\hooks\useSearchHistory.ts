
import { useState, useEffect } from 'react';
// Location type definition since @/data/locations doesn't exist
interface Location {
  id: string;
  name: string;
  type: 'city' | 'region' | 'country' | 'property';
  country?: string;
  region?: string;
}

const STORAGE_KEY = 'villawise_search_history';
const MAX_HISTORY_ITEMS = 5;

export interface SearchHistoryItem {
  id: string;
  name: string;
  type: Location['type'];
  country: string;
  region?: string;
  searchedAt: number;
  // Complete search state (optional for backward compatibility)
  checkIn?: string;
  checkOut?: string;
  guests?: {
    adults: number;
    children: number;
    infants: number;
    pets: number;
  };
}

export const useSearchHistory = () => {
  const [searchHistory, setSearchHistory] = useState<SearchHistoryItem[]>([]);

  useEffect(() => {
    // Fetch search history from server
    fetch('/api/user/search-history')
      .then(response => response.json())
      .then(data => setSearchHistory(data))
      .catch(error => {
        console.error('Failed to fetch search history:', error);
        // Fallback to localStorage if server fails
        const stored = localStorage.getItem(STORAGE_KEY);
        if (stored) {
          try {
            const parsed = JSON.parse(stored);
            setSearchHistory(parsed);
          } catch (parseError) {
            console.error('Failed to parse local search history:', parseError);
          }
        }
      });
  }, []);



  const addToHistory = async (location: Location, searchState?: {
    checkIn?: string;
    checkOut?: string;
    guests?: {
      adults: number;
      children: number;
      infants: number;
      pets: number;
    };
  }) => {
    const historyItem: SearchHistoryItem = {
      id: location.id,
      name: location.name,
      type: location.type,
      country: location.country || 'Spain',
      region: location.region,
      searchedAt: Date.now(),
      checkIn: searchState?.checkIn,
      checkOut: searchState?.checkOut,
      guests: searchState?.guests
    };

    try {
      // Save to server
      await fetch('/api/user/search-history', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          location: location.name,
          checkIn: searchState?.checkIn,
          checkOut: searchState?.checkOut,
          guests: searchState?.guests
        })
      });

      // Update local state
      setSearchHistory(prev => {
        const filtered = prev.filter(item => item.id !== location.id);
        const updated = [historyItem, ...filtered].slice(0, MAX_HISTORY_ITEMS);
        
        // Also save to localStorage as backup
        localStorage.setItem(STORAGE_KEY, JSON.stringify(updated));
        return updated;
      });
    } catch (error) {
      console.error('Failed to save search history to server:', error);
      
      // Fallback to localStorage only
      setSearchHistory(prev => {
        const filtered = prev.filter(item => item.id !== location.id);
        const updated = [historyItem, ...filtered].slice(0, MAX_HISTORY_ITEMS);
        localStorage.setItem(STORAGE_KEY, JSON.stringify(updated));
        return updated;
      });
    }
  };

  const clearHistory = async () => {
    try {
      // Clear from server
      await fetch('/api/user/search-history', {
        method: 'DELETE',
      });

      // Update local state
      setSearchHistory([]);
    } catch (error) {
      console.error('Failed to clear search history on server:', error);
      // Fallback to localStorage
      setSearchHistory([]);
      localStorage.removeItem(STORAGE_KEY);
    }
  };

  return {
    searchHistory,
    addToHistory,
    clearHistory
  };
};
