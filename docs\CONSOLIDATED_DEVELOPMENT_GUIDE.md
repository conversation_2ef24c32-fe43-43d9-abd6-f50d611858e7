# VillaWise Development Guide

## Critical Data Management Rules

### ⚠️ NEVER COMMIT LARGE FILES

The following file types are **STRICTLY PROHIBITED** from being committed to Git:

#### Prohibited File Types
- **GeoNames data files:** `*.txt`, `*.zip`, `alternateNames*.txt`
- **Database dumps:** `*.dump`, `*.sql.gz`  
- **Large datasets:** `*.csv`, `*.tsv` (over 10MB)
- **Binary data:** Archives, compressed files over 1MB
- **Temporary files:** Cache files, processing artifacts

#### Protected Directories
- `data/` - **ALL FILES EXCLUDED** from Git (except `.gitkeep`)
- Automatic downloads go to `data/` directory
- Never manually place large files in root directory

### Data Storage Architecture

```
project-root/
├── data/                    # ← EXCLUDED FROM GIT
│   ├── .gitkeep            # ← Only file tracked
│   ├── README.md           # ← Documentation only
│   ├── ES.txt              # ← GeoNames Spanish data (~24MB)
│   ├── IT.txt              # ← GeoNames Italian data (~18MB) 
│   ├── alternateNamesV2.txt # ← Multilingual names (~724MB)
│   └── *.zip               # ← Downloaded archives
└── server/                 # ← Source code (tracked)
```

## Mandatory Development Practices

### 🚨 CRITICAL REQUIREMENTS - NEVER SKIP THESE:

1. **TypeScript Check (MANDATORY)**
   - **ALWAYS run:** `timeout 30s npx tsc --noEmit --skipLibCheck`
   - **MUST be executed before ANY file changes are committed**
   - **If TypeScript check fails, fix ALL errors before proceeding**

2. **Translation Implementation (MANDATORY)**
   - **ALWAYS implement translations for ALL new components or pages**
   - **Use existing translation mechanism: `useTranslations(namespace)` hook**
   - **Add translation keys to `server/controllers/shared/translationController.ts`**
   - **Support both English (en) and Dutch (nl) languages**

3. **Testing Requirements**
   - **Test ALL changes thoroughly before marking complete**
   - **Verify server starts without errors**
   - **Check health endpoint responds:** `curl http://localhost:5000/api/health`

## Development Workflow

### Before Making Changes
1. **Check .gitignore coverage** for any new file types
2. **Verify data files** are in `data/` directory  
3. **Run `git status`** to ensure no large files staged
4. **Use `git ls-files --others --exclude-standard`** to check untracked files

### TypeScript Development
```bash
# Run TypeScript check
timeout 30s npx tsc --noEmit --skipLibCheck

# Alternative check script
node scripts/test/typecheck.js

# Fix compilation errors before committing
npm run build
```

### Translation Implementation
```typescript
// Use in components
const t = useTranslations('componentName');
return <h1>{t('title')}</h1>;

// Add to translationController.ts 
export const translations = {
  en: {
    componentName: {
      title: "Welcome",
      subtitle: "Get started with {name}"
    }
  },
  nl: {
    componentName: {
      title: "Welkom", 
      subtitle: "Ga aan de slag met {name}"
    }
  }
};
```

### GeoNames Data Handling
- Data automatically downloads to `data/` directory
- Never move GeoNames files to tracked directories
- Use `GeoNamesDownloader` service for all data operations
- Files are cached and reused (24-hour expiry)

## Architecture Guidelines

### Feature-Based Structure
```
client/src/features/
├── public/        # Public features (no auth required)
│   ├── home/
│   ├── search/
│   ├── property-details/
│   └── inspiration/
├── auth/          # Authentication system
├── guest/         # Protected guest features
├── host/          # Protected host features
└── admin/         # Protected admin features
```

### Component Organization
- Use feature-based modules with dedicated components, hooks, and services
- Follow mobile-first responsive design patterns
- Implement proper TypeScript typing throughout
- Use shadcn/ui + Radix UI components

### API Development
```typescript
// Backend route structure
server/
├── controllers/
│   ├── guest/     # Guest-specific endpoints
│   ├── host/      # Host-specific endpoints
│   ├── public/    # Public endpoints (/api/public/*)
│   └── shared/    # Shared endpoints (auth, translations)
├── dal/           # Data Access Layer
├── routes/        # Express route definitions
└── middleware/    # Authentication and validation
```

## Error Handling & Debugging

### Common Development Issues

1. **TypeScript Compilation Errors**
   ```bash
   # Check for type errors
   npx tsc --noEmit --skipLibCheck
   
   # Fix common issues
   - Missing type definitions
   - Incorrect interface implementations
   - Null safety violations
   ```

2. **Translation Display Issues**
   ```bash
   # Clear translation cache
   localStorage.removeItem('villawise_translations_cache')
   
   # Verify namespace registration
   # Check translationController.ts for missing keys
   ```

3. **Authentication Problems**
   ```bash
   # Test auth endpoint
   curl -H "Authorization: Bearer token" http://localhost:5000/api/auth/me
   
   # Check Supabase configuration
   # Verify OAuth redirect URLs
   ```

4. **Database Connection Issues**
   ```bash
   # Test database health
   curl http://localhost:5000/api/health
   
   # Check environment variables
   echo $SUPABASE_URL
   echo $SUPABASE_ANON_KEY
   ```

### Development Commands

```bash
# Start development
npm run dev                    # Port 5000

# Type checking  
timeout 30s npx tsc --noEmit --skipLibCheck

# Production build
node scripts/build/build-railway.js

# Database operations
# Located in /database/utils/

# Health check
curl http://localhost:5000/api/health
```

## Testing Strategy

### Manual Testing Checklist
- [ ] TypeScript compilation passes
- [ ] Server starts without errors
- [ ] Authentication flow works
- [ ] All translations display correctly (EN/NL)
- [ ] Mobile responsive design functions
- [ ] API endpoints return expected data
- [ ] Cache operations work correctly

### Performance Testing
- Monitor Supabase database performance
- Test with Redis cache enabled/disabled
- Verify map clustering with 100+ properties
- Check translation loading times

## Emergency Data Cleanup

If large files accidentally committed:

```bash
# Check current repository size
git count-objects -vH

# Remove large files from Git history (DESTRUCTIVE)
git filter-branch --force --index-filter \
'git rm --cached --ignore-unmatch data/*.txt data/*.zip' \
--prune-empty --tag-name-filter cat -- --all

# Force push (only if absolutely necessary)
git push --force --all
git push --force --tags
```

## Size Monitoring

### Repository Size Limits
- **Total repository:** < 1GB (GitHub recommended)
- **Individual files:** < 100MB (GitHub hard limit)
- **Data directory:** Excluded, can be unlimited

### Regular Checks
```bash
# Check repository size
du -sh .git
git count-objects -vH

# List largest files
git ls-files | xargs ls -la | sort -k5 -rn | head -10

# Check for large untracked files
find . -size +10M -not -path "./.git/*" -not -path "./node_modules/*"
```

## Code Quality Standards

### TypeScript Requirements
- No `any` types allowed
- Proper interface definitions for all data structures
- Null safety throughout codebase
- Generic types where appropriate

### React Best Practices
- Functional components with hooks
- Proper dependency arrays in useEffect
- Memoization for expensive computations
- Error boundaries for production

### Performance Considerations
- Lazy loading for dashboard modules
- Image optimization and lazy loading
- Database query optimization
- Cache strategies (Redis + Memory fallback)

## Security Best Practices

- Supabase Row Level Security (RLS) enabled
- OAuth authentication with secure JWT tokens
- Input validation with Zod schemas
- Environment variables never exposed
- Regular security dependency updates

## Documentation Policy

### Documentation Rules
- **ALWAYS ask permission before creating/updating documentation**
- **All new documentation must be added to `/docs/` folder**
- **All deployment files must be in `/deployment/railway/` folder**
- Update replit.md with architectural changes immediately

### Documentation Structure
```
docs/
├── CONSOLIDATED_DEPLOYMENT_GUIDE.md  # This file
├── CONSOLIDATED_DEVELOPMENT_GUIDE.md # Development practices
├── API_REFERENCE.md                  # API documentation
├── TRANSLATION_SYSTEM_COMPREHENSIVE_GUIDE.md
├── GEONAMES_IMPLEMENTATION_GUIDE.md
└── architecture/                     # Architecture docs
```

This development guide provides comprehensive instructions for developing VillaWise with all critical requirements, best practices, and troubleshooting procedures.