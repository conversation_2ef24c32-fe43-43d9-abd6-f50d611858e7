import { useState, useEffect } from "react";
import { useTranslations } from "@/lib/translations";
import { useLocation } from "wouter";
import { Search, Home, MapPin, Calendar, Users, ChevronRight, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { useIsMobile } from "@/hooks/use-mobile";
import { useDebounce } from "@/hooks/useDebounce";
import { LocationAutocomplete } from "./LocationAutocomplete";
import { DateRangePicker } from "./DateRangePicker";
import { GuestSelector } from "./GuestSelector";
import { RecentSearches } from "./RecentSearches";

import { useSearchPersistence } from "@/features/guest/search/hooks";
import { useSearchHistory } from "@/hooks/useSearchHistory";
import { useSearchQuery } from "@/hooks/useSearchQuery";

interface PropertySearcherProps {
  variant?: "hero" | "compact";
  onSearch?: (params: SearchParams) => void;
  initialValues?: Partial<SearchParams>;
  showHomeIcon?: boolean;
}

export interface SearchParams {
  location: string;
  dateRange?: { from: Date; to: Date };
  dateFlexibility?: number | "exact" | null;
  guests: {
    adults: number;
    children: number;
    infants: number;
    pets: number;
  };
}

export function PropertySearcher({
  variant = "hero",
  onSearch,
  initialValues,
  showHomeIcon = true,
}: PropertySearcherProps) {
  const t = useTranslations("searchBar");
  const [, navigate] = useLocation();
  const isMobile = useIsMobile();
  
  // Enhanced search persistence
  const { searchState, persistSearch, executeSearch, getSmartDefaults, isSaving } = useSearchPersistence();
  const { addToHistory } = useSearchHistory();
  
  // URL parameter synchronization
  const { searchState: urlSearchState, updateSearch: updateUrlSearch, navigateToSearch } = useSearchQuery();
  
  // Debounced persistence to avoid too frequent saves
  const debouncedPersistSearch = useDebounce(persistSearch, 300);
  
  // State for tracking date picker interaction
  const [isDatePickerActive, setIsDatePickerActive] = useState(false);
  
  // Initialize state with URL params (highest priority), then initialValues, then persisted values
  const [locationValue, setLocationValue] = useState(() => {
    if (urlSearchState.location) return urlSearchState.location;
    if (initialValues?.location) return initialValues.location;
    return searchState.location || '';
  });
  
  const [dateRange, setDateRange] = useState<{ from: Date; to: Date } | undefined>(() => {
    console.log('📋 PropertySearcher: Initializing dateRange - URL:', urlSearchState.dateRange);
    console.log('📋 PropertySearcher: Initializing dateRange - initialValues:', initialValues?.dateRange);
    console.log('📋 PropertySearcher: Initializing dateRange - searchState:', searchState.dateRange);
    
    if (urlSearchState.dateRange) return urlSearchState.dateRange;
    if (initialValues?.dateRange) return initialValues.dateRange;
    return searchState.dateRange;
  });
  
  const [dateFlexibility, setDateFlexibility] = useState<number | "exact" | null>(() => {
    if (urlSearchState.dateFlexibility !== null && urlSearchState.dateFlexibility !== undefined) return urlSearchState.dateFlexibility;
    if (initialValues?.dateFlexibility !== undefined) return initialValues.dateFlexibility ?? null;
    return searchState.dateFlexibility ?? null;
  });
  
  const [guests, setGuests] = useState(() => {
    if (urlSearchState.guests) return urlSearchState.guests;
    if (initialValues?.guests) return initialValues.guests;
    return searchState.guests;
  });

  // Sync with URL parameters when they change (but only if URL has actual parameters)
  useEffect(() => {
    const hasUrlParams = urlSearchState.location || urlSearchState.dateRange || 
                        urlSearchState.dateFlexibility !== null ||
                        urlSearchState.guests.adults !== 2 || urlSearchState.guests.children !== 0 ||
                        urlSearchState.guests.infants !== 0 || urlSearchState.guests.pets !== 0;
    
    if (hasUrlParams && urlSearchState.location !== locationValue) {
      console.log('📋 PropertySearcher: Updating location from URL:', urlSearchState.location);
      setLocationValue(urlSearchState.location);
    }
  }, [urlSearchState.location]);

  useEffect(() => {
    // Don't sync from URL if user is actively selecting dates
    if (isDatePickerActive) return;
    
    const urlDateRange = urlSearchState.dateRange;
    const currentDateRange = dateRange;
    
    // Only sync from URL if URL has date parameters
    if (urlDateRange && (!currentDateRange || 
        currentDateRange.from?.getTime() !== urlDateRange.from?.getTime() ||
        currentDateRange.to?.getTime() !== urlDateRange.to?.getTime())) {
      console.log('📋 PropertySearcher: Updating dateRange from URL:', urlDateRange);
      setDateRange(urlDateRange);
    }
    // Don't clear dateRange if URL doesn't have dates - keep cached values
  }, [urlSearchState.dateRange?.from?.getTime(), urlSearchState.dateRange?.to?.getTime(), isDatePickerActive]);

  useEffect(() => {
    // Only sync from URL if URL has dateFlexibility parameter
    if (urlSearchState.dateFlexibility !== null && urlSearchState.dateFlexibility !== dateFlexibility) {
      console.log('📋 PropertySearcher: Updating dateFlexibility from URL:', urlSearchState.dateFlexibility);
      setDateFlexibility(urlSearchState.dateFlexibility ?? null);
    }
  }, [urlSearchState.dateFlexibility]);

  useEffect(() => {
    const hasUrlGuests = urlSearchState.guests.adults !== 2 || urlSearchState.guests.children !== 0 ||
                        urlSearchState.guests.infants !== 0 || urlSearchState.guests.pets !== 0;
    
    if (hasUrlGuests && JSON.stringify(urlSearchState.guests) !== JSON.stringify(guests)) {
      console.log('📋 PropertySearcher: Updating guests from URL:', urlSearchState.guests);
      setGuests(urlSearchState.guests);
    }
  }, [urlSearchState.guests]);

  // Initialize from initialValues prop (higher priority) or searchState (fallback)
  useEffect(() => {
    if (initialValues?.location && locationValue !== initialValues.location) {
      console.log('📋 PropertySearcher: Updating location from initialValues:', initialValues.location);
      setLocationValue(initialValues.location);
    } else if (!initialValues?.location && searchState.location && locationValue !== searchState.location) {
      console.log('📋 PropertySearcher: Updating location from searchState:', searchState.location);
      setLocationValue(searchState.location);
    }
  }, [initialValues?.location, searchState.location]);

  useEffect(() => {
    // Update from initialValues (URL parameters) when they change
    if (initialValues?.dateRange) {
      const hasChanged = !dateRange || 
        dateRange.from?.getTime() !== initialValues.dateRange.from?.getTime() ||
        dateRange.to?.getTime() !== initialValues.dateRange.to?.getTime();
      
      if (hasChanged) {
        console.log('📋 PropertySearcher: Updating dateRange from initialValues:', initialValues.dateRange);
        setDateRange(initialValues.dateRange);
      }
    }
  }, [initialValues?.dateRange?.from?.getTime(), initialValues?.dateRange?.to?.getTime()]);

  // Restore cached dateRange when searchState changes (if no URL params)
  useEffect(() => {
    if (!urlSearchState.dateRange && searchState.dateRange && (!dateRange || 
        dateRange.from?.getTime() !== searchState.dateRange.from?.getTime() ||
        dateRange.to?.getTime() !== searchState.dateRange.to?.getTime())) {
      console.log('📋 PropertySearcher: Restoring dateRange from cache:', searchState.dateRange);
      setDateRange(searchState.dateRange);
    }
  }, [searchState.dateRange, urlSearchState.dateRange]);

  useEffect(() => {
    if (initialValues?.dateFlexibility !== undefined && dateFlexibility !== initialValues.dateFlexibility) {
      console.log('📋 PropertySearcher: Updating dateFlexibility from initialValues:', initialValues.dateFlexibility);
      setDateFlexibility(initialValues.dateFlexibility);
    } else if (initialValues?.dateFlexibility === undefined && searchState.dateFlexibility !== undefined && dateFlexibility !== searchState.dateFlexibility) {
      console.log('📋 PropertySearcher: Updating dateFlexibility from searchState:', searchState.dateFlexibility);
      setDateFlexibility(searchState.dateFlexibility);
    }
  }, [initialValues?.dateFlexibility, searchState.dateFlexibility]);

  useEffect(() => {
    if (initialValues?.guests && JSON.stringify(guests) !== JSON.stringify(initialValues.guests)) {
      console.log('📋 PropertySearcher: Updating guests from initialValues:', initialValues.guests);
      setGuests(initialValues.guests);
    } else if (!initialValues?.guests && searchState.guests && JSON.stringify(guests) !== JSON.stringify(searchState.guests)) {
      console.log('📋 PropertySearcher: Updating guests from searchState:', searchState.guests);
      setGuests(searchState.guests);
    }
  }, [initialValues?.guests, searchState.guests]);

  // Method to restore complete search state from history
  const restoreSearchState = (historyItem: any) => {
    const locationName = historyItem.name || historyItem.location || '';
    setLocationValue(locationName);
    
    let restoredDateRange = undefined;
    if (historyItem.checkIn && historyItem.checkOut) {
      restoredDateRange = {
        from: new Date(historyItem.checkIn),
        to: new Date(historyItem.checkOut)
      };
      setDateRange(restoredDateRange);
    }
    
    const restoredGuests = historyItem.guests || guests;
    setGuests(restoredGuests);
    
    // Restore date flexibility if available
    if (historyItem.dateFlexibility !== undefined) {
      setDateFlexibility(historyItem.dateFlexibility);
    }
    
    // Persist the restored state
    const restoredParams = {
      location: locationName,
      dateRange: restoredDateRange,
      dateFlexibility: historyItem.dateFlexibility ?? null,
      guests: restoredGuests,
    };
    persistSearch(restoredParams);
  };
  const [activeStep, setActiveStep] = useState<
    "location" | "date" | "guests" | null
  >(null);
  const [showMobileSearch, setShowMobileSearch] = useState(false);

  // Prevent body scroll when mobile search is open
  useEffect(() => {
    if (showMobileSearch) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    // Cleanup on unmount
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [showMobileSearch]);

  // Update state when initialValues change (for search page refinement)
  // Only update once on mount to prevent input interference
  const [hasInitialized, setHasInitialized] = useState(false);
  
  // Simplified initialization - only location and guests from persistence, dates from URL only
  useEffect(() => {
    if (!hasInitialized) {
      console.log('📋 PropertySearcher: Initializing with searchState:', searchState);
      console.log('📋 PropertySearcher: Initial values:', initialValues);
      
      // Apply persisted state for location and guests only (no dates)
      if (!initialValues?.location && searchState.location) {
        console.log('📋 PropertySearcher: Setting location from persistence:', searchState.location);
        setLocationValue(searchState.location);
      }
      if (!initialValues?.guests && searchState.guests) {
        console.log('📋 PropertySearcher: Setting guests from persistence:', searchState.guests);
        setGuests(searchState.guests);
      }
      
      // Apply initial values if provided (they take precedence)
      if (initialValues) {
        if (initialValues.location !== undefined) {
          setLocationValue(initialValues.location);
        }
        if (initialValues.dateRange !== undefined) {
          setDateRange(initialValues.dateRange);
        }
        if (initialValues.dateFlexibility !== undefined) {
          setDateFlexibility(initialValues.dateFlexibility);
        }
        if (initialValues.guests !== undefined) {
          setGuests(initialValues.guests);
        }
      }
      
      setHasInitialized(true);
    }
  }, [searchState.location, searchState.guests, initialValues, hasInitialized]);

  const handleLocationChange = (value: string) => {
    setLocationValue(value);
    // Debounced auto-save search state as user types
    const updatedParams = { location: value, dateRange, dateFlexibility: dateFlexibility ?? null, guests };
    debouncedPersistSearch(updatedParams);
    
    // Update URL parameters (replace current state)
    updateUrlSearch({ location: value }, true);
  };

  const handleDateRangeChange = (
    range: { from: Date; to: Date } | undefined,
  ) => {
    setDateRange(range);
    // Debounced persistence to avoid interference during user interaction
    const updatedParams = { location: locationValue, dateRange: range, dateFlexibility: dateFlexibility ?? null, guests };
    debouncedPersistSearch(updatedParams);
    
    // Debounced URL update to prevent sync conflicts during date selection
    setTimeout(() => {
      updateUrlSearch({ dateRange: range }, true);
    }, 500);
  };

  const handleDateFlexibilityChange = (
    flexibility: number | "exact" | null,
  ) => {
    setDateFlexibility(flexibility);
    // Simplified: only persist non-date params
    const nonDateParams = { location: locationValue, dateFlexibility: flexibility ?? null, guests };
    debouncedPersistSearch(nonDateParams);
    
    // Update URL parameters with date flexibility
    updateUrlSearch({ dateFlexibility: flexibility ?? null }, true);
  };

  const handleGuestsChange = (newGuests: typeof guests) => {
    setGuests(newGuests);
    // Immediate save for guests (less frequent changes)
    const updatedParams = { location: locationValue, dateRange, dateFlexibility: dateFlexibility ?? null, guests: newGuests };
    persistSearch(updatedParams);
    
    // Update URL parameters with guests
    updateUrlSearch({ guests: newGuests }, true);
  };

  const handleSearch = async () => {
    const searchParams: SearchParams = {
      location: locationValue,
      dateRange,
      dateFlexibility: dateFlexibility ?? null,
      guests,
    };

    console.log('🔍 PropertySearcher handleSearch:', {
      locationValue,
      dateRange,
      dateFlexibility,
      guests,
      searchParams
    });

    // Execute search with backend persistence
    await executeSearch(searchParams);

    // Save complete search to history
    if (locationValue.trim()) {
      try {
        await addToHistory(
          {
            id: locationValue.toLowerCase().replace(/\s+/g, '-'),
            name: locationValue,
            type: 'city',
            country: 'Spain'
          },
          {
            checkIn: dateRange?.from?.toISOString().split("T")[0],
            checkOut: dateRange?.to?.toISOString().split("T")[0],
            guests
          }
        );
      } catch (error) {
        console.error('Failed to save search to history:', error);
      }
    }

    if (onSearch) {
      onSearch(searchParams);
    } else {
      // Default behavior: navigate to search page using URL synchronization
      navigateToSearch({
        location: locationValue,
        dateRange,
        dateFlexibility: dateFlexibility ?? null,
        guests,
      });
    }
  };

  const isCompact = variant === "compact";
  const enableSequentialFlow = variant === "hero";

  // Mobile Search Modal
  const renderMobileSearchModal = () => (
    <div className="fixed inset-0 bg-white z-50 flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b bg-white sticky top-0">
        <Button variant="ghost" size="sm" onClick={() => setShowMobileSearch(false)}>
          <X className="h-4 w-4" />
        </Button>
        <span className="font-medium text-sm">{t("search")}</span>
        <div className="w-8" />
      </div>

      {/* Search Form */}
      <div className="flex-1 p-3 space-y-4 overflow-y-auto">
        {/* Location */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-900">{t("where")}</label>
          <div className="border border-gray-300 rounded-lg p-3 bg-gray-50">
            <LocationAutocomplete
              value={locationValue}
              onChange={handleLocationChange}
              placeholder={t("searchDestination")}
              autoFocus
              onLocationSelect={(location) => {
                setLocationValue(location);
              }}
              onCompleteSearchSelect={restoreSearchState}
            />
          </div>
        </div>

        {/* Dates */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-900">{t("when")}</label>
          <div className="border border-gray-300 rounded-lg p-3 bg-gray-50">
            <DateRangePicker
              value={dateRange}
              onDateRangeChange={handleDateRangeChange}
              flexibility={dateFlexibility}
              onFlexibilityChange={handleDateFlexibilityChange}
              placeholder={t("addDates")}
            />
          </div>
        </div>

        {/* Guests */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-900">{t("who")}</label>
          <div className="border border-gray-300 rounded-lg p-3 bg-gray-50">
            <GuestSelector
              guests={guests}
              onChange={handleGuestsChange}
              placeholder={t("addGuests")}
            />
          </div>
        </div>
      </div>

      {/* Search Button */}
      <div className="p-3 border-t bg-white sticky bottom-0">
        <Button 
          onClick={async () => {
            await handleSearch();
            setShowMobileSearch(false);
          }}
          className="w-full h-11 bg-primary hover:bg-primary/90 text-primary-foreground rounded-lg"
        >
          <Search className="h-4 w-4 mr-2" />
          {t("search")}
        </Button>
      </div>
    </div>
  );

  // Mobile Compact Search Bar
  const renderMobileCompactBar = () => (
    <Card className="w-full mx-auto border border-border rounded-full shadow-sm bg-card">
      <div className="flex items-center p-1">
        {/* Home Icon */}
        {showHomeIcon && isCompact && (
          <Button
            variant="ghost"
            size="icon"
            className="mr-1 h-8 w-8 text-muted-foreground hover:text-foreground hover:bg-muted rounded-full"
            onClick={() => navigate("/")}
          >
            <Home className="h-4 w-4" />
          </Button>
        )}

        {/* Compact Search Trigger */}
        <button
          onClick={() => setShowMobileSearch(true)}
          className="flex-1 flex items-center space-x-2 px-3 py-2 text-left min-w-0"
        >
          <Search className="h-4 w-4 text-gray-400 flex-shrink-0" />
          <div className="flex-1 min-w-0">
            <div className="text-sm text-gray-900 truncate">
              {locationValue || t("where")}
            </div>
            <div className="text-xs text-gray-500 truncate">
              {dateRange ? t("datesSelected") : t("anyWeek")} • {guests.adults + guests.children} {guests.adults + guests.children === 1 ? t("guest") : t("guests")}
            </div>
          </div>
        </button>

        {/* Mobile Search Button */}
        <Button
          onClick={handleSearch}
          size="icon"
          className="ml-1 h-8 w-8 bg-primary hover:bg-primary/90 text-primary-foreground rounded-full shadow-md"
        >
          <Search className="h-4 w-4" />
        </Button>
      </div>
    </Card>
  );

  // Desktop Layout (existing)
  const renderDesktopLayout = () => (
    <Card
      className={`w-full mx-auto ${
        isCompact 
          ? "border border-border rounded-full shadow-sm bg-card" 
          : "border border-border bg-card shadow-lg rounded-full max-w-4xl"
      }`}
    >
      <div className="flex items-center p-2">
        {/* Home Icon - only show for compact variant */}
        {showHomeIcon && isCompact && (
          <Button
            variant="ghost"
            size="icon"
            className="mr-2 h-12 w-12 text-muted-foreground hover:text-foreground hover:bg-muted rounded-full"
            onClick={() => navigate("/")}
          >
            <Home className="h-5 w-5" />
          </Button>
        )}

        {/* Location Section */}
        <div className="flex-1 min-w-0 px-4 py-3 border-r border-gray-200">
          <div className="flex items-center space-x-3">
            <MapPin className="h-4 w-4 text-gray-400 flex-shrink-0" />
            <div className="min-w-0 flex-1">
              <div className="text-xs font-medium text-gray-500 mb-1">
                {t("where")}
              </div>
              <LocationAutocomplete
                value={locationValue}
                onChange={handleLocationChange}
                placeholder={t("searchDestination")}
                compact={true}
                onFocus={() => setActiveStep("location")}
                onCompleteSearchSelect={restoreSearchState}
              />
            </div>
          </div>
        </div>

        {/* Date Section */}
        <div className="flex-1 min-w-0 px-4 py-3 border-r border-gray-200">
          <div className="flex items-center space-x-3">
            <Calendar className="h-4 w-4 text-gray-400 flex-shrink-0" />
            <div className="min-w-0 flex-1">
              <div className="text-xs font-medium text-gray-500 mb-1">
                {t("when")}
              </div>
              <DateRangePicker
                value={dateRange}
                onDateRangeChange={handleDateRangeChange}
                flexibility={dateFlexibility ?? null}
                onFlexibilityChange={handleDateFlexibilityChange}
                compact={true}
                placeholder={t("addDates")}
                onFocus={() => {
                  setActiveStep("date");
                  setIsDatePickerActive(true);
                }}
                onBlur={() => {
                  setIsDatePickerActive(false);
                }}
              />
            </div>
          </div>
        </div>

        {/* Guests Section */}
        <div className="flex-1 min-w-0 px-4 py-3">
          <div className="flex items-center space-x-3">
            <Users className="h-4 w-4 text-gray-400 flex-shrink-0" />
            <div className="min-w-0 flex-1">
              <div className="text-xs font-medium text-gray-500 mb-1">
                {t("who")}
              </div>
              <GuestSelector
                guests={guests}
                onChange={handleGuestsChange}
                compact={true}
                placeholder={t("addGuests")}
                onFocus={() => setActiveStep("guests")}
              />
            </div>
          </div>
        </div>

        {/* Search Button */}
        <div className="flex-shrink-0 pl-2 pr-2">
          <Button
            onClick={handleSearch}
            size="icon"
            className="w-12 h-12 bg-primary hover:bg-primary/90 text-primary-foreground rounded-full shadow-md hover:shadow-lg transition-all"
          >
            <Search className="h-5 w-5" />
          </Button>
        </div>
        </div>
      </Card>
  );

  return (
    <>
      {/* Conditional rendering based on device */}
      {isMobile ? renderMobileCompactBar() : renderDesktopLayout()}
      
      {/* Mobile Search Modal */}
      {showMobileSearch && renderMobileSearchModal()}
    </>
  );
}