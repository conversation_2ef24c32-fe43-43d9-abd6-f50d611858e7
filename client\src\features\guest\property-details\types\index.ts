export type { PropertyDetails } from '@/lib/apiClient';

export interface PropertyDetailsProps {
  propertyId: string;
}

export interface BookingFormData {
  checkIn: Date | null;
  checkOut: Date | null;
  guests: {
    adults: number;
    children: number;
    infants: number;
    pets: number;
  };
}

export interface CalendarDay {
  date: Date;
  available: boolean;
  price?: number;
  isToday: boolean;
  isSelected: boolean;
  isInRange: boolean;
  isRangeStart: boolean;
  isRangeEnd: boolean;
}