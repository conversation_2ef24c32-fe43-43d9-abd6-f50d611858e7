import React, { useState } from "react";
import { useLocation } from "wouter";
import { MainLayout } from "@/components/MainLayout";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { Link } from "wouter";
import { Loader2, Mail, ArrowRight, ArrowLeft } from "lucide-react";
import { useTranslations } from '@/lib/translations';

export function ForgotPasswordPage() {
  const [, navigate] = useLocation();
  const { toast } = useToast();
  const t = useTranslations('auth.forgotPassword');
  const [isLoading, setIsLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const [email, setEmail] = useState("");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const response = await fetch('/api/auth/forgot-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (data.success) {
        setEmailSent(true);
        toast({
          title: t('resetEmailSent'),
          description: t('resetEmailSentDescription'),
        });
      } else {
        throw new Error(data.message || 'Failed to send reset email');
      }
    } catch (error) {
      console.error('Password reset error:', error);
      toast({
        title: t('resetEmailError'),
        description: error instanceof Error ? error.message : t('resetEmailErrorDescription'),
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (emailSent) {
    return (
      <MainLayout showFooter={false}>
        <div className="flex items-center justify-center min-h-[80vh] py-12 px-4">
          <Card className="w-full max-w-md mx-auto bg-white/95 backdrop-blur-sm border shadow-xl">
            <CardHeader className="text-center relative">
              <Link href="/">
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute left-0 top-0 h-8 w-8 p-0 text-gray-500 hover:text-gray-700"
                >
                  <ArrowLeft className="h-4 w-4" />
                </Button>
              </Link>
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Mail className="w-8 h-8 text-green-600" />
              </div>
              <CardTitle className="text-2xl font-bold text-gray-900">
                {t('checkEmailTitle')}
              </CardTitle>
              <p className="text-gray-600 mt-2">
                {t('checkEmailDescription')}
              </p>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="text-center space-y-4">
                <div className="space-y-2">
                  <h3 className="text-lg font-semibold">{t('emailSentTitle')}</h3>
                  <p className="text-gray-600">
                    {t('emailSentDescription')} <strong>{email}</strong>
                  </p>
                  <p className="text-sm text-gray-500">
                    {t('emailNotReceived')}
                  </p>
                </div>
                
                <div className="space-y-3">
                  <Button
                    onClick={() => setEmailSent(false)}
                    variant="outline"
                    className="w-full"
                  >
                    {t('tryDifferentEmail')}
                  </Button>
                  
                  <Link href="/login">
                    <Button className="w-full">
                      {t('backToSignIn')}
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout showFooter={false}>
      <div className="flex items-center justify-center min-h-[80vh] py-12 px-4">
        <Card className="w-full max-w-md mx-auto bg-white/95 backdrop-blur-sm border shadow-xl">
          <CardHeader className="text-center relative">
            <Link href="/">
              <Button
                variant="ghost"
                size="sm"
                className="absolute left-0 top-0 h-8 w-8 p-0 text-gray-500 hover:text-gray-700"
              >
                <ArrowLeft className="h-4 w-4" />
              </Button>
            </Link>
            <div className="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <Mail className="w-8 h-8 text-primary" />
            </div>
            <CardTitle className="text-2xl font-bold text-gray-900">
              {t('title')}
            </CardTitle>
            <p className="text-gray-600 mt-2">
              {t('subtitle')}
            </p>
          </CardHeader>
          <CardContent className="space-y-6">
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">{t('emailLabel')}</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  placeholder={t('emailPlaceholder')}
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  disabled={isLoading}
                  className="h-12"
                  autoFocus
                />
              </div>
              
              <Button
                type="submit"
                className="w-full h-12"
                disabled={isLoading || !email}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {t('sendingReset')}
                  </>
                ) : (
                  <>
                    {t('sendResetButton')}
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </>
                )}
              </Button>
            </form>
            
            <div className="text-center">
              <p className="text-sm text-gray-600">
                {t('rememberPassword')}{' '}
                <Link href="/login">
                  <Button variant="link" className="p-0 h-auto text-sm">
                    {t('signIn')}
                  </Button>
                </Link>
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
}