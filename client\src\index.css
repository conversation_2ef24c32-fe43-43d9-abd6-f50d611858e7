
@import 'leaflet/dist/leaflet.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Prevent horizontal scrolling on mobile */
@media (max-width: 768px) {
  html, body {
    overflow-x: hidden;
    max-width: 100vw;
    width: 100%;
  }
  
  body {
    position: relative;
  }
  
  * {
    box-sizing: border-box;
  }
  
  /* Ensure no element extends beyond viewport width */
  *:not(style):not(script) {
    max-width: 100%;
  }
  
  /* Specific mobile container adjustments */
  .mobile-container {
    width: 100%;
    max-width: 100vw;
    overflow-x: hidden;
  }
  
  /* Grid adjustments for mobile */
  .grid {
    width: 100%;
  }
  
  /* Button and card adjustments */
  button, .card, .button {
    max-width: 100%;
    word-wrap: break-word;
  }
  
  /* Text overflow handling */
  h1, h2, h3, h4, h5, h6, p, span, div {
    word-wrap: break-word;
    overflow-wrap: break-word;
  }
}

/* Custom Leaflet marker styles */
.custom-marker {
  background: transparent !important;
  border: none !important;
}

/* Custom cluster marker styles */
.custom-cluster-marker {
  background: transparent !important;
  border: none !important;
}

/* Cluster marker hover effects */
.leaflet-marker-icon:hover {
  z-index: 1000 !important;
}



/* Ensure cluster markers are clickable */
.leaflet-marker-icon {
  pointer-events: auto !important;
}

.leaflet-interactive {
  pointer-events: auto !important;
}

/* Ensure Leaflet controls are properly styled */
.leaflet-control-container {
  font-family: inherit;
}

.leaflet-popup-content-wrapper {
  font-family: inherit;
}

/* Ensure dropdowns appear above Leaflet maps */
.leaflet-container {
  z-index: 1 !important;
}

/* Location dropdown should always be above maps */
.location-dropdown {
  z-index: 10001 !important;
}

/* Ensure datepicker popover appears above all content including browser UI */
[data-radix-popper-content-wrapper] {
  z-index: 999999 !important;
}

/* Ensure all popovers appear above maps and other content */
[data-radix-popover-content] {
  z-index: 999999 !important;
}

/* Additional positioning fix for calendar popover */
[data-radix-popover-content][data-side="bottom"] {
  margin-top: 8px !important;
}

/* Cluster group animations */
.leaflet-cluster-anim .leaflet-marker-icon, .leaflet-cluster-anim .leaflet-marker-shadow {
  transition: transform 0.3s ease-out, opacity 0.3s ease-in;
}

/* Mobile sidebar enhancements */
@media (max-width: 768px) {
  .mobile-sidebar-tooltip {
    pointer-events: none;
    transform: translateX(0);
    transition: opacity 0.2s ease-in-out, visibility 0.2s ease-in-out;
  }
  
  .mobile-sidebar-tooltip.visible {
    opacity: 1;
    visibility: visible;
  }
  
  /* Ensure tooltips don't interfere with touch interactions */
  .sidebar-nav-item:hover .mobile-sidebar-tooltip {
    opacity: 1;
    visibility: visible;
  }
  
  /* Smooth transitions for mobile navigation */
  .sidebar-nav-item {
    transition: all 0.3s ease-in-out;
  }
  
  .sidebar-nav-item:active {
    transform: scale(0.95);
  }
}

/* Spiderfy animation for overlapping markers */
.leaflet-marker-shadow {
  display: none !important;
}

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */

@layer base {
  :root {
    --background: 40 38% 97%;
    --foreground: 200 50% 20%;

    --card: 40 30% 98%;
    --card-foreground: 200 50% 20%;

    --popover: 40 30% 98%;
    --popover-foreground: 200 50% 20%;

    --primary: 45 93% 58%;
    --primary-foreground: 0 0% 0%;
    
    --secondary: 45 93% 85%;
    --secondary-foreground: 0 0% 0%;

    --muted: 35 25% 92%;
    --muted-foreground: 200 30% 40%;

    --accent: 45 93% 68%;
    --accent-foreground: 0 0% 0%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 45 93% 58%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 45 93% 58%;

    --sidebar-primary-foreground: 0 0% 0%;

    --sidebar-accent: 45 93% 85%;

    --sidebar-accent-foreground: 0 0% 0%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 45 93% 58%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 45 93% 58%;
    --primary-foreground: 0 0% 0%;

    --secondary: 45 93% 25%;
    --secondary-foreground: 0 0% 100%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 45 93% 68%;
    --accent-foreground: 0 0% 0%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 45 93% 58%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 45 93% 58%;
    --sidebar-primary-foreground: 0 0% 0%;
    --sidebar-accent: 45 93% 25%;
    --sidebar-accent-foreground: 0 0% 100%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 45 93% 58%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', system-ui, sans-serif;
  }
}

/* Custom scrollbar hiding */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Mobile touch improvements for date picker */
.touch-pan-y {
  touch-action: pan-y;
  overscroll-behavior: contain;
}

/* Prevent background scroll when modal is open */
.scroll-locked {
  overflow: hidden !important;
  height: 100vh;
}

/* Calendar specific improvements */
[data-radix-popper-content-wrapper] {
  touch-action: pan-y;
  overscroll-behavior: contain;
}

/* Line clamp utility */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

/* VillaWise brand gradient sweep animation */
@keyframes gradientSweep {
  0% {
    background-position: -100% 0;
  }
  50% {
    background-position: 100% 0;
  }
  100% {
    background-position: 100% 0;
  }
}

.gradient-sweep-text {
  background: linear-gradient(
    90deg,
    hsl(var(--foreground)) 0%,
    hsl(var(--foreground)) 30%,
    hsl(var(--primary)) 40%,
    hsl(var(--primary)) 60%,
    hsl(var(--foreground)) 70%,
    hsl(var(--foreground)) 100%
  );
  background-size: 200% 100%;
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  animation: gradientSweep 2s ease-in-out;
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Custom focus styles */
.focus-visible:focus-visible {
  outline: 2px solid #FF385C;
  outline-offset: 2px;
}
