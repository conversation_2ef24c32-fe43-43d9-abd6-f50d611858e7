import React, { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Eye, EyeOff } from "lucide-react";
import { useTranslations } from '@/lib/translations';

interface PasswordInputProps {
  id?: string;
  name?: string;
  placeholder?: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  required?: boolean;
  disabled?: boolean;
  className?: string;
  showStrength?: boolean;
}

export function PasswordInput({
  id,
  name,
  placeholder = "Enter your password",
  value,
  onChange,
  required = false,
  disabled = false,
  className = "",
  showStrength = false,
}: PasswordInputProps) {
  const [showPassword, setShowPassword] = useState(false);
  const t = useTranslations('auth.passwordStrength');

  const getPasswordStrength = (password: string) => {
    if (!password) return { strength: 0, label: "", color: "" };
    
    let strength = 0;
    const checks = [
      password.length >= 8,
      /[a-z]/.test(password),
      /[A-Z]/.test(password),
      /[0-9]/.test(password),
      /[^a-zA-Z0-9]/.test(password),
    ];
    
    strength = checks.filter(Boolean).length;
    
    if (strength < 2) return { strength, label: t('weak'), color: "bg-red-500" };
    if (strength < 4) return { strength, label: t('fair'), color: "bg-yellow-500" };
    if (strength < 5) return { strength, label: t('good'), color: "bg-blue-500" };
    return { strength, label: t('strong'), color: "bg-green-500" };
  };

  const passwordStrength = showStrength && value ? getPasswordStrength(value) : null;

  return (
    <div className="space-y-2">
      <div className="relative">
        <Input
          id={id}
          name={name}
          type={showPassword ? "text" : "password"}
          placeholder={placeholder}
          value={value}
          onChange={onChange}
          required={required}
          disabled={disabled}
          className={`pr-10 ${className}`}
        />
        <Button
          type="button"
          variant="ghost"
          size="sm"
          className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
          onClick={() => setShowPassword(!showPassword)}
          disabled={disabled}
        >
          {showPassword ? (
            <EyeOff className="h-4 w-4 text-gray-400" />
          ) : (
            <Eye className="h-4 w-4 text-gray-400" />
          )}
        </Button>
      </div>
      
      {passwordStrength && value && (
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <div className="flex-1 h-2 bg-gray-200 rounded-full overflow-hidden">
              <div 
                className={`h-full transition-all duration-300 ${passwordStrength.color}`}
                style={{ width: `${(passwordStrength.strength / 5) * 100}%` }}
              />
            </div>
            <span className={`text-xs font-medium ${
              passwordStrength.strength < 2 ? 'text-red-600' :
              passwordStrength.strength < 4 ? 'text-yellow-600' :
              passwordStrength.strength < 5 ? 'text-blue-600' :
              'text-green-600'
            }`}>
              {passwordStrength.label}
            </span>
          </div>
          <p className="text-xs text-gray-500">
            {t('helpText')}
          </p>
        </div>
      )}
    </div>
  );
}