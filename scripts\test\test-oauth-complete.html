<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OAuth Flow Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .step {
            margin: 20px 0;
            padding: 15px;
            border-left: 4px solid #3498db;
            background-color: #f8f9fa;
        }
        .success {
            border-left-color: #27ae60;
            background-color: #d4edda;
        }
        .error {
            border-left-color: #e74c3c;
            background-color: #f8d7da;
        }
        button {
            background-color: #3498db;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #2980b9;
        }
        .disabled {
            background-color: #bdc3c7;
            cursor: not-allowed;
        }
        pre {
            background-color: #f4f4f4;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>OAuth Flow Test - VillaWise</h1>
        <p>This test simulates the complete OAuth flow to identify where the login issue occurs.</p>
        
        <div id="step1" class="step">
            <h3>Step 1: Test OAuth URL Generation</h3>
            <button onclick="testOAuthURL()">Test OAuth URL</button>
            <div id="step1-result"></div>
        </div>
        
        <div id="step2" class="step">
            <h3>Step 2: Simulate OAuth Callback</h3>
            <p>This simulates what happens after Google OAuth redirects back to your app.</p>
            <button onclick="simulateOAuthCallback()">Simulate OAuth Callback</button>
            <div id="step2-result"></div>
        </div>
        
        <div id="step3" class="step">
            <h3>Step 3: Test Token Validation</h3>
            <p>This tests the OAuth user validation endpoint with a mock token.</p>
            <button onclick="testTokenValidation()">Test Token Validation</button>
            <div id="step3-result"></div>
        </div>
        
        <div id="step4" class="step">
            <h3>Step 4: Manual OAuth Test</h3>
            <p>Click this button to start a real Google OAuth login flow.</p>
            <button onclick="startRealOAuth()">Start Real OAuth Login</button>
            <div id="step4-result"></div>
        </div>
    </div>

    <script>
        // Utility functions
        function log(stepId, message, isError = false) {
            const result = document.getElementById(stepId + '-result');
            const className = isError ? 'error' : 'success';
            result.innerHTML += `<div class="${className}"><pre>${message}</pre></div>`;
        }

        function clearLog(stepId) {
            document.getElementById(stepId + '-result').innerHTML = '';
        }

        // Step 1: Test OAuth URL Generation
        async function testOAuthURL() {
            clearLog('step1');
            log('step1', 'Testing OAuth URL generation...');
            
            try {
                const response = await fetch('/api/auth/google');
                const data = await response.json();
                
                if (data.success) {
                    log('step1', `✅ OAuth URL generated successfully:\n${data.url}`);
                } else {
                    log('step1', `❌ OAuth URL generation failed: ${data.message}`, true);
                }
            } catch (error) {
                log('step1', `❌ Network error: ${error.message}`, true);
            }
        }

        // Step 2: Simulate OAuth Callback
        async function simulateOAuthCallback() {
            clearLog('step2');
            log('step2', 'Simulating OAuth callback processing...');
            
            // Simulate the OAuth callback URL with mock tokens
            const mockUrl = 'http://localhost:5000/auth/callback#access_token=mock_access_token_123&refresh_token=mock_refresh_token_456&expires_in=3600';
            
            log('step2', `📝 Mock OAuth callback URL:\n${mockUrl}`);
            
            // Extract tokens from URL hash (simulating frontend processing)
            const hash = mockUrl.split('#')[1];
            const hashParams = new URLSearchParams(hash);
            const accessToken = hashParams.get('access_token');
            const refreshToken = hashParams.get('refresh_token');
            
            log('step2', `🔑 Extracted tokens:\n- Access Token: ${accessToken}\n- Refresh Token: ${refreshToken}`);
            
            // Simulate localStorage storage
            log('step2', '💾 Tokens would be stored in localStorage');
            log('step2', '✅ OAuth callback simulation complete');
        }

        // Step 3: Test Token Validation
        async function testTokenValidation() {
            clearLog('step3');
            log('step3', 'Testing OAuth user validation endpoint...');
            
            try {
                const response = await fetch('/api/auth/oauth-user', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer mock_token_for_testing'
                    }
                });
                
                const data = await response.json();
                
                log('step3', `📝 Response Status: ${response.status}`);
                log('step3', `📝 Response Data: ${JSON.stringify(data, null, 2)}`);
                
                if (response.status === 401) {
                    log('step3', '✅ Token validation endpoint working correctly (rejects invalid tokens)');
                } else {
                    log('step3', '❌ Unexpected response from validation endpoint', true);
                }
            } catch (error) {
                log('step3', `❌ Network error: ${error.message}`, true);
            }
        }

        // Step 4: Start Real OAuth Login
        async function startRealOAuth() {
            clearLog('step4');
            log('step4', 'Starting real Google OAuth login...');
            
            try {
                const response = await fetch('/api/auth/google');
                const data = await response.json();
                
                if (data.success) {
                    log('step4', `✅ Redirecting to Google OAuth:\n${data.url}`);
                    
                    // Add event listener to monitor OAuth completion
                    window.addEventListener('message', function(event) {
                        log('step4', `📨 Received message: ${JSON.stringify(event.data)}`);
                    });
                    
                    // Open OAuth in new window to monitor the flow
                    const popup = window.open(data.url, 'oauth', 'width=600,height=600');
                    
                    // Monitor popup
                    const checkClosed = setInterval(() => {
                        if (popup.closed) {
                            clearInterval(checkClosed);
                            log('step4', '🔄 OAuth popup closed - checking for successful login...');
                            
                            // Check if user is now logged in
                            setTimeout(() => {
                                fetch('/api/auth/me')
                                    .then(response => response.json())
                                    .then(data => {
                                        if (data.success) {
                                            log('step4', `✅ Login successful! User: ${data.user.email}`);
                                        } else {
                                            log('step4', `❌ Login failed: ${data.message}`, true);
                                        }
                                    })
                                    .catch(error => {
                                        log('step4', `❌ Error checking login status: ${error.message}`, true);
                                    });
                            }, 1000);
                        }
                    }, 1000);
                } else {
                    log('step4', `❌ Failed to start OAuth: ${data.message}`, true);
                }
            } catch (error) {
                log('step4', `❌ Network error: ${error.message}`, true);
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('step1', 'Ready to test OAuth URL generation');
            log('step2', 'Ready to simulate OAuth callback');
            log('step3', 'Ready to test token validation');
            log('step4', 'Ready to test real OAuth login');
        });
    </script>
</body>
</html>