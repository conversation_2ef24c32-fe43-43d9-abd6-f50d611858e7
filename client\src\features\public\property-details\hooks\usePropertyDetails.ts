import { useQuery } from '@tanstack/react-query';
import { getPropertyDetails } from '@/lib/apiClient';
import type { PropertyDetails } from '../types';

export function usePropertyDetails(propertyId: string) {
  return useQuery<PropertyDetails>({
    queryKey: ['/api/public/properties', propertyId],
    queryFn: () => getPropertyDetails(propertyId),
    enabled: !!propertyId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}