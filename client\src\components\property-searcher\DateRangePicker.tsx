import { useState, useEffect } from "react";
import { Calendar as CalendarIcon, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { addDays, format, differenceInDays } from "date-fns";
import { nl } from "date-fns/locale";
import { cn } from "@/lib/utils";
import { useTranslations } from "@/lib/translations";
import { useLocale } from "@/lib/i18n";
import { useIsMobile } from "@/hooks/use-mobile";
import type { DateRange } from "react-day-picker";

interface DateRangePickerProps {
  value?: { from: Date; to: Date } | undefined;
  onDateRangeChange?: (range: { from: Date; to: Date } | undefined) => void;
  flexibility?: number | "exact" | null;
  onFlexibilityChange?: (flexibility: number | "exact" | null) => void;
  compact?: boolean;
  placeholder?: string;
  autoOpen?: boolean;
  onFocus?: () => void;
  onBlur?: () => void;
}

export function DateRangePicker({ value, onDateRangeChange, flexibility, onFlexibilityChange, compact = false, placeholder, autoOpen = false, onFocus, onBlur }: DateRangePickerProps) {
  const t = useTranslations("dateRangePicker");
  const { locale } = useLocale();
  const isMobile = useIsMobile();
  const [date, setDate] = useState<DateRange | undefined>(value ? { from: value.from, to: value.to } : undefined);
  const [open, setOpen] = useState(false);
  const [selectedFlexibility, setSelectedFlexibility] = useState<number | "exact" | null>(flexibility || null);
  const [isSelectingRange, setIsSelectingRange] = useState(false);

  // Sync internal state with external value prop - avoid infinite loops
  useEffect(() => {
    if (value && (!date || date.from?.getTime() !== value.from?.getTime() || date.to?.getTime() !== value.to?.getTime())) {
      setDate({ from: value.from, to: value.to });
    } else if (!value && date) {
      setDate(undefined);
    }
  }, [value?.from?.getTime(), value?.to?.getTime()]); // Only depend on actual timestamps

  // Sync flexibility with external prop
  useEffect(() => {
    if (flexibility !== selectedFlexibility) {
      setSelectedFlexibility(flexibility || null);
    }
  }, [flexibility]);

  // Enhanced date selection logic for better UX
  const handleDateChange = (range: DateRange | undefined) => {
    // Handle clearing
    if (!range) {
      setDate(undefined);
      onDateRangeChange?.(undefined);
      setIsSelectingRange(false);
      return;
    }

    // If we have a complete range and a new range is being selected
    if (date?.from && date?.to && range?.from && !range?.to) {
      // User clicked on a new date after having a complete range
      // Start fresh with the new date
      setDate({ from: range.from, to: undefined });
      setIsSelectingRange(true);
      return;
    }

    // Normal range selection flow
    setDate(range);
    
    // If we have a complete range, notify parent and mark as complete
    if (range?.from && range?.to) {
      onDateRangeChange?.({ from: range.from, to: range.to });
      setIsSelectingRange(false);
    } else if (range?.from && !range?.to) {
      // User selected start date, now in selection mode
      setIsSelectingRange(true);
    }
  };

  // Prevent body scroll when mobile date picker is open
  useEffect(() => {
    if (isMobile && open) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isMobile, open]);

  const handleFocus = () => {
    onFocus?.();
    setOpen(true);
  };

  useEffect(() => {
    if (autoOpen && !isMobile) {
      setOpen(true);
    }
  }, [autoOpen, isMobile]);

  // Mobile full-screen date picker
  const renderMobileDatePicker = () => (
    <div className="fixed inset-0 bg-white z-50 flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b bg-white">
        <Button variant="ghost" size="sm" onClick={() => {
          setOpen(false);
          onBlur?.();
        }}>
          <X className="h-4 w-4" />
        </Button>
        <span className="font-medium text-sm">Select dates</span>
        <div className="w-8" />
      </div>

      {/* Calendar */}
      <div className="flex-1 overflow-y-auto p-4">
        <Calendar
          initialFocus
          mode="range"
          defaultMonth={date?.from}
          selected={date}
          onSelect={handleDateChange}
          numberOfMonths={1}
          disabled={(date) => date < new Date()}
          className="w-full"
        />
      </div>

      {/* Footer with options */}
      <div className="p-4 border-t bg-white">
        <div className="flex flex-col gap-3">
          {/* Flexibility options */}
          <div className="flex flex-wrap items-center gap-2">
            <button
              className={`px-3 py-1.5 text-xs border rounded-full transition-colors whitespace-nowrap ${
                selectedFlexibility === "exact"
                  ? "bg-primary text-primary-foreground border-primary"
                  : "border-border hover:bg-muted"
              }`}
              onClick={() => {
                setSelectedFlexibility("exact");
                onFlexibilityChange?.("exact");
              }}
            >
              {t('exactDates')}
            </button>
            {[1, 2, 3, 7].map((days) => (
              <button
                key={days}
                className={`px-3 py-1.5 text-xs border rounded-full transition-colors whitespace-nowrap ${
                  selectedFlexibility === days
                    ? "bg-primary text-primary-foreground border-primary"
                    : "border-border hover:bg-muted"
                }`}
                onClick={() => {
                  setSelectedFlexibility(days);
                  onFlexibilityChange?.(days);
                }}
              >
                ±{days} {days === 1 ? t('day') : t('days')}
              </button>
            ))}
          </div>

          {/* Action buttons */}
          <div className="flex justify-between items-center gap-3">
            <button
              onClick={() => {
                setDate(undefined);
                onDateRangeChange?.(undefined);
                setSelectedFlexibility(null);
                onFlexibilityChange?.(null);
              }}
              className="text-primary hover:text-primary/80 font-medium text-sm px-4 py-2"
            >
              {t('clear')}
            </button>
            <button
              onClick={() => {
                setOpen(false);
                onBlur?.();
              }}
              className="bg-primary hover:bg-primary/90 text-primary-foreground px-6 py-2.5 rounded-lg font-medium text-sm flex-1"
            >
              {t('done')}
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <>
      {isMobile ? (
        // Mobile: Simple button that opens full-screen picker
        <button
          onClick={() => setOpen(true)}
          className={cn(
            compact 
              ? "w-full justify-start text-left font-medium p-0 h-auto text-sm text-gray-900 hover:bg-transparent flex items-start"
              : "w-full justify-start text-left font-normal flex items-start",
            !date && (compact ? "text-gray-400" : "text-muted-foreground")
          )}
        >
          {!compact && <CalendarIcon className="mr-2 h-4 w-4 mt-0.5 flex-shrink-0" />}
          <span>
            {date?.from ? (
              date.to ? (
                <>
                  {format(date.from, "LLL dd", {
                    locale: locale === "nl" ? nl : undefined,
                  })}{" "}
                  -{" "}
                  {format(date.to, "LLL dd", {
                    locale: locale === "nl" ? nl : undefined,
                  })}
                  {selectedFlexibility && selectedFlexibility !== "exact" && (
                    <span className="text-primary ml-1">
                      +{selectedFlexibility}
                    </span>
                  )}
                </>
              ) : (
                format(date.from, "LLL dd", {
                  locale: locale === "nl" ? nl : undefined,
                })
              )
            ) : (
              placeholder || t("selectDates")
            )}
          </span>
        </button>
      ) : (
        // Desktop: Popover approach
        <Popover open={open} onOpenChange={(newOpen) => {
          setOpen(newOpen);
          if (!newOpen) {
            onBlur?.();
          }
        }}>
          <PopoverTrigger asChild>
            <Button
              variant={compact ? "ghost" : "outline"}
              className={cn(
                compact 
                  ? "w-full justify-start text-left font-medium p-0 h-auto text-sm text-gray-900 hover:bg-transparent"
                  : "w-full justify-start text-left font-normal",
                !date && (compact ? "text-gray-400" : "text-muted-foreground")
              )}
              onClick={handleFocus}
            >
              {!compact && <CalendarIcon className="mr-2 h-4 w-4" />}
              {date?.from ? (
                date.to ? (
                  <>
                    {format(date.from, "LLL dd", {
                      locale: locale === "nl" ? nl : undefined,
                    })}{" "}
                    -{" "}
                    {format(date.to, "LLL dd", {
                      locale: locale === "nl" ? nl : undefined,
                    })}
                    {selectedFlexibility && selectedFlexibility !== "exact" && (
                      <span className="text-primary ml-1">
                        +{selectedFlexibility}
                      </span>
                    )}
                  </>
                ) : (
                  format(date.from, "LLL dd", {
                    locale: locale === "nl" ? nl : undefined,
                  })
                )
              ) : (
                <span>{placeholder || t("selectDates")}</span>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent 
            className="w-auto p-0 max-h-[70vh] overflow-hidden flex flex-col z-[999999]" 
            align="start" 
            sideOffset={8}
            side="bottom"
            avoidCollisions={true}
            collisionPadding={16}
          >
            <div className="flex-1 overflow-y-auto">
              <Calendar
                initialFocus
                mode="range"
                defaultMonth={date?.from}
                selected={date}
                onSelect={handleDateChange}
                numberOfMonths={2}
                disabled={(date) => date < new Date()}
                className="p-4"
              />
            </div>

            {/* Footer with options */}
            <div className="p-3 border-t border-border bg-white sticky bottom-0 shadow-lg">
              <div className="flex flex-col gap-2">
                {/* Flexibility options */}
                <div className="flex flex-wrap items-center gap-1.5">
                  <button
                    className={`px-2 py-1 text-xs border rounded-full transition-colors whitespace-nowrap ${
                      selectedFlexibility === "exact"
                        ? "bg-primary text-primary-foreground border-primary"
                        : "border-border hover:bg-muted"
                    }`}
                    onClick={() => {
                      setSelectedFlexibility("exact");
                      onFlexibilityChange?.("exact");
                      console.log("Exact dates selected");
                    }}
                  >
                    {t('exactDates')}
                  </button>
                  {[1, 2, 3, 7].map((days) => (
                    <button
                      key={days}
                      className={`px-2 py-1 text-xs border rounded-full transition-colors whitespace-nowrap ${
                        selectedFlexibility === days
                          ? "bg-primary text-primary-foreground border-primary"
                          : "border-border hover:bg-muted"
                      }`}
                      onClick={() => {
                        setSelectedFlexibility(days);
                        onFlexibilityChange?.(days);
                        console.log(`Flexible search: ±${days} days selected`);
                      }}
                    >
                      ±{days} {days === 1 ? t('day') : t('days')}
                    </button>
                  ))}
                </div>

                {/* Action buttons */}
                <div className="flex justify-between items-center gap-2 pt-2">
                  <button
                    onClick={() => {
                      setDate(undefined);
                      onDateRangeChange?.(undefined);
                      setSelectedFlexibility(null);
                      onFlexibilityChange?.(null);
                    }}
                    className="text-primary hover:text-primary/80 font-medium text-sm px-3 py-2"
                  >
                    {t('clear')}
                  </button>
                  <button
                    onClick={() => setOpen(false)}
                    className="bg-primary hover:bg-primary/90 text-primary-foreground px-6 py-2 rounded-lg font-medium text-sm flex-shrink-0"
                  >
                    {t('done')}
                  </button>
                </div>
              </div>
            </div>
          </PopoverContent>
        </Popover>
      )}
      
      {/* Mobile modal */}
      {isMobile && open && renderMobileDatePicker()}
    </>
  );
}