import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { GuestDashboardLayout } from '../features/guest/dashboard';
import { useUser } from '../features/shared/auth/hooks/useAuth';
import { MainLayout } from '../components/MainLayout';

const GuestDashboardPage: React.FC = () => {
  const { data: user } = useUser();

  const { data: guestBookings = [] } = useQuery({
    queryKey: ['/api/bookings/guest'],
    enabled: !!user,
  });

  const { data: wishlists = [] } = useQuery({
    queryKey: ['/api/wishlists'],
    enabled: !!user,
  });

  const { data: guestMessages = [] } = useQuery({
    queryKey: ['/api/messages/guest'],
    enabled: !!user,
  });

  const handleUpgradeToHost = () => {
    // Redirect to host onboarding
    window.location.href = '/host/onboarding';
  };

  return (
    <MainLayout 
      showFooter={false} 
      hideOverlay={true}
      className="bg-gray-50"
      style={{ backgroundImage: 'none', minHeight: '100vh' }}
    >
      <GuestDashboardLayout
        guestBookings={guestBookings as any[]}
        wishlists={wishlists as any[]}
        guestMessages={guestMessages as any[]}
        user={user}
        onUpgradeToHost={handleUpgradeToHost}
        isUpgrading={false}
      />
    </MainLayout>
  );
};

export default GuestDashboardPage;