import { useTranslations } from "@/lib/translations";
import { LocaleSwitcher } from "./LocaleSwitcher";
import { UserMenu } from "./UserMenu";

export const Header = () => {
  const tBrand = useTranslations("brand");

  return (
    <header className="bg-background shadow-sm border-b border-border sticky top-0 z-50 w-full">
      <div className="flex justify-between items-center h-16 w-full px-4 sm:px-6 lg:px-8">
        {/* Logo */}
        <div className="flex items-center min-w-0">
          <a
            href="/"
            className="text-3xl font-bold text-foreground hover:text-primary transition-colors duration-300"
            style={{ marginLeft: 0 }}
          >
            {tBrand("name")}
          </a>
        </div>

        {/* Right side actions */}
        <div className="flex items-center space-x-4 min-w-0 relative">
          <LocaleSwitcher />
          {/* Unified User Menu for all screen sizes */}
          <div className="relative">
            <UserMenu />
          </div>
        </div>
      </div>
    </header>
  );
};
