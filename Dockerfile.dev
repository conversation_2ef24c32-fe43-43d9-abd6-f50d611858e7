# Development Dockerfile for VillaWise
FROM node:20-alpine

WORKDIR /app

# Install system dependencies for development
RUN apk add --no-cache git

# Copy package files
COPY package*.json ./
COPY tsconfig.json ./
COPY vite.config.ts ./
COPY tailwind.config.ts ./
COPY postcss.config.js ./

# Install all dependencies (including dev dependencies)
RUN npm ci

# Copy source code
COPY . .

# Expose development port
EXPOSE 5000

# Start development server
CMD ["npm", "run", "dev"]