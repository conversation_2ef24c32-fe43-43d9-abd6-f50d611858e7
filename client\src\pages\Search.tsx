import { useEffect, useState, useRef } from "react";
import { useSearch } from "wouter";
import {
  PropertyGrid,
  SearchHeader,
  useSearch as usePropertySearch,
} from "../features/public/search";
import { SearchParams } from "@/components/property-searcher";
import { useSearchHistory } from "@/hooks/useSearchHistory";
import { MapView } from "@/components/map/MapView";
import { ViewToggle } from "@/components/map/ViewToggle";
import { useViewMode } from "@/hooks/useViewMode";
import { MainLayout } from "@/components/MainLayout";
import { useSearchState } from "../features/public/search/hooks/useSearchState";
import type { Property } from "@/lib/apiClient";

export default function Search() {
  const searchString = useSearch();
  const { searchState, updateFilters, updateResults, hasActiveSearch } = useSearchState();
  const { addToHistory } = useSearchHistory();
  const { viewMode, setViewMode } = useViewMode();
  const [selectedPropertyId, setSelectedPropertyId] = useState<string | undefined>();
  const lastSearchParamsRef = useRef<string>('');

  // Parse search parameters from URL
  const searchParams = new URLSearchParams(searchString);
  const searchControlParams: SearchParams = {
    location: searchParams.get('location') || '',
    dateRange: searchParams.get('checkIn') && searchParams.get('checkOut') ? {
      from: new Date(searchParams.get('checkIn')!),
      to: new Date(searchParams.get('checkOut')!)
    } : undefined,
    dateFlexibility: searchParams.get('dateFlexibility') ? 
      (searchParams.get('dateFlexibility') === 'exact' ? 'exact' : parseInt(searchParams.get('dateFlexibility')!)) : null,
    guests: {
      adults: parseInt(searchParams.get('adults') || '2'),
      children: parseInt(searchParams.get('children') || '0'),
      infants: parseInt(searchParams.get('infants') || '0'),
      pets: parseInt(searchParams.get('pets') || '0'),
    }
  };

  console.log('🔄 Search page: URL searchString:', searchString);
  console.log('🔄 Search page: searchControlParams:', searchControlParams);
  console.log('🔄 Search page: searchState.filters:', searchState.filters);

  const handleSearchRefine = (params: SearchParams) => {
    const newFilters = {
      location: params.location,
      checkIn: params.dateRange?.from.toISOString().split('T')[0] || "",
      checkOut: params.dateRange?.to.toISOString().split('T')[0] || "",
      dateFlexibility: params.dateFlexibility,
      guests: params.guests
    };
    updateFilters(newFilters);
    
    // Update URL with new parameters including dateFlexibility
    const urlParams = new URLSearchParams({
      location: params.location,
      ...(params.dateRange?.from && {
        checkIn: params.dateRange.from.toISOString().split("T")[0],
      }),
      ...(params.dateRange?.to && {
        checkOut: params.dateRange.to.toISOString().split("T")[0],
      }),
      ...(params.dateFlexibility !== null && params.dateFlexibility !== undefined && {
        dateFlexibility: params.dateFlexibility.toString(),
      }),
      adults: params.guests.adults.toString(),
      children: params.guests.children.toString(),
      infants: params.guests.infants.toString(),
      pets: params.guests.pets.toString(),
    });
    
    // Update URL without causing navigation
    window.history.replaceState({}, '', `/search?${urlParams.toString()}`);
  };

  // Create a stable search key to prevent infinite re-renders - include dateFlexibility
  const searchKey = `${searchState.filters.location}-${searchState.filters.checkIn}-${searchState.filters.checkOut}-${searchState.filters.dateFlexibility}-${JSON.stringify(searchState.filters.guests)}`;
  
  // Only trigger search if parameters actually changed
  const shouldTriggerSearch = searchKey !== lastSearchParamsRef.current && Boolean(searchState.filters.location);
  
  // Update the ref when we trigger a search
  useEffect(() => {
    if (shouldTriggerSearch) {
      lastSearchParamsRef.current = searchKey;
    }
  }, [searchKey, shouldTriggerSearch]);

  // Use debounced search with current search state filters
  const searchFilters = {
    location: searchState.filters.location || "",
    checkIn: searchState.filters.checkIn || "",
    checkOut: searchState.filters.checkOut || "",
    guests: searchState.filters.guests,
  };
  
  const { data: searchResults, isLoading } = usePropertySearch(
    searchFilters,
    { enabled: Boolean(searchState.filters.location) }
  );

  // Update stored results when search completes (disable automatic history saving)
  useEffect(() => {
    if (searchResults && !isLoading && searchState.filters.location) {
      updateResults(searchResults.properties || [], searchResults.total || 0);
      
      // NOTE: Automatic search history saving disabled to prevent input responsiveness issues
      // Search history is only saved when users explicitly perform complete searches
    }
  }, [searchResults, isLoading]); // Remove both updateResults and searchState.filters from dependencies to prevent infinite loop

  // Transform search results for display
  const transformedProperties = (searchResults?.properties || searchState.results || []).map(property => ({
    id: property.id,
    title: property.title,
    hostType: property.propertyType || 'Private host',
    location: property.location,
    maxGuests: property.maxGuests,
    bedrooms: property.bedrooms,
    bathrooms: property.bathrooms,
    price: `€${property.price?.amount || property.price} per night`,
    rating: property.rating,
    reviewCount: property.reviewCount,
    images: property.images || [],
    badge: property.badges?.[0]
  }));

  // Use for property grid display
  const gridProperties = transformedProperties;

  // Transform for map with numeric price - cast to any to resolve type conflicts
  const mapProperties = transformedProperties.map(property => ({
    ...property,
    price: typeof property.price === 'string' 
      ? parseInt(property.price.replace(/[^0-9]/g, '')) || 0
      : property.price
  })) as any[];



  return (
    <MainLayout 
      showFooter={false} 
      className="bg-blue-50/30 min-h-screen" 
      style={{ backgroundImage: 'none' }}
      hideOverlay={true}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <SearchHeader
          searchParams={searchControlParams}
          totalResults={searchResults?.total || searchState.totalResults}
          filters={searchState.filters}
          onFiltersChange={updateFilters}
          onSearchRefine={handleSearchRefine}
        />

        {/* View Toggle Controls */}
        <div className="mt-4 flex justify-end">
          <ViewToggle 
            currentView={viewMode} 
            onViewChange={setViewMode}
          />
        </div>

        {/* Main Content Area */}
        <div className="mt-6">
          {viewMode === 'list' && (
            <PropertyGrid
              properties={gridProperties}
              isLoading={isLoading && !searchState.results.length}
              onPropertyHover={setSelectedPropertyId}
              viewMode="list"
            />
          )}

          {viewMode === 'split' && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 h-[80vh]">
              <div className="overflow-y-auto">
                <PropertyGrid
                  properties={gridProperties}
                  isLoading={isLoading && !searchState.results.length}
                  onPropertyHover={setSelectedPropertyId}
                  viewMode="split"
                />
              </div>
              <div className="sticky top-0">
                <MapView
                  properties={mapProperties}
                  selectedProperty={selectedPropertyId}
                  onPropertySelect={setSelectedPropertyId}
                  className="h-full rounded-lg overflow-hidden border border-gray-200"
                />
              </div>
            </div>
          )}

          {viewMode === 'map' && (
            <div className="h-[80vh]">
              <MapView
                properties={mapProperties}
                selectedProperty={selectedPropertyId}
                onPropertySelect={setSelectedPropertyId}
                className="h-full rounded-lg overflow-hidden border border-gray-200"
              />
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  );
}
