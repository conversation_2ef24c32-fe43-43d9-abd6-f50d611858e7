@echo off
echo Testing VillaWise Local Development Setup (Windows)
echo ====================================================

REM Check Node.js version
echo Checking Node.js version...
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js not found!
    echo Please install Node.js 18+ from https://nodejs.org/
    pause
    exit /b 1
)
echo Node.js version:
node --version

REM Check npm version
echo.
echo NPM version:
npm --version

REM Check if .env.example exists
if not exist .env.example (
    echo ERROR: .env.example not found!
    echo This script must be run from the project root directory.
    pause
    exit /b 1
)

REM Create test .env if it doesn't exist
if not exist .env (
    echo Creating .env from .env.example...
    copy .env.example .env
    echo.
    echo IMPORTANT: Please edit .env and add your Supabase credentials:
    echo   SUPABASE_URL=https://your-project-ref.supabase.co
    echo   SUPABASE_ANON_KEY=your-anon-key-here
    echo   SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here
    echo.
    echo The application will use memory cache by default (no Redis needed).
    pause
)

REM Check if node_modules exists
if not exist node_modules (
    echo Installing dependencies...
    npm install
    if errorlevel 1 (
        echo ERROR: Failed to install dependencies!
        pause
        exit /b 1
    )
)

REM Test TypeScript compilation
echo.
echo Testing TypeScript compilation...
npx tsc --noEmit --skipLibCheck
if errorlevel 1 (
    echo ERROR: TypeScript compilation failed!
    pause
    exit /b 1
)

echo.
echo SUCCESS: All checks passed!
echo.
echo To start development:
echo   npm run dev
echo   OR
echo   scripts\windows\start-dev.bat
echo.
echo To build for production:
echo   npm run build
echo.
echo To test Docker:
echo   scripts\windows\docker-build.bat
echo   scripts\windows\docker-run.bat
echo.
echo The application uses memory cache by default - no Redis setup required.
pause