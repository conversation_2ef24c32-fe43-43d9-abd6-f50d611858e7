// GeoNames data downloader
import fs from 'fs/promises';
import path from 'path';
import https from 'https';
import { createWriteStream } from 'fs';
import { createGunzip } from 'zlib';
import { pipeline } from 'stream/promises';
import AdmZip from 'adm-zip';
import { GeoNamesConfigManager } from '../../config/geonames-scope';

export class GeoNamesDownloader {
  private readonly baseUrl = 'https://download.geonames.org/export/zip/';
  private readonly dataDir: string;
  private configManager: GeoNamesConfigManager;

  constructor(dataDir = './data') {
    this.dataDir = dataDir;
    this.configManager = new GeoNamesConfigManager();
  }

  async ensureDataDir(): Promise<void> {
    try {
      await fs.access(this.dataDir);
    } catch {
      await fs.mkdir(this.dataDir, { recursive: true });
    }
  }

  async downloadCountryData(countryCode: string): Promise<string> {
    await this.ensureDataDir();
    
    const zipUrl = `${this.baseUrl}${countryCode}.zip`;
    const zipPath = path.join(this.dataDir, `${countryCode}.zip`);
    const txtPath = path.join(this.dataDir, `${countryCode}.txt`);

    // Check if already exists and is recent (within 24 hours)
    try {
      const stats = await fs.stat(txtPath);
      const age = Date.now() - stats.mtime.getTime();
      if (age < 24 * 60 * 60 * 1000) {
        console.log(`[DOWNLOAD] Using cached ${countryCode} data`);
        return txtPath;
      }
    } catch {
      // File doesn't exist, continue with download
    }

    console.log(`[DOWNLOAD] Downloading ${countryCode} from ${zipUrl}`);

    try {
      // Download zip file
      await this.downloadFile(zipUrl, zipPath);
      
      // Extract zip file
      await this.extractZip(zipPath, txtPath);
      
      // Clean up zip file
      await fs.unlink(zipPath);
      
      console.log(`[DOWNLOAD] Successfully downloaded ${countryCode}`);
      return txtPath;
    } catch (error) {
      console.error(`[DOWNLOAD] Failed to download ${countryCode}:`, error);
      throw error;
    }
  }

  async downloadAlternateNames(): Promise<string> {
    await this.ensureDataDir();
    
    const zipUrl = `https://download.geonames.org/export/dump/alternateNamesV2.zip`;
    const zipPath = path.join(this.dataDir, 'alternateNamesV2.zip');
    const txtPath = path.join(this.dataDir, 'alternateNamesV2.txt');

    // Check if already exists and is recent
    try {
      const stats = await fs.stat(txtPath);
      const age = Date.now() - stats.mtime.getTime();
      if (age < 24 * 60 * 60 * 1000) {
        console.log('[DOWNLOAD] Using cached alternate names data');
        return txtPath;
      }
    } catch {
      // File doesn't exist, continue with download
    }

    console.log(`[DOWNLOAD] Downloading alternate names from ${zipUrl}`);

    try {
      await this.downloadFile(zipUrl, zipPath);
      await this.extractZip(zipPath, txtPath);
      await fs.unlink(zipPath);
      
      console.log('[DOWNLOAD] Successfully downloaded alternate names');
      return txtPath;
    } catch (error) {
      console.error('[DOWNLOAD] Failed to download alternate names:', error);
      throw error;
    }
  }

  private async downloadFile(url: string, outputPath: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const file = createWriteStream(outputPath);
      
      https.get(url, (response) => {
        if (response.statusCode !== 200) {
          reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`));
          return;
        }
        
        response.pipe(file);
        
        file.on('finish', () => {
          file.close();
          resolve();
        });
        
        file.on('error', reject);
      }).on('error', reject);
    });
  }

  private async extractZip(zipPath: string, outputPath: string): Promise<void> {
    try {
      const zip = new AdmZip(zipPath);
      const zipEntries = zip.getEntries();
      
      console.log(`[EXTRACT] Found ${zipEntries.length} entries in ${zipPath}`);
      zipEntries.forEach((entry: any) => {
        console.log(`[EXTRACT] - ${entry.entryName} (${entry.header.size} bytes)`);
      });
      
      // Find the .txt file in the zip (should be the main data file, not README)
      // For alternate names, prioritize the large data file over language codes
      const txtEntry = zipEntries.find((entry: any) => 
        entry.entryName.endsWith('.txt') && 
        !entry.entryName.toLowerCase().includes('readme') &&
        !entry.entryName.toLowerCase().includes('iso-language') &&
        entry.header.size > 10000000 // At least 10MB for real data files
      ) || zipEntries.find((entry: any) => 
        entry.entryName.endsWith('.txt') && 
        !entry.entryName.toLowerCase().includes('readme') &&
        entry.header.size > 1000 // Fallback to any substantial .txt file
      );
      
      if (!txtEntry) {
        // If no large .txt file found, try any .txt file
        const anyTxtEntry = zipEntries.find((entry: any) => entry.entryName.endsWith('.txt'));
        if (anyTxtEntry) {
          console.log(`[EXTRACT] Warning: Using ${anyTxtEntry.entryName} (${anyTxtEntry.header.size} bytes)`);
          await fs.writeFile(outputPath, anyTxtEntry.getData());
        } else {
          throw new Error('No .txt file found in zip archive');
        }
      } else {
        const sizeInMB = Math.round(txtEntry.header.size / 1024 / 1024);
        console.log(`[EXTRACT] Extracting ${txtEntry.entryName} (${sizeInMB}MB)`);
        
        // For large files (>100MB), extract directly without loading into memory
        if (txtEntry.header.size > 100 * 1024 * 1024) {
          console.log(`[EXTRACT] Large file detected, using streaming extraction`);
          
          // Extract the entry to a buffer and write in chunks
          const entryBuffer = txtEntry.getData();
          await fs.writeFile(outputPath, entryBuffer);
        } else {
          await fs.writeFile(outputPath, txtEntry.getData());
        }
      }
      
    } catch (error) {
      console.error('[EXTRACT] Failed to extract zip:', error);
      throw error;
    }
  }

  async downloadAllConfiguredData(): Promise<{
    countryFiles: Map<string, string>;
    alternateNamesFile: string;
  }> {
    const countries = this.configManager.getAllCountryCodes();
    const countryFiles = new Map<string, string>();

    console.log(`[DOWNLOAD] Starting download for ${countries.length} countries`);

    // Download country files in parallel (with limit)
    const concurrencyLimit = 3;
    const chunks = [];
    
    for (let i = 0; i < countries.length; i += concurrencyLimit) {
      chunks.push(countries.slice(i, i + concurrencyLimit));
    }

    for (const chunk of chunks) {
      const promises = chunk.map(async (country) => {
        const filePath = await this.downloadCountryData(country);
        countryFiles.set(country, filePath);
      });

      await Promise.all(promises);
    }

    // Download alternate names
    const alternateNamesFile = await this.downloadAlternateNames();

    return {
      countryFiles,
      alternateNamesFile
    };
  }

  async cleanup(): Promise<void> {
    try {
      const files = await fs.readdir(this.dataDir);
      
      // Remove files older than 48 hours
      const now = Date.now();
      const maxAge = 48 * 60 * 60 * 1000;
      
      for (const file of files) {
        const filePath = path.join(this.dataDir, file);
        const stats = await fs.stat(filePath);
        
        if (now - stats.mtime.getTime() > maxAge) {
          await fs.unlink(filePath);
          console.log(`[CLEANUP] Removed old file: ${file}`);
        }
      }
    } catch (error) {
      console.warn('[CLEANUP] Failed to cleanup old files:', error);
    }
  }
}