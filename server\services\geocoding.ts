import { supabase } from '../supabase';

export interface GeocodeResult {
  address: string;
  latitude: number;
  longitude: number;
  confidence: number;
  provider: string;
}

export class GeocodingService {
  private static readonly CACHE_TTL = 30 * 24 * 60 * 60 * 1000; // 30 days
  private static readonly OPENCAGE_API_KEY = process.env.OPENCAGE_API_KEY;

  /**
   * Geocode an address to coordinates with caching
   */
  async geocodeLocation(address: string): Promise<{lat: number, lng: number} | null> {
    try {
      // Check cache first
      const cached = await this.getCachedGeocode(address);
      if (cached) {
        console.log(`[GEOCODING] Cache hit for: ${address}`);
        return { lat: cached.latitude, lng: cached.longitude };
      }

      // Try OpenCage API if available
      if (GeocodingService.OPENCAGE_API_KEY) {
        const result = await this.geocodeWithOpenCage(address);
        if (result) {
          await this.cacheGeocode(address, result);
          return { lat: result.latitude, lng: result.longitude };
        }
      }

      // Fallback to Maps.co
      const result = await this.geocodeWithMapsCo(address);
      if (result) {
        await this.cacheGeocode(address, result);
        return { lat: result.latitude, lng: result.longitude };
      }

      console.warn(`[GEOCODING] Failed to geocode: ${address}`);
      return null;
    } catch (error) {
      console.error(`[GEOCODING] Error geocoding ${address}:`, error);
      return null;
    }
  }

  /**
   * OpenCage geocoding implementation
   */
  private async geocodeWithOpenCage(address: string): Promise<GeocodeResult | null> {
    const url = `https://api.opencagedata.com/geocode/v1/json?q=${encodeURIComponent(address)}&key=${GeocodingService.OPENCAGE_API_KEY}&limit=1`;
    
    const response = await fetch(url);
    const data = await response.json();
    
    if (data.results && data.results.length > 0) {
      const result = data.results[0];
      return {
        address: result.formatted,
        latitude: result.geometry.lat,
        longitude: result.geometry.lng,
        confidence: result.confidence,
        provider: 'opencage'
      };
    }
    
    return null;
  }

  /**
   * Maps.co geocoding implementation (free fallback)
   */
  private async geocodeWithMapsCo(address: string): Promise<GeocodeResult | null> {
    const url = `https://geocode.maps.co/search?q=${encodeURIComponent(address)}&limit=1`;
    
    const response = await fetch(url);
    const data = await response.json();
    
    if (data && data.length > 0) {
      const result = data[0];
      return {
        address: result.display_name,
        latitude: parseFloat(result.lat),
        longitude: parseFloat(result.lon),
        confidence: 0.8,
        provider: 'maps.co'
      };
    }
    
    return null;
  }

  /**
   * Get cached geocode result
   */
  private async getCachedGeocode(address: string): Promise<GeocodeResult | null> {
    if (!supabase) return null;
    
    const { data, error } = await supabase
      .from('geocoding_cache')
      .select('*')
      .eq('address', address.toLowerCase())
      .gte('created_at', new Date(Date.now() - GeocodingService.CACHE_TTL).toISOString())
      .single();
    
    if (error || !data) return null;
    
    return {
      address: data.address,
      latitude: data.latitude,
      longitude: data.longitude,
      confidence: data.confidence,
      provider: data.provider
    };
  }

  /**
   * Cache geocode result
   */
  private async cacheGeocode(address: string, result: GeocodeResult): Promise<void> {
    if (!supabase) return;
    
    try {
      await supabase
        .from('geocoding_cache')
        .upsert({
          address: address.toLowerCase(),
          latitude: result.latitude,
          longitude: result.longitude,
          confidence: result.confidence,
          provider: result.provider
        }, { onConflict: 'address' });
    } catch (error) {
      console.error('[GEOCODING] Error caching result:', error);
    }
  }
}

export const geocodingService = new GeocodingService();