import { Router } from 'express';
import { guestReviewController } from '../../controllers/guest/guestReviewController';

const router = Router();

// Guest review routes
router.get('/reviews/:guestId', guestReviewController.getReviewsByGuest.bind(guestReviewController));
router.get('/reviews/property/:propertyId', guestReviewController.getReviewsByProperty.bind(guestReviewController));
router.post('/review', guestReviewController.createReview.bind(guestReviewController));

export default router;