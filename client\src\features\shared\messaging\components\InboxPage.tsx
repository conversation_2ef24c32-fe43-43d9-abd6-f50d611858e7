import { useState, useEffect } from 'react';
import { useLocation, useParams } from 'wouter';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  MessageCircle, 
  Search, 
  Filter, 
  Plus, 
  MoreVertical,
  MapPin,
  Calendar,
  Users
} from 'lucide-react';
import { useRealTimeConversations } from '../hooks/useRealTimeMessages';
import { useUser } from '@/features/shared/auth/hooks/useAuth';
import { useTranslations } from '@/lib/translations';
import { ConversationItem } from './ConversationItem';
import { MessageThread } from './MessageThread';
// import { NewConversationModal } from './NewConversationModal';

interface ConversationData {
  id: string;
  guest_id: string;
  host_id: string;
  property_id?: string;
  subject?: string;
  status: string;
  last_message_at: string;
  unread_count: number;
  last_message?: {
    content: string;
    sender_type: string;
    created_at: string;
  };
  property?: {
    id: string;
    title: string;
    location: string;
  };
  participant?: {
    id: string;
    name: string;
    avatar_url?: string;
  };
}

export const InboxPage = () => {
  const [location, setLocation] = useLocation();
  const { conversationId } = useParams<{ conversationId?: string }>();
  const { data: user } = useUser();
  const t = useTranslations('messaging');
  
  // State
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<'active' | 'archived'>('active');
  const [selectedConversation, setSelectedConversation] = useState<ConversationData | null>(null);
  const [showNewConversation, setShowNewConversation] = useState(false);
  
  // Real-time conversations
  const { conversations, isLoading, isConnected } = useRealTimeConversations(
    user?.id,
    (user as any)?.user_metadata?.role || 'guest'
  );
  
  // Filter conversations
  const filteredConversations = conversations.filter((conv: ConversationData) => {
    const matchesSearch = !searchQuery || 
      conv.subject?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      conv.participant?.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      conv.property?.title.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesStatus = conv.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });
  
  // Handle conversation selection
  useEffect(() => {
    if (conversationId) {
      const conversation = conversations.find((c: ConversationData) => c.id === conversationId);
      if (conversation) {
        setSelectedConversation(conversation);
      }
    } else {
      setSelectedConversation(null);
    }
  }, [conversationId, conversations]);
  
  // Handle conversation click
  const handleConversationClick = (conversation: ConversationData) => {
    setSelectedConversation(conversation);
    setLocation(`/inbox/${conversation.id}`);
  };
  
  // Calculate total unread count
  const totalUnreadCount = filteredConversations.reduce(
    (sum: number, conv: ConversationData) => sum + conv.unread_count, 
    0
  );
  
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <MessageCircle className="h-12 w-12 mx-auto mb-4 text-muted-foreground animate-pulse" />
          <p className="text-muted-foreground">Loading messages...</p>
        </div>
      </div>
    );
  }
  
  return (
    <div className="flex h-[calc(100vh-200px)] bg-background">
      {/* Conversations Sidebar */}
      <div className="w-80 border-r bg-muted/20">
        <div className="p-4 border-b bg-background">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <MessageCircle className="h-5 w-5" />
              <h2 className="font-semibold">Messages</h2>
              {totalUnreadCount > 0 && (
                <Badge variant="destructive" className="h-5 px-2 text-xs">
                  {totalUnreadCount}
                </Badge>
              )}
            </div>
            <div className="flex items-center gap-1">
              <Button 
                variant="ghost" 
                size="sm"
                onClick={() => setShowNewConversation(true)}
              >
                <Plus className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </div>
          </div>
          
          {/* Search */}
          <div className="relative mb-3">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search conversations..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-9"
            />
          </div>
          
          {/* Filters */}
          <div className="flex gap-2">
            <Button
              variant={statusFilter === 'active' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setStatusFilter('active')}
              className="flex-1"
            >
              Active
            </Button>
            <Button
              variant={statusFilter === 'archived' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setStatusFilter('archived')}
              className="flex-1"
            >
              Archived
            </Button>
          </div>
          
          {/* Connection Status */}
          <div className="flex items-center gap-2 mt-3 text-xs text-muted-foreground">
            <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
            {isConnected ? 'Connected' : 'Connecting...'}
          </div>
        </div>
        
        {/* Conversations List */}
        <ScrollArea className="flex-1">
          <div className="p-2">
            {filteredConversations.length === 0 ? (
              <div className="text-center py-8">
                <MessageCircle className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <p className="text-muted-foreground mb-2">No conversations found</p>
                {statusFilter === 'active' && (
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => setShowNewConversation(true)}
                  >
                    Start New Conversation
                  </Button>
                )}
              </div>
            ) : (
              filteredConversations.map((conversation: ConversationData) => (
                <ConversationItem
                  key={conversation.id}
                  conversation={conversation}
                  isSelected={selectedConversation?.id === conversation.id}
                  onClick={() => handleConversationClick(conversation)}
                  currentUserId={user?.id}
                />
              ))
            )}
          </div>
        </ScrollArea>
      </div>
      
      {/* Message Thread */}
      <div className="flex-1 flex flex-col">
        {selectedConversation ? (
          <MessageThread
            conversation={selectedConversation}
            currentUserId={user?.id}
            userType={(user as any)?.user_metadata?.role || 'guest'}
          />
        ) : (
          <div className="flex-1 flex items-center justify-center bg-muted/10">
            <div className="text-center">
              <MessageCircle className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-lg font-medium mb-2">Select a conversation</h3>
              <p className="text-muted-foreground max-w-sm">
                Choose a conversation from the sidebar to start messaging, or create a new conversation.
              </p>
              <Button 
                className="mt-4"
                onClick={() => setShowNewConversation(true)}
              >
                <Plus className="h-4 w-4 mr-2" />
                New Conversation
              </Button>
            </div>
          </div>
        )}
      </div>
      
      {/* New Conversation Modal - TODO: Implement */}
      {showNewConversation && (
        <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center">
          <div className="bg-background p-6 rounded-lg max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">New Conversation</h3>
            <p className="text-muted-foreground mb-4">
              New conversation modal will be implemented in the next phase.
            </p>
            <Button onClick={() => setShowNewConversation(false)}>
              Close
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};