import { Router } from 'express';
import { guestWishlistController } from '../../controllers/guest/guestWishlistController';

const router = Router();

// Guest wishlist routes
router.get('/wishlists/:guestId', guestWishlistController.getWishlists.bind(guestWishlistController));
router.get('/wishlist/:wishlistId', guestWishlistController.getWishlist.bind(guestWishlistController));
router.post('/wishlist', guestWishlistController.createWishlist.bind(guestWishlistController));
router.put('/wishlist/:wishlistId', guestWishlistController.updateWishlist.bind(guestWishlistController));
router.delete('/wishlist/:wishlistId', guestWishlistController.deleteWishlist.bind(guestWishlistController));

// Wishlist property routes
router.get('/wishlist/:wishlistId/properties', guestWishlistController.getWishlistProperties.bind(guestWishlistController));
router.post('/wishlist/:wishlistId/properties', guestWishlistController.addPropertyToWishlist.bind(guestWishlistController));
router.delete('/wishlist/:wishlistId/properties/:propertyId', guestWishlistController.removePropertyFromWishlist.bind(guestWishlistController));

export default router;