import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useTranslations } from '@/lib/translations';
import { CalendarDays, MapPin } from 'lucide-react';

interface BookingsContentProps {
  guestBookings: any[];
}

const BookingsContent: React.FC<BookingsContentProps> = ({ guestBookings }) => {
  const t = useTranslations('guestDashboard');

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'bg-green-50 text-green-700';
      case 'pending':
        return 'bg-yellow-50 text-yellow-700';
      case 'completed':
        return 'bg-blue-50 text-blue-700';
      case 'cancelled':
        return 'bg-red-50 text-red-700';
      default:
        return 'bg-gray-50 text-gray-700';
    }
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-semibold text-gray-900">{t('navigation.bookings')}</h2>
        <p className="text-gray-600 mt-2">Manage your trip bookings and reservations</p>
      </div>

      {guestBookings.length > 0 ? (
        <div className="space-y-4">
          {guestBookings.map((booking: any) => (
            <Card key={booking.id}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                      <MapPin className="h-8 w-8 text-gray-500" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-lg">{booking.property?.title || 'Property'}</h3>
                      <p className="text-gray-600">{booking.property?.location || 'Location'}</p>
                      <p className="text-sm text-gray-500">
                        {formatDate(booking.check_in_date)} - {formatDate(booking.check_out_date)}
                      </p>
                      <p className="text-sm text-gray-500">
                        {booking.guest_count} guests • {booking.nights || 0} nights
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <Badge variant="outline" className={getStatusColor(booking.status)}>
                      {booking.status}
                    </Badge>
                    <p className="text-lg font-semibold text-gray-900 mt-2">
                      {formatCurrency(booking.total_amount)}
                    </p>
                    <p className="text-sm text-gray-500">
                      Booking #{booking.id}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <CalendarDays className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600">No bookings yet</p>
          <p className="text-sm text-gray-500 mt-2">Start exploring properties to make your first booking</p>
        </div>
      )}
    </div>
  );
};

export default BookingsContent;