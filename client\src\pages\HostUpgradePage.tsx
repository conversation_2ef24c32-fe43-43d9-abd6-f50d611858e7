import React, { useState } from 'react';
import { useLocation } from 'wouter';
import { MainLayout } from '../components/MainLayout';
import { useUser } from '../features/shared/auth/hooks/useAuth';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Loader2, Crown, CheckCircle, AlertCircle, Home, Users, DollarSign, Calendar } from 'lucide-react';
import { useTranslations } from '../lib/translations';
import { useToast } from '../hooks/use-toast';
import { apiRequest } from '../lib/queryClient';

const HostUpgradePage: React.FC = () => {
  const { data: user, isLoading: userLoading, refetch } = useUser();
  const [, navigate] = useLocation();
  const [isUpgrading, setIsUpgrading] = useState(false);
  const t = useTranslations('hostUpgrade');
  const { toast } = useToast();

  const handleUpgrade = async () => {
    setIsUpgrading(true);
    try {
      const data = await apiRequest('/api/upgrade-to-host', {
        method: 'POST'
      });

      if (data.success) {
        toast({
          title: t('upgradeSuccess'),
          description: t('upgradeSuccessDescription'),
          duration: 5000
        });
        
        // Refetch user data to update state
        await refetch();
        
        // Navigate to host dashboard after successful upgrade
        setTimeout(() => {
          navigate('/host/dashboard');
        }, 2000);
      } else {
        throw new Error(data.message || 'Upgrade failed');
      }
    } catch (error) {
      console.error('Upgrade error:', error);
      toast({
        title: t('upgradeFailed'),
        description: t('upgradeFailedDescription'),
        variant: 'destructive',
        duration: 5000
      });
    } finally {
      setIsUpgrading(false);
    }
  };

  // Show loading state while checking authentication
  if (userLoading) {
    return (
      <MainLayout className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-96">
          <CardContent className="p-8 text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
            <p className="text-gray-600">{t('loading')}</p>
          </CardContent>
        </Card>
      </MainLayout>
    );
  }

  // Redirect to login if not authenticated
  if (!user) {
    navigate('/login');
    return null;
  }

  // Redirect to host dashboard if already a host
  if (user.is_host) {
    navigate('/host/dashboard');
    return null;
  }

  return (
    <MainLayout className="min-h-screen bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800">
      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* Header - Improved readability */}
        <div className="text-center mb-12">
          <div className="relative inline-block">
            <Crown className="h-20 w-20 text-yellow-400 mx-auto mb-6 drop-shadow-lg" />
          </div>
          <h1 className="text-5xl font-bold text-white mb-4 drop-shadow-lg">
            {t('title')}
          </h1>
          <p className="text-blue-100 text-xl max-w-2xl mx-auto drop-shadow-md">
            {t('subtitle')}
          </p>
        </div>

        {/* Main Content - Two Column Layout with Equal Height */}
        <div className="grid md:grid-cols-2 gap-8">
          {/* Benefits Card */}
          <Card className="bg-white/95 backdrop-blur-sm border-0 shadow-xl flex flex-col h-full">
            <CardHeader className="pb-4 flex-shrink-0">
              <CardTitle className="text-2xl font-bold text-gray-900 flex items-center">
                <CheckCircle className="h-6 w-6 text-green-600 mr-3" />
                {t('benefits.title')}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6 flex-grow">
              {/* List Property */}
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center">
                  <Home className="h-5 w-5 text-yellow-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-1">
                    {t('benefits.listProperty.title')}
                  </h3>
                  <p className="text-gray-600">
                    {t('benefits.listProperty.description')}
                  </p>
                </div>
              </div>

              {/* Earn Money */}
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                  <DollarSign className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-1">
                    {t('benefits.earnMoney.title')}
                  </h3>
                  <p className="text-gray-600">
                    {t('benefits.earnMoney.description')}
                  </p>
                </div>
              </div>

              {/* Manage Bookings */}
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                  <Calendar className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-1">
                    {t('benefits.manageBookings.title')}
                  </h3>
                  <p className="text-gray-600">
                    {t('benefits.manageBookings.description')}
                  </p>
                </div>
              </div>

              {/* Host Community */}
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                  <Users className="h-5 w-5 text-purple-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-1">
                    {t('benefits.hostCommunity.title')}
                  </h3>
                  <p className="text-gray-600">
                    {t('benefits.hostCommunity.description')}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Upgrade Card */}
          <Card className="bg-white/95 backdrop-blur-sm border-0 shadow-xl flex flex-col h-full">
            <CardHeader className="pb-4 flex-shrink-0">
              <CardTitle className="text-2xl font-bold text-gray-900 flex items-center">
                <Crown className="h-6 w-6 text-yellow-500 mr-3" />
                {t('upgrade.title')}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6 flex-grow flex flex-col">
              {/* Requirements */}
              <div className="flex-grow">
                <div className="flex items-center mb-4">
                  <AlertCircle className="h-5 w-5 text-blue-600 mr-2" />
                  <h3 className="font-semibold text-gray-900">
                    {t('upgrade.requirements.title')}
                  </h3>
                </div>
                <ul className="space-y-3 text-gray-600">
                  <li className="flex items-center">
                    <span className="text-green-600 mr-2 text-lg">•</span>
                    {t('upgrade.requirements.verified')}
                  </li>
                  <li className="flex items-center">
                    <span className="text-green-600 mr-2 text-lg">•</span>
                    {t('upgrade.requirements.agreement')}
                  </li>
                  <li className="flex items-center">
                    <span className="text-green-600 mr-2 text-lg">•</span>
                    {t('upgrade.requirements.guidelines')}
                  </li>
                </ul>
              </div>

              {/* Action Buttons */}
              <div className="space-y-3 pt-4 flex-shrink-0">
                <Button 
                  onClick={handleUpgrade}
                  disabled={isUpgrading}
                  className="w-full bg-yellow-500 hover:bg-yellow-600 text-white font-semibold py-3 text-lg"
                >
                  {isUpgrading ? (
                    <>
                      <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                      {t('upgrade.upgrading')}
                    </>
                  ) : (
                    <>
                      <Crown className="mr-2 h-5 w-5" />
                      {t('upgrade.button')}
                    </>
                  )}
                </Button>
                
                <Button 
                  variant="outline"
                  onClick={() => navigate('/')}
                  className="w-full"
                >
                  {t('upgrade.backToHome')}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </MainLayout>
  );
};

export default HostUpgradePage;