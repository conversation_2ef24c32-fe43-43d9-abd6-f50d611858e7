// Test script to verify date caching functionality
// This script will create a test scenario where dates are cached and restored

// Create a test search with dates
const testSearchData = {
  location: "Calpe",
  dateRange: {
    from: new Date('2025-08-10'),
    to: new Date('2025-08-17')
  },
  dateFlexibility: 2,
  guests: {
    adults: 2,
    children: 1,
    infants: 0,
    pets: 0
  }
};

// Save to localStorage
const searchDefaults = {
  guests: testSearchData.guests,
  recentLocations: [],
  lastSearch: {
    location: testSearchData.location,
    dateRange: {
      from: testSearchData.dateRange.from.toISOString(),
      to: testSearchData.dateRange.to.toISOString()
    },
    dateFlexibility: testSearchData.dateFlexibility,
    guests: testSearchData.guests,
    timestamp: Date.now()
  }
};

console.log('Setting test data in localStorage...');
localStorage.setItem('villawise-search-defaults', JSON.stringify(searchDefaults));

// Verify it was saved
const saved = localStorage.getItem('villawise-search-defaults');
console.log('Saved data:', JSON.parse(saved));

console.log('Test search data with dates has been saved. Reload the page to test.');