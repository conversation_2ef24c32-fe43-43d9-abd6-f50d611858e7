
import { requireAuth, requireRole } from '../auth/session'
import { getHostProperties } from '../entities/properties'
import { getBookings, getBookingStats } from '../entities/bookings'
import { getMessages, getMessageStats, getConversations } from '../entities/messages'
import { getCurrentUserProfile } from '../entities/users'
import { memoryCache } from '../cache/memoryCache'
import { UserDTO } from '../dto/user.dto'
import { PropertyDTO } from '../dto/property.dto'
import { BookingDTO, BookingStats } from '../dto/booking.dto'
import { MessageDTO, MessageStats, Conversation } from '../dto/message.dto'

export interface HostDashboardData {
  profile: UserDTO | null
  properties: PropertyDTO[]
  bookings: BookingDTO[]
  messages: MessageDTO[]
  conversations: Conversation[]
  stats: {
    bookings: BookingStats
    messages: MessageStats
    properties: {
      total: number
      active: number
      draft: number
      paused: number
    }
  }
}

export interface GuestDashboardData {
  profile: UserDTO | null
  bookings: BookingDTO[]
  messages: MessageDTO[]
  conversations: Conversation[]
  stats: {
    bookings: BookingStats
    messages: MessageStats
  }
}

export const getHostDashboardData = async (authHeader: string): Promise<HostDashboardData> => {
  const user = await requireRole(authHeader, 'host')
  
  const cacheKey = `dashboard:host:${user.userId}`
  const cached = memoryCache.get(cacheKey) as HostDashboardData | null
  if (cached) return cached
  
  try {
    // Parallel data fetching
    const [profile, properties, bookings, messages, conversations, bookingStats, messageStats] = await Promise.all([
      getCurrentUserProfile(authHeader),
      getHostProperties(authHeader),
      getBookings(authHeader, 'host', { status: ['pending', 'confirmed'] }),
      getMessages(authHeader, 'host', { unreadOnly: true }),
      getConversations(authHeader),
      getBookingStats(authHeader, 'host'),
      getMessageStats(authHeader, 'host')
    ])
    
    // Calculate property stats
    const propertyStats = {
      total: properties.length,
      active: properties.filter(p => p.status === 'active').length,
      draft: properties.filter(p => p.status === 'draft').length,
      paused: properties.filter(p => p.status === 'paused').length
    }
    
    const dashboardData: HostDashboardData = {
      profile,
      properties: properties.slice(0, 5), // Latest 5 properties
      bookings: bookings.slice(0, 10), // Latest 10 bookings
      messages: messages.slice(0, 5), // Latest 5 unread messages
      conversations: conversations.slice(0, 10), // Latest 10 conversations
      stats: {
        bookings: bookingStats,
        messages: messageStats,
        properties: propertyStats
      }
    }
    
    // Cache for 2 minutes (dashboard data should be relatively fresh)
    memoryCache.set(cacheKey, dashboardData, 120)
    
    return dashboardData
  } catch (error) {
    console.error('Error fetching host dashboard data:', error)
    throw error
  }
}

export const getGuestDashboardData = async (authHeader: string): Promise<GuestDashboardData> => {
  const user = await requireAuth(authHeader)
  
  const cacheKey = `dashboard:guest:${user.userId}`
  const cached = memoryCache.get(cacheKey) as GuestDashboardData | null
  if (cached) return cached
  
  try {
    // Parallel data fetching
    const [profile, bookings, messages, conversations, bookingStats, messageStats] = await Promise.all([
      getCurrentUserProfile(authHeader),
      getBookings(authHeader, 'guest', { status: ['confirmed', 'pending'] }),
      getMessages(authHeader, 'guest', { unreadOnly: true }),
      getConversations(authHeader),
      getBookingStats(authHeader, 'guest'),
      getMessageStats(authHeader, 'guest')
    ])
    
    const dashboardData: GuestDashboardData = {
      profile,
      bookings: bookings.slice(0, 10), // Latest 10 bookings
      messages: messages.slice(0, 5), // Latest 5 unread messages
      conversations: conversations.slice(0, 10), // Latest 10 conversations
      stats: {
        bookings: bookingStats,
        messages: messageStats
      }
    }
    
    // Cache for 2 minutes
    memoryCache.set(cacheKey, dashboardData, 120)
    
    return dashboardData
  } catch (error) {
    console.error('Error fetching guest dashboard data:', error)
    throw error
  }
}

export const getAdminDashboardData = async (authHeader: string) => {
  const user = await requireRole(authHeader, 'admin')
  
  const cacheKey = 'dashboard:admin'
  const cached = memoryCache.get(cacheKey)
  if (cached) return cached
  
  try {
    // Import user and property stats functions
    const { getUserStats } = await import('../entities/users')
    const { getProperties } = await import('../entities/properties')
    
    // Parallel data fetching
    const [userStats, recentProperties] = await Promise.all([
      getUserStats(authHeader),
      getProperties(authHeader, {})
    ])
    
    const dashboardData = {
      stats: {
        users: userStats,
        properties: {
          total: recentProperties.total,
          active: recentProperties.properties.filter(p => p.status === 'active').length,
          pending: recentProperties.properties.filter(p => p.status === 'draft').length
        }
      },
      recentProperties: recentProperties.properties.slice(0, 10)
    }
    
    // Cache for 5 minutes
    memoryCache.set(cacheKey, dashboardData, 300)
    
    return dashboardData
  } catch (error) {
    console.error('Error fetching admin dashboard data:', error)
    throw error
  }
}

// Invalidate dashboard caches when relevant data changes
export const invalidateDashboardCache = (userId: string, role?: string) => {
  if (role === 'host' || !role) {
    memoryCache.delete(`dashboard:host:${userId}`)
  }
  if (role === 'guest' || !role) {
    memoryCache.delete(`dashboard:guest:${userId}`)
  }
  if (role === 'admin' || !role) {
    memoryCache.delete('dashboard:admin')
  }
}