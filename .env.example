# VillaWise Environment Configuration
# Copy this file to .env and fill in your actual values

# Application
NODE_ENV=development
PORT=5000

# Supabase Configuration (Required)
SUPABASE_URL=https://your-project-ref.supabase.co
SUPABASE_ANON_KEY=your-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here

# Cache Configuration (Optional)
USE_REDIS_CACHE=false

# Upstash Redis Configuration (Optional - only needed if USE_REDIS_CACHE=true)
# Sign up at https://console.upstash.com/ to get these credentials
UPSTASH_REDIS_REST_URL=https://your-upstash-url.upstash.io
UPSTASH_REDIS_REST_TOKEN=your-upstash-token

# Domain Configuration (Optional)
# Set this for production deployments if you need to override Vite host checking
CUSTOM_DOMAIN=yourdomain.com

# OAuth Configuration (Auto-detected)
# The system automatically detects the origin domain from request headers
# No manual configuration needed - works with Replit, Railway, custom domains