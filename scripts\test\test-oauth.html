<!DOCTYPE html>
<html>
<head>
    <title>OAuth Test</title>
</head>
<body>
    <h1>OAuth Test Page</h1>
    <script>
        console.log('Test: Current URL:', window.location.href);
        console.log('Test: Hash:', window.location.hash);
        
        // Simulate the OAuth callback processing
        if (window.location.hash) {
            const hashParams = new URLSearchParams(window.location.hash.substring(1));
            const accessToken = hashParams.get('access_token');
            const refreshToken = hashParams.get('refresh_token');
            
            console.log('Test: Found tokens:', {
                accessToken: accessToken ? 'present' : 'missing',
                refreshToken: refreshToken ? 'present' : 'missing'
            });
            
            if (accessToken && refreshToken) {
                document.body.innerHTML += '<p>✅ OAuth tokens found and processed successfully!</p>';
                console.log('Test: Tokens would be stored in localStorage');
            } else {
                document.body.innerHTML += '<p>❌ OAuth tokens not found</p>';
            }
        } else {
            document.body.innerHTML += '<p>No OAuth tokens in URL hash</p>';
        }
    </script>
</body>
</html>