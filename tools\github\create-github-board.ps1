# GitHub Project Board Creation Script for VillaWise MVP (PowerShell)
# This script loads all issues from create-issues-batch.json and imports them to GitHub
# Run this after installing GitHub CLI: https://cli.github.com/

param(
    [Parameter(Mandatory=$true)]
    [string]$RepoOwner,
    
    [Parameter(Mandatory=$true)]
    [string]$RepoName
)

# Configuration
$REPO = "$RepoOwner/$RepoName"
$PROJECT_TITLE = "VillaWise MVP - Host Onboarding Focus"

Write-Host "🚀 Creating VillaWise MVP Project Board (Host-Centric)..." -ForegroundColor Green
Write-Host "🔍 Target repository: $REPO" -ForegroundColor Cyan

# Check if GitHub CLI is installed
try {
    $ghVersion = gh --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ GitHub CLI is installed" -ForegroundColor Green
        Write-Host "   Version: $($ghVersion.Split("`n")[0])" -ForegroundColor Gray
    } else {
        Write-Host "❌ GitHub CLI is not installed. Please install from: https://cli.github.com/" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ GitHub CLI is not installed. Please install from: https://cli.github.com/" -ForegroundColor Red
    exit 1
}

# Check if user is authenticated
try {
    $authStatus = gh auth status 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ GitHub CLI is authenticated" -ForegroundColor Green
        Write-Host "   Auth status: $($authStatus.Split("`n")[0])" -ForegroundColor Gray
    } else {
        Write-Host "❌ Please run 'gh auth login' first" -ForegroundColor Red
        Write-Host "   Auth error: $authStatus" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ Please run 'gh auth login' first" -ForegroundColor Red
    exit 1
}

# Check if repository exists
try {
    $repoInfo = gh repo view $REPO 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Repository found: $REPO" -ForegroundColor Green
    } else {
        Write-Host "❌ Repository not found or no access: $REPO" -ForegroundColor Red
        Write-Host "   GitHub CLI output: $repoInfo" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ Error checking repository: $_" -ForegroundColor Red
    exit 1
}

Write-Host "📋 Creating project board..." -ForegroundColor Yellow

# Create the project board (GitHub Projects v2)
try {
    Write-Host "🔄 Creating project board..." -ForegroundColor Cyan
    $projectOutput = gh project create --title $PROJECT_TITLE --body "VillaWise MVP development board prioritizing Spanish villa owner onboarding and property registration" --owner $RepoOwner 2>&1
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Project board created successfully" -ForegroundColor Green
        Write-Host "   Project URL: $projectOutput" -ForegroundColor Gray
    } else {
        Write-Host "⚠️  Project board creation failed or already exists" -ForegroundColor Yellow
        Write-Host "   GitHub CLI output: $projectOutput" -ForegroundColor Yellow
        Write-Host "   Exit code: $LASTEXITCODE" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ PowerShell error creating project board: $_" -ForegroundColor Red
}

Write-Host "🏷️  Creating labels..." -ForegroundColor Yellow

# Create labels
$labels = @(
    @{name="critical"; color="d73a4a"; description="Blocks core host onboarding functionality"},
    @{name="high"; color="ff9500"; description="Important for MVP host experience"},
    @{name="medium"; color="0075ca"; description="Nice to have for host onboarding"},
    @{name="future"; color="7057ff"; description="Post-MVP features"},
    @{name="feature"; color="a2eeef"; description="New functionality"},
    @{name="bug"; color="d876e3"; description="Fix broken behavior"},
    @{name="technical"; color="0e8a16"; description="Infrastructure work"},
    @{name="documentation"; color="1d76db"; description="Docs and guides"},
    @{name="testing"; color="f9d71c"; description="Test coverage"},
    @{name="epic:host-onboarding"; color="b60205"; description="Host Onboarding Experience Epic"},
    @{name="epic:property-registration"; color="d93f0b"; description="Property Registration System Epic"},
    @{name="epic:ai-assistance"; color="fbca04"; description="AI-Guided Setup Epic"},
    @{name="epic:calendar-management"; color="5319e7"; description="Calendar Integration Epic"},
    @{name="epic:spanish-compliance"; color="0052cc"; description="Spanish Market Compliance Epic"},
    @{name="epic:infrastructure"; color="006b75"; description="Technical Infrastructure Epic"}
)

foreach ($label in $labels) {
    try {
        $labelResult = gh label create $label.name --color $label.color --description $label.description --repo $REPO 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  ✅ Created label: $($label.name)" -ForegroundColor Green
        } else {
            Write-Host "  ⚠️  Label $($label.name) might already exist or failed" -ForegroundColor Yellow
            Write-Host "     GitHub CLI output: $labelResult" -ForegroundColor Gray
        }
    } catch {
        Write-Host "  ❌ PowerShell error creating label $($label.name): $_" -ForegroundColor Red
    }
}

Write-Host "📝 Loading issues from JSON file..." -ForegroundColor Yellow

# Load issues from JSON file
$jsonPath = Join-Path $PSScriptRoot "create-issues-batch.json"
Write-Host "🔍 Looking for JSON file at: $jsonPath" -ForegroundColor Cyan

if (-not (Test-Path $jsonPath)) {
    Write-Host "❌ JSON file not found at: $jsonPath" -ForegroundColor Red
    Write-Host "   Please ensure create-issues-batch.json exists in the same directory" -ForegroundColor Red
    Write-Host "   Current directory: $PSScriptRoot" -ForegroundColor Red
    exit 1
}

try {
    $jsonContent = Get-Content $jsonPath -Raw
    Write-Host "✅ JSON file read successfully ($($jsonContent.Length) characters)" -ForegroundColor Green
    
    $issues = $jsonContent | ConvertFrom-Json
    Write-Host "✅ Loaded $($issues.Count) issues from JSON file" -ForegroundColor Green
    
    # Display first issue as sample
    Write-Host "📋 Sample issue: $($issues[0].title)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ Failed to parse JSON file: $_" -ForegroundColor Red
    Write-Host "   JSON file path: $jsonPath" -ForegroundColor Red
    exit 1
}

Write-Host "📝 Creating $($issues.Count) MVP issues (Host Onboarding Focus)..." -ForegroundColor Yellow

$issueCount = 0
$successCount = 0
$failureCount = 0

foreach ($issue in $issues) {
    $issueCount++
    Write-Host "🔄 Processing issue $issueCount/$($issues.Count): $($issue.title)" -ForegroundColor Cyan
    
    try {
        # Prepare labels
        $labels = if ($issue.labels -is [array]) { $issue.labels -join "," } else { $issue.labels }
        Write-Host "   Labels: $labels" -ForegroundColor Gray
        
        # Create issue with detailed error capture
        $result = gh issue create --title $issue.title --body $issue.body --label $labels --repo $REPO 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            $successCount++
            Write-Host "  ✅ Created issue: $($issue.title)" -ForegroundColor Green
            Write-Host "     Issue URL: $result" -ForegroundColor Gray
        } else {
            $failureCount++
            Write-Host "  ❌ Failed to create issue: $($issue.title)" -ForegroundColor Red
            Write-Host "     GitHub CLI error: $result" -ForegroundColor Red
            Write-Host "     Exit code: $LASTEXITCODE" -ForegroundColor Red
        }
    } catch {
        $failureCount++
        Write-Host "  ❌ PowerShell error creating issue: $($issue.title)" -ForegroundColor Red
        Write-Host "     Error: $_" -ForegroundColor Red
        Write-Host "     Exception: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "✅ VillaWise MVP Host-Centric Project Board creation complete!" -ForegroundColor Green
Write-Host "📊 Successfully created $successCount/$issueCount issues" -ForegroundColor Cyan
if ($failureCount -gt 0) {
    Write-Host "⚠️  $failureCount issues failed to create" -ForegroundColor Yellow
}
Write-Host ""
Write-Host "📋 Created Board: VillaWise MVP - Host Onboarding Focus" -ForegroundColor Cyan
Write-Host "🎯 Focus: Spanish villa owner onboarding and property registration" -ForegroundColor Cyan
Write-Host "📊 Phase 1 Priority: Host experience optimization" -ForegroundColor Cyan
Write-Host ""
Write-Host "🎯 Next steps:" -ForegroundColor Yellow
Write-Host "1. Visit https://github.com/$REPO/projects to view your project board" -ForegroundColor White
Write-Host "2. Create Kanban columns: Backlog → Ready → In Progress → Review → Done" -ForegroundColor White
Write-Host "3. Add created issues to the project board" -ForegroundColor White
Write-Host "4. Start with Sprint 1: Host registration wizard + Spanish compliance" -ForegroundColor White
Write-Host ""
Write-Host "🚀 Sprint Planning:" -ForegroundColor Yellow
Write-Host "   Sprint 1-2: Host registration wizard + Spanish compliance" -ForegroundColor White
Write-Host "   Sprint 3-4: Property listing wizard + photo upload" -ForegroundColor White
Write-Host "   Sprint 5-6: AI-guided optimization + recommendations" -ForegroundColor White
Write-Host "   Sprint 7-8: Calendar management + availability tools" -ForegroundColor White