import { useTranslations } from '@/lib/translations';
import { Button } from '@/components/ui/button';

export default function OwnerCTA() {
  const t = useTranslations('ownerCta');

  return (
    <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
      <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 className="text-4xl lg:text-5xl font-bold mb-6 leading-tight">
          {t('title')}
        </h2>
        <p className="text-xl lg:text-2xl mb-10 text-white/90 max-w-3xl mx-auto leading-relaxed">
          {t('subtitle')}
        </p>
        <Button 
          size="lg" 
          variant="secondary"
          className="px-10 py-4 text-lg font-semibold bg-white text-blue-600 hover:bg-gray-50 rounded-xl shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-1"
        >
          {t('buttonText')}
        </Button>
      </div>
    </section>
  );
}