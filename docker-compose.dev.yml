# Development Docker Compose for VillaWise
version: '3.8'

services:
  villawise-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=development
      - PORT=5000
      - USE_REDIS_CACHE=false
      # Add your environment variables here
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
    volumes:
      # Mount source code for hot reloading
      - ./client:/app/client
      - ./server:/app/server
      - ./shared:/app/shared
      - ./package.json:/app/package.json
      - ./tsconfig.json:/app/tsconfig.json
      - ./vite.config.ts:/app/vite.config.ts
      - ./tailwind.config.ts:/app/tailwind.config.ts
      # Exclude node_modules to prevent conflicts
      - /app/node_modules
    restart: unless-stopped
    command: npm run dev

