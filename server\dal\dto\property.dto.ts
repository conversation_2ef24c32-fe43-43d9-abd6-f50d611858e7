
import { UserSession } from '../auth/session'

export interface PropertyData {
  id: string
  title: string
  description: string
  location: string
  price_per_night: number
  max_guests: number
  bedrooms: number
  bathrooms: number
  amenities: string[]
  images: string[]
  host_id: string
  status: 'active' | 'inactive' | 'draft' | 'paused'
  created_at: string
  updated_at: string
  coordinates?: { lat: number; lng: number }
  rating?: number
  reviews_count?: number
}

export class PropertyDTO {
  id: string
  title: string
  description: string
  location: string
  pricePerNight: number
  maxGuests: number
  bedrooms: number
  bathrooms: number
  amenities: string[]
  images: string[]
  status?: string
  hostId?: string
  coordinates?: { lat: number; lng: number }
  rating?: number
  reviewsCount?: number
  createdAt?: string
  updatedAt?: string

  constructor(property: PropertyData, userRole?: UserSession['role']) {
    this.id = property.id
    this.title = property.title
    this.description = property.description
    this.location = property.location
    this.pricePerNight = property.price_per_night
    this.maxGuests = property.max_guests
    this.bedrooms = property.bedrooms
    this.bathrooms = property.bathrooms
    this.amenities = property.amenities || []
    this.images = property.images || []
    this.coordinates = property.coordinates
    this.rating = property.rating
    this.reviewsCount = property.reviews_count

    // Only include sensitive data for hosts/admins
    if (userRole === 'host' || userRole === 'admin') {
      this.status = property.status
      this.hostId = property.host_id
      this.createdAt = property.created_at
      this.updatedAt = property.updated_at
    }
  }

  static fromArray(properties: PropertyData[], userRole?: UserSession['role']): PropertyDTO[] {
    return properties.map(property => new PropertyDTO(property, userRole))
  }
}

export interface PropertySearchFilters {
  location?: string
  minPrice?: number
  maxPrice?: number
  minGuests?: number
  maxGuests?: number
  bedrooms?: number
  amenities?: string[]
  dateRange?: {
    checkIn: string
    checkOut: string
  }
}

export interface PropertySearchResult {
  properties: PropertyDTO[]
  total: number
  page: number
  limit: number
  hasMore: boolean
}