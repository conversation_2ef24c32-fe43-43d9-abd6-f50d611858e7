/**
 * Spanish Regions Seeder
 * 
 * Seeds Spanish administrative regions (autonomous communities) with translations.
 * Replaces hardcoded region data from geonames-scope.ts
 */

export default {
  name: '02_spanish_regions',
  description: 'Spanish administrative regions and translations',
  environment: 'shared' as const,
  order: 3,

  async execute(supabase: any): Promise<void> {
    console.log('   🏛️ Seeding Spanish regions...');

    // Spanish autonomous communities (regions) with ISO 3166-2 codes
    const spanishRegions = [
      { country_id: 724, iso_code: 'ES-AN', geonames_admin1_code: 'AN' }, // Andalusia
      { country_id: 724, iso_code: 'ES-AR', geonames_admin1_code: 'AR' }, // Aragon
      { country_id: 724, iso_code: 'ES-AS', geonames_admin1_code: 'AS' }, // Asturias
      { country_id: 724, iso_code: 'ES-IB', geonames_admin1_code: 'IB' }, // Balearic Islands
      { country_id: 724, iso_code: 'ES-PV', geonames_admin1_code: 'PV' }, // Basque Country
      { country_id: 724, iso_code: 'ES-CN', geonames_admin1_code: 'CN' }, // Canary Islands
      { country_id: 724, iso_code: 'ES-CB', geonames_admin1_code: 'CB' }, // Cantabria
      { country_id: 724, iso_code: 'ES-CM', geonames_admin1_code: 'CM' }, // Castile-La Mancha
      { country_id: 724, iso_code: 'ES-CL', geonames_admin1_code: 'CL' }, // Castile and León
      { country_id: 724, iso_code: 'ES-CT', geonames_admin1_code: 'CT' }, // Catalonia
      { country_id: 724, iso_code: 'ES-EX', geonames_admin1_code: 'EX' }, // Extremadura
      { country_id: 724, iso_code: 'ES-GA', geonames_admin1_code: 'GA' }, // Galicia
      { country_id: 724, iso_code: 'ES-MD', geonames_admin1_code: 'MD' }, // Madrid
      { country_id: 724, iso_code: 'ES-MC', geonames_admin1_code: 'MC' }, // Murcia
      { country_id: 724, iso_code: 'ES-NA', geonames_admin1_code: 'NA' }, // Navarre
      { country_id: 724, iso_code: 'ES-RI', geonames_admin1_code: 'RI' }, // La Rioja
      { country_id: 724, iso_code: 'ES-VC', geonames_admin1_code: 'VC' }, // Valencia
    ];

    // Insert regions
    const { data: insertedRegions, error: regionError } = await supabase
      .from('region_codes')
      .upsert(spanishRegions, { onConflict: 'iso_code' })
      .select('id, iso_code, geonames_admin1_code');

    if (regionError) {
      throw new Error(`Failed to seed Spanish regions: ${regionError.message}`);
    }

    console.log(`   ✅ Seeded ${spanishRegions.length} Spanish regions`);

    // Create mapping for translations
    const regionMapping: { [key: string]: number } = {};
    insertedRegions?.forEach((region: any) => {
      regionMapping[region.geonames_admin1_code] = region.id;
    });

    // Region name translations
    const regionTranslations = [
      // Andalusia (AN)
      { entity_type: 'region', entity_id: regionMapping['AN'], language_code: 'en', text: 'Andalusia', is_official: true },
      { entity_type: 'region', entity_id: regionMapping['AN'], language_code: 'es', text: 'Andalucía', is_official: true },
      { entity_type: 'region', entity_id: regionMapping['AN'], language_code: 'nl', text: 'Andalusië', is_official: false },

      // Valencia (VC) - Costa Blanca region
      { entity_type: 'region', entity_id: regionMapping['VC'], language_code: 'en', text: 'Valencia', is_official: true },
      { entity_type: 'region', entity_id: regionMapping['VC'], language_code: 'es', text: 'Valencia', is_official: true },
      { entity_type: 'region', entity_id: regionMapping['VC'], language_code: 'ca', text: 'València', is_official: true },
      { entity_type: 'region', entity_id: regionMapping['VC'], language_code: 'nl', text: 'Valencia', is_official: false },

      // Catalonia (CT) - Costa Brava region
      { entity_type: 'region', entity_id: regionMapping['CT'], language_code: 'en', text: 'Catalonia', is_official: true },
      { entity_type: 'region', entity_id: regionMapping['CT'], language_code: 'es', text: 'Cataluña', is_official: true },
      { entity_type: 'region', entity_id: regionMapping['CT'], language_code: 'ca', text: 'Catalunya', is_official: true },
      { entity_type: 'region', entity_id: regionMapping['CT'], language_code: 'nl', text: 'Catalonië', is_official: false },

      // Balearic Islands (IB)
      { entity_type: 'region', entity_id: regionMapping['IB'], language_code: 'en', text: 'Balearic Islands', is_official: true },
      { entity_type: 'region', entity_id: regionMapping['IB'], language_code: 'es', text: 'Islas Baleares', is_official: true },
      { entity_type: 'region', entity_id: regionMapping['IB'], language_code: 'ca', text: 'Illes Balears', is_official: true },
      { entity_type: 'region', entity_id: regionMapping['IB'], language_code: 'nl', text: 'Balearen', is_official: false },

      // Canary Islands (CN)
      { entity_type: 'region', entity_id: regionMapping['CN'], language_code: 'en', text: 'Canary Islands', is_official: true },
      { entity_type: 'region', entity_id: regionMapping['CN'], language_code: 'es', text: 'Islas Canarias', is_official: true },
      { entity_type: 'region', entity_id: regionMapping['CN'], language_code: 'nl', text: 'Canarische Eilanden', is_official: false },

      // Madrid (MD)
      { entity_type: 'region', entity_id: regionMapping['MD'], language_code: 'en', text: 'Madrid', is_official: true },
      { entity_type: 'region', entity_id: regionMapping['MD'], language_code: 'es', text: 'Madrid', is_official: true },
      { entity_type: 'region', entity_id: regionMapping['MD'], language_code: 'nl', text: 'Madrid', is_official: false },

      // Basque Country (PV)
      { entity_type: 'region', entity_id: regionMapping['PV'], language_code: 'en', text: 'Basque Country', is_official: true },
      { entity_type: 'region', entity_id: regionMapping['PV'], language_code: 'es', text: 'País Vasco', is_official: true },
      { entity_type: 'region', entity_id: regionMapping['PV'], language_code: 'eu', text: 'Euskadi', is_official: true },
      { entity_type: 'region', entity_id: regionMapping['PV'], language_code: 'nl', text: 'Baskenland', is_official: false },

      // Galicia (GA)
      { entity_type: 'region', entity_id: regionMapping['GA'], language_code: 'en', text: 'Galicia', is_official: true },
      { entity_type: 'region', entity_id: regionMapping['GA'], language_code: 'es', text: 'Galicia', is_official: true },
      { entity_type: 'region', entity_id: regionMapping['GA'], language_code: 'gl', text: 'Galicia', is_official: true },
      { entity_type: 'region', entity_id: regionMapping['GA'], language_code: 'nl', text: 'Galicië', is_official: false },

      // Asturias (AS)
      { entity_type: 'region', entity_id: regionMapping['AS'], language_code: 'en', text: 'Asturias', is_official: true },
      { entity_type: 'region', entity_id: regionMapping['AS'], language_code: 'es', text: 'Asturias', is_official: true },
      { entity_type: 'region', entity_id: regionMapping['AS'], language_code: 'nl', text: 'Asturië', is_official: false },

      // Murcia (MC)
      { entity_type: 'region', entity_id: regionMapping['MC'], language_code: 'en', text: 'Murcia', is_official: true },
      { entity_type: 'region', entity_id: regionMapping['MC'], language_code: 'es', text: 'Murcia', is_official: true },
      { entity_type: 'region', entity_id: regionMapping['MC'], language_code: 'nl', text: 'Murcia', is_official: false },
    ].filter(t => t.entity_id); // Filter out undefined entity_ids

    // Insert region translations
    const { error: translationError } = await supabase
      .from('translations')
      .upsert(regionTranslations, { onConflict: 'entity_type,entity_id,language_code' });

    if (translationError) {
      throw new Error(`Failed to seed region translations: ${translationError.message}`);
    }

    console.log(`   ✅ Seeded ${regionTranslations.length} region translations`);
  },

  async rollback(supabase: any): Promise<void> {
    await supabase.from('translations').delete().eq('entity_type', 'region');
    await supabase.from('region_codes').delete().eq('country_id', 724);
    console.log('   🧹 Cleaned Spanish regions and translations');
  }
};
