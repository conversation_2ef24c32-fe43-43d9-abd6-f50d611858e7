import React from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useTranslations } from '@/lib/translations';
import { 
  CalendarDays, 
  Heart, 
  Star,
  MessageSquare,
  Plus
} from 'lucide-react';

interface OverviewContentProps {
  guestBookings: any[];
  wishlists: any[];
  guestMessages: any[];
  user: any;
  onUpgradeToHost: () => void;
  isUpgrading: boolean;
  showBecomeHostSection: boolean;
}

const OverviewContent: React.FC<OverviewContentProps> = ({
  guestBookings,
  wishlists,
  guestMessages,
  user,
  onUpgradeToHost,
  isUpgrading,
  showBecomeHostSection
}) => {
  const t = useTranslations('guestDashboard');

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const upcomingBookings = guestBookings.filter(booking => {
    const checkInDate = new Date(booking.check_in_date);
    return checkInDate > new Date();
  });

  const totalSpent = guestBookings.reduce((sum, booking) => sum + (booking.total_amount || 0), 0);

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">{t('greeting')}</h1>
        <p className="text-gray-600 mt-2">{t('description')}</p>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{t('recentBookings.title')}</p>
                <p className="text-2xl font-bold text-gray-900">{guestBookings.length}</p>
              </div>
              <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <CalendarDays className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{t('wishlists.title')}</p>
                <p className="text-2xl font-bold text-gray-900">{wishlists.length}</p>
              </div>
              <div className="h-12 w-12 bg-red-100 rounded-lg flex items-center justify-center">
                <Heart className="h-6 w-6 text-red-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-primary/5 border-primary/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-primary">{t('overview.totalSpent')}</p>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(totalSpent)}</p>
              </div>
              <div className="h-12 w-12 bg-primary/10 rounded-lg flex items-center justify-center">
                <Star className="h-6 w-6 text-primary" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Become Host Section */}
      {showBecomeHostSection && (
        <Card className="mb-8 bg-gradient-to-r from-primary/10 to-orange-50 border-primary/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {t('becomeHost.title')}
                </h3>
                <p className="text-gray-600 mb-4">
                  {t('becomeHost.description')}
                </p>
                <Button 
                  onClick={onUpgradeToHost}
                  disabled={isUpgrading}
                  className="bg-primary hover:bg-primary/90"
                >
                  {isUpgrading ? t('becomeHost.upgrading') : t('becomeHost.button')}
                </Button>
              </div>
              <div className="hidden md:block">
                <div className="w-24 h-24 bg-primary/20 rounded-full flex items-center justify-center">
                  <Plus className="h-12 w-12 text-primary" />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Upcoming Bookings */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle className="flex items-center">
              <CalendarDays className="h-5 w-5 mr-2" />
              <span>{t('overview.upcomingTrips')}</span>
            </CardTitle>
            <Button variant="outline" size="sm">
              {t('recentBookings.viewAll')}
            </Button>
          </CardHeader>
          <CardContent>
            {upcomingBookings.length > 0 ? (
              <div className="space-y-4">
                {upcomingBookings.slice(0, 3).map((booking: any) => (
                  <div key={booking.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                        <CalendarDays className="h-5 w-5 text-gray-500" />
                      </div>
                      <div>
                        <p className="font-medium">{booking.property?.title || 'Eigendom'}</p>
                        <p className="text-sm text-gray-600">
                          {formatDate(booking.check_in_date)} - {formatDate(booking.check_out_date)}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <Badge variant="outline" className="bg-green-50 text-green-700">
{t('recentBookings.accepted')}
                      </Badge>
                      <p className="text-sm text-gray-600 mt-1">
                        {formatCurrency(booking.total_amount)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-600">{t('recentBookings.noBookings')}</p>
                <Button variant="outline" className="mt-2">
{t('recentBookings.startExploring')}
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Recent Messages */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle className="flex items-center">
              <MessageSquare className="h-5 w-5 mr-2" />
              <span>{t('overview.messages')}</span>
            </CardTitle>
            <Button variant="outline" size="sm">
{t('overview.viewAllMessages')}
            </Button>
          </CardHeader>
          <CardContent>
            {guestMessages.length > 0 ? (
              <div className="space-y-4">
                {guestMessages.slice(0, 3).map((message: any) => (
                  <div key={message.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                        <MessageSquare className="h-5 w-5 text-gray-500" />
                      </div>
                      <div>
                        <p className="font-medium">{message.host_name || 'Verhuurder'}</p>
                        <p className="text-sm text-gray-600 truncate max-w-48">
                          {message.content}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      {!message.read && (
                        <Badge variant="default" className="bg-primary text-primary-foreground text-xs">
Nieuw
                        </Badge>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-600">{t('overview.noMessages')}</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default OverviewContent;