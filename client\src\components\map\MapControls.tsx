import { useState } from 'react';
import { Settings, DollarSign, Home, Star } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

export type MarkerStyle = 'price' | 'rating' | 'propertyType' | 'minimal';
export type MarkerSize = 'small' | 'medium' | 'large';

interface MapControlsProps {
  markerStyle: MarkerStyle;
  markerSize: MarkerSize;
  showPrices: boolean;
  showRatings: boolean;
  clusterMarkers: boolean;
  onMarkerStyleChange: (style: MarkerStyle) => void;
  onMarkerSizeChange: (size: MarkerSize) => void;
  onShowPricesChange: (show: boolean) => void;
  onShowRatingsChange: (show: boolean) => void;
  onClusterMarkersChange: (cluster: boolean) => void;
}

export function MapControls({
  markerStyle,
  markerSize,
  showPrices,
  showRatings,
  clusterMarkers,
  onMarkerStyleChange,
  onMarkerSizeChange,
  onShowPricesChange,
  onShowRatingsChange,
  onClusterMarkersChange
}: MapControlsProps) {
  const [isOpen, setIsOpen] = useState(false);

  const markerStyleOptions = [
    { value: 'price', label: 'Price Display', icon: DollarSign },
    { value: 'rating', label: 'Rating Display', icon: Star },
    { value: 'propertyType', label: 'Property Type', icon: Home },
    { value: 'minimal', label: 'Minimal Dots', icon: null }
  ];

  const markerSizeOptions = [
    { value: 'small', label: 'Small' },
    { value: 'medium', label: 'Medium' },
    { value: 'large', label: 'Large' }
  ];

  return (
    <div className="absolute top-4 right-4 z-10">
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button 
            variant="outline" 
            size="sm"
            className="bg-white/95 backdrop-blur-sm border-gray-200 shadow-lg hover:bg-white"
          >
            <Settings className="h-4 w-4 mr-2" />
            Markers
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80 p-4" align="end">
          <div className="space-y-4">
            <div>
              <h4 className="font-medium text-sm mb-3">Marker Style</h4>
              <div className="grid grid-cols-2 gap-2">
                {markerStyleOptions.map((option) => (
                  <Button
                    key={option.value}
                    variant={markerStyle === option.value ? "default" : "outline"}
                    size="sm"
                    onClick={() => onMarkerStyleChange(option.value as MarkerStyle)}
                    className="justify-start h-auto p-3"
                  >
                    <div className="flex flex-col items-center gap-1">
                      {option.icon && <option.icon className="h-4 w-4" />}
                      <span className="text-xs">{option.label}</span>
                    </div>
                  </Button>
                ))}
              </div>
            </div>

            <div>
              <Label htmlFor="marker-size" className="text-sm font-medium">
                Marker Size
              </Label>
              <Select value={markerSize} onValueChange={onMarkerSizeChange}>
                <SelectTrigger className="mt-2">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {markerSizeOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-3">
              <h4 className="font-medium text-sm">Display Options</h4>
              
              <div className="flex items-center justify-between">
                <Label htmlFor="show-prices" className="text-sm">
                  Show Prices
                </Label>
                <Switch
                  id="show-prices"
                  checked={showPrices}
                  onCheckedChange={onShowPricesChange}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="show-ratings" className="text-sm">
                  Show Ratings
                </Label>
                <Switch
                  id="show-ratings"
                  checked={showRatings}
                  onCheckedChange={onShowRatingsChange}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="cluster-markers" className="text-sm">
                  Cluster Markers
                </Label>
                <Switch
                  id="cluster-markers"
                  checked={clusterMarkers}
                  onCheckedChange={onClusterMarkersChange}
                />
              </div>
            </div>

            <div className="pt-2 border-t">
              <div className="flex items-center gap-2 text-xs text-gray-500">
                <Badge variant="secondary" className="text-xs">
                  Live Preview
                </Badge>
                Changes apply instantly to map
              </div>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}