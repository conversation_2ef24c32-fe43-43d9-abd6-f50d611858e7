import { createClient, SupabaseClient } from '@supabase/supabase-js'

// <PERSON><PERSON> missing Supabase environment variables gracefully for CI/CD
// For TypeScript builds (like GitHub Actions), create a mock client to avoid null checks everywhere
const createMockClient = (): SupabaseClient => {
  return {
    auth: {
      getUser: () => Promise.resolve({ data: { user: null }, error: new Error('Mock client - no real connection') })
    },
    from: () => ({
      select: () => ({ data: null, error: new Error('Mock client - no real connection') }),
      insert: () => ({ data: null, error: new Error('Mock client - no real connection') }),
      update: () => ({ data: null, error: new Error('Mock client - no real connection') }),
      delete: () => ({ data: null, error: new Error('Mock client - no real connection') }),
      eq: function() { return this; },
      gte: function() { return this; },
      lte: function() { return this; },
      ilike: function() { return this; },
      in: function() { return this; },
      or: function() { return this; },
      range: function() { return this; },
      order: function() { return this; },
      limit: function() { return this; },
      single: function() { return this; }
    })
  } as any;
};

let supabase: SupabaseClient;
let supabaseAdmin: SupabaseClient;

if (process.env.SUPABASE_URL && process.env.SUPABASE_ANON_KEY) {
  // Create main client with anonymous key for auth operations
  supabase = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_ANON_KEY
  );
  
  // Create admin client with service role key for user creation operations
  if (process.env.SUPABASE_SERVICE_ROLE_KEY) {
    supabaseAdmin = createClient(
      process.env.SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    );
    console.log('✅ Supabase admin client created with service role key');
    console.log('   - Main client uses anonymous key for auth operations');
    console.log('   - Admin client uses service role key for user creation');
  } else {
    console.warn('⚠️  No service role key found - using anonymous key for all operations');
    supabaseAdmin = supabase; // Fallback to main client
  }
} else {
  console.warn('Supabase environment variables not found. Using mock client for TypeScript builds.');
  supabase = createMockClient();
  supabaseAdmin = createMockClient();
}

export { supabase, supabaseAdmin };

// Database types for VillaWise (Updated with new table structure)
export interface User {
  id: string
  email: string
  password: string
  username: string
  first_name: string | null
  last_name: string | null
  phone: string | null
  is_host: boolean | null
  avatar_url: string | null
  oauth_provider: string | null
  oauth_id: string | null
  locale: string | null
  created_at: string | null
  updated_at: string | null
}

// Host-centric types (renamed from Property)
export interface HostProperty {
  id: string
  host_id: string
  title: string
  description: string
  location: string
  city: string
  country: string
  coordinates: { lat: number; lng: number }
  price_per_night: string
  max_guests: number
  bedrooms: number
  bathrooms: number
  amenities: string[] | null
  images: string[] | null
  property_type: string
  is_active: boolean | null
  rating: string | null
  review_count: number | null
  created_at: string | null
  updated_at: string | null
}

// Guest-centric types (renamed from Booking)
export interface GuestBooking {
  id: string
  host_property_id: string
  guest_id: string
  check_in: string
  check_out: string
  guests: { adults: number; children: number; infants: number; pets: number }
  total_price: string
  status: string
  special_requests: string | null
  created_at: string | null
  updated_at: string | null
}

export interface GuestReview {
  id: string
  booking_id: string
  guest_id: string
  host_property_id: string
  rating: number
  comment: string | null
  created_at: string | null
}

export interface GuestSearchHistory {
  id: string
  user_id: string | null
  session_id: string | null
  location: string
  check_in: string | null
  check_out: string | null
  guests: { adults: number; children: number; infants: number; pets: number } | null
  created_at: string | null
}

export interface GuestProfile {
  id: string
  user_id: string
  bio: string | null
  avatar_url: string | null
  travel_preferences: string[] | null
  identity_verified: boolean | null
  email_verified: boolean | null
  phone_verified: boolean | null
  emergency_contact: any | null
  created_at: string | null
  updated_at: string | null
}

export interface GuestWishlist {
  id: string
  guest_id: string
  name: string
  description: string | null
  is_public: boolean | null
  created_at: string | null
  updated_at: string | null
}

export interface GuestWishlistProperty {
  id: string
  wishlist_id: string
  host_property_id: string
  added_at: string
}

// Communication types
export interface CommConversation {
  id: string
  guest_id: string
  host_id: string
  host_property_id: string | null
  subject: string | null
  last_message_at: string
  is_read_by_guest: boolean | null
  is_read_by_host: boolean | null
  created_at: string
}

export interface CommMessage {
  id: string
  conversation_id: string
  sender_id: string
  sender_type: string
  content: string
  message_type: string | null
  is_read: boolean | null
  created_at: string
}

export interface HelpArticle {
  id: string
  title: string
  content: string
  category: string
  subcategory: string | null
  is_faq: boolean | null
  display_order: number | null
  is_active: boolean | null
  created_at: string | null
  updated_at: string | null
}

// Legacy types for backward compatibility (with proper field mapping)
export interface Property extends HostProperty {}

export interface Booking {
  id: string
  property_id: string
  guest_id: string
  check_in: string
  check_out: string
  guests: { adults: number; children: number; infants: number; pets: number }
  total_price: string
  status: string
  special_requests: string | null
  created_at: string | null
  updated_at: string | null
}

export interface Review {
  id: string
  booking_id: string
  guest_id: string
  property_id: string
  rating: number
  comment: string | null
  created_at: string | null
}

export interface SearchHistory extends GuestSearchHistory {}

// Insert types (Updated with new table structure)
export interface InsertUser {
  id?: string // Optional for auto-generated IDs, required for Supabase Auth IDs
  email: string
  username: string
  first_name?: string
  last_name?: string
  phone?: string
  is_host?: boolean
  avatar_url?: string
  oauth_provider?: string
  oauth_id?: string
  locale?: string
}

export interface InsertHostProperty {
  host_id: string
  title: string
  description: string
  location: string
  city: string
  country: string
  coordinates: { lat: number; lng: number }
  price_per_night: string
  max_guests: number
  bedrooms: number
  bathrooms: number
  amenities?: string[]
  images?: string[]
  property_type: string
  rating?: string
  review_count?: number
}

export interface InsertGuestBooking {
  host_property_id: string
  guest_id: string
  check_in: Date
  check_out: Date
  guests: { adults: number; children: number; infants: number; pets: number }
  total_price: string
  status: string
  special_requests?: string
}

export interface InsertGuestReview {
  booking_id: string
  guest_id: string
  host_property_id: string
  rating: number
  comment?: string
}

export interface InsertGuestSearchHistory {
  user_id?: string
  session_id?: string
  location: string
  check_in?: Date
  check_out?: Date
  guests?: { adults: number; children: number; infants: number; pets: number }
}

export interface InsertGuestProfile {
  user_id: string
  bio?: string
  avatar_url?: string
  travel_preferences?: string[]
  identity_verified?: boolean
  email_verified?: boolean
  phone_verified?: boolean
  emergency_contact?: any
}

export interface InsertGuestWishlist {
  guest_id: string
  name: string
  description?: string
  is_public?: boolean
}

export interface InsertGuestWishlistProperty {
  wishlist_id: string
  host_property_id: string
}

export interface InsertCommConversation {
  guest_id: string
  host_id: string
  host_property_id?: string
  subject?: string
}

export interface InsertCommMessage {
  conversation_id: string
  sender_id: string
  sender_type: 'guest' | 'host'
  content: string
  message_type?: 'text' | 'image' | 'system'
}

export interface InsertHelpArticle {
  title: string
  content: string
  category: string
  subcategory?: string
  is_faq?: boolean
  display_order?: number
  is_active?: boolean
}

// Legacy insert types for backward compatibility (with proper field mapping)
export interface InsertProperty extends InsertHostProperty {}

export interface InsertBooking {
  property_id: string
  guest_id: string
  check_in: Date
  check_out: Date
  guests: { adults: number; children: number; infants: number; pets: number }
  total_price: string
  status: string
  special_requests?: string
}

export interface InsertReview {
  booking_id: string
  guest_id: string
  property_id: string
  rating: number
  comment?: string
}

export interface InsertSearchHistory extends InsertGuestSearchHistory {}

// New table interfaces for complete functionality
export interface HostProfile {
  id: string
  user_id: string
  bio: string | null
  avatar_url: string | null
  is_verified: boolean | null
  is_superhost: boolean | null
  response_rate: number | null
  response_time: string | null
  languages: string[] | null
  created_at: string | null
  updated_at: string | null
}

export interface PropertyDetails {
  id: string
  property_id: string
  house_rules: string[] | null
  check_in_time: string | null
  check_out_time: string | null
  cancellation_policy: string | null
  minimum_stay: number | null
  maximum_stay: number | null
  instant_book: boolean | null
  smoking_allowed: boolean | null
  pets_allowed: boolean | null
  parties_allowed: boolean | null
  additional_rules: string | null
  wifi_details: any | null
  parking_details: any | null
  accessibility_features: string[] | null
  safety_features: string[] | null
  created_at: string | null
  updated_at: string | null
}

export interface PropertyReview {
  id: string
  property_id: string
  guest_id: string
  booking_id: string | null
  rating: number
  cleanliness_rating: number | null
  accuracy_rating: number | null
  communication_rating: number | null
  location_rating: number | null
  checkin_rating: number | null
  value_rating: number | null
  comment: string | null
  host_reply: string | null
  host_reply_date: string | null
  is_featured: boolean | null
  is_public: boolean | null
  created_at: string | null
  updated_at: string | null
}

export interface PropertyAvailability {
  id: string
  property_id: string
  date: string
  is_available: boolean | null
  price_per_night: number | null
  minimum_stay: number | null
  notes: string | null
  created_at: string | null
  updated_at: string | null
}

export interface InsertHostProfile {
  user_id: string
  bio?: string
  avatar_url?: string
  is_verified?: boolean
  is_superhost?: boolean
  response_rate?: number
  response_time?: string
  languages?: string[]
}

export interface InsertPropertyDetails {
  property_id: string
  house_rules?: string[]
  check_in_time?: string
  check_out_time?: string
  cancellation_policy?: string
  minimum_stay?: number
  maximum_stay?: number
  instant_book?: boolean
  smoking_allowed?: boolean
  pets_allowed?: boolean
  parties_allowed?: boolean
  additional_rules?: string
  wifi_details?: any
  parking_details?: any
  accessibility_features?: string[]
  safety_features?: string[]
}

export interface InsertPropertyReview {
  property_id: string
  guest_id: string
  booking_id?: string
  rating: number
  cleanliness_rating?: number
  accuracy_rating?: number
  communication_rating?: number
  location_rating?: number
  checkin_rating?: number
  value_rating?: number
  comment?: string
  host_reply?: string
  host_reply_date?: Date
  is_featured?: boolean
  is_public?: boolean
}

export interface InsertPropertyAvailability {
  property_id: string
  date: Date
  is_available?: boolean
  price_per_night?: number
  minimum_stay?: number
  notes?: string
}