import React from 'react';
import { Button } from '@/components/ui/button';
import { 
  User, 
  CalendarDays, 
  Heart, 
  MessageSquare,
  Star,
  Settings
} from 'lucide-react';
import { useTranslations } from '@/lib/translations';

interface BottomNavigationProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
}

export const BottomNavigation: React.FC<BottomNavigationProps> = ({
  activeTab,
  onTabChange
}) => {
  const t = useTranslations('guestDashboard');

  const navItems = [
    { id: 'dashboard', icon: User, label: t('navigation.dashboard') },
    { id: 'bookings', icon: CalendarDays, label: t('navigation.bookings') },
    { id: 'messages', icon: MessageSquare, label: t('navigation.messages') },
    { id: 'wishlists', icon: Heart, label: t('navigation.wishlists') },
    { id: 'settings', icon: Settings, label: t('navigation.settings') }
  ];

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50 md:hidden">
      <div className="flex justify-around items-center px-2 py-1">
        {navItems.map(({ id, icon: Icon, label }) => (
          <Button
            key={id}
            variant="ghost"
            size="sm"
            className={`flex flex-col items-center py-2 px-3 min-w-0 h-auto relative ${
              activeTab === id 
                ? 'text-primary bg-primary/10' 
                : 'text-gray-600 hover:text-gray-900'
            }`}
            onClick={() => onTabChange(id)}
          >
            <Icon className="h-5 w-5 mb-1" />
            {id === 'messages' && (
              <span className="absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full"></span>
            )}
            <span className="text-xs font-medium truncate max-w-16">
              {label}
            </span>
          </Button>
        ))}
      </div>
    </div>
  );
};