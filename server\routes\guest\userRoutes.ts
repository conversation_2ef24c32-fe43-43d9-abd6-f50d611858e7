import { Router } from 'express';
import { userHistoryController } from '../../controllers/guest/userHistoryController';

const router = Router();

// User-specific routes
router.get('/search-history', userHistoryController.getSearchHistory.bind(userHistoryController));
router.post('/search-history', userHistoryController.addToSearchHistory.bind(userHistoryController));
router.delete('/search-history', userHistoryController.clearSearchHistory.bind(userHistoryController));

export default router;