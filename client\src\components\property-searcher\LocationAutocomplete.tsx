import { useState, useRef, useEffect } from "react";
import { MapPin, Clock, TrendingUp, Waves, Building2 } from "lucide-react";
import { useTranslations } from "@/lib/translations";
import { useLocale } from "@/lib/i18n";
import { searchLocations, getPopularSuggestions, type Location, type SuggestionCategory } from "@/lib/apiClient";
import {
  useSearchHistory,
  type SearchHistoryItem,
} from "@/hooks/useSearchHistory";
// Removed localStorage integration - simplified approach

interface LocationAutocompleteProps {
  value: string;
  onChange: (value: string) => void;
  onLocationSelect?: (location: string) => void;
  onCompleteSearchSelect?: (searchState: any) => void;
  placeholder?: string;
  compact?: boolean;
  autoFocus?: boolean;
  onFocus?: () => void;
  // URL synchronization support
  syncWithUrl?: boolean;
}

export const LocationAutocomplete: React.FC<LocationAutocompleteProps> = ({
  value,
  onChange,
  onLocationSelect,
  onCompleteSearchSelect,
  placeholder,
  compact = false,
  autoFocus = false,
  onFocus,
}) => {
  const t = useTranslations("locationAutocomplete");
  const { locale } = useLocale();
  const [isOpen, setIsOpen] = useState(false);
  const [searchResults, setSearchResults] = useState<Location[]>([]);
  const [suggestions, setSuggestions] = useState<SuggestionCategory[]>([]);
  const [isLoadingSuggestions, setIsLoadingSuggestions] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const { searchHistory, addToHistory, clearHistory } = useSearchHistory();
  // Removed recentLocations - simplified approach

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        event.target &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
      return () => document.removeEventListener("mousedown", handleClickOutside);
    }
  }, [isOpen]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    onChange(newValue);
    // Search results will be updated by the useEffect with debouncing
  };

  const handleLocationSelect = (location: Location | SearchHistoryItem, isCompleteSearch = false) => {
    onChange(location.name);
    
    // If it's a complete search from history, restore full state
    if (isCompleteSearch && 'searchedAt' in location && onCompleteSearchSelect) {
      onCompleteSearchSelect(location);
    } else {
      onLocationSelect?.(location.name);
    }
    
    // Simplified approach - no localStorage tracking
    
    setIsOpen(false);
    setSearchResults([]);
  };

  const handleInputFocus = () => {
    console.log('[CLIENT] Input focused, opening dropdown');
    setIsOpen(true);
    onFocus?.();
    if (value.trim() === "") {
      setSearchResults([]);
    }
  };

  useEffect(() => {
    if (autoFocus && inputRef.current) {
      inputRef.current.focus();
    }
  }, [autoFocus]);

  // Debounced search effect with improved performance
  useEffect(() => {
    if (!value.trim()) {
      setSearchResults([]);
      return;
    }

    // Only search if the value is at least 2 characters
    if (value.trim().length < 2) {
      setSearchResults([]);
      return;
    }

    const handler = setTimeout(async () => {
      console.log(`[CLIENT] Searching for: "${value}" in locale: ${locale}`);
      try {
        const results = await searchLocations(value, locale);
        console.log(`[CLIENT] Received ${results.length} results:`, results);
        setSearchResults(results.slice(0, 8));
      } catch (error) {
        console.error('Failed to search locations:', error);
        setSearchResults([]);
      }
    }, 500); // Increased debounce delay

    return () => clearTimeout(handler);
  }, [value]);

  // Load suggestions on mount only
  useEffect(() => {
    const loadSuggestions = async () => {
      if (suggestions.length === 0 && !isLoadingSuggestions) {
        setIsLoadingSuggestions(true);
        try {
          const data = await getPopularSuggestions();
          setSuggestions(data);
        } catch (error) {
          console.error('Failed to load suggestions:', error);
        } finally {
          setIsLoadingSuggestions(false);
        }
      }
    };

    loadSuggestions();
  }, []); // Remove dependencies that cause infinite re-renders

  // Helper function to format search history item like Airbnb
  const formatSearchHistoryItem = (item: SearchHistoryItem) => {
    const parts = [];
    
    // Add date range if available
    if (item.checkIn && item.checkOut) {
      const checkIn = new Date(item.checkIn);
      const checkOut = new Date(item.checkOut);
      const formatter = new Intl.DateTimeFormat('nl-NL', { day: 'numeric', month: 'short' });
      const dateRange = `${formatter.format(checkIn)}–${formatter.format(checkOut)}`;
      parts.push(dateRange);
    }
    
    // Add guest count if available
    if (item.guests) {
      const { adults = 1, children = 0, infants = 0, pets = 0 } = item.guests;
      const totalAdults = adults;
      const totalChildren = children;
      
      const guestParts = [];
      if (totalAdults > 0) {
        guestParts.push(`${totalAdults} ${totalAdults === 1 ? 'volwassene' : 'volwassenen'}`);
      }
      if (totalChildren > 0) {
        guestParts.push(`${totalChildren} ${totalChildren === 1 ? 'kind' : 'kinderen'}`);
      }
      if (infants > 0) {
        guestParts.push(`${infants} ${infants === 1 ? 'baby' : "baby's"}`);
      }
      if (pets > 0) {
        guestParts.push(`${pets} ${pets === 1 ? 'huisdier' : 'huisdieren'}`);
      }
      
      if (guestParts.length > 0) {
        parts.push(guestParts.join(', '));
      }
    }
    
    return {
      primary: item.name,
      secondary: parts.length > 0 ? parts.join(' • ') : `${item.region || ''}, ${item.country}`,
      full: `${item.name}${parts.length > 0 ? ' • ' + parts.join(' • ') : ''}`
    };
  };

  const renderLocationItem = (
    location: Location | SearchHistoryItem | any,
    icon: React.ReactNode,
    onClick: () => void,
    isSearchHistory = false
  ) => {
    // Use Airbnb-style formatting for search history items
    const displayData = isSearchHistory && 'searchedAt' in location 
      ? formatSearchHistoryItem(location as SearchHistoryItem)
      : {
          primary: location.name,
          secondary: "region" in location && location.region
            ? `${location.region}, ${location.country}`
            : location.country,
          full: location.name
        };

    return (
      <button
        key={location.id}
        onClick={onClick}
        className="w-full flex items-center space-x-3 px-4 py-3 hover:bg-muted/50 text-left transition-colors border-b border-border/50 last:border-b-0"
      >
        <div className="flex-shrink-0 text-gray-400 w-8 flex justify-center">{icon}</div>
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium text-gray-900">
            {displayData.primary}
          </p>
          <p className="text-xs text-gray-500">
            {displayData.secondary}
          </p>
        </div>
      </button>
    );
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <div className="relative">
        <input
          ref={inputRef}
          type="text"
          value={value}
          onChange={handleInputChange}
          onFocus={handleInputFocus}
          placeholder={placeholder || t("placeholder")}
          className={compact 
            ? "w-full text-sm font-medium text-gray-900 bg-transparent border-none outline-none placeholder-gray-400"
            : "w-full pl-4 pr-10 py-3 border border-input rounded-xl focus:ring-2 focus:ring-ring focus:border-transparent text-sm bg-background"
          }
        />
        {!compact && <MapPin className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />}
      </div>

      {isOpen && (
        <div className={`absolute top-full left-0 ${compact ? 'min-w-[300px] max-w-[500px] w-max' : 'w-full max-w-md'} bg-popover border border-border rounded-xl shadow-xl mt-2 location-dropdown max-h-80 overflow-y-auto`}>
          {/* Search Results */}
          {searchResults.length > 0 && (
            <div>
              <div className="px-4 py-3 border-b border-gray-100 bg-gray-50">
                <h3 className="text-xs font-medium text-gray-600 uppercase tracking-wide">
                  {t("searchResults")}
                </h3>
              </div>
              {searchResults.map((location) =>
                renderLocationItem(
                  location,
                  <MapPin className="h-4 w-4 text-green-600" />,
                  () => handleLocationSelect(location),
                  false
                )
              )}
            </div>
          )}

          {/* Popular Suggestions */}
          {value.trim() === "" && (
            <div>
              {suggestions.map((category) => {
                const getCategoryIcon = (categoryKey: string) => {
                  switch (categoryKey) {
                    case 'popularDestinations':
                      return <TrendingUp className="h-4 w-4 text-orange-500" />;
                    case 'beachAndSea':
                      return <Waves className="h-4 w-4 text-primary" />;
                    case 'cityTrips':
                      return <Building2 className="h-4 w-4 text-purple-500" />;
                    default:
                      return <MapPin className="h-4 w-4 text-gray-500" />;
                  }
                };

                const getLocationIcon = (categoryKey: string, location: any) => {
                  const iconStyles = {
                    popularDestinations: "bg-gradient-to-br from-blue-400 to-blue-600",
                    beachAndSea: "bg-gradient-to-br from-cyan-400 to-blue-500", 
                    cityTrips: "bg-gradient-to-br from-purple-400 to-pink-500"
                  };
                  
                  const iconClass = iconStyles[categoryKey as keyof typeof iconStyles] || "bg-gradient-to-br from-gray-400 to-gray-600";
                  
                  return (
                    <div className={`w-10 h-10 rounded-lg ${iconClass} flex items-center justify-center shadow-sm`}>
                      <MapPin className="h-5 w-5 text-white" />
                    </div>
                  );
                };

                return (
                  <div key={category.titleKey}>
                    <div className="px-4 py-3 border-b border-border/50 bg-muted/30">
                      <h3 className="text-sm font-medium text-gray-700 flex items-center">
                        {getCategoryIcon(category.titleKey)}
                        <span className="ml-2">
                          {t(category.titleKey)}
                        </span>
                      </h3>
                    </div>
                    {category.locations.slice(0, 4).map((location) =>
                      renderLocationItem(
                        location,
                        getLocationIcon(category.titleKey, location),
                        () => handleLocationSelect(location)
                      )
                    )}
                  </div>
                );
              })}
            </div>
          )}

          {/* No Results */}
          {value.trim() !== "" && searchResults.length === 0 && (
            <div className="px-4 py-8 text-center text-gray-500">
              <MapPin className="h-6 w-6 mx-auto mb-2 text-gray-300" />
              <p className="text-sm">{t("noResults")}</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};