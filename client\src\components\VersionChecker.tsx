import { useEffect, useRef } from 'react';
import { translationCache } from '@/lib/translationCache';

interface VersionCheckerProps {
  children: React.ReactNode;
  checkInterval?: number; // Check interval in milliseconds
  onVersionChange?: (newVersion: string, oldVersion: string) => void;
}

/**
 * Version checker component that monitors for new app deployments
 * and automatically invalidates translation cache when needed
 */
export function VersionChecker({ 
  children, 
  checkInterval = 5 * 60 * 1000, // 5 minutes default
  onVersionChange 
}: VersionCheckerProps) {
  const currentVersionRef = useRef<string | null>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    let mounted = true;

    const checkVersion = async () => {
      try {
        const buildMeta = await translationCache.getCurrentVersion();
        if (!buildMeta || !mounted) return;

        const newVersion = buildMeta.version;
        const oldVersion = currentVersionRef.current;

        if (oldVersion && oldVersion !== newVersion) {
          console.log(`Version change detected: ${oldVersion} -> ${newVersion}`);
          
          // Clear translation cache
          await translationCache.clear();
          
          // Notify callback
          onVersionChange?.(newVersion, oldVersion);
          
          // In development, we might want to reload automatically
          if (process.env.NODE_ENV === 'development') {
            console.log('Development mode: Auto-reloading for version change');
            window.location.reload();
          }
        }

        currentVersionRef.current = newVersion;
      } catch (error) {
        console.error('Version check failed:', error);
      }
    };

    // Initial version check
    checkVersion();

    // Set up periodic checking in production
    if (process.env.NODE_ENV === 'production') {
      intervalRef.current = setInterval(checkVersion, checkInterval);
    }

    return () => {
      mounted = false;
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [checkInterval, onVersionChange]);

  return <>{children}</>;
}

/**
 * Hook to manually trigger version check and cache refresh
 */
export function useVersionCheck() {
  const checkAndRefresh = async () => {
    try {
      console.log('Manually checking for version updates...');
      
      // Force refresh build metadata
      const buildMeta = await translationCache.getCurrentVersion();
      if (buildMeta) {
        console.log(`Current version: ${buildMeta.version} (${buildMeta.buildHash})`);
      }
      
      // Clear cache to force fresh translations
      await translationCache.clear();
      
      return buildMeta;
    } catch (error) {
      console.error('Manual version check failed:', error);
      return null;
    }
  };

  const clearCache = async () => {
    await translationCache.clear();
    console.log('Translation cache cleared manually');
  };

  return {
    checkAndRefresh,
    clearCache,
    getBuildInfo: () => translationCache.getCurrentVersion()
  };
}