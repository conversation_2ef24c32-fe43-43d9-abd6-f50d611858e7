// GeoNames data parser and processor
import { createReadStream } from 'fs';
import { createInterface } from 'readline';
import { GeoNamesPlace, GeoNamesAlternateName, ProcessedGeoNamesData } from './types';
import { GeoNamesConfigManager } from '../../config/geonames-scope';

export class GeoNamesParser {
  private configManager: GeoNamesConfigManager;

  constructor() {
    this.configManager = new GeoNamesConfigManager();
  }

  async parseCountryFile(filePath: string, countryCode: string): Promise<GeoNamesPlace[]> {
    const places: GeoNamesPlace[] = [];
    const fileStream = createReadStream(filePath);
    const rl = createInterface({
      input: fileStream,
      crlfDelay: Infinity
    });

    console.log(`[PARSER] Processing ${countryCode} file: ${filePath}`);
    let lineCount = 0;
    let processedCount = 0;

    for await (const line of rl) {
      lineCount++;
      
      if (line.trim() === '' || line.startsWith('#')) {
        continue;
      }

      try {
        const place = this.parseGeoNamesLine(line, countryCode);
        
        if (place) {
          const shouldInclude = this.configManager.shouldIncludePlace(place);
          if (shouldInclude) {
            places.push(place);
            processedCount++;
          } else if (processedCount < 5) {
            // Log first few rejections for debugging
            console.log(`[PARSER] Rejected place: ${place.name}, feature: ${place.feature_code}, pop: ${place.population}, coords: ${place.latitude},${place.longitude}`);
          }
        }
      } catch (error) {
        console.warn(`[PARSER] Error parsing line ${lineCount}:`, error);
        continue;
      }

      // Progress logging for large files
      if (lineCount % 10000 === 0) {
        console.log(`[PARSER] ${countryCode}: ${lineCount} lines read, ${processedCount} places processed`);
      }
    }

    console.log(`[PARSER] ${countryCode} completed: ${processedCount} places from ${lineCount} lines`);
    return places;
  }

  private parseGeoNamesLine(line: string, countryCode: string): GeoNamesPlace | null {
    const fields = line.split('\t');
    
    // Check if this is geonames format (19 fields) or postal format (12 fields)
    if (fields.length === 19) {
      return this.parseGeoNamesFormat(fields, countryCode);
    } else if (fields.length === 12) {
      return this.parsePostalCodeFormat(fields, countryCode);
    } else {
      if (fields.length > 0) {
        console.warn(`[PARSER] Unexpected field count: ${fields.length}, expected 19 or 12. Line: ${line.substring(0, 100)}`);
      }
      return null;
    }
  }

  private parseGeoNamesFormat(fields: string[], countryCode: string): GeoNamesPlace | null {
    try {
      // Official GeoNames format (19 fields):
      // 0: geonameid, 1: name, 2: asciiname, 3: alternatenames, 4: latitude, 5: longitude,
      // 6: feature class, 7: feature code, 8: country code, 9: cc2, 10: admin1 code,
      // 11: admin2 code, 12: admin3 code, 13: admin4 code, 14: population,
      // 15: elevation, 16: dem, 17: timezone, 18: modification date
      
      const geonamesId = parseInt(fields[0]);
      const latitude = parseFloat(fields[4]);
      const longitude = parseFloat(fields[5]);
      const population = parseInt(fields[14]) || 0;
      const elevation = parseInt(fields[15]) || undefined;
      
      // Validate required fields
      if (isNaN(geonamesId) || isNaN(latitude) || isNaN(longitude)) {
        return null;
      }
      
      const place: GeoNamesPlace = {
        geonames_id: geonamesId,
        name: fields[1].trim(),
        ascii_name: fields[2].trim(),
        country_code: fields[8].trim(),
        feature_class: fields[6].trim(),
        feature_code: fields[7].trim(),
        admin1_code: fields[10].trim(),
        admin2_code: fields[11].trim(),
        latitude,
        longitude,
        population,
        elevation,
        timezone: fields[17].trim() || this.getTimezoneForCountry(countryCode)
      };

      return place;
    } catch (error) {
      console.warn(`[PARSER] Error parsing geonames line: ${error}, line: ${fields.slice(0, 5).join('\\t')}...`);
      return null;
    }
  }

  private parsePostalCodeFormat(fields: string[], countryCode: string): GeoNamesPlace | null {
    try {
      // Official GeoNames postal code format (12 fields):
      // 0: country_code, 1: postal_code, 2: place_name, 3: admin_name1, 4: admin_code1,
      // 5: admin_name2, 6: admin_code2, 7: admin_name3, 8: admin_code3,
      // 9: latitude, 10: longitude, 11: accuracy
      
      const latitude = parseFloat(fields[9]);
      const longitude = parseFloat(fields[10]);
      const accuracy = parseInt(fields[11]) || 1;
      
      // Validate coordinates
      if (isNaN(latitude) || isNaN(longitude)) {
        console.warn(`[PARSER] Invalid coordinates: lat=${fields[9]}, lng=${fields[10]}`);
        return null;
      }
      
      // Spain coordinate bounds check including Canary Islands and all territories
      if (countryCode === 'ES' && (latitude < 27.5 || latitude > 44 || longitude < -18.5 || longitude > 5)) {
        console.warn(`[PARSER] Coordinates outside Spain bounds: ${latitude},${longitude}`);
        return null;
      }
      
      // Generate a unique ID based on postal code and place name
      const uniqueString = `${fields[0]}-${fields[1]}-${fields[2]}`;
      const geonamesId = Math.abs(uniqueString.split('').reduce((a, b) => {
        a = ((a << 5) - a) + b.charCodeAt(0);
        return a & a;
      }, 0));
      
      const place: GeoNamesPlace = {
        geonames_id: geonamesId,
        name: fields[2].trim(),
        ascii_name: fields[2].trim(),
        latitude: latitude,
        longitude: longitude,
        feature_class: 'P', // Populated place
        feature_code: 'PPL', // Populated place  
        country_code: fields[0].trim(),
        admin1_code: fields[4].trim() || '',
        admin2_code: fields[6].trim() || fields[8].trim() || '',
        population: 0, // Not available in postal code format
        elevation: undefined,
        timezone: this.getTimezoneForCountry(fields[0].trim())
      };

      return place;
    } catch (error) {
      console.warn(`[PARSER] Error parsing postal code line: ${error}, line: ${fields.slice(0, 5).join('\\t')}...`);
      return null;
    }
  }
  
  private getTimezoneForCountry(countryCode: string): string {
    const timezones: Record<string, string> = {
      'ES': 'Europe/Madrid',
      'FR': 'Europe/Paris',
      'IT': 'Europe/Rome',
      'DE': 'Europe/Berlin',
      'PT': 'Europe/Lisbon'
    };
    return timezones[countryCode] || 'UTC';
  }

  async parseAlternateNamesFile(filePath: string): Promise<GeoNamesAlternateName[]> {
    const names: GeoNamesAlternateName[] = [];
    const supportedLanguages = new Set(this.configManager.getAllLanguages());
    
    const fileStream = createReadStream(filePath);
    const rl = createInterface({
      input: fileStream,
      crlfDelay: Infinity
    });

    console.log(`[PARSER] Processing alternate names file: ${filePath}`);
    let lineCount = 0;
    let processedCount = 0;

    for await (const line of rl) {
      lineCount++;
      
      if (line.trim() === '' || line.startsWith('#')) {
        continue;
      }

      try {
        const alternateName = this.parseAlternateNameLine(line);
        
        if (alternateName && supportedLanguages.has(alternateName.language_code)) {
          names.push(alternateName);
          processedCount++;
        }
      } catch (error) {
        console.warn(`[PARSER] Error parsing alternate name line ${lineCount}:`, error);
        continue;
      }

      // Progress logging
      if (lineCount % 50000 === 0) {
        console.log(`[PARSER] Alternate names: ${lineCount} lines read, ${processedCount} names processed`);
      }
    }

    console.log(`[PARSER] Alternate names completed: ${processedCount} names from ${lineCount} lines`);
    return names;
  }

  private parseAlternateNameLine(line: string): GeoNamesAlternateName | null {
    const fields = line.split('\t');
    
    if (fields.length < 4) {
      // Skip lines with less than minimum required fields
      return null;
    }

    // Alternate names file format:
    // 0: alternateNameId, 1: geonameid, 2: isolanguage, 3: alternate name,
    // 4: isPreferredName, 5: isShortName, 6: isColloquial, 7: isHistoric
    // Some files may have fewer fields, so use defaults for missing ones

    const alternateName: GeoNamesAlternateName = {
      alternate_name_id: parseInt(fields[0]),
      geonames_id: parseInt(fields[1]),
      language_code: fields[2] || '',
      name: fields[3] || '',
      is_preferred: (fields[4] || '0') === '1',
      is_short: (fields[5] || '0') === '1',
      is_colloquial: (fields[6] || '0') === '1',
      is_historic: (fields[7] || '0') === '1'
    };

    // Validate required fields
    if (isNaN(alternateName.alternate_name_id) || 
        isNaN(alternateName.geonames_id) ||
        !alternateName.language_code ||
        !alternateName.name) {
      return null;
    }

    // Filter out very long names (likely corrupted data)
    if (alternateName.name.length > 400) {
      return null;
    }

    // Skip certain language codes we don't want
    const skipLanguages = ['link', 'wkdt', 'fr_1793', 'abbr'];
    if (skipLanguages.includes(alternateName.language_code)) {
      return null;
    }

    return alternateName;
  }

  private deduplicateLocations(locations: GeoNamesPlace[]): GeoNamesPlace[] {
    const locationMap = new Map<number, GeoNamesPlace>();
    
    for (const location of locations) {
      const existingLocation = locationMap.get(location.geonames_id);
      
      if (!existingLocation) {
        locationMap.set(location.geonames_id, location);
      } else {
        // Keep the location with higher population, or if same, the one with more complete data
        const shouldReplace = this.shouldReplaceLocation(existingLocation, location);
        if (shouldReplace) {
          locationMap.set(location.geonames_id, location);
          console.log(`[PARSER] Duplicate resolved: ${location.name} (${location.geonames_id}) replaced previous entry`);
        }
      }
    }
    
    return Array.from(locationMap.values());
  }

  private shouldReplaceLocation(existing: GeoNamesPlace, candidate: GeoNamesPlace): boolean {
    // Priority 1: Population (higher is better)
    if (candidate.population > existing.population) return true;
    if (existing.population > candidate.population) return false;
    
    // Priority 2: Feature importance (capitals > major cities > cities)
    const existingPriority = this.getFeaturePriority(existing.feature_code);
    const candidatePriority = this.getFeaturePriority(candidate.feature_code);
    if (candidatePriority > existingPriority) return true;
    if (existingPriority > candidatePriority) return false;
    
    // Priority 3: Data completeness (more fields filled)
    const existingCompleteness = this.getDataCompleteness(existing);
    const candidateCompleteness = this.getDataCompleteness(candidate);
    return candidateCompleteness > existingCompleteness;
  }

  private getFeaturePriority(featureCode: string): number {
    const priorities: Record<string, number> = {
      'PPLC': 100, // Capital of a political entity
      'PPLA': 80,  // Seat of a first-order administrative division
      'PPLA2': 70, // Seat of a second-order administrative division
      'PPLA3': 60, // Seat of a third-order administrative division
      'PPL': 50,   // Populated place
      'PPLF': 40,  // Farm village
      'PPLS': 30,  // Populated places
      'PPLX': 20   // Section of populated place
    };
    return priorities[featureCode] || 10;
  }

  private getDataCompleteness(location: GeoNamesPlace): number {
    let score = 0;
    if (location.name && location.name.length > 0) score += 10;
    if (location.ascii_name && location.ascii_name.length > 0) score += 5;
    if (location.admin1_code && location.admin1_code.length > 0) score += 5;
    if (location.admin2_code && location.admin2_code.length > 0) score += 3;
    if (location.population > 0) score += 10;
    if (location.elevation !== undefined && location.elevation !== null) score += 2;
    if (location.timezone && location.timezone.length > 0) score += 2;
    return score;
  }

  /**
   * Enhanced deduplication by name + admin1_code to eliminate entries like multiple Valencia
   * Keeps the location with the highest quality score (population + feature importance + data completeness)
   */
  private deduplicateLocationsByQuality(locations: GeoNamesPlace[]): GeoNamesPlace[] {
    const locationMap = new Map<string, GeoNamesPlace>();
    
    // Sort by quality score to ensure we keep the best entries
    const sortedLocations = locations.sort((a, b) => {
      const scoreA = this.getQualityScore(a);
      const scoreB = this.getQualityScore(b);
      return scoreB - scoreA;
    });
    
    console.log(`[PARSER] Enhanced deduplication: processing ${sortedLocations.length} pre-sorted locations`);
    
    for (const location of sortedLocations) {
      // Create composite key: name + admin1_code + country_code for regional uniqueness
      const key = `${location.name.toLowerCase().trim()}-${location.admin1_code || 'unknown'}-${location.country_code}`;
      
      if (!locationMap.has(key)) {
        locationMap.set(key, location);
      } else {
        const existing = locationMap.get(key)!;
        console.log(`[PARSER] Duplicate location found: "${location.name}" (${location.geonames_id}) vs "${existing.name}" (${existing.geonames_id})`);
        console.log(`  - Quality scores: new=${this.getQualityScore(location)}, existing=${this.getQualityScore(existing)}`);
        // Since we sorted by quality score, existing should already be better
        // But let's double-check and keep the better one
        if (this.getQualityScore(location) > this.getQualityScore(existing)) {
          locationMap.set(key, location);
          console.log(`  - ✓ Replaced with better quality entry`);
        } else {
          console.log(`  - ✓ Kept existing higher quality entry`);
        }
      }
    }
    
    const uniqueLocations = Array.from(locationMap.values());
    console.log(`[PARSER] Enhanced deduplication completed: ${locations.length} -> ${uniqueLocations.length}`);
    
    return uniqueLocations;
  }

  /**
   * Calculate quality score for location ranking during deduplication
   * Higher score = better quality = priority to keep
   */
  private getQualityScore(location: GeoNamesPlace): number {
    let score = 0;
    
    // Population weight (most important factor)
    score += (location.population || 0) * 2;
    
    // Feature importance
    score += this.getFeaturePriority(location.feature_code) * 1000;
    
    // Data completeness
    score += this.getDataCompleteness(location) * 100;
    
    // Tourism regions get bonus points (check if field exists)
    if ((location as any).tourism_region && (location as any).tourism_region.length > 0) {
      score += 5000;
    }
    
    return score;
  }

  private deduplicateAlternateNames(names: GeoNamesAlternateName[]): GeoNamesAlternateName[] {
    const nameMap = new Map<string, GeoNamesAlternateName>();
    
    for (const name of names) {
      // Create composite key: geonames_id + language_code + name
      const key = `${name.geonames_id}-${name.language_code}-${name.name}`;
      const existingName = nameMap.get(key);
      
      if (!existingName) {
        nameMap.set(key, name);
      } else {
        // Keep preferred names over non-preferred ones
        if (name.is_preferred && !existingName.is_preferred) {
          nameMap.set(key, name);
        }
      }
    }
    
    return Array.from(nameMap.values());
  }

  async processAllData(countryFiles: Map<string, string>, alternateNamesFile: string): Promise<ProcessedGeoNamesData> {
    const allLocations: GeoNamesPlace[] = [];
    const allAlternateNames: GeoNamesAlternateName[] = [];

    // Process country files
    console.log(`[PARSER] Processing ${countryFiles.size} country files`);
    
    for (const [countryCode, filePath] of countryFiles) {
      console.log(`[PARSER] Starting ${countryCode}...`);
      const countryPlaces = await this.parseCountryFile(filePath, countryCode);
      allLocations.push(...countryPlaces);
      console.log(`[PARSER] ${countryCode} completed: ${countryPlaces.length} places`);
    }

    // Process alternate names
    console.log('[PARSER] Processing alternate names...');
    const alternateNames = await this.parseAlternateNamesFile(alternateNamesFile);
    
    // Filter alternate names to only include those for locations we have
    const locationIds = new Set(allLocations.map(l => l.geonames_id));
    const filteredNames = alternateNames.filter(name => locationIds.has(name.geonames_id));
    
    allAlternateNames.push(...filteredNames);

    // Step 1: Deduplicate locations by geonames_id (same location, duplicate IDs)
    const geonamesDeduplicatedLocations = this.deduplicateLocations(allLocations);
    
    // Step 2: Enhanced name-based deduplication (multiple entries for same place)
    const nameDeduplicatedLocations = this.deduplicateLocationsByQuality(geonamesDeduplicatedLocations);
    
    // Deduplicate alternate names by geonames_id + language_code combination
    const deduplicatedNames = this.deduplicateAlternateNames(allAlternateNames);

    console.log(`[PARSER] Multi-level deduplication completed:`);
    console.log(`  - Original locations: ${allLocations.length}`);
    console.log(`  - After geonames_id deduplication: ${geonamesDeduplicatedLocations.length} (${allLocations.length - geonamesDeduplicatedLocations.length} duplicate IDs removed)`);
    console.log(`  - After name deduplication: ${nameDeduplicatedLocations.length} (${geonamesDeduplicatedLocations.length - nameDeduplicatedLocations.length} duplicate names removed)`);
    console.log(`  - Total duplicates eliminated: ${allLocations.length - nameDeduplicatedLocations.length} (${Math.round((allLocations.length - nameDeduplicatedLocations.length) / allLocations.length * 100)}%)`);
    console.log(`  - Alternate names: ${allAlternateNames.length} -> ${deduplicatedNames.length} (after deduplication)`);

    return {
      locations: nameDeduplicatedLocations,
      alternateNames: deduplicatedNames
    };
  }

  // Utility method to get processing statistics
  getProcessingStats(data: ProcessedGeoNamesData): any {
    const locationsByCountry = data.locations.reduce((acc, loc) => {
      acc[loc.country_code] = (acc[loc.country_code] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const namesByLanguage = data.alternateNames.reduce((acc, name) => {
      acc[name.language_code] = (acc[name.language_code] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const locationsByFeature = data.locations.reduce((acc, loc) => {
      const key = `${loc.feature_class}.${loc.feature_code}`;
      acc[key] = (acc[key] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      summary: {
        totalLocations: data.locations.length,
        totalAlternateNames: data.alternateNames.length,
        countries: Object.keys(locationsByCountry).length,
        languages: Object.keys(namesByLanguage).length
      },
      breakdown: {
        locationsByCountry,
        namesByLanguage,
        locationsByFeature
      }
    };
  }
}