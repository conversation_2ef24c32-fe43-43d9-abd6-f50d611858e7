#!/bin/bash

# Railway Docker Deployment Script
# This script sets up Docker deployment for Railway

echo "🐳 Setting up Docker deployment for Railway..."

# Check if required files exist
if [ ! -f "deployment/railway/Dockerfile.railway" ]; then
    echo "❌ Error: Dockerfile.railway not found in deployment/railway/"
    exit 1
fi

if [ ! -f "deployment/railway/railway.toml" ]; then
    echo "❌ Error: railway.toml not found in deployment/railway/"
    exit 1
fi

# Copy Railway configuration to root (required by Railway)
echo "📁 Copying Railway configuration to root directory..."
cp deployment/railway/railway.toml ./

echo "✅ Railway Docker configuration completed!"
echo ""
echo "🔧 Next steps:"
echo "1. Ensure environment variables are set in Railway dashboard:"
echo "   - SUPABASE_URL"
echo "   - SUPABASE_ANON_KEY"
echo "   - NODE_ENV (should be 'production')"
echo ""
echo "2. Commit and push these changes to your repository:"
echo "   git add ."
echo "   git commit -m 'Switch to Docker deployment on Railway'"
echo "   git push origin main"
echo ""
echo "3. Railway will automatically detect the Docker configuration and deploy"
echo ""
echo "4. Monitor deployment:"
echo "   railway logs"
echo ""
echo "5. Test health endpoint after deployment:"
echo "   curl https://your-app.railway.app/api/health"
echo ""
echo "📋 Files ready for deployment:"
echo "  - railway.toml (copied to root)"
echo "  - deployment/railway/Dockerfile.railway (Docker build file)"
echo "  - .dockerignore (build optimization)"
echo ""
echo "🎯 Your app will now deploy using Docker instead of nixpacks!"