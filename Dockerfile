# Production Dockerfile for VillaWise
# Multi-stage build optimized for production deployment

FROM node:20-alpine AS builder

WORKDIR /app

# Copy package files first (better layer caching)
COPY package*.json ./
COPY tsconfig.json ./
COPY vite.config.ts ./
COPY tailwind.config.ts ./
COPY postcss.config.js ./

# Install all dependencies (including dev dependencies for building)
RUN npm ci --production=false

# Copy source code
COPY client/ ./client/
COPY server/ ./server/
COPY shared/ ./shared/
COPY scripts/ ./scripts/
COPY global.d.ts ./

# Build the application
ENV NODE_OPTIONS="--max-old-space-size=4096"
RUN npm run build && node scripts/build-production.js

# Production stage
FROM node:20-alpine AS production

WORKDIR /app

# Install production dependencies only
COPY package*.json ./
RUN npm ci --production=true && npm cache clean --force

# Copy built application
COPY --from=builder /app/dist ./dist

# Copy all necessary server files
COPY --from=builder /app/server ./server
COPY --from=builder /app/shared ./shared
COPY --from=builder /app/global.d.ts ./

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && adduser -S villawise -u 1001

# Set proper permissions
RUN chown -R villawise:nodejs /app
USER villawise

# Expose port (configurable via environment)
EXPOSE ${PORT:-5000}

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "const http = require('http'); const port = process.env.PORT || 5000; http.get(\`http://localhost:\${port}/api/health\`, (res) => { process.exit(res.statusCode === 200 ? 0 : 1) }).on('error', () => process.exit(1))"

# Start the application
CMD ["npm", "run", "start"]