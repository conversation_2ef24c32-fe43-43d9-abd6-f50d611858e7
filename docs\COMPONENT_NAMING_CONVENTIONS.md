# Component Naming Conventions - VillaWise

## Overview

VillaWise follows React community best practices for component naming, emphasizing clean, simple names that let folder structure provide context.

## Core Principles

### ✅ Simple Names with Folder Context
- **Good**: `Layout.tsx` in `mobile/` folder
- **Bad**: `MobileDashboardLayout.tsx` 
- **Reasoning**: Folder path already provides context

### ✅ Import Aliases for Same Names  
```tsx
// When importing components with same names from different folders
import { Layout as MobileLayout } from './mobile/Layout';
import { Layout as DesktopLayout } from './desktop/Layout';
import { Navigation as MobileNav } from './mobile/Navigation';
```

### ✅ PascalCase for Components
- **Component Files**: `UserProfile.tsx`, `SearchForm.tsx`
- **Component Names**: `const UserProfile = () => { ... }`

### ✅ Descriptive but Concise
- **Good**: `Sidebar.tsx`, `Navigation.tsx`, `Content.tsx`
- **Bad**: `LeftNavigationSidebarComponent.tsx`

## File Structure Examples

### Mobile Dashboard Structure
```
features/guest/dashboard/components/
├── DashboardLayout.tsx          # Main responsive wrapper
├── mobile/
│   ├── Layout.tsx              # Not MobileDashboardLayout.tsx
│   ├── Navigation.tsx          # Not MobileBottomNavigation.tsx
│   ├── TabContent.tsx          # Not MobileTabContent.tsx
│   └── Header.tsx              # Not MobileHeader.tsx
├── desktop/
│   ├── Layout.tsx              # Same simple name as mobile
│   ├── Sidebar.tsx             # Not DesktopSidebar.tsx
│   └── Content.tsx             # Not DesktopContent.tsx
└── tablet/
    └── CollapsibleSidebar.tsx  # Descriptive when needed
```

### Navigation Components
```
components/navigation/
├── BottomNavigation.tsx        # Clear context from folder
├── TabletSidebar.tsx           # Device context needed here
├── DesktopSidebar.tsx          # Device context needed here
└── MobileMenu.tsx              # Device context needed here
```

## Import Patterns

### Standard Imports (No Conflicts)
```tsx
import { Navigation } from './mobile/Navigation';
import { Sidebar } from './desktop/Sidebar';
import { Content } from './shared/Content';
```

### Alias Imports (When Names Conflict)
```tsx
import { Layout as MobileLayout } from './mobile/Layout';
import { Layout as DesktopLayout } from './desktop/Layout';
import { Layout as TabletLayout } from './tablet/Layout';

// Usage in component
const DashboardLayout = () => {
  const device = useResponsive();
  
  switch(device) {
    case 'mobile': return <MobileLayout />;
    case 'desktop': return <DesktopLayout />;
    case 'tablet': return <TabletLayout />;
  }
};
```

### Path Alias Imports (With Configured Aliases)
```tsx
import { Layout as MobileLayout } from '@/features/guest/dashboard/mobile';
import { Layout as DesktopLayout } from '@/features/guest/dashboard/desktop';
```

## Naming Rules by File Type

### Component Files
- **Pattern**: PascalCase matching component name
- **Examples**: `UserProfile.tsx`, `SearchResults.tsx`, `PropertyCard.tsx`

### Hook Files  
- **Pattern**: camelCase with `use` prefix
- **Examples**: `useResponsive.ts`, `useAuth.ts`, `useLocalStorage.ts`

### Utility Files
- **Pattern**: camelCase descriptive names
- **Examples**: `dateUtils.ts`, `formatters.ts`, `validation.ts`

### Service Files
- **Pattern**: camelCase with service suffix
- **Examples**: `apiService.ts`, `authService.ts`, `storageService.ts`

## Anti-Patterns to Avoid

### ❌ Redundant Prefixes
```tsx
// Bad - folder already indicates it's mobile
MobileDashboardLayout.tsx
DesktopPropertyContent.tsx
TabletNavigationSidebar.tsx

// Good - folder provides context
Layout.tsx (in mobile/ folder)
Content.tsx (in desktop/ folder) 
Sidebar.tsx (in tablet/ folder)
```

### ❌ Overly Specific Names
```tsx
// Bad - too verbose
GuestDashboardMobileBottomNavigationComponent.tsx
HostPropertyManagementDesktopSidebarLayout.tsx

// Good - concise and clear
Navigation.tsx (in mobile/ folder)
Sidebar.tsx (in desktop/ folder)
```

### ❌ Generic Names Without Context
```tsx
// Bad - too generic when not in contextual folder
Component.tsx
Item.tsx
Thing.tsx

// Good - descriptive even when simple
Layout.tsx
Navigation.tsx
Content.tsx
```

## Benefits of This Approach

### ✅ Cleaner Codebase
- Shorter file names are easier to read and type
- Less visual noise in IDE file explorers
- Cleaner import statements

### ✅ Better Organization
- Folder structure becomes more meaningful
- Related components are clearly grouped
- Easy to understand project architecture

### ✅ Industry Standard
- Follows React community best practices
- Matches patterns used by major libraries (Next.js, Gatsby, etc.)
- Consistent with most React style guides

### ✅ Maintainability
- Easier refactoring when moving components
- Less likelihood of naming conflicts
- Simpler mental model for developers

## Implementation Guidelines

1. **Start with simple names** - Let folder structure provide context
2. **Use aliases sparingly** - Only when absolutely necessary for conflicts
3. **Be consistent** - Follow the same patterns across the entire codebase
4. **Consider readability** - Names should be immediately understandable
5. **Avoid abbreviations** - Use full words unless very commonly abbreviated

This approach creates a cleaner, more maintainable codebase that follows React ecosystem best practices while making code easier to read and understand.