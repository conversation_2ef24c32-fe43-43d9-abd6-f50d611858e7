import { supabase } from '../../supabase';
import { LocationResult } from './types';
import { Logger } from '../../utils/logger';

/**
 * Intelligent Location Search with Popularity Weighting
 * 
 * Advanced PostgreSQL-based search combining fuzzy matching with tourism popularity.
 * Handles typos and ranks results by vacation rental relevance:
 * - "torrev" → "Torrevieja" (ranked by Costa Blanca popularity)
 * - "barselona" → "Barcelona" (major city priority)
 * - "valenc ane" → "Valencia D'Aneu" (trigram similarity)
 */
export class LocationSearchService {
  
  /**
   * Perform popularity-weighted fuzzy search with trigram similarity
   * @deprecated Use fuzzyLocationSearchI18n for multilingual support
   */
  async fuzzyLocationSearch(
    query: string, 
    limit: number = 10, 
    minSimilarity: number = 0.25
  ): Promise<LocationResult[]> {
    if (!query?.trim()) {
      return [];
    }

    if (!supabase) {
      Logger.warn('[FUZZY-SEARCH] Supabase client not available');
      return [];
    }

    try {
      Logger.info('[FUZZY-SEARCH] Executing popularity-weighted fuzzy search', { 
        query, 
        limit, 
        minSimilarity 
      });

      // Use fuzzy search function with built-in deduplication as primary method
      const { data, error } = await supabase.rpc('fuzzy_location_search', {
        search_query: query,
        min_similarity: minSimilarity,
        result_limit: limit
      });

      if (error) {
        Logger.warn('[FUZZY-SEARCH] Fuzzy search failed, trying fallback', { error });
        
        // Fallback to basic query without deduplication
        const { data: fallbackData, error: fallbackError } = await supabase
          .from('geonames_locations')
          .select(`
            id, geonames_id, name, ascii_name, country_code, admin1_code, admin2_code,
            feature_class, feature_code, latitude, longitude, population, elevation, 
            timezone, popularity_score, property_count, tourism_region, 
            tourism_region_type, is_tourism_region
          `)
          .or(`name.ilike.%${query}%,ascii_name.ilike.%${query}%,tourism_region.ilike.%${query}%`)
          .eq('country_code', 'ES')
          .order('popularity_score', { ascending: false })
          .limit(limit);
        
        if (fallbackError) {
          Logger.warn('[FUZZY-SEARCH] Enhanced search failed, using fallback', { error: fallbackError });
          return this.fallbackSearch(query, limit);
        }
        
        return this.transformToLocationResults(fallbackData || []);
      }

      // Update search frequency for analytics (non-blocking)
      this.updateSearchFrequency(query).catch(() => {});

      const results = this.transformToLocationResults(data || []);
      
      Logger.info('[FUZZY-SEARCH] Tourism-enhanced search completed', { 
        query, 
        resultsCount: results.length,
        topResult: results.length > 0 ? {
          name: results[0].name,
          tourism_region: results[0].tourism_region,
          is_tourism_region: results[0].is_tourism_region
        } : null
      });

      return results;

    } catch (error) {
      Logger.error('[FUZZY-SEARCH] Search failed', { query, error });
      return this.fallbackSearch(query, limit);
    }
  }

  /**
   * Perform multilingual fuzzy search with locale-aware translations
   * Supports authentic GeoNames alternate names and regional translations
   */
  async fuzzyLocationSearchI18n(
    query: string, 
    locale: string = 'en',
    limit: number = 10, 
    minSimilarity: number = 0.25
  ): Promise<LocationResult[]> {
    if (!query?.trim()) {
      return [];
    }

    if (!supabase) {
      Logger.warn('[FUZZY-SEARCH-I18N] Supabase client not available');
      return [];
    }

    try {
      Logger.info('[FUZZY-SEARCH-I18N] Executing multilingual fuzzy search', { 
        query, 
        locale,
        limit, 
        minSimilarity 
      });

      // Use internationalized search function (no need for over-fetching with sync-level deduplication)
      const { data, error } = await supabase.rpc('fuzzy_location_search_i18n', {
        search_query: query,
        locale: locale,
        min_similarity: minSimilarity,
        result_limit: limit
      });

      if (error) {
        Logger.warn('[FUZZY-SEARCH-I18N] I18n search failed, falling back to basic search', { error });
        
        // Fallback to basic fuzzy search without i18n
        return await this.fuzzyLocationSearch(query, limit, minSimilarity);
      }

      // Update search frequency for analytics (non-blocking)
      this.updateSearchFrequency(query).catch(() => {});

      const results = this.transformToLocationResultsI18n(data || []);

      Logger.info('[FUZZY-SEARCH-I18N] Multilingual results found', { 
        count: results.length,
        locale,
        topResults: results.slice(0, 3).map(r => ({
          name: r.name, 
          localizedName: r.localized_name,
          displayName: r.display_name,
          similarity: r.similarity_score,
          type: r.match_type,
          source: r.match_source
        }))
      });

      return results;
    } catch (error) {
      Logger.warn('[FUZZY-SEARCH-I18N] Multilingual search failed', { error, query, locale });
      
      // Fallback to basic search
      return await this.fuzzyLocationSearch(query, limit, minSimilarity);
    }
  }

  /**
   * Fallback search using basic ILIKE matching
   */
  private async fallbackSearch(query: string, limit: number): Promise<LocationResult[]> {
    try {
      const { data, error } = await supabase!
        .from('geonames_locations')
        .select('*')
        .ilike('name', `%${query}%`)
        .order('population', { ascending: false })
        .limit(limit);

      if (error) {
        Logger.error('[FUZZY-SEARCH] Fallback search failed', { error });
        return [];
      }

      return this.transformToLocationResults(data || [], true);
    } catch (error) {
      Logger.error('[FUZZY-SEARCH] Fallback search error', { error });
      return [];
    }
  }

  /**
   * Deduplicate location results based on name and admin1_code
   */
  private deduplicateLocationResults(data: any[]): any[] {
    const seen = new Map<string, any>();
    
    // Sort by similarity score and popularity to keep the best matches
    const sorted = [...data].sort((a, b) => {
      const scoreA = (a.similarity_score || 0) * 100 + (a.popularity_score || 0);
      const scoreB = (b.similarity_score || 0) * 100 + (b.popularity_score || 0);
      return scoreB - scoreA;
    });
    
    for (const item of sorted) {
      const key = `${item.name?.toLowerCase()}-${item.admin1_code || 'unknown'}`;
      if (!seen.has(key)) {
        seen.set(key, item);
      }
    }
    
    return Array.from(seen.values());
  }

  /**
   * Transform database results to LocationResult format
   */
  private transformToLocationResults(data: any[], isFallback = false): LocationResult[] {
    return data.map((row: any) => ({
      id: row.id || `${row.geonames_id}`,
      geonames_id: row.geonames_id || 0,
      name: row.name,
      local_name: row.local_name,
      ascii_name: row.ascii_name,
      country_code: row.country_code,
      country_name: this.getCountryName(row.country_code),
      admin1_code: row.admin1_code,
      admin1_name: this.getRegionName(row.admin1_code),
      admin2_code: row.admin2_code,
      feature_class: row.feature_class,
      feature_code: row.feature_code,
      coordinates: {
        lat: row.latitude ? parseFloat(row.latitude) : 0,
        lng: row.longitude ? parseFloat(row.longitude) : 0,
      },
      latitude: row.latitude ? parseFloat(row.latitude) : 0,
      longitude: row.longitude ? parseFloat(row.longitude) : 0,
      population: row.population || 0,
      popularity_score: this.calculatePopularityScore(row.population, row.feature_code),
      property_count: 0, // Will be populated from property database
      relevance_score: isFallback ? 0.5 : parseFloat(row.similarity_score || 0),
      display_name: this.createDisplayName(row),
      region_name: this.getRegionName(row.admin1_code),
      tourism_region: row.tourism_region,
      tourism_region_type: row.tourism_region_type,
      is_tourism_region: row.is_tourism_region || false,
    }));
  }

  /**
   * Transform internationalized database results to LocationResult format
   */
  private transformToLocationResultsI18n(data: any[]): LocationResult[] {
    return data.map((row: any) => ({
      id: row.id || `${row.geonames_id}`,
      geonames_id: row.geonames_id || 0,
      name: row.name,
      localized_name: row.localized_name,
      display_name: row.display_name,
      local_name: row.localized_name, // For backward compatibility
      ascii_name: row.ascii_name,
      country_code: row.country_code,
      country_name: row.country_name,
      region_name: row.region_name,
      admin1_code: row.admin1_code,
      admin1_name: row.region_name, // Use translated region name
      admin2_code: row.admin2_code,
      feature_class: row.feature_class,
      feature_code: row.feature_code,
      coordinates: {
        lat: row.latitude ? parseFloat(row.latitude) : 0,
        lng: row.longitude ? parseFloat(row.longitude) : 0,
      },
      latitude: row.latitude ? parseFloat(row.latitude) : 0,
      longitude: row.longitude ? parseFloat(row.longitude) : 0,
      population: row.population || 0,
      popularity_score: row.popularity_score || 0,
      property_count: row.property_count || 0,
      tourism_region: row.tourism_region,
      tourism_region_type: row.tourism_region_type,
      is_tourism_region: row.is_tourism_region || false,
      similarity_score: row.similarity_score || 0,
      match_type: row.match_type || 'unknown',
      match_source: row.match_source || 'unknown',
      relevance_score: this.calculateRelevanceScore(
        row.similarity_score || 0, 
        row.popularity_score || 0, 
        row.property_count || 0
      ),
      type: this.getLocationTypeFromFeature(row.feature_class, row.feature_code)
    }));
  }

  /**
   * Calculate relevance score combining similarity, popularity and property count
   */
  private calculateRelevanceScore(similarity: number, popularity: number, propertyCount: number): number {
    const similarityWeight = 0.5;
    const popularityWeight = 0.3;
    const propertyWeight = 0.2;
    
    const normalizedSimilarity = Math.min(similarity * 100, 100);
    const normalizedPopularity = Math.min(popularity, 100);
    const normalizedPropertyCount = Math.min(propertyCount * 2, 100);
    
    return Math.round(
      normalizedSimilarity * similarityWeight +
      normalizedPopularity * popularityWeight +
      normalizedPropertyCount * propertyWeight
    );
  }

  /**
   * Get location type from feature classification
   */
  private getLocationTypeFromFeature(featureClass: string, featureCode: string): string {
    if (featureClass === 'P') {
      if (featureCode === 'PPLC') return 'capital';
      if (featureCode === 'PPLA') return 'admin_center';
      if (featureCode === 'PPL') return 'city';
      if (featureCode === 'PPLX') return 'suburb';
      return 'settlement';
    }
    
    if (featureClass === 'A') return 'administrative';
    if (featureClass === 'L') return 'area';
    if (featureClass === 'T') return 'terrain';
    if (featureClass === 'H') return 'hydrography';
    if (featureClass === 'V') return 'vegetation';
    if (featureClass === 'R') return 'road_railroad';
    if (featureClass === 'S') return 'spot_building';
    if (featureClass === 'U') return 'undersea';
    
    return 'other';
  }

  /**
   * Update search frequency for analytics and future ranking improvements
   */
  private async updateSearchFrequency(query: string): Promise<void> {
    try {
      if (query?.trim() && query.length >= 3) {
        await supabase.rpc('update_search_frequency', {
          location_name: query.trim()
        });
      }
    } catch (error) {
      // Silent fail - analytics shouldn't break search
    }
  }

  /**
   * Calculate popularity score based on population and feature type
   */
  private calculatePopularityScore(population: number, featureCode: string): number {
    let baseScore = Math.min(Math.log10(population + 1) * 10, 100);
    
    // Boost important cities
    if (featureCode === 'PPLC') baseScore += 20; // Capital
    else if (featureCode === 'PPLA') baseScore += 15; // Admin center
    else if (featureCode === 'PPL') baseScore += 10; // City
    
    return Math.round(baseScore);
  }

  /**
   * Create display name with regional context
   */
  private createDisplayName(location: any): string {
    let displayName = location.name;
    const regionParts: string[] = [];
    
    if (location.admin1_code) {
      const regionName = this.getRegionName(location.admin1_code);
      if (regionName && regionName !== location.name) {
        regionParts.push(regionName);
      }
    }
    
    const countryName = this.getCountryName(location.country_code);
    if (countryName) {
      regionParts.push(countryName);
    }
    
    if (regionParts.length > 0) {
      displayName += ', ' + regionParts.join(', ');
    }
    
    return displayName;
  }

  /**
   * Get region name from admin1 code
   */
  private getRegionName(admin1Code: string): string {
    const spanishRegions: { [key: string]: string } = {
      'AN': 'Andalucía', 'AR': 'Aragón', 'AS': 'Asturias', 'IB': 'Baleares',
      'PV': 'País Vasco', 'CN': 'Canarias', 'CB': 'Cantabria', 'CM': 'Castilla-La Mancha',
      'CL': 'Castilla y León', 'CT': 'Cataluña', 'EX': 'Extremadura', 'GA': 'Galicia',
      'MD': 'Madrid', 'MC': 'Murcia', 'NC': 'Navarra', 'RI': 'La Rioja',
      'VC': 'Valencia', 'CE': 'Ceuta', 'ML': 'Melilla'
    };
    return spanishRegions[admin1Code] || admin1Code || '';
  }

  /**
   * Get country name from country code
   */
  private getCountryName(countryCode: string): string {
    const countryNames: { [key: string]: string } = {
      'ES': 'España', 'FR': 'France', 'IT': 'Italia', 'PT': 'Portugal',
      'DE': 'Deutschland', 'GB': 'United Kingdom', 'US': 'United States', 'CA': 'Canada'
    };
    return countryNames[countryCode] || countryCode || '';
  }
}

export const locationSearchService = new LocationSearchService();