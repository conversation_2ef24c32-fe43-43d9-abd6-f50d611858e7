import { Request, Response } from 'express';
import { Logger } from '../../utils/logger';
import { storage } from '../../storage';
import { insertGuestWishlistSchema, insertGuestWishlistPropertySchema } from '../../../shared/schema';
import { z } from 'zod';

export class GuestWishlistController {
  
  async getWishlists(req: Request, res: Response) {
    const startTime = Date.now();
    try {
      const guestId = req.params.guestId;
      const requestingUserId = (req as any).userId; // From auth middleware
      
      if (!guestId) {
        return res.status(400).json({ error: 'Guest ID is required' });
      }
      
      // RLS-style check: Users can only access their own wishlists
      if (guestId !== requestingUserId) {
        return res.status(403).json({ 
          error: 'Access denied - You can only access your own wishlists' 
        });
      }
      
      Logger.info(`Guest wishlists requested for guest: ${guestId}`);
      
      const wishlists = await storage.getGuestWishlists(guestId);
      
      Logger.api('GET', `/api/guest/wishlists/${guestId}`, 200, Date.now() - startTime);
      res.json(wishlists);
    } catch (error) {
      Logger.error('Error fetching guest wishlists', error);
      Logger.api('GET', `/api/guest/wishlists/${req.params.guestId}`, 500, Date.now() - startTime);
      res.status(500).json({ error: 'Failed to fetch guest wishlists' });
    }
  }

  async getWishlist(req: Request, res: Response) {
    const startTime = Date.now();
    try {
      const wishlistId = req.params.wishlistId;
      
      if (!wishlistId) {
        return res.status(400).json({ error: 'Wishlist ID is required' });
      }
      
      Logger.info(`Guest wishlist requested: ${wishlistId}`);
      
      const wishlist = await storage.getGuestWishlist(wishlistId);
      
      if (!wishlist) {
        return res.status(404).json({ error: 'Wishlist not found' });
      }
      
      Logger.api('GET', `/api/guest/wishlist/${wishlistId}`, 200, Date.now() - startTime);
      res.json(wishlist);
    } catch (error) {
      Logger.error('Error fetching guest wishlist', error);
      Logger.api('GET', `/api/guest/wishlist/${req.params.wishlistId}`, 500, Date.now() - startTime);
      res.status(500).json({ error: 'Failed to fetch guest wishlist' });
    }
  }

  async createWishlist(req: Request, res: Response) {
    const startTime = Date.now();
    try {
      const validation = insertGuestWishlistSchema.safeParse(req.body);
      
      if (!validation.success) {
        return res.status(400).json({ 
          error: 'Invalid wishlist data', 
          details: validation.error.errors 
        });
      }
      
      const wishlistData = validation.data;
      Logger.info(`Creating guest wishlist for guest: ${wishlistData.guest_id}`);
      
      const wishlist = await storage.createGuestWishlist(wishlistData);
      
      Logger.api('POST', '/api/guest/wishlist', 201, Date.now() - startTime);
      res.status(201).json(wishlist);
    } catch (error) {
      Logger.error('Error creating guest wishlist', error);
      Logger.api('POST', '/api/guest/wishlist', 500, Date.now() - startTime);
      res.status(500).json({ error: 'Failed to create guest wishlist' });
    }
  }

  async updateWishlist(req: Request, res: Response) {
    const startTime = Date.now();
    try {
      const wishlistId = req.params.wishlistId;
      
      if (!wishlistId) {
        return res.status(400).json({ error: 'Wishlist ID is required' });
      }
      
      const updateSchema = insertGuestWishlistSchema.partial().omit({ guest_id: true });
      const validation = updateSchema.safeParse(req.body);
      
      if (!validation.success) {
        return res.status(400).json({ 
          error: 'Invalid wishlist data', 
          details: validation.error.errors 
        });
      }
      
      const updates = validation.data;
      Logger.info(`Updating guest wishlist: ${wishlistId}`);
      
      const wishlist = await storage.updateGuestWishlist(wishlistId, updates);
      
      if (!wishlist) {
        return res.status(404).json({ error: 'Wishlist not found' });
      }
      
      Logger.api('PUT', `/api/guest/wishlist/${wishlistId}`, 200, Date.now() - startTime);
      res.json(wishlist);
    } catch (error) {
      Logger.error('Error updating guest wishlist', error);
      Logger.api('PUT', `/api/guest/wishlist/${req.params.wishlistId}`, 500, Date.now() - startTime);
      res.status(500).json({ error: 'Failed to update guest wishlist' });
    }
  }

  async deleteWishlist(req: Request, res: Response) {
    const startTime = Date.now();
    try {
      const wishlistId = req.params.wishlistId;
      
      if (!wishlistId) {
        return res.status(400).json({ error: 'Wishlist ID is required' });
      }
      
      Logger.info(`Deleting guest wishlist: ${wishlistId}`);
      
      await storage.deleteGuestWishlist(wishlistId);
      
      Logger.api('DELETE', `/api/guest/wishlist/${wishlistId}`, 200, Date.now() - startTime);
      res.json({ success: true });
    } catch (error) {
      Logger.error('Error deleting guest wishlist', error);
      Logger.api('DELETE', `/api/guest/wishlist/${req.params.wishlistId}`, 500, Date.now() - startTime);
      res.status(500).json({ error: 'Failed to delete guest wishlist' });
    }
  }

  async getWishlistProperties(req: Request, res: Response) {
    const startTime = Date.now();
    try {
      const wishlistId = req.params.wishlistId;
      
      if (!wishlistId) {
        return res.status(400).json({ error: 'Wishlist ID is required' });
      }
      
      Logger.info(`Wishlist properties requested for wishlist: ${wishlistId}`);
      
      const properties = await storage.getWishlistProperties(wishlistId);
      
      Logger.api('GET', `/api/guest/wishlist/${wishlistId}/properties`, 200, Date.now() - startTime);
      res.json(properties);
    } catch (error) {
      Logger.error('Error fetching wishlist properties', error);
      Logger.api('GET', `/api/guest/wishlist/${req.params.wishlistId}/properties`, 500, Date.now() - startTime);
      res.status(500).json({ error: 'Failed to fetch wishlist properties' });
    }
  }

  async addPropertyToWishlist(req: Request, res: Response) {
    const startTime = Date.now();
    try {
      const wishlistId = req.params.wishlistId;
      const propertyId = req.body.propertyId;
      
      if (!wishlistId || !propertyId) {
        return res.status(400).json({ error: 'Wishlist ID and property ID are required' });
      }
      
      const wishlistPropertyData = {
        wishlist_id: wishlistId,
        host_property_id: propertyId
      };
      
      const validation = insertGuestWishlistPropertySchema.safeParse(wishlistPropertyData);
      
      if (!validation.success) {
        return res.status(400).json({ 
          error: 'Invalid wishlist property data', 
          details: validation.error.errors 
        });
      }
      
      Logger.info(`Adding property ${propertyId} to wishlist ${wishlistId}`);
      
      const wishlistProperty = await storage.addPropertyToWishlist(validation.data);
      
      Logger.api('POST', `/api/guest/wishlist/${wishlistId}/properties`, 201, Date.now() - startTime);
      res.status(201).json(wishlistProperty);
    } catch (error) {
      Logger.error('Error adding property to wishlist', error);
      Logger.api('POST', `/api/guest/wishlist/${req.params.wishlistId}/properties`, 500, Date.now() - startTime);
      res.status(500).json({ error: 'Failed to add property to wishlist' });
    }
  }

  async removePropertyFromWishlist(req: Request, res: Response) {
    const startTime = Date.now();
    try {
      const wishlistId = req.params.wishlistId;
      const propertyId = req.params.propertyId;
      
      if (!wishlistId || !propertyId) {
        return res.status(400).json({ error: 'Wishlist ID and property ID are required' });
      }
      
      Logger.info(`Removing property ${propertyId} from wishlist ${wishlistId}`);
      
      await storage.removePropertyFromWishlist(wishlistId, propertyId);
      
      Logger.api('DELETE', `/api/guest/wishlist/${wishlistId}/properties/${propertyId}`, 200, Date.now() - startTime);
      res.json({ success: true });
    } catch (error) {
      Logger.error('Error removing property from wishlist', error);
      Logger.api('DELETE', `/api/guest/wishlist/${req.params.wishlistId}/properties/${req.params.propertyId}`, 500, Date.now() - startTime);
      res.status(500).json({ error: 'Failed to remove property from wishlist' });
    }
  }
}

export const guestWishlistController = new GuestWishlistController();