@echo off
echo Building VillaWise Docker Image...

REM Check if <PERSON><PERSON> is running
docker version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Docker is not running or not installed!
    echo Please start Docker Desktop and try again.
    pause
    exit /b 1
)

REM Build the production Docker image
echo Building production image...
docker build -t villawise:latest .

if errorlevel 1 (
    echo ERROR: Docker build failed!
    pause
    exit /b 1
)

echo SUCCESS: Docker image built successfully!
echo You can now run: docker run -p 5000:5000 --env-file .env villawise:latest
pause