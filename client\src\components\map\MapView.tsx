import { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'react-leaflet';
import MarkerClusterGroup from 'react-leaflet-cluster';
import { DivIcon, LatLng } from 'leaflet';
import type { Property } from '@/lib/apiClient';
import { MapControls, type MarkerStyle, type MarkerSize } from './MapControls';

interface MapViewProps {
  properties: Property[];
  selectedProperty?: string;
  onPropertySelect?: (propertyId: string) => void;
  className?: string;
}

export function MapView({ properties, selectedProperty, onPropertySelect, className }: MapViewProps) {
  const [error, setError] = useState<string | null>(null);

  // Custom control states
  const [markerStyle, setMarkerStyle] = useState<MarkerStyle>('price');
  const [markerSize, setMarkerSize] = useState<MarkerSize>('medium');
  const [showPrices, setShowPrices] = useState(true);
  const [showRatings, setShowRatings] = useState(false);
  const [clusterMarkers, setClusterMarkers] = useState(true);

  const sizeMap = { small: 60, medium: 80, large: 100 };

  // Map center - Costa Blanca region
  const center: [number, number] = [38.5396, -0.1312];
  const defaultZoom = 10;

  // Helper function to generate stable property coordinates
  const getPropertyCoordinates = (property: Property): LatLng | null => {
    // Map locations to approximate coordinates
    const locationMap: Record<string, {lat: number, lng: number}> = {
      'barcelona': { lat: 41.3851, lng: 2.1734 },
      'madrid': { lat: 40.4168, lng: -3.7038 },
      'valencia': { lat: 39.4699, lng: -0.3763 },
      'seville': { lat: 37.3891, lng: -5.9845 },
      'costa-blanca': { lat: 38.3452, lng: -0.4810 },
      'costa-del-sol': { lat: 36.5201, lng: -4.8779 },
      'benissa': { lat: 38.7167, lng: 0.0500 },
      'calpe': { lat: 38.6426, lng: 0.0417 },
      'javea': { lat: 38.7917, lng: 0.1667 },
      'altea': { lat: 38.6000, lng: -0.0500 },
      'moraira': { lat: 38.6833, lng: 0.1333 },
      'benitachell': { lat: 38.7333, lng: 0.1167 },
      'denia': { lat: 38.8417, lng: 0.1067 },
      'teulada': { lat: 38.7167, lng: 0.1333 },
      'rome': { lat: 41.9028, lng: 12.4964 },
      'florence': { lat: 43.7696, lng: 11.2558 },
      'venice': { lat: 45.4408, lng: 12.3155 },
      'paris': { lat: 48.8566, lng: 2.3522 },
      'nice': { lat: 43.7102, lng: 7.2620 },
      'amsterdam': { lat: 52.3676, lng: 4.9041 }
    };

    // Create deterministic offset based on property ID to avoid overlapping
    const createStableOffset = (id: string, scale: number = 0.01) => {
      let hash = 0;
      for (let i = 0; i < id.length; i++) {
        const char = id.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // Convert to 32bit integer
      }
      // Convert hash to stable decimal between -1 and 1
      const normalized = (hash % 2000) / 1000 - 1;
      return normalized * scale;
    };

    // Try to match property location or title to coordinates
    let locationStr = '';
    if (property.location?.city) {
      locationStr = property.location.city.toLowerCase();
    }
    const title = property.title.toLowerCase();
    
    for (const [key, coords] of Object.entries(locationMap)) {
      if (locationStr.includes(key) || title.includes(key)) {
        // Add stable offset based on property ID to avoid overlapping markers
        return new LatLng(
          coords.lat + createStableOffset(property.id + 'lat', 0.01),
          coords.lng + createStableOffset(property.id + 'lng', 0.01)
        );
      }
    }

    // Default to Costa Blanca area with stable offset
    return new LatLng(
      38.5396 + createStableOffset(property.id + 'default_lat', 0.1),
      -0.1312 + createStableOffset(property.id + 'default_lng', 0.1)
    );
  };

  // Create custom marker icon using DivIcon
  const createMarkerIcon = (property: Property, isSelected: boolean = false): DivIcon => {
    const size = sizeMap[markerSize];
    
    let iconHtml = '';
    
    switch (markerStyle) {
      case 'price':
        iconHtml = createPriceMarkerHtml(property, isSelected, size);
        break;
      case 'rating':
        iconHtml = createRatingMarkerHtml(property, isSelected, size);
        break;
      case 'propertyType':
        iconHtml = createTypeMarkerHtml(property, isSelected, size);
        break;
      case 'minimal':
        iconHtml = createMinimalMarkerHtml(property, isSelected, size);
        break;
      default:
        iconHtml = createPriceMarkerHtml(property, isSelected, size);
    }

    return new DivIcon({
      html: iconHtml,
      className: 'custom-marker',
      iconSize: [size, size * 0.5],
      iconAnchor: [size / 2, size * 0.5],
    });
  };

  const createPriceMarkerHtml = (property: Property, isSelected: boolean, size: number): string => {
    const backgroundColor = isSelected ? '#ef4444' : '#000000';
    const priceText = showPrices ? String(property.price).replace(/€|\s*per\s*night/gi, '') : '';
    
    return `
      <div style="
        position: relative;
        width: ${size * 0.9}px;
        height: ${size * 0.35}px;
        background-color: ${backgroundColor};
        color: white;
        border: 2px solid white;
        border-radius: ${size * 0.15}px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: Arial, sans-serif;
        font-weight: bold;
        font-size: ${Math.max(8, size * 0.12)}px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
      ">
        ${showPrices ? `€${priceText}` : ''}
        <div style="
          position: absolute;
          bottom: -6px;
          left: 50%;
          transform: translateX(-50%);
          width: 0;
          height: 0;
          border-left: 6px solid transparent;
          border-right: 6px solid transparent;
          border-top: 6px solid ${backgroundColor};
        "></div>
      </div>
    `;
  };

  const createRatingMarkerHtml = (property: Property, isSelected: boolean, size: number): string => {
    const backgroundColor = isSelected ? '#ef4444' : '#10b981';
    const radius = size * 0.3;
    
    return `
      <div style="
        width: ${radius * 2}px;
        height: ${radius * 2}px;
        background-color: ${backgroundColor};
        color: white;
        border: 2px solid white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: Arial, sans-serif;
        font-weight: bold;
        font-size: ${Math.max(8, radius * 0.4)}px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
      ">
        ${showRatings && property.rating ? property.rating.toFixed(1) : '★'}
      </div>
    `;
  };

  const createTypeMarkerHtml = (property: Property, isSelected: boolean, size: number): string => {
    const colors = {
      'villa': '#8b5cf6',
      'apartment': '#3b82f6', 
      'house': '#10b981',
      'penthouse': '#f59e0b',
      'default': '#6b7280'
    };
    
    const type = property.hostType?.toLowerCase() || 'default';
    const backgroundColor = isSelected ? '#ef4444' : (colors[type as keyof typeof colors] || colors.default);
    const typeText = type.charAt(0).toUpperCase();
    
    return `
      <div style="
        width: ${size * 0.6}px;
        height: ${size * 0.6}px;
        background-color: ${backgroundColor};
        color: white;
        border: 2px solid white;
        border-radius: ${size * 0.09}px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: Arial, sans-serif;
        font-weight: bold;
        font-size: ${Math.max(8, size * 0.12)}px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
      ">
        ${typeText}
      </div>
    `;
  };

  const createMinimalMarkerHtml = (property: Property, isSelected: boolean, size: number): string => {
    const backgroundColor = isSelected ? '#ef4444' : '#374151';
    const radius = size * 0.2;
    
    return `
      <div style="
        width: ${radius * 2}px;
        height: ${radius * 2}px;
        background-color: ${backgroundColor};
        border: 2px solid white;
        border-radius: 50%;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
      "></div>
    `;
  };

  // Create popup content
  const createPopupContent = (property: Property): string => {
    const imageUrl = property.images?.[0] || '';
    const priceDisplay = typeof property.price === 'string' 
      ? property.price.replace(' per night', '') 
      : `€${property.price}`;
    
    return `
      <div style="max-width: 200px; font-family: Arial, sans-serif;">
        ${imageUrl ? `<img src="${imageUrl}" alt="${property.title}" style="width: 100%; height: 120px; object-fit: cover; border-radius: 8px; margin-bottom: 8px;" />` : ''}
        <h3 style="margin: 0 0 4px 0; font-size: 14px; font-weight: bold;">${property.title}</h3>
        <p style="margin: 0 0 4px 0; color: #666; font-size: 12px;">${property.hostType} • ${property.maxGuests} guests</p>
        <p style="margin: 0; font-weight: bold; font-size: 14px;">${priceDisplay}/night</p>
      </div>
    `;
  };

  // Store property data for cluster calculations
  const propertyDataMap = new Map<string, Property>();
  properties.forEach(property => {
    propertyDataMap.set(property.id, property);
  });

  // Create custom cluster icon
  const createClusterIcon = (cluster: any) => {
    const count = cluster.getChildCount();
    const size = count < 10 ? 40 : count < 100 ? 50 : 60;
    
    // Calculate average price for cluster styling
    const childMarkers = cluster.getAllChildMarkers();
    let totalPrice = 0;
    let validPrices = 0;
    
    childMarkers.forEach((marker: any) => {
      // Get property data from the marker's custom data
      const propertyId = marker.options.alt; // We'll store property ID in alt
      const property = propertyDataMap.get(propertyId);
      if (property && property.price) {
        const priceNum = typeof property.price === 'string' 
          ? parseInt(property.price.replace(/[€,\s]/g, '')) 
          : property.price;
        if (!isNaN(priceNum)) {
          totalPrice += priceNum;
          validPrices++;
        }
      }
    });
    
    const avgPrice = validPrices > 0 ? Math.round(totalPrice / validPrices) : 0;
    
    // Color based on price range
    let backgroundColor = '#374151'; // Default gray
    if (avgPrice > 200) backgroundColor = '#ef4444'; // Red for expensive
    else if (avgPrice > 100) backgroundColor = '#f59e0b'; // Orange for medium
    else if (avgPrice > 50) backgroundColor = '#10b981'; // Green for affordable
    
    return new DivIcon({
      html: `
        <div style="
          width: ${size}px;
          height: ${size}px;
          background-color: ${backgroundColor};
          color: white;
          border: 3px solid white;
          border-radius: 50%;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          font-family: Arial, sans-serif;
          font-weight: bold;
          font-size: ${size > 50 ? '12px' : '10px'};
          box-shadow: 0 4px 8px rgba(0,0,0,0.3);
          cursor: pointer;
        ">
          <div style="font-size: ${size > 50 ? '14px' : '12px'};">${count}</div>
          ${avgPrice > 0 ? `<div style="font-size: ${size > 50 ? '8px' : '7px'}; opacity: 0.9;">€${avgPrice}</div>` : ''}
        </div>
      `,
      className: 'custom-cluster-marker',
      iconSize: [size, size],
      iconAnchor: [size / 2, size / 2],
    });
  };

  if (error) {
    return (
      <div className={`flex items-center justify-center bg-gray-100 ${className || ''}`}>
        <div className="text-center">
          <p className="text-gray-600 mb-2">Failed to load map</p>
          <p className="text-sm text-gray-500">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative ${className || ''}`}>
      <MapContainer
        center={center}
        zoom={defaultZoom}
        style={{ height: '100%', width: '100%', minHeight: '400px' }}
        scrollWheelZoom={true}
        doubleClickZoom={true}
        dragging={true}
        keyboard={true}
      >
        <TileLayer
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        />
        
        {clusterMarkers ? (
          <MarkerClusterGroup
            chunkedLoading
            iconCreateFunction={createClusterIcon}
            maxClusterRadius={80}
            spiderfyOnMaxZoom={true}
            showCoverageOnHover={false}
            zoomToBoundsOnClick={true}
            removeOutsideVisibleBounds={true}
          >
            {properties.map((property) => {
              const position = getPropertyCoordinates(property);
              if (!position) return null;

              return (
                <Marker
                  key={property.id}
                  position={position}
                  icon={createMarkerIcon(property, selectedProperty === property.id)}
                  alt={property.id}
                  eventHandlers={{
                    click: () => {
                      onPropertySelect?.(property.id);
                    },
                  }}
                >
                  <Popup>
                    <div dangerouslySetInnerHTML={{ __html: createPopupContent(property) }} />
                  </Popup>
                </Marker>
              );
            })}
          </MarkerClusterGroup>
        ) : (
          // Individual markers when clustering is disabled
          properties.map((property) => {
            const position = getPropertyCoordinates(property);
            if (!position) return null;

            return (
              <Marker
                key={property.id}
                position={position}
                icon={createMarkerIcon(property, selectedProperty === property.id)}
                eventHandlers={{
                  click: () => {
                    onPropertySelect?.(property.id);
                  },
                }}
              >
                <Popup>
                  <div dangerouslySetInnerHTML={{ __html: createPopupContent(property) }} />
                </Popup>
              </Marker>
            );
          })
        )}
      </MapContainer>
      
      <MapControls
        markerStyle={markerStyle}
        markerSize={markerSize}
        showPrices={showPrices}
        showRatings={showRatings}
        clusterMarkers={clusterMarkers}
        onMarkerStyleChange={setMarkerStyle}
        onMarkerSizeChange={setMarkerSize}
        onShowPricesChange={setShowPrices}
        onShowRatingsChange={setShowRatings}
        onClusterMarkersChange={setClusterMarkers}
      />
    </div>
  );
}