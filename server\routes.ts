import type { Express } from "express";
import { createServer, type Server } from "http";
import { searchRoutes } from "./routes/public/searchRoutes";
import { authRoutes } from "./routes/auth/authRoutes";
import { hostUpgradeRoutes } from "./routes/shared/hostUpgradeRoutes";
import contentRoutes from "./routes/public/contentRoutes";
import { locationRoutes } from "./routes/public/locationRoutes";
import { geoNamesRoutes } from "./routes/geonames-routes";
import translationRoutes from "./routes/shared/translationRoutes";
import userRoutes from "./routes/guest/userRoutes";
import healthRoutes from "./routes/health";
import propertyRoutes from "./routes/public/propertyRoutes";
import propertyManagementRoutes from "./routes/host/propertyManagementRoutes";
import imageRoutes from "./routes/host/imageRoutes";
import guestProfileRoutes from "./routes/guest/guestProfileRoutes";
import guestBookingRoutes from "./routes/guest/guestBookingRoutes";
import guestWishlistRoutes from "./routes/guest/guestWishlistRoutes";
import guestReviewRoutes from "./routes/guest/guestReviewRoutes";
import guestCommunicationRoutes from "./routes/guest/guestCommunicationRoutes";
import guestHelpRoutes from "./routes/guest/guestHelpRoutes";
import messagingRoutes from "./messaging/routes";
import { requireAuth, requireHost } from "./middleware/auth";

// DAL-enhanced routes
import { hostDashboardRouter } from "./routes/host/hostDashboardRoutes";
import { guestDashboardRouter } from "./routes/guest/guestDashboardRoutes";
import { dalPropertyRouter } from "./routes/shared/dalPropertyRoutes";


export async function registerRoutes(app: Express): Promise<Server> {
  // Debug middleware to log all API requests
  app.use('/api', (req, res, next) => {
    console.log(`[${new Date().toLocaleTimeString()}] API Request: ${req.method} ${req.originalUrl}`);
    next();
  });

  // Health check routes (no /api prefix for Docker health checks)
  app.use('/api', healthRoutes);
  
  // API routes - Register BEFORE Vite to ensure they are handled first
  // Public routes (no authentication required)
  app.use('/api/public', searchRoutes);
  app.use('/api/public/content', contentRoutes);
  app.use('/api/public/locations', locationRoutes);
  app.use('/api/public/geonames', geoNamesRoutes);
  app.use('/api/public/properties', propertyRoutes);
  
  // Authentication routes
  app.use('/api', authRoutes);
  
  // Shared routes
  app.use('/api', hostUpgradeRoutes);
  app.use('/api/translations', translationRoutes);
  app.use('/api/user', userRoutes);
  // Host routes - require host privileges in all environments
  app.use('/api/host', requireHost, propertyManagementRoutes);
  app.use('/api/images', imageRoutes);
  
  // Guest-specific API routes - PROTECTED with authentication
  app.use('/api/guest', requireAuth, guestProfileRoutes);
  app.use('/api/guest', requireAuth, guestBookingRoutes);
  app.use('/api/guest', requireAuth, guestWishlistRoutes);
  app.use('/api/guest', requireAuth, guestReviewRoutes);
  app.use('/api/guest', requireAuth, guestCommunicationRoutes);
  app.use('/api/guest', requireAuth, guestHelpRoutes);

  // Messaging routes - NEW comprehensive messaging system
  app.use('/api/messaging', messagingRoutes);

  // DAL-enhanced routes
  app.use('/api/host/dal', requireHost, hostDashboardRouter);
  app.use('/api/guest/dal', requireAuth, guestDashboardRouter);
  app.use('/api/properties/dal', requireAuth, dalPropertyRouter);


  // Setup Vite after API routes so it doesn't intercept them
  const server = await createServer(app);
  
  // Dynamic domain compatibility - allow any host for maximum flexibility
  const replatDomain = process.env.REPLIT_DOMAINS;
  const allowedHosts = [
    "localhost",
    "127.0.0.1",
    "0.0.0.0",
    // Replit domains (when available)
    ...(replatDomain ? [replatDomain, "*.picard.replit.dev", "*.replit.dev"] : []),
    // Allow all TLDs for maximum deployment flexibility
    "*",
  ];
  
  // Setup development or production server
  if (process.env.NODE_ENV === "production") {
    // Production: serve static files only (no vite imports)
    const { serveStatic } = await import("./static-server");
    serveStatic(app);
  } else {
    // Development: setup Vite development server
    try {
      // Only import vite-dev-server in development mode
      const viteDevServer = await import("./vite-dev-server.js");
      await viteDevServer.setupViteWithCustomConfig(app, server, allowedHosts);
    } catch (error) {
      console.error("Failed to setup Vite development server:", error);
      console.error("This is normal in production builds where vite-dev-server is excluded");
      // Fallback to static server if Vite fails
      const { serveStatic } = await import("./static-server");
      serveStatic(app);
    }
  }

  return server;
}