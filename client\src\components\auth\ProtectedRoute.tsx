import { useEffect } from 'react';
import { useLocation } from 'wouter';
import { useUser } from '@/features/shared/auth/hooks/useAuth';
import { Card, CardContent } from '@/components/ui/card';
import { Loader2 } from 'lucide-react';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  redirectTo?: string;
}

export function ProtectedRoute({ 
  children, 
  requireAuth = true, 
  redirectTo = '/login' 
}: ProtectedRouteProps) {
  const { data: user, isLoading } = useUser();
  const [, navigate] = useLocation();

  useEffect(() => {
    if (!isLoading && requireAuth && !user) {
      navigate(redirectTo);
    }
  }, [user, isLoading, requireAuth, redirectTo, navigate]);

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-96">
          <CardContent className="p-8 text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
            <p className="text-gray-600">Checking authentication...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  // If authentication is required but user is not logged in, don't render children
  if (requireAuth && !user) {
    return null; // Navigation will happen in useEffect
  }

  return <>{children}</>;
}