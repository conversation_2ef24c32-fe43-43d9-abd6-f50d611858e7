import { Request, Response, NextFunction } from 'express'
import { memoryCache } from '../dal/cache/memoryCache'

// Route-level caching middleware
export const cacheRoute = (duration: number = 300) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const key = `route:${req.method}:${req.originalUrl}:${req.headers.authorization || 'anonymous'}`
    const cached = memoryCache.get(key)
    
    if (cached) {
      return res.json(cached)
    }
    
    // Store original json method
    const originalJson = res.json
    
    // Override json method to cache response
    res.json = function(data: any) {
      // Only cache successful responses
      if (res.statusCode >= 200 && res.statusCode < 300) {
        memoryCache.set(key, data, duration)
      }
      
      // Call original json method
      return originalJson.call(this, data)
    }
    
    // Set cache headers
    res.set('Cache-Control', `private, max-age=${duration}`)
    next()
  }
}

// Cache monitoring middleware
export const cacheMonitor = (req: Request, res: Response, next: NextFunction) => {
  const stats = memoryCache.getStats()
  
  // Add cache stats to response headers (for debugging)
  res.set('X-Cache-Size', stats.size.toString())
  res.set('X-Cache-Memory', `${Math.round(stats.memoryUsage / 1024 / 1024)}MB`)
  res.set('X-Cache-Hit-Rate', `${Math.round(stats.hitRate * 100)}%`)
  
  next()
}

// Cache invalidation helper
export const invalidateRouteCache = (pattern: string) => {
  memoryCache.invalidatePattern(`route:${pattern}`)
}