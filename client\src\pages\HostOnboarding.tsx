import React, { useState } from 'react';
import { useTranslations } from '@/lib/translations';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { ChevronLeft, ChevronRight, Home, Users, Camera, MapPin, Euro, Settings } from 'lucide-react';
import { LocaleSwitcher } from '@/components/LocaleSwitcher';

// Step components
import { PropertyTypeStep } from '@/features/host/properties/components/wizard/PropertyTypeStep';
import { LocationStep } from '@/features/host/properties/components/wizard/LocationStep';
import { GuestCapacityStep } from '@/features/host/properties/components/wizard/GuestCapacityStep';
import { AmenitiesStep } from '@/features/host/properties/components/wizard/AmenitiesStep';
import { PhotosStep } from '@/features/host/properties/components/wizard/PhotosStep';
import { TitleDescriptionStep } from '@/features/host/properties/components/wizard/TitleDescriptionStep';
import { PricingStep } from '@/features/host/properties/components/wizard/PricingStep';
import { PolicyStep } from '@/features/host/properties/components/wizard/PolicyStep';
import { ReviewStep } from '@/features/host/properties/components/wizard/ReviewStep';

export interface HostOnboardingData {
  // Property basics
  propertyType: string;
  spaceType: 'entire_place' | 'private_room' | 'shared_room';
  
  // Location
  address: string;
  city: string;
  country: string;
  coordinates: { lat: number; lng: number };
  
  // Capacity
  maxGuests: number;
  bedrooms: number;
  bathrooms: number;
  
  // Amenities
  amenities: string[];
  
  // Photos
  photos: string[];
  
  // Listing details
  title: string;
  description: string;
  
  // Pricing
  pricePerNight: number;
  currency: string;
  
  // Policies
  houseRules: string[];
  cancellationPolicy: string;
  checkInTime: string;
  checkOutTime: string;
  
  // Spanish compliance
  touristLicense: string;
  businessRegistration?: string;
}

const STEPS = [
  { id: 'property-type', titleKey: 'propertyType', icon: Home },
  { id: 'location', titleKey: 'location', icon: MapPin },
  { id: 'capacity', titleKey: 'capacity', icon: Users },
  { id: 'amenities', titleKey: 'amenities', icon: Settings },
  { id: 'photos', titleKey: 'photos', icon: Camera },
  { id: 'listing', titleKey: 'listing', icon: Home },
  { id: 'pricing', titleKey: 'pricing', icon: Euro },
  { id: 'policies', titleKey: 'policies', icon: Settings },
  { id: 'review', titleKey: 'review', icon: Settings }
];

export const HostOnboarding = () => {
  const t = useTranslations('hostOnboarding');
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState<Partial<HostOnboardingData>>({
    spaceType: 'entire_place',
    country: 'Spain',
    currency: 'EUR',
    amenities: [],
    photos: [],
    houseRules: [],
    cancellationPolicy: 'flexible'
  });

  const progress = ((currentStep + 1) / STEPS.length) * 100;

  const updateFormData = (data: Partial<HostOnboardingData>) => {
    setFormData(prev => ({ ...prev, ...data }));
  };

  const handleNext = () => {
    if (currentStep < STEPS.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const renderStep = () => {
    const stepId = STEPS[currentStep].id;
    
    switch (stepId) {
      case 'property-type':
        return <PropertyTypeStep data={formData} onUpdate={updateFormData} />;
      case 'location':
        return <LocationStep data={formData} onUpdate={updateFormData} />;
      case 'capacity':
        return <GuestCapacityStep data={formData} onUpdate={updateFormData} />;
      case 'amenities':
        return <AmenitiesStep data={formData} onUpdate={updateFormData} />;
      case 'photos':
        return <PhotosStep data={formData} onUpdate={updateFormData} />;
      case 'listing':
        return <TitleDescriptionStep data={formData} onUpdate={updateFormData} />;
      case 'pricing':
        return <PricingStep data={formData} onUpdate={updateFormData} />;
      case 'policies':
        return <PolicyStep data={formData} onUpdate={updateFormData} />;
      case 'review':
        return <ReviewStep data={formData} onUpdate={updateFormData} />;
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-gray-900 dark:to-gray-800">
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              {t('title')}
            </h1>
            <div className="flex items-center space-x-4">
              <LocaleSwitcher />
              <div className="text-sm text-gray-600 dark:text-gray-400">
                {t('step')} {currentStep + 1} {t('of')} {STEPS.length}
              </div>
            </div>
          </div>
          
          <Progress value={progress} className="h-2 mb-4" />
          
          <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
            {React.createElement(STEPS[currentStep].icon, { className: "h-4 w-4" })}
            <span>{t(`steps.${STEPS[currentStep].titleKey}`)}</span>
          </div>
        </div>

        {/* Main Content */}
        <Card className="mb-8">
          <CardContent className="p-8">
            {renderStep()}
          </CardContent>
        </Card>

        {/* Navigation */}
        <div className="flex justify-between items-center">
          <Button
            type="button"
            variant="outline"
            onClick={handlePrevious}
            disabled={currentStep === 0}
            className="flex items-center space-x-2"
          >
            <ChevronLeft className="h-4 w-4" />
            <span>{t('previous')}</span>
          </Button>

          <div className="flex space-x-2">
            {STEPS.map((step, index) => (
              <div
                key={step.id}
                className={`w-3 h-3 rounded-full ${
                  index === currentStep
                    ? 'bg-primary'
                    : index < currentStep
                    ? 'bg-primary/60'
                    : 'bg-gray-200 dark:bg-gray-700'
                }`}
              />
            ))}
          </div>

          <Button
            type="button"
            onClick={handleNext}
            disabled={currentStep === STEPS.length - 1}
            className="flex items-center space-x-2 bg-primary hover:bg-primary/90"
          >
            <span>{currentStep === STEPS.length - 1 ? t('finish') : t('next')}</span>
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};