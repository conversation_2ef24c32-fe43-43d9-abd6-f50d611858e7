// Type declarations for Express extensions and better TypeScript compatibility

// Supabase Auth User type (what we get from supabase.auth.getUser())
interface SupabaseAuthUser {
  id: string;
  email?: string;
  user_metadata?: {
    name?: string;
    avatar_url?: string | null;
    first_name?: string;
    last_name?: string;
    username?: string;
    is_host?: boolean;
  };
  app_metadata?: {
    provider?: string;
    providers?: string[];
  };
  aud?: string;
  created_at?: string;
  updated_at?: string;
}

declare global {
  namespace Express {
    interface Request {
      user?: SupabaseAuthUser;
      userId?: string;
      isAuthenticated?: () => boolean;
    }
  }
}

export {};
