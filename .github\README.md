# VillaWise GitHub Actions - Streamlined Validation

## Workflow Overview

This repository uses streamlined GitHub Actions workflows for code validation and release management. Railway handles all deployment operations.

## Active Workflows

### 🚂 `railway-deploy.yml` - Production Deployment
- **Triggers**: Push to main, PR to main
- **Features**: Build validation, Railway deployment, health checks
- **Environment**: Production via Railway

### ✅ `pr-checks.yml` - Pull Request Validation  
- **Triggers**: PR opened/updated
- **Features**: Fast validation, automated PR comments, security audits

### 🏷️ `release.yml` - Release Management
- **Triggers**: Version tags (v*.*.*)
- **Features**: GitHub releases, changelog generation (simplified)

### 🔍 `validate-workflows.yml` - Workflow Validation
- **Triggers**: Workflow file changes
- **Features**: YAML validation, action version checks, syntax verification

## Artifact Strategy

### Validation Artifacts (7 days)
- `test-coverage-{sha}` - Test results and coverage reports
- PR validation results - Build status and security audit reports

### Release Artifacts (90 days)
- `RELEASE_INFO.txt` - Release metadata and deployment notes
- Auto-generated changelogs

## Getting Started

1. **Development**: Push to feature branches triggers PR validation
2. **Production**: Merge to `main` triggers Railway deployment
3. **Releases**: Create git tags like `v1.0.0` for GitHub releases
4. **Validation**: All workflows validate code quality and security

## Configuration

### Required Repository Secrets
```bash
SUPABASE_URL=your_production_supabase_url
SUPABASE_ANON_KEY=your_production_anon_key
RAILWAY_TOKEN=your_railway_token
RAILWAY_SERVICE_ID=your_railway_service_id
RAILWAY_APP_URL=your_railway_app_url
```

## Features

✅ **Railway Integration**: Seamless deployment to Railway platform  
✅ **Validation Focus**: Code quality and security checks  
✅ **Health Monitoring**: Automatic health endpoint testing  
✅ **Security Scanning**: npm audit in PR validation  
✅ **Smart Caching**: npm cache for faster dependency installation  
✅ **Streamlined Workflows**: Focused on validation, not redundant builds  

## Monitoring

- **Build Status**: Check Actions tab for real-time status
- **PR Feedback**: Automated comments on pull requests  
- **Release Notes**: Auto-generated changelogs
- **Artifact Downloads**: Build outputs available for debugging

## Documentation

See [`docs/GITHUB_WORKFLOWS.md`](../docs/GITHUB_WORKFLOWS.md) for complete documentation including:
- Detailed workflow explanations
- Troubleshooting guides  
- Customization instructions
- Best practices and security considerations

---

*This streamlined CI/CD pipeline focuses on code validation and quality assurance, with Railway handling all deployment operations for the VillaWise vacation rental platform.*