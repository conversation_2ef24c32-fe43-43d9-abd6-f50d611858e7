
import { supabase } from '../../supabase'
import { getCurrentUser, requireAuth, requireRole } from '../auth/session'
import { PropertyDTO, PropertyData, PropertySearchFilters, PropertySearchResult } from '../dto/property.dto'
import { memoryCache } from '../cache/memoryCache'

export const getProperties = async (
  authHeader?: string,
  filters?: PropertySearchFilters
): Promise<PropertySearchResult> => {
  const user = await getCurrentUser(authHeader)
  
  const cacheKey = `properties:search:${JSON.stringify(filters)}`
  const cached = memoryCache.get(cacheKey) as PropertySearchResult | null
  if (cached) return cached
  
  try {
    let query = supabase
      .from('properties')
      .select(`
        *,
        users:host_id (
          id,
          full_name,
          profile_picture
        )
      `)
      .eq('status', 'active')
    
    if (filters?.location) {
      query = query.ilike('location', `%${filters.location}%`)
    }
    
    if (filters?.minPrice !== undefined) {
      query = query.gte('price_per_night', filters.minPrice)
    }
    
    if (filters?.maxPrice !== undefined) {
      query = query.lte('price_per_night', filters.maxPrice)
    }
    
    const { data: properties, error, count } = await query
      .range(0, 49)
      .order('created_at', { ascending: false })
    
    if (error) {
      throw new Error(`Failed to fetch properties: ${error.message}`)
    }
    
    const result: PropertySearchResult = {
      properties: PropertyDTO.fromArray(properties || [], user?.role),
      total: count || 0,
      page: 1,
      limit: 50,
      hasMore: (count || 0) > 50
    }
    
    memoryCache.set(cacheKey, result, 300)
    return result
  } catch (error) {
    console.error('Error fetching properties:', error)
    throw error
  }
}

export const getPropertyById = async (
  propertyId: string,
  authHeader?: string
): Promise<PropertyDTO | null> => {
  const user = await getCurrentUser(authHeader)
  
  const cacheKey = `property:${propertyId}`
  const cached = memoryCache.get(cacheKey) as PropertyDTO | null
  if (cached) return cached
  
  try {
    const { data: property, error } = await supabase
      .from('properties')
      .select(`
        *,
        users:host_id (
          id,
          full_name,
          profile_picture,
          bio
        )
      `)
      .eq('id', propertyId)
      .single()
    
    if (error || !property) {
      return null
    }
    
    if (property.status !== 'active' && 
        user?.role !== 'admin' && 
        user?.userId !== property.host_id) {
      return null
    }
    
    const dto = new PropertyDTO(property, user?.role)
    memoryCache.set(cacheKey, dto, 600)
    return dto
  } catch (error) {
    console.error('Error fetching property:', error)
    return null
  }
}

export const createProperty = async (
  authHeader: string,
  propertyData: Partial<PropertyData>
): Promise<PropertyDTO> => {
  const user = await requireRole(authHeader, 'host')
  
  try {
    const { data: property, error } = await supabase
      .from('properties')
      .insert({
        ...propertyData,
        host_id: user.userId,
        status: 'draft',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()
    
    if (error) {
      throw new Error(`Failed to create property: ${error.message}`)
    }
    
    memoryCache.invalidatePattern(`properties:host:${user.userId}`)
    memoryCache.invalidatePattern('properties:search:*')
    
    return new PropertyDTO(property, user.role)
  } catch (error) {
    console.error('Error creating property:', error)
    throw error
  }
}

export const getHostProperties = async (
  authHeader: string,
  filters?: { status?: string[] }
): Promise<PropertyDTO[]> => {
  const user = await requireRole(authHeader, 'host')
  
  const cacheKey = `properties:host:${user.userId}:${JSON.stringify(filters)}`
  const cached = memoryCache.get(cacheKey) as PropertyDTO[] | null
  if (cached) return cached
  
  try {
    let query = supabase
      .from('properties')
      .select('*')
      .eq('host_id', user.userId)
    
    if (filters?.status && filters.status.length > 0) {
      query = query.in('status', filters.status)
    }
    
    const { data: properties, error } = await query.order('created_at', { ascending: false })
    
    if (error) {
      throw new Error(`Failed to fetch host properties: ${error.message}`)
    }
    
    const result = PropertyDTO.fromArray(properties || [], user.role)
    memoryCache.set(cacheKey, result, 300)
    return result
  } catch (error) {
    console.error('Error fetching host properties:', error)
    throw error
  }
}

export const updateProperty = async (
  authHeader: string,
  propertyId: string,
  updates: Partial<PropertyData>
): Promise<PropertyDTO> => {
  const user = await requireRole(authHeader, 'host')
  
  try {
    const { data: property, error } = await supabase
      .from('properties')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', propertyId)
      .eq('host_id', user.userId)
      .select()
      .single()
    
    if (error) {
      throw new Error(`Failed to update property: ${error.message}`)
    }
    
    memoryCache.invalidatePattern(`property:${propertyId}`)
    memoryCache.invalidatePattern(`properties:host:${user.userId}:*`)
    memoryCache.invalidatePattern('properties:search:*')
    
    return new PropertyDTO(property, user.role)
  } catch (error) {
    console.error('Error updating property:', error)
    throw error
  }
}

export const deleteProperty = async (
  authHeader: string,
  propertyId: string
): Promise<void> => {
  const user = await requireRole(authHeader, 'host')
  
  try {
    const { error } = await supabase
      .from('properties')
      .delete()
      .eq('id', propertyId)
      .eq('host_id', user.userId)
    
    if (error) {
      throw new Error(`Failed to delete property: ${error.message}`)
    }
    
    memoryCache.invalidatePattern(`property:${propertyId}`)
    memoryCache.invalidatePattern(`properties:host:${user.userId}:*`)
    memoryCache.invalidatePattern('properties:search:*')
  } catch (error) {
    console.error('Error deleting property:', error)
    throw error
  }
}

export const getPopularProperties = async (
  authHeader?: string,
  limit: number = 12
): Promise<PropertyDTO[]> => {
  const user = await getCurrentUser(authHeader)
  
  const cacheKey = `properties:popular:${limit}`
  const cached = memoryCache.get(cacheKey) as PropertyDTO[] | null
  if (cached) return cached
  
  try {
    const { data: properties, error } = await supabase
      .from('properties')
      .select(`
        *,
        users:host_id (
          id,
          full_name,
          profile_picture
        )
      `)
      .eq('status', 'active')
      .order('rating', { ascending: false })
      .limit(limit)
    
    if (error) {
      throw new Error(`Failed to fetch popular properties: ${error.message}`)
    }
    
    const result = PropertyDTO.fromArray(properties || [], user?.role)
    
    // Cache for 30 minutes
    memoryCache.set(cacheKey, result, 1800)
    
    return result
  } catch (error) {
    console.error('Error fetching popular properties:', error)
    return []
  }
}