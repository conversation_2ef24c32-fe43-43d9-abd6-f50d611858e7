import { Clock, MapPin, X, RotateCcw } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { useTranslations } from "@/lib/translations";
import { useSearchPersistence } from "@/features/guest/search/hooks";
import { formatDistanceToNow } from "date-fns";

interface RecentSearchesProps {
  onSearchSelect: (search: any) => void;
  className?: string;
}

export function RecentSearches({ onSearchSelect, className }: RecentSearchesProps) {
  const t = useTranslations("searchBar");
  const { getLastSearchData, clearSearchData } = useSearchPersistence();
  
  const lastSearch = getLastSearchData();

  if (!lastSearch) {
    return null;
  }

  const formatSearchDisplay = (search: any) => {
    const location = search.location || search.name || '';
    const guests = search.guests;
    const dateRange = search.checkIn && search.checkOut ? {
      from: new Date(search.checkIn),
      to: new Date(search.checkOut),
    } : search.dateRange;

    let display = location;
    
    if (guests) {
      const totalGuests = guests.adults + guests.children;
      if (totalGuests > 1) {
        display += ` • ${totalGuests} guests`;
      }
    }
    
    if (dateRange) {
      const fromDate = dateRange.from;
      const toDate = dateRange.to;
      if (fromDate && toDate) {
        const fromStr = fromDate.toLocaleDateString('en-US', { 
          month: 'short', 
          day: 'numeric' 
        });
        const toStr = toDate.toLocaleDateString('en-US', { 
          month: 'short', 
          day: 'numeric' 
        });
        display += ` • ${fromStr} - ${toStr}`;
      }
    }

    return display;
  };

  const getTimeAgo = (timestamp: number) => {
    if (!timestamp) return '';
    try {
      return formatDistanceToNow(new Date(timestamp), { addSuffix: true });
    } catch {
      return '';
    }
  };

  return (
    <Card className={`p-3 space-y-2 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Clock className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm font-medium text-muted-foreground">
            Last Search
          </span>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={clearSearchData}
          className="h-6 text-xs text-muted-foreground hover:text-foreground"
        >
          <X className="h-3 w-3 mr-1" />
          Clear
        </Button>
      </div>
      
      <div className="space-y-1">
        <div
          className="flex items-center justify-between p-2 rounded-md hover:bg-muted/50 cursor-pointer group"
          onClick={() => onSearchSelect(lastSearch)}
        >
          <div className="flex items-center space-x-2 flex-1 min-w-0">
            <MapPin className="h-3 w-3 text-muted-foreground flex-shrink-0" />
            <div className="flex-1 min-w-0">
              <div className="text-sm text-foreground truncate">
                {formatSearchDisplay(lastSearch)}
              </div>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
            onClick={(e) => {
              e.stopPropagation();
              onSearchSelect(lastSearch);
            }}
          >
            <RotateCcw className="h-3 w-3" />
          </Button>
        </div>
      </div>
    </Card>
  );
}