#!/bin/bash

# GeoNames Data Download Script
# Downloads required GeoNames data files on-demand instead of storing in Git

GEONAMES_DIR="data/geonames"
GEONAMES_BASE_URL="https://download.geonames.org/export/dump"

echo "🌍 Starting GeoNames data download..."

# Create directory if it doesn't exist
mkdir -p "$GEONAMES_DIR"

# Download Spain data
echo "📥 Downloading Spain location data..."
if [ ! -f "$GEONAMES_DIR/ES.txt" ]; then
    curl -L "$GEONAMES_BASE_URL/ES.zip" -o "$GEONAMES_DIR/ES.zip"
    unzip -o "$GEONAMES_DIR/ES.zip" -d "$GEONAMES_DIR"
    rm "$GEONAMES_DIR/ES.zip"
    echo "✅ Spain data downloaded ($(du -sh $GEONAMES_DIR/ES.txt | cut -f1))"
else
    echo "✅ Spain data already exists"
fi

# Download alternate names (multilingual support)
echo "📥 Downloading multilingual alternate names..."
if [ ! -f "$GEONAMES_DIR/alternateNamesV2.txt" ]; then
    curl -L "$GEONAMES_BASE_URL/alternateNamesV2.zip" -o "$GEONAMES_DIR/alternateNamesV2.zip"
    unzip -o "$GEONAMES_DIR/alternateNamesV2.zip" -d "$GEONAMES_DIR"
    rm "$GEONAMES_DIR/alternateNamesV2.zip"
    echo "✅ Alternate names downloaded ($(du -sh $GEONAMES_DIR/alternateNamesV2.txt | cut -f1))"
else
    echo "✅ Alternate names already exist"
fi

echo "🎉 GeoNames data download completed!"
echo "📊 Total data size: $(du -sh $GEONAMES_DIR | cut -f1)"