import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useTranslations } from '@/lib/translations';
import { MessageSquare, Send, Clock } from 'lucide-react';

interface MessagesContentProps {
  messages: any[];
  userType: 'host' | 'guest';
}

const MessagesContent: React.FC<MessagesContentProps> = ({ messages, userType }) => {
  const t = useTranslations(userType === 'host' ? 'hostDashboard' : 'guestDashboard');

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-semibold text-gray-900">
          {t('navigation.messages')}
        </h2>
        <Button variant="outline" className="mt-4">
          <Send className="h-4 w-4 mr-2" />
          New Message
        </Button>
      </div>

      {messages.length > 0 ? (
        <div className="space-y-4">
          {messages.map((message: any) => (
            <Card key={message.id} className="cursor-pointer hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">
                    {userType === 'host' ? message.guest_name : message.host_name}
                  </CardTitle>
                  <div className="flex items-center space-x-2">
                    {!message.read && (
                      <Badge variant="default" className="bg-primary text-primary-foreground">
                        New
                      </Badge>
                    )}
                    <Badge variant="outline" className="text-xs">
                      <Clock className="h-3 w-3 mr-1" />
                      {formatDate(message.created_at)}
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 mb-2">{message.property_title}</p>
                <p className="text-gray-800 line-clamp-2">{message.content}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600">No messages yet</p>
          <p className="text-sm text-gray-500 mt-2">
            {userType === 'host' 
              ? 'Messages from guests will appear here' 
              : 'Messages from hosts will appear here'}
          </p>
        </div>
      )}
    </div>
  );
};

export default MessagesContent;