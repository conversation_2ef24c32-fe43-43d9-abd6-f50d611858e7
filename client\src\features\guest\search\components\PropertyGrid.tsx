import { useTranslations } from '@/lib/translations';
import { PropertyCard } from './PropertyCard';
import { Skeleton } from '@/components/ui/skeleton';
import { Property } from '@/lib/apiClient';

interface PropertyGridProps {
  properties: Property[];
  isLoading: boolean;
  onPropertyHover?: (propertyId: string | undefined) => void;
  viewMode?: 'list' | 'split' | 'map';
}

export function PropertyGrid({ properties, isLoading, onPropertyHover, viewMode = 'list' }: PropertyGridProps) {
  const t = useTranslations('searchPage');

  // Limit properties in split view to show fewer cards like Airbnb
  const displayProperties = viewMode === 'split' ? properties.slice(0, 5) : properties;
  
  // Adjust grid layout based on view mode
  const gridClasses = viewMode === 'split' 
    ? "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-1 xl:grid-cols-2 gap-4"
    : "grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-3";

  const skeletonCount = viewMode === 'split' ? 5 : 12;

  if (isLoading) {
    return (
      <div className={gridClasses}>
        {Array.from({ length: skeletonCount }).map((_, index) => (
          <div key={index} className="space-y-1 max-w-[200px]">
            <Skeleton className="aspect-square w-full rounded-lg" />
            <div className="space-y-1 px-1">
              <Skeleton className="h-2.5 w-3/4" />
              <Skeleton className="h-2.5 w-1/2" />
              <Skeleton className="h-2.5 w-1/3" />
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (!properties || properties.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-muted-foreground">{t("noResults")}</p>
      </div>
    );
  }

  return (
    <div className={gridClasses}>
      {displayProperties.map((property) => (
        <PropertyCard
          key={property.id}
          property={property}
        />
      ))}
    </div>
  );
}