/**
 * Tourism Regions Seeder
 *
 * Seeds Spanish tourism regions with coordinate boundaries and translations.
 * Migrates data from server/services/geonames/tourism-data.ts
 */

export default {
  name: "03_tourism_regions",
  description: "Spanish tourism regions with geographic boundaries",
  environment: "shared" as const,
  order: 4,

  async execute(supabase: any): Promise<void> {
    console.log("   🏖️ Seeding tourism regions...");

    // Tourism regions with coordinate boundaries (from SPANISH_TOURISM_REGIONS)
    const tourismRegions = [
      {
        country_id: 724, // Spain
        region_type: "coastal",
        coordinate_bounds: {
          lat_min: 41.6,
          lat_max: 42.3,
          lng_min: 2.8,
          lng_max: 3.3,
        },
        center_coordinates: `(41.8833,2.9167)`, // PostgreSQL POINT format
        popularity_score: 85,
      },
      {
        country_id: 724, // Spain
        region_type: "coastal",
        coordinate_bounds: {
          lat_min: 36.3,
          lat_max: 36.8,
          lng_min: -5.2,
          lng_max: -3.8,
        },
        center_coordinates: `(36.5,-4.5)`,
        popularity_score: 90,
      },
      {
        country_id: 724, // Spain
        region_type: "coastal",
        coordinate_bounds: {
          lat_min: 37.8,
          lat_max: 38.8,
          lng_min: -0.8,
          lng_max: -0.1,
        },
        center_coordinates: `(38.3,-0.45)`,
        popularity_score: 88,
      },
      {
        country_id: 724, // Spain
        region_type: "coastal",
        coordinate_bounds: {
          lat_min: 36.0,
          lat_max: 37.0,
          lng_min: -6.0,
          lng_max: -5.0,
        },
        center_coordinates: `(36.5,-5.5)`,
        popularity_score: 75,
      },
      {
        country_id: 724, // Spain
        region_type: "coastal",
        coordinate_bounds: {
          lat_min: 43.3,
          lat_max: 43.6,
          lng_min: -8.8,
          lng_max: -6.8,
        },
        center_coordinates: `(43.45,-7.8)`,
        popularity_score: 70,
      },
      {
        country_id: 724, // Spain
        region_type: "island",
        coordinate_bounds: {
          lat_min: 39.2,
          lat_max: 40.1,
          lng_min: 2.3,
          lng_max: 4.3,
        },
        center_coordinates: `(39.65,3.3)`,
        popularity_score: 92,
      },
      {
        country_id: 724, // Spain
        region_type: "island",
        coordinate_bounds: {
          lat_min: 27.6,
          lat_max: 29.4,
          lng_min: -18.2,
          lng_max: -13.4,
        },
        center_coordinates: `(28.5,-15.8)`,
        popularity_score: 85,
      },
      {
        country_id: 724, // Spain
        region_type: "mountain",
        coordinate_bounds: {
          lat_min: 42.6,
          lat_max: 43.0,
          lng_min: -3.0,
          lng_max: -1.5,
        },
        center_coordinates: `(42.8,-2.25)`,
        popularity_score: 65,
      },
      {
        country_id: 724, // Spain
        region_type: "cultural",
        coordinate_bounds: {
          lat_min: 40.3,
          lat_max: 40.5,
          lng_min: -3.8,
          lng_max: -3.6,
        },
        center_coordinates: `(40.4,-3.7)`,
        popularity_score: 95,
      },
      {
        country_id: 724, // Spain
        region_type: "cultural",
        coordinate_bounds: {
          lat_min: 41.6,
          lat_max: 41.7,
          lng_min: 2.1,
          lng_max: 2.2,
        },
        center_coordinates: `(41.65,2.15)`,
        popularity_score: 90,
      },
      {
        country_id: 724, // Spain
        region_type: "cultural",
        coordinate_bounds: {
          lat_min: 37.3,
          lat_max: 37.4,
          lng_min: -6.0,
          lng_max: -5.9,
        },
        center_coordinates: `(37.35,-5.95)`,
        popularity_score: 80,
      },
      {
        country_id: 724, // Spain
        region_type: "cultural",
        coordinate_bounds: {
          lat_min: 39.8,
          lat_max: 39.9,
          lng_min: -4.1,
          lng_max: -4.0,
        },
        center_coordinates: `(39.85,-4.05)`,
        popularity_score: 75,
      },
    ];

    // Insert tourism regions
    const { data: insertedRegions, error: regionError } = await supabase
      .from("tourism_region_codes")
      .insert(tourismRegions)
      .select("id, region_type, center_coordinates");

    if (regionError) {
      throw new Error(`Failed to seed tourism regions: ${regionError.message}`);
    }

    console.log(`   ✅ Seeded ${tourismRegions.length} tourism regions`);

    // Tourism region translations (from SPANISH_TOURISM_REGIONS names)
    const tourismTranslations = [
      // Costa Brava (ID 1)
      {
        entity_type: "tourism_region",
        entity_id: 1,
        language_code: "en",
        text: "Costa Brava",
        is_official: true,
      },
      {
        entity_type: "tourism_region",
        entity_id: 1,
        language_code: "es",
        text: "Costa Brava",
        is_official: true,
      },
      {
        entity_type: "tourism_region",
        entity_id: 1,
        language_code: "ca",
        text: "Costa Brava",
        is_official: true,
      },
      {
        entity_type: "tourism_region",
        entity_id: 1,
        language_code: "nl",
        text: "Costa Brava",
        is_official: false,
      },

      // Costa del Sol (ID 2)
      {
        entity_type: "tourism_region",
        entity_id: 2,
        language_code: "en",
        text: "Costa del Sol",
        is_official: true,
      },
      {
        entity_type: "tourism_region",
        entity_id: 2,
        language_code: "es",
        text: "Costa del Sol",
        is_official: true,
      },
      {
        entity_type: "tourism_region",
        entity_id: 2,
        language_code: "nl",
        text: "Costa del Sol",
        is_official: false,
      },

      // Costa Blanca (ID 3)
      {
        entity_type: "tourism_region",
        entity_id: 3,
        language_code: "en",
        text: "Costa Blanca",
        is_official: true,
      },
      {
        entity_type: "tourism_region",
        entity_id: 3,
        language_code: "es",
        text: "Costa Blanca",
        is_official: true,
      },
      {
        entity_type: "tourism_region",
        entity_id: 3,
        language_code: "ca",
        text: "Costa Blanca",
        is_official: true,
      },
      {
        entity_type: "tourism_region",
        entity_id: 3,
        language_code: "nl",
        text: "Costa Blanca",
        is_official: false,
      },

      // Costa de la Luz (ID 4)
      {
        entity_type: "tourism_region",
        entity_id: 4,
        language_code: "en",
        text: "Costa de la Luz",
        is_official: true,
      },
      {
        entity_type: "tourism_region",
        entity_id: 4,
        language_code: "es",
        text: "Costa de la Luz",
        is_official: true,
      },
      {
        entity_type: "tourism_region",
        entity_id: 4,
        language_code: "nl",
        text: "Costa de la Luz",
        is_official: false,
      },

      // Costa Verde (ID 5)
      {
        entity_type: "tourism_region",
        entity_id: 5,
        language_code: "en",
        text: "Costa Verde",
        is_official: true,
      },
      {
        entity_type: "tourism_region",
        entity_id: 5,
        language_code: "es",
        text: "Costa Verde",
        is_official: true,
      },
      {
        entity_type: "tourism_region",
        entity_id: 5,
        language_code: "nl",
        text: "Costa Verde",
        is_official: false,
      },

      // Balearic Islands (ID 6)
      {
        entity_type: "tourism_region",
        entity_id: 6,
        language_code: "en",
        text: "Balearic Islands",
        is_official: true,
      },
      {
        entity_type: "tourism_region",
        entity_id: 6,
        language_code: "es",
        text: "Islas Baleares",
        is_official: true,
      },
      {
        entity_type: "tourism_region",
        entity_id: 6,
        language_code: "ca",
        text: "Illes Balears",
        is_official: true,
      },
      {
        entity_type: "tourism_region",
        entity_id: 6,
        language_code: "nl",
        text: "Balearen",
        is_official: false,
      },

      // Canary Islands (ID 7)
      {
        entity_type: "tourism_region",
        entity_id: 7,
        language_code: "en",
        text: "Canary Islands",
        is_official: true,
      },
      {
        entity_type: "tourism_region",
        entity_id: 7,
        language_code: "es",
        text: "Islas Canarias",
        is_official: true,
      },
      {
        entity_type: "tourism_region",
        entity_id: 7,
        language_code: "nl",
        text: "Canarische Eilanden",
        is_official: false,
      },

      // Picos de Europa (ID 8)
      {
        entity_type: "tourism_region",
        entity_id: 8,
        language_code: "en",
        text: "Picos de Europa",
        is_official: true,
      },
      {
        entity_type: "tourism_region",
        entity_id: 8,
        language_code: "es",
        text: "Picos de Europa",
        is_official: true,
      },
      {
        entity_type: "tourism_region",
        entity_id: 8,
        language_code: "nl",
        text: "Picos de Europa",
        is_official: false,
      },

      // Madrid (ID 9)
      {
        entity_type: "tourism_region",
        entity_id: 9,
        language_code: "en",
        text: "Madrid",
        is_official: true,
      },
      {
        entity_type: "tourism_region",
        entity_id: 9,
        language_code: "es",
        text: "Madrid",
        is_official: true,
      },
      {
        entity_type: "tourism_region",
        entity_id: 9,
        language_code: "nl",
        text: "Madrid",
        is_official: false,
      },

      // Barcelona (ID 10)
      {
        entity_type: "tourism_region",
        entity_id: 10,
        language_code: "en",
        text: "Barcelona",
        is_official: true,
      },
      {
        entity_type: "tourism_region",
        entity_id: 10,
        language_code: "es",
        text: "Barcelona",
        is_official: true,
      },
      {
        entity_type: "tourism_region",
        entity_id: 10,
        language_code: "ca",
        text: "Barcelona",
        is_official: true,
      },
      {
        entity_type: "tourism_region",
        entity_id: 10,
        language_code: "nl",
        text: "Barcelona",
        is_official: false,
      },

      // Seville (ID 11)
      {
        entity_type: "tourism_region",
        entity_id: 11,
        language_code: "en",
        text: "Seville",
        is_official: true,
      },
      {
        entity_type: "tourism_region",
        entity_id: 11,
        language_code: "es",
        text: "Sevilla",
        is_official: true,
      },
      {
        entity_type: "tourism_region",
        entity_id: 11,
        language_code: "nl",
        text: "Sevilla",
        is_official: false,
      },

      // Toledo (ID 12)
      {
        entity_type: "tourism_region",
        entity_id: 12,
        language_code: "en",
        text: "Toledo",
        is_official: true,
      },
      {
        entity_type: "tourism_region",
        entity_id: 12,
        language_code: "es",
        text: "Toledo",
        is_official: true,
      },
      {
        entity_type: "tourism_region",
        entity_id: 12,
        language_code: "nl",
        text: "Toledo",
        is_official: false,
      },
    ];

    // Insert tourism region translations
    const { error: translationError } = await supabase
      .from("translations")
      .upsert(tourismTranslations, {
        onConflict: "entity_type,entity_id,language_code",
      });

    if (translationError) {
      throw new Error(
        `Failed to seed tourism region translations: ${translationError.message}`
      );
    }

    console.log(
      `   ✅ Seeded ${tourismTranslations.length} tourism region translations`
    );
  },

  async rollback(supabase: any): Promise<void> {
    await supabase
      .from("translations")
      .delete()
      .eq("entity_type", "tourism_region");
    await supabase.from("tourism_region_codes").delete().eq("country_id", 724);
    console.log("   🧹 Cleaned tourism regions and translations");
  },
};
