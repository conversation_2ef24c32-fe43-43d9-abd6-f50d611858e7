import { z } from 'zod';

// Inspiration item schema
export const inspirationItemSchema = z.object({
  image: z.string(),
  alt: z.string(),
  title: z.string(),
  desc: z.string(),
});

// Inspiration data schema
export const inspirationDataSchema = z.object({
  header: z.string(),
  items: z.array(inspirationItemSchema),
});

// Type exports
export type InspirationItem = z.infer<typeof inspirationItemSchema>;
export type InspirationData = z.infer<typeof inspirationDataSchema>;

export interface InspirationSectionProps {
  data?: InspirationData;
}