# Data Access Layer (DAL) Architecture

## Overview

The VillaWise DAL provides a structured, secure, and performant data access layer that abstracts database operations and implements consistent caching, authentication, and data transformation patterns.

## Architecture Components

```mermaid
graph TB
    subgraph "Frontend Layer"
        A[React Components]
        B[TanStack Query]
        C[Query Client]
    end
    
    subgraph "Backend API Layer"
        D[Express Routes]
        E[Route Controllers]
    end
    
    subgraph "DAL Core"
        F[Entity Layer]
        G[DTO Layer]
        H[Auth Layer]
        I[Cache Layer]
        J[Aggregator Layer]
    end
    
    subgraph "Data Layer"
        K[Supabase Client]
        L[PostgreSQL Database]
        M[Memory Cache]
    end
    
    A --> B
    B --> C
    C --> D
    D --> E
    E --> F
    E --> J
    F --> G
    F --> H
    F --> I
    G --> H
    H --> I
    I --> M
    F --> K
    K --> L
    
    style F fill:#e1f5fe
    style G fill:#f3e5f5
    style H fill:#fff3e0
    style I fill:#e8f5e8
    style J fill:#fce4ec
```

## DAL Layer Structure

### 1. Entity Layer (`server/dal/entities/`)
**Purpose**: Direct database operations and business logic
- `properties.ts` - Property CRUD operations
- `bookings.ts` - Booking management
- `users.ts` - User profile operations
- `messages.ts` - Communication system

### 2. DTO Layer (`server/dal/dto/`)
**Purpose**: Safe data transformation for client consumption
- `property.dto.ts` - Property data transformation
- `booking.dto.ts` - Booking data transformation
- `user.dto.ts` - User data transformation with role-based access
- `message.dto.ts` - Message data transformation

### 3. Authentication Layer (`server/dal/auth/`)
**Purpose**: JWT verification and session management
- `session.ts` - Token verification, user session management

### 4. Cache Layer (`server/dal/cache/`)
**Purpose**: Performance optimization through memory caching
- `memoryCache.ts` - In-memory cache implementation with TTL

### 5. Aggregator Layer (`server/dal/aggregators/`)
**Purpose**: Complex data aggregation for dashboard views
- `dashboard.ts` - Multi-entity data aggregation

## Communication Flow Diagrams

### Search Page Flow

```mermaid
sequenceDiagram
    participant UI as Search Page
    participant TQ as TanStack Query
    participant API as Express API
    participant DAL as Properties Entity
    participant Cache as Memory Cache
    participant DB as Supabase DB
    
    UI->>TQ: Search properties
    TQ->>API: GET /api/properties/search
    API->>DAL: getProperties(filters)
    
    DAL->>Cache: Check cache
    alt Cache Hit
        Cache-->>DAL: Return cached data
        DAL-->>API: PropertyDTO[]
    else Cache Miss
        DAL->>DB: Query properties
        DB-->>DAL: Raw property data
        DAL->>DAL: Transform to PropertyDTO
        DAL->>Cache: Store result (TTL: 5min)
        DAL-->>API: PropertyDTO[]
    end
    
    API-->>TQ: JSON response
    TQ-->>UI: Render properties
```

### Dashboard Authentication Flow

```mermaid
sequenceDiagram
    participant UI as Dashboard Page
    participant TQ as TanStack Query
    participant QC as Query Client
    participant API as Express API
    participant Auth as Session Auth
    participant Cache as Auth Cache
    participant SB as Supabase Auth
    
    UI->>TQ: Load dashboard data
    TQ->>QC: Add Authorization header
    QC->>API: GET /api/dashboard/host
    API->>Auth: requireAuth(token)
    
    Auth->>Cache: Check session cache
    alt Session Cached
        Cache-->>Auth: Return user session
    else Session Not Cached
        Auth->>SB: Verify JWT token
        SB-->>Auth: User data
        Auth->>Auth: Create UserSession
        Auth->>Cache: Cache session (TTL: 5min)
    end
    
    Auth-->>API: UserSession | throw error
    
    alt Authenticated
        API->>API: Fetch dashboard data
        API-->>TQ: Dashboard data
        TQ-->>UI: Render dashboard
    else Unauthorized
        API-->>TQ: 401 error
        TQ-->>UI: Redirect to login
    end
```

### Property Management Flow

```mermaid
sequenceDiagram
    participant UI as Host Dashboard
    participant TQ as TanStack Query
    participant API as Property API
    participant DAL as Properties Entity
    participant Auth as Session Auth
    participant DTO as PropertyDTO
    participant DB as Supabase DB
    
    UI->>TQ: Create property
    TQ->>API: POST /api/properties
    API->>Auth: requireRole('host')
    Auth-->>API: UserSession (role: host)
    
    API->>DAL: createProperty(data, authHeader)
    DAL->>Auth: requireAuth(authHeader)
    DAL->>DB: Insert property
    DB-->>DAL: Raw property data
    DAL->>DTO: new PropertyDTO(data, role)
    DTO-->>DAL: Transformed data
    DAL->>Cache: Invalidate related caches
    DAL-->>API: PropertyDTO
    
    API-->>TQ: Success response
    TQ->>TQ: Invalidate queries
    TQ-->>UI: Refresh property list
```

## Caching Strategy

### Multi-Level Caching Architecture

```mermaid
graph TB
    subgraph "Frontend Caching"
        A[TanStack Query Cache]
        B[localStorage Cache]
    end
    
    subgraph "Backend Caching"
        C[Session Cache]
        D[Entity Cache]
        E[Route Cache]
    end
    
    subgraph "Cache Keys"
        F["session:${token}"]
        G["properties:search:${filters}"]
        H["bookings:${userType}:${userId}"]
        I["conversations:${userId}"]
    end
    
    A --> |5 min stale time| C
    B --> |localStorage| D
    C --> F
    D --> G
    D --> H
    D --> I
    E --> |Express middleware| D
```

### Cache TTL Strategy

| Cache Type | TTL | Use Case |
|------------|-----|----------|
| Session Cache | 5 minutes | User authentication |
| Property Search | 5 minutes | Search results |
| User Profile | 5 minutes | Profile data |
| Conversations | 5 minutes | Message lists |
| Bookings | 2 minutes | Booking data |
| Messages | 1 minute | Real-time messaging |
| Dashboard Aggregations | 10 minutes | Dashboard stats |

## Data Transformation Pipeline

### DTO Transformation Process

```mermaid
graph LR
    subgraph "Database Layer"
        A[Raw Database Row]
    end
    
    subgraph "Entity Layer"
        B[Entity Function]
        C[Business Logic]
    end
    
    subgraph "DTO Layer"
        D[DTO Constructor]
        E[Role-Based Filtering]
        F[Field Transformation]
    end
    
    subgraph "API Response"
        G[JSON Response]
    end
    
    A --> B
    B --> C
    C --> D
    D --> E
    E --> F
    F --> G
    
    style D fill:#f3e5f5
    style E fill:#fff3e0
    style F fill:#e8f5e8
```

### PropertyDTO Example

```typescript
// Raw database data
interface PropertyData {
  id: string
  title: string
  price_per_night: number
  host_id: string
  status: 'active' | 'inactive'
  created_at: string
}

// Transformed DTO
class PropertyDTO {
  id: string
  title: string
  pricePerNight: number
  hostId?: string        // Only for hosts/admins
  status?: string        // Only for hosts/admins
  createdAt?: string     // Only for hosts/admins
  
  constructor(property: PropertyData, userRole?: UserSession['role']) {
    this.id = property.id
    this.title = property.title
    this.pricePerNight = property.price_per_night
    
    // Role-based data exposure
    if (userRole === 'host' || userRole === 'admin') {
      this.hostId = property.host_id
      this.status = property.status
      this.createdAt = property.created_at
    }
  }
}
```

## Authorization Flow

### Role-Based Access Control

```mermaid
graph TB
    subgraph "Auth Functions"
        A[getCurrentUser]
        B[requireAuth]
        C[requireRole]
    end
    
    subgraph "Session Management"
        D[verifySession]
        E[Session Cache]
        F[Supabase Auth]
    end
    
    subgraph "User Roles"
        G[Guest]
        H[Host]
        I[Admin]
    end
    
    A --> D
    B --> A
    C --> B
    D --> E
    D --> F
    
    B --> G
    C --> H
    C --> I
    
    style C fill:#ffebee
    style B fill:#fff3e0
    style A fill:#e8f5e8
```

### Authorization Patterns

1. **Public Access**: No authentication required
   ```typescript
   const properties = await getProperties() // No authHeader
   ```

2. **User Required**: Any authenticated user
   ```typescript
   const bookings = await getBookings(authHeader, 'guest')
   ```

3. **Role Required**: Specific role needed
   ```typescript
   const hostProperties = await getHostProperties(authHeader) // Requires 'host' role
   ```

## Performance Optimizations

### Parallel Data Fetching

```mermaid
graph TB
    subgraph "Dashboard Aggregator"
        A[getHostDashboardData]
    end
    
    subgraph "Parallel Execution"
        B[Profile Data]
        C[Properties Data]
        D[Bookings Data]
        E[Messages Data]
        F[Conversations Data]
        G[Booking Stats]
        H[Message Stats]
    end
    
    A --> B
    A --> C
    A --> D
    A --> E
    A --> F
    A --> G
    A --> H
    
    style A fill:#e1f5fe
```

```typescript
// Parallel data fetching example
const [profile, properties, bookings, messages, conversations, bookingStats, messageStats] = 
  await Promise.all([
    getCurrentUserProfile(authHeader),
    getHostProperties(authHeader),
    getBookings(authHeader, 'host', { status: ['pending', 'confirmed'] }),
    getMessages(authHeader, 'host', { unreadOnly: true }),
    getConversations(authHeader),
    getBookingStats(authHeader, 'host'),
    getMessageStats(authHeader, 'host')
  ])
```

## Error Handling Strategy

### Layered Error Handling

```mermaid
graph TB
    subgraph "Frontend"
        A[TanStack Query Error]
        B[Error Boundaries]
        C[Toast Notifications]
    end
    
    subgraph "Backend"
        D[Route Error Handler]
        E[DAL Error Handler]
        F[Database Error]
    end
    
    A --> D
    B --> D
    D --> E
    E --> F
    
    D --> C
    
    style F fill:#ffebee
    style E fill:#fff3e0
    style D fill:#e8f5e8
    style A fill:#e1f5fe
```

### Error Response Format

```typescript
// Consistent error response format
interface APIError {
  success: false
  message: string
  error?: string
  statusCode: number
}

// Success response format
interface APISuccess<T> {
  success: true
  data: T
  meta?: {
    total?: number
    page?: number
    limit?: number
  }
}
```

## Database Integration

### Supabase Integration Pattern

```mermaid
graph LR
    subgraph "DAL Entity"
        A[Entity Function]
        B[Auth Check]
        C[Cache Check]
    end
    
    subgraph "Supabase"
        D[Query Builder]
        E[RLS Policies]
        F[PostgreSQL]
    end
    
    A --> B
    B --> C
    C --> D
    D --> E
    E --> F
    
    style E fill:#ffebee
```

### Query Patterns

1. **Simple Select**
   ```typescript
   const { data, error } = await supabase
     .from('properties')
     .select('*')
     .eq('status', 'active')
   ```

2. **Join with Relations**
   ```typescript
   const { data, error } = await supabase
     .from('bookings')
     .select(`
       *,
       properties:property_id (id, title, location),
       guest:guest_id (id, full_name, email)
     `)
   ```

3. **Role-Based Filtering**
   ```typescript
   let query = supabase.from('bookings').select('*')
   
   if (userType === 'host') {
     query = query.eq('host_id', user.userId)
   } else {
     query = query.eq('guest_id', user.userId)
   }
   ```

## Frontend Integration Points

### TanStack Query Integration

```typescript
// Hook example for property search
export function usePropertySearch(filters: PropertySearchFilters) {
  return useQuery({
    queryKey: ['/api/properties/search', filters],
    enabled: !!filters.location,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

// Mutation example for creating property
export function useCreateProperty() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (data: PropertyData) => 
      apiRequest('/api/properties', {
        method: 'POST',
        body: JSON.stringify(data)
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/properties'] })
    }
  })
}
```

### Authorization Header Management

```typescript
// Automatic token injection in Query Client
const accessToken = localStorage.getItem('sb_access_token')
const headers: HeadersInit = {
  'Content-Type': 'application/json',
}

if (accessToken && accessToken !== 'missing') {
  headers['Authorization'] = `Bearer ${accessToken}`
}
```

## Security Considerations

### Data Access Security

1. **Authentication Required**: All user-specific operations require valid JWT
2. **Role-Based Access**: Sensitive operations restricted by user role
3. **DTO Filtering**: DTOs filter sensitive data based on user role
4. **Cache Isolation**: Cache keys include user context to prevent data leakage

### Security Patterns

```typescript
// Secure entity function pattern
export const getBookings = async (
  authHeader: string,                    // Required authentication
  userType: 'host' | 'guest',           // Role-based filtering
  filters?: BookingFilters
): Promise<BookingDTO[]> => {
  const user = await requireAuth(authHeader)  // Verify authentication
  
  // User-specific data filtering
  if (userType === 'host') {
    query = query.eq('host_id', user.userId)
  } else {
    query = query.eq('guest_id', user.userId)
  }
  
  // Return role-filtered DTOs
  return BookingDTO.fromArray(bookings || [], user.role, user.userId)
}
```

## Monitoring and Debugging

### Debug Logging

```typescript
// Entity function with debug logging
export const getProperties = async (filters?: PropertySearchFilters) => {
  console.log(`[DAL] Fetching properties with filters:`, filters)
  
  const cacheKey = `properties:search:${JSON.stringify(filters)}`
  const cached = memoryCache.get(cacheKey)
  
  if (cached) {
    console.log(`[CACHE] Hit for key: ${cacheKey}`)
    return cached
  }
  
  console.log(`[DB] Query properties from database`)
  // ... database operations
}
```

### Performance Monitoring

- Cache hit/miss ratios
- Query execution times
- Memory usage tracking
- Authentication success rates

## Best Practices

### DAL Development Guidelines

1. **Always use DTOs**: Never return raw database objects to the frontend
2. **Implement caching**: Cache frequently accessed data with appropriate TTL
3. **Require authentication**: Use `requireAuth` or `requireRole` for protected operations
4. **Handle errors gracefully**: Provide meaningful error messages
5. **Use TypeScript strictly**: Leverage type safety throughout the DAL
6. **Optimize queries**: Use Supabase query optimization features
7. **Log operations**: Include debug logging for troubleshooting

### Code Organization

- Keep entity functions focused on single responsibilities
- Use aggregators for complex multi-entity operations
- Maintain consistent naming conventions
- Document complex business logic
- Write comprehensive tests for critical paths

This DAL architecture provides a robust foundation for the VillaWise platform, ensuring data integrity, performance, and security while maintaining developer productivity.