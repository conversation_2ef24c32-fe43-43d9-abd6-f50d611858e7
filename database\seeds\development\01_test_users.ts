/**
 * Development Test Users Seeder
 * 
 * Creates test user accounts for development and testing.
 * NOT safe for production use.
 */

export default {
  name: '01_test_users',
  description: 'Test user accounts for development',
  environment: 'development' as const,
  order: 10,

  async execute(supabase: any): Promise<void> {
    console.log('   👤 Seeding test users...');

    // Test users with UUIDs that won't conflict with real auth.users
    const testUsers = [
      {
        id: '********-0000-0000-0000-********0001',
        email: '<EMAIL>',
        username: 'testhost',
        first_name: '<PERSON>',
        last_name: '<PERSON>',
        phone: '+***********',
        is_host: true,
        profile_image_url: 'https://images.unsplash.com/photo-*************-2616b612b786?auto=format&fit=crop&w=200&q=80'
      },
      {
        id: '********-0000-0000-0000-********0002',
        email: '<EMAIL>',
        username: 'testguest',
        first_name: '<PERSON>',
        last_name: '<PERSON>',
        phone: '+***********',
        is_host: false,
        profile_image_url: 'https://images.unsplash.com/photo-*************-0a1dd7228f2d?auto=format&fit=crop&w=200&q=80'
      },
      {
        id: '********-0000-0000-0000-********0003',
        email: '<EMAIL>',
        username: 'testhost2',
        first_name: 'Carlos',
        last_name: 'García',
        phone: '+34987654321',
        is_host: true,
        profile_image_url: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?auto=format&fit=crop&w=200&q=80'
      }
    ];

    // Insert test users (will be ignored if they exist)
    for (const user of testUsers) {
      const { error } = await supabase
        .from('users')
        .upsert(user, { onConflict: 'id' });

      if (error && !error.message.includes('violates row-level security')) {
        console.warn(`   ⚠️  Could not seed user ${user.email}: ${error.message}`);
      }
    }

    console.log(`   ✅ Processed ${testUsers.length} test users`);
    console.log('   ℹ️  Note: Some users may be skipped due to RLS policies');
  },

  async rollback(supabase: any): Promise<void> {
    // Clean up test users
    const testUserIds = [
      '********-0000-0000-0000-********0001',
      '********-0000-0000-0000-********0002',
      '********-0000-0000-0000-********0003'
    ];

    for (const id of testUserIds) {
      await supabase.from('users').delete().eq('id', id);
    }
    
    console.log('   🧹 Cleaned test users');
  }
};