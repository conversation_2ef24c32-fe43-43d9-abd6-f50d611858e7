#!/bin/bash
set -e

echo "☢️ COMPLETE GIT HISTORY REWRITE - Final Solution"
echo "This will permanently remove large files from entire Git history"
echo ""

# Step 1: Backup current work
echo "📦 Creating backup branch..."
git branch backup-$(date +%Y%m%d-%H%M%S) || true

# Step 2: Complete history rewrite
echo "🔥 Rewriting entire Git history to remove large files..."
git filter-branch --force --index-filter '
git rm --cached --ignore-unmatch data/geonames/alternateNamesV2.txt
git rm --cached --ignore-unmatch data/geonames/alternateNamesV2.zip  
git rm --cached --ignore-unmatch data/geonames/ES.txt
git rm --cached --ignore-unmatch data/geonames/ES.zip
git rm --cached --ignore-unmatch "data/geonames/*.txt"
git rm --cached --ignore-unmatch "data/geonames/*.zip"
git rm --cached --ignore-unmatch "scripts/test/data/geonames/alternateNamesV2.zip"
git rm --cached --ignore-unmatch "scripts/test/data/geonames/alternateNamesV2.txt"
' --prune-empty --tag-name-filter cat -- --all

# Step 3: Clean up all filter-branch remnants
echo "🧹 Cleaning up filter-branch remnants..."
rm -rf .git/refs/original/ || true
git reflog expire --expire=now --all
git gc --prune=now --aggressive

# Step 4: Remove any remaining large files
echo "🗑️ Final cleanup of working directory..."
find . -name "alternateNamesV2.*" -delete || true
find . -name "ES.txt" -delete || true
find . -name "*.zip" -path "*/geonames/*" -delete || true

# Step 5: Add final clean state
echo "📝 Adding clean repository state..."
git add .gitignore scripts/ server/ replit.md || true
git commit -m "Complete repository cleanup: All large files removed

- Tourism region system preserved and functional
- GeoNames data downloaded on-demand via API
- Repository size optimized for GitHub
- All code functionality maintained" || echo "Nothing to commit"

# Step 6: Final aggressive cleanup
echo "🔧 Final Git optimization..."
git gc --aggressive --prune=now

echo "📊 Final repository size:"
du -sh .git

# Step 7: Force push with clean history
echo "🚀 Force pushing clean repository..."
git push origin main --force

echo ""
echo "✅ COMPLETE SUCCESS!"
echo "🎉 Repository cleaned and pushed successfully"
echo "📋 Summary:"
echo "   - All large files permanently removed from Git history"
echo "   - Tourism region functionality preserved"
echo "   - Repository now accepts normal Git operations"
echo "   - Your excellent work is now deployed!"