import { Request, Response } from 'express';
import { Logger } from '../../utils/logger';
import { conversationService } from '../services/conversationService';
import { realtimeService } from '../services/realtimeService';
import { z } from 'zod';

// Validation schemas
const createConversationSchema = z.object({
  hostId: z.string().min(1),
  propertyId: z.string().optional(),
  bookingId: z.string().optional(),
  subject: z.string().optional()
});

const getConversationsSchema = z.object({
  status: z.enum(['active', 'archived', 'closed']).optional(),
  propertyId: z.string().optional(),
  limit: z.coerce.number().min(1).max(100).default(20),
  offset: z.coerce.number().min(0).default(0),
  search: z.string().optional()
});

export class ConversationController {
  
  /**
   * GET /api/messaging/conversations
   * Get conversations for authenticated user
   */
  async getConversations(req: Request, res: Response): Promise<void> {
    const startTime = Date.now();
    try {
      const userId = (req as any).userId;
      const userType = (req as any).user?.user_metadata?.role || 'guest';
      
      const validation = getConversationsSchema.safeParse(req.query);
      if (!validation.success) {
        res.status(400).json({
          success: false,
          message: 'Invalid query parameters',
          errors: validation.error.errors
        });
        return;
      }
      
      const options = validation.data;
      
      const result = await conversationService.getConversations(
        userId,
        userType === 'host' ? 'host' : 'guest',
        options
      );
      
      Logger.api('GET', '/api/messaging/conversations', 200, Date.now() - startTime);
      res.json({
        success: true,
        data: result.conversations,
        pagination: {
          total: result.total,
          limit: options.limit,
          offset: options.offset,
          hasMore: result.total > options.offset + options.limit
        }
      });
    } catch (error) {
      Logger.error('ConversationController.getConversations error:', error);
      Logger.api('GET', '/api/messaging/conversations', 500, Date.now() - startTime);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch conversations',
        error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
      });
    }
  }
  
  /**
   * POST /api/messaging/conversations
   * Create new conversation
   */
  async createConversation(req: Request, res: Response): Promise<void> {
    const startTime = Date.now();
    try {
      const userId = (req as any).userId;
      
      const validation = createConversationSchema.safeParse(req.body);
      if (!validation.success) {
        res.status(400).json({
          success: false,
          message: 'Invalid request data',
          errors: validation.error.errors
        });
        return;
      }
      
      const { hostId, propertyId, bookingId, subject } = validation.data;
      
      const conversation = await conversationService.createConversation({
        guestId: userId,
        hostId,
        propertyId,
        bookingId,
        subject
      });
      
      Logger.api('POST', '/api/messaging/conversations', 201, Date.now() - startTime);
      res.status(201).json({
        success: true,
        data: conversation,
        message: 'Conversation created successfully'
      });
    } catch (error) {
      Logger.error('ConversationController.createConversation error:', error);
      Logger.api('POST', '/api/messaging/conversations', 500, Date.now() - startTime);
      res.status(500).json({
        success: false,
        message: 'Failed to create conversation',
        error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
      });
    }
  }
  
  /**
   * GET /api/messaging/conversations/:id
   * Get single conversation with details
   */
  async getConversation(req: Request, res: Response): Promise<void> {
    const startTime = Date.now();
    try {
      const userId = (req as any).userId;
      const conversationId = req.params.id;
      
      if (!conversationId) {
        res.status(400).json({
          success: false,
          message: 'Conversation ID is required'
        });
        return;
      }
      
      const conversation = await conversationService.getConversationById(
        conversationId,
        userId
      );
      
      if (!conversation) {
        res.status(404).json({
          success: false,
          message: 'Conversation not found or access denied'
        });
        return;
      }
      
      Logger.api('GET', `/api/messaging/conversations/${conversationId}`, 200, Date.now() - startTime);
      res.json({
        success: true,
        data: conversation
      });
    } catch (error) {
      Logger.error('ConversationController.getConversation error:', error);
      Logger.api('GET', `/api/messaging/conversations/${req.params.id}`, 500, Date.now() - startTime);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch conversation',
        error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
      });
    }
  }
  
  /**
   * PATCH /api/messaging/conversations/:id/archive
   * Archive conversation
   */
  async archiveConversation(req: Request, res: Response): Promise<void> {
    const startTime = Date.now();
    try {
      const userId = (req as any).userId;
      const conversationId = req.params.id;
      
      if (!conversationId) {
        res.status(400).json({
          success: false,
          message: 'Conversation ID is required'
        });
        return;
      }
      
      const success = await conversationService.archiveConversation(
        conversationId,
        userId
      );
      
      if (!success) {
        res.status(404).json({
          success: false,
          message: 'Conversation not found or access denied'
        });
        return;
      }
      
      Logger.api('PATCH', `/api/messaging/conversations/${conversationId}/archive`, 200, Date.now() - startTime);
      res.json({
        success: true,
        message: 'Conversation archived successfully'
      });
    } catch (error) {
      Logger.error('ConversationController.archiveConversation error:', error);
      Logger.api('PATCH', `/api/messaging/conversations/${req.params.id}/archive`, 500, Date.now() - startTime);
      res.status(500).json({
        success: false,
        message: 'Failed to archive conversation',
        error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
      });
    }
  }
  
  /**
   * PATCH /api/messaging/conversations/:id/read
   * Mark conversation as read
   */
  async markAsRead(req: Request, res: Response): Promise<void> {
    const startTime = Date.now();
    try {
      const userId = (req as any).userId;
      const conversationId = req.params.id;
      
      if (!conversationId) {
        res.status(400).json({
          success: false,
          message: 'Conversation ID is required'
        });
        return;
      }
      
      const success = await conversationService.markAsRead(
        conversationId,
        userId
      );
      
      if (!success) {
        res.status(404).json({
          success: false,
          message: 'Conversation not found or access denied'
        });
        return;
      }
      
      Logger.api('PATCH', `/api/messaging/conversations/${conversationId}/read`, 200, Date.now() - startTime);
      res.json({
        success: true,
        message: 'Conversation marked as read'
      });
    } catch (error) {
      Logger.error('ConversationController.markAsRead error:', error);
      Logger.api('PATCH', `/api/messaging/conversations/${req.params.id}/read`, 500, Date.now() - startTime);
      res.status(500).json({
        success: false,
        message: 'Failed to mark conversation as read',
        error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
      });
    }
  }
  
  /**
   * POST /api/messaging/conversations/:id/subscribe
   * Subscribe to real-time updates for conversation
   */
  async subscribeToConversation(req: Request, res: Response): Promise<void> {
    const startTime = Date.now();
    try {
      const userId = (req as any).userId;
      const conversationId = req.params.id;
      
      if (!conversationId) {
        res.status(400).json({
          success: false,
          message: 'Conversation ID is required'
        });
        return;
      }
      
      // Verify user has access to conversation
      const conversation = await conversationService.getConversationById(
        conversationId,
        userId
      );
      
      if (!conversation) {
        res.status(404).json({
          success: false,
          message: 'Conversation not found or access denied'
        });
        return;
      }
      
      // Set up real-time subscription (this would typically be handled by frontend)
      // For now, just return subscription info
      Logger.api('POST', `/api/messaging/conversations/${conversationId}/subscribe`, 200, Date.now() - startTime);
      res.json({
        success: true,
        data: {
          conversationId,
          channelName: `conversation:${conversationId}`,
          events: ['message_created', 'message_updated', 'typing_indicator', 'presence_update']
        },
        message: 'Subscription info retrieved'
      });
    } catch (error) {
      Logger.error('ConversationController.subscribeToConversation error:', error);
      Logger.api('POST', `/api/messaging/conversations/${req.params.id}/subscribe`, 500, Date.now() - startTime);
      res.status(500).json({
        success: false,
        message: 'Failed to subscribe to conversation',
        error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
      });
    }
  }
}

export const conversationController = new ConversationController();