/**
 * Image URL Resolution Utility
 * Handles proper image URL generation and validation
 */

export function resolveImageUrls(images: string[] | null | undefined): string[] | null {
  if (!Array.isArray(images) || images.length === 0) {
    return null;
  }

  const validImages = images.filter(isValidImageUrl);
  return validImages.length > 0 ? validImages : null;
}

/**
 * Checks if an image URL is valid (not a placeholder or test image)
 */
function isValidImageUrl(imageUrl: string): boolean {
  if (!imageUrl || typeof imageUrl !== 'string') {
    return false;
  }

  // Check for placeholder patterns
  const placeholderPatterns = [
    /^photo-\d{13}-[a-zA-Z0-9]{12}$/,
    /^photo-\d{10,15}-[a-zA-Z0-9]{10,15}$/,
    /^photo-\d{10,15}$/,
    /^[a-f0-9-]{36}$/,
    /^placeholder/i,
    /^temp/i,
    /^demo/i,
    /^test/i
  ];

  for (const pattern of placeholderPatterns) {
    if (pattern.test(imageUrl)) {
      return false;
    }
  }

  // If it starts with a path, check if it has a valid extension
  if (imageUrl.startsWith('/') || imageUrl.startsWith('http')) {
    const validExtensions = ['.jpg', '.jpeg', '.png', '.webp'];
    const hasValidExtension = validExtensions.some(ext => 
      imageUrl.toLowerCase().includes(ext)
    );
    return hasValidExtension;
  }

  // Reject URLs that look like generic IDs
  if (imageUrl.match(/^[a-f0-9-]{20,}$/)) {
    return false;
  }

  return true;
}