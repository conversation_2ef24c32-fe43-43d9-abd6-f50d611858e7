// GeoNames scope configuration system
export interface CountryConfig {
  code: string;           // ISO 3166-1 alpha-2 
  name: string;
  languages: string[];    // Supported language codes
  priority: number;       // Processing priority (1-10)
  minPopulation: number;  // Minimum population for places
  regions?: string[];     // Specific regions/states to include
}

export interface GeoNamesScopeConfig {
  countries: CountryConfig[];
  globalLanguages: string[];  // Always include these languages
  featureCodes: string[];     // Geographic feature types to include
}

// Spain-focused initial configuration
export const spainInitialConfig: GeoNamesScopeConfig = {
  countries: [
    {
      code: 'ES',
      name: 'Spain',
      languages: ['es', 'ca', 'eu'],  // Spanish, Catalan, Basque
      priority: 10,
      minPopulation: 100,
      regions: [
        'VC',  // Valencia (Costa Blanca)
        'AN',  // Andalusia (Costa del Sol) 
        'CT',  // Catalonia (Barcelona, Costa Brava)
        'IB',  // Balearic Islands (Mallorca, Ibiza)
        'CN'   // Canary Islands
      ]
    }
  ],
  globalLanguages: ['en', 'nl', 'fr', 'de', 'it'],  // English, Dutch, French, German, Italian for European market
  featureCodes: [
    'PPLC',  // Capital (Madrid)
    'PPL',   // Cities and towns
    'PPLA',  // Provincial capitals (Valencia, Seville, Barcelona, etc.)
    'PPLA2', // Important regional centers
    'PPLA3', // Smaller towns
    'PPLA4', // Villages
    'PPLF',  // Farm villages
    'PPLL',  // Populated locality
    'PPLQ',  // Abandoned populated place
    'PPLR',  // Religious populated place
    'PPLS',  // Populated places
    'PPLW',  // Destroyed populated place
    'PPLX',  // Historical populated place
    'ADM1',  // Autonomous communities
    'ADM2',  // Provinces
    'ADM3'   // Municipalities
  ]
};

// Default configuration - Start with Spain
export const defaultScopeConfig: GeoNamesScopeConfig = spainInitialConfig;

export class GeoNamesConfigManager {
  private config!: GeoNamesScopeConfig;
  
  constructor() {
    this.loadConfiguration();
  }
  
  private loadConfiguration(): void {
    // Load from environment variables if provided
    const customConfig = process.env.GEONAMES_CONFIG;
    
    if (customConfig) {
      try {
        this.config = JSON.parse(customConfig);
        console.log('[CONFIG] Using custom GeoNames configuration');
      } catch (error) {
        console.warn('[CONFIG] Invalid custom config, using default');
        this.config = defaultScopeConfig;
      }
    } else {
      // Environment-based overrides
      this.config = {
        ...defaultScopeConfig,
        countries: this.parseCountriesFromEnv(),
        globalLanguages: this.parseLanguagesFromEnv()
      };
    }
    
    this.validateConfiguration();
  }
  
  private parseCountriesFromEnv(): CountryConfig[] {
    const countries = process.env.GEONAMES_COUNTRIES || 'ES';
    const languages = process.env.GEONAMES_LANGUAGES || 'en,nl,es,ca';
    const minPop = parseInt(process.env.GEONAMES_MIN_POPULATION || '1000');
    
    return countries.split(',').map(code => ({
      code: code.trim(),
      name: this.getCountryName(code.trim()),
      languages: languages.split(',').map(l => l.trim()),
      priority: 10,
      minPopulation: minPop,
      regions: process.env.GEONAMES_FOCUS_REGIONS?.split(',').map(r => r.trim())
    }));
  }
  
  private parseLanguagesFromEnv(): string[] {
    const langs = process.env.GEONAMES_GLOBAL_LANGUAGES || 'en,nl,fr,de,it';
    return langs.split(',').map(l => l.trim());
  }
  
  private getCountryName(code: string): string {
    const names: Record<string, string> = {
      'ES': 'Spain',
      'FR': 'France', 
      'IT': 'Italy',
      'PT': 'Portugal',
      'DE': 'Germany',
      'NL': 'Netherlands'
    };
    return names[code] || code;
  }
  
  private validateConfiguration(): void {
    if (!this.config.countries.length) {
      throw new Error('At least one country must be configured');
    }
    
    if (!this.config.globalLanguages.length) {
      throw new Error('At least one global language must be configured');
    }
    
    console.log(`[CONFIG] Configured for ${this.config.countries.length} countries, ${this.getAllLanguages().length} languages`);
  }
  
  // Getters for sync service
  getAllCountryCodes(): string[] {
    return this.config.countries.map(c => c.code);
  }
  
  getAllLanguages(): string[] {
    const countryLangs = this.config.countries.flatMap(c => c.languages);
    return [...new Set([...this.config.globalLanguages, ...countryLangs])];
  }
  
  getCountryConfig(countryCode: string): CountryConfig | undefined {
    return this.config.countries.find(c => c.code === countryCode);
  }
  
  getFeatureCodes(): string[] {
    return this.config.featureCodes;
  }
  
  shouldIncludePlace(place: any): boolean {
    const countryConfig = this.getCountryConfig(place.country_code);
    if (!countryConfig) return false;
    
    // For postal code data, we'll apply minimal filtering since population is not available
    // Focus on geographic distribution rather than population-based filtering
    
    // Always include populated places (P.PPL) from postal code data
    if (place.feature_class === 'P' && place.feature_code === 'PPL') {
      return true;
    }
    
    // Include administrative divisions if present
    if (place.feature_class === 'A') {
      return true;
    }
    
    // For other features, check if they match our configured feature codes
    return this.config.featureCodes.includes(place.feature_code);
  }
  
  getConfig(): GeoNamesScopeConfig {
    return this.config;
  }
}

// Data size estimates for Spain configuration
export const spainDataEstimates = {
  locations: {
    total: 8000,        // Spanish cities/towns with pop > 1000
    breakdown: {
      'P.PPLC': 1,      // Capital (Madrid)
      'P.PPLA': 50,     // Provincial capitals
      'P.PPLA2': 200,   // Important regional centers  
      'P.PPL': 7500,    // Cities and towns
      'A.ADM1': 17,     // Autonomous communities
      'A.ADM2': 50      // Provinces
    }
  },
  alternateNames: {
    total: 25000,       // Names in 4 languages
    breakdown: {
      'en': 8000,       // English names
      'nl': 1000,       // Dutch names (limited)
      'es': 8000,       // Spanish names
      'ca': 6000,       // Catalan names (Valencia, Catalonia)
      'eu': 2000        // Basque names (Basque Country)
    }
  },
  storage: {
    locations: '2MB',
    alternateNames: '5MB',
    indexes: '3MB',
    total: '10MB'
  },
  syncTime: {
    initial: '5-10 minutes',
    incremental: '1-2 minutes'
  }
};