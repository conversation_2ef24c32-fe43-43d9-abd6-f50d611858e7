import { useQuery } from '@tanstack/react-query';
import { explorationService } from '../services/explorationApi';
import { ExplorationProperty } from '../types';

export function usePopularInSpain() {
  return useQuery({
    queryKey: ['exploration', 'popular-spain'],
    queryFn: explorationService.getPopularInSpain,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

export function useNewInFrance() {
  return useQuery({
    queryKey: ['exploration', 'new-france'],
    queryFn: explorationService.getNewInFrance,
    staleTime: 10 * 60 * 1000,
  });
}

export function useGuestFavorites() {
  return useQuery({
    queryKey: ['exploration', 'guest-favorites'],
    queryFn: explorationService.getGuestFavorites,
    staleTime: 10 * 60 * 1000,
  });
}