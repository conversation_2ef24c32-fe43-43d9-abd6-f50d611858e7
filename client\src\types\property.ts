export interface Property {
  id: string;
  title: string;
  type: string;
  location?: string;
  guests: number;
  bedrooms: number;
  bathrooms: number;
  price: number;
  rating: number;
  reviewCount: number;
  imageUrl: string;
  images: string[];
  amenities: string[];
  description: string;
  host: {
    name: string;
    avatar: string;
    isSuperhost: boolean;
  };
  badges?: string[];
}

export type ViewMode = 'list' | 'split' | 'map';