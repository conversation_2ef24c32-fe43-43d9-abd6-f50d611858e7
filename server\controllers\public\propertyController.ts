import { Request, Response } from 'express';
import { Logger } from '../../utils/logger';
import { supabase } from '../../supabase';
import { cacheBackend } from '../../dal/cache/redisCache';

export class PropertyController {

  async getPropertyDetails(req: Request, res: Response) {
    try {
      const { id } = req.params;
      
      Logger.info('Property details request received', { 
        propertyId: id,
        ip: req.ip 
      });

      // Check cache first  
      const cacheKey = `property:details:${id}`;
      const cached = await cacheBackend.get(cacheKey);
      if (cached) {
        return res.json(cached);
      }

      if (!supabase) {
        return res.status(500).json({ 
          success: false, 
          message: "Database service unavailable" 
        });
      }
      
      // Get property with host and review data
      const { data: property, error: propertyError } = await supabase
        .from('properties')
        .select(`
          *,
          users(
            id,
            username,
            first_name,
            last_name,
            created_at
          )
        `)
        .eq('id', id)
        .single();

      // Try to get additional data from related tables (gracefully handle missing tables)
      let hostProfile = null;
      let reviews: any[] = [];
      let availability: any[] = [];
      let propertyDetailsData = null;

      // Try to get host profile (table might not exist)
      if (property && property.users) {
        try {
          if (supabase) {
            const { data: profile } = await supabase
              .from('host_profiles')
              .select('*')
              .eq('user_id', property.users.id)
              .single();
            hostProfile = profile;
          }
        } catch (error) {
          // Table doesn't exist, use defaults
        }
      }

      // Try to get property details for booking rules
      if (property) {
        try {
          if (supabase) {
            const { data: details } = await supabase
              .from('property_details')
              .select('*')
              .eq('property_id', property.id)
              .single();
            propertyDetailsData = details;
          }
        } catch (error) {
          // Table doesn't exist or no data, use defaults
        }
      }

      // Try to get property reviews (table might not exist)
      try {
        if (supabase) {
          const { data: reviewData } = await supabase
            .from('property_reviews')
            .select('*')
            .eq('property_id', id)
            .order('review_date', { ascending: false });
          reviews = reviewData || [];
        }
      } catch (error) {
        // Table doesn't exist, use empty array
      }

      // Try to get property availability (table might not exist)
      try {
        if (supabase) {
          const today = new Date();
          const tenDaysLater = new Date(today);
          tenDaysLater.setDate(today.getDate() + 10);
          
          const { data: availabilityData } = await supabase
            .from('property_availability')
            .select('*')
            .eq('property_id', id)
            .gte('date', today.toISOString().split('T')[0])
            .lte('date', tenDaysLater.toISOString().split('T')[0])
            .order('date', { ascending: true });
          availability = availabilityData || [];
        }
      } catch (error) {
        // Table doesn't exist, use empty array
      }

      if (propertyError || !property) {
        Logger.warn('Property not found', { propertyId: id });
        return res.status(404).json({ error: 'Property not found' });
      }

      // Transform property data to match frontend expectations
      const transformedProperty = {
        id: property.id,
        title: property.title,
        subtitle: `${property.property_type} in ${property.city} · ${property.country}`,
        hostType: `Entire ${property.property_type}`,
        price: `€ ${Math.round(Number(property.price_per_night) * 7)},-`,
        pricePerNight: Number(property.price_per_night),
        rating: property.rating ? Number(property.rating) : null,
        reviewCount: property.review_count || (reviews ? reviews.length : 0),
        images: property.images || [],
        gallery: {
          main: property.images?.[0] || null,
          thumbnails: property.images?.slice(1) || []
        },
        badge: hostProfile?.is_superhost ? "Superhost" : null,
        location: {
          city: property.city,
          region: null, // Should come from database, not hardcoded
          country: property.country,
          address: `${property.location}, ${property.city}, ${property.country}`,
          coordinates: property.coordinates
        },
        maxGuests: property.max_guests,
        bedrooms: property.bedrooms,
        bathrooms: property.bathrooms,
        amenities: this.transformAmenities(property.amenities),
        description: property.description,
        houseRules: [
          `Maximum ${property.max_guests} guests`
        ],
        host: this.transformHost(property.users, hostProfile),
        reviews: this.transformReviews(reviews || []),
        availability: this.transformAvailability(availability || []),
        bookingRules: this.transformBookingRules(propertyDetailsData),
        trustIndicators: this.transformTrustIndicators(propertyDetailsData)
      };

      // Cache for 10 minutes (property details change less frequently but can be updated by hosts)
      await cacheBackend.set(cacheKey, transformedProperty, 600);

      Logger.info('Returning property details', { 
        propertyId: id,
        title: transformedProperty.title 
      });
      
      Logger.api('GET', `/api/properties/${id}`, 200);
      
      res.json(transformedProperty);
    } catch (error) {
      Logger.error('Error fetching property details:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  private transformAmenities(amenitiesArray: string[] | null) {
    if (!amenitiesArray) {
      return {
        wifi: false,
        parking: false,
        pool: false,
        kitchen: false,
        airConditioning: false,
        heating: false,
        tv: false,
        washer: false,
        balcony: false,
        garden: false
      };
    }

    return {
      wifi: amenitiesArray.includes('WiFi') || amenitiesArray.includes('Wi-Fi'),
      parking: amenitiesArray.includes('Free parking') || amenitiesArray.includes('Parking'),
      pool: amenitiesArray.includes('Pool'),
      kitchen: amenitiesArray.includes('Kitchen'),
      airConditioning: amenitiesArray.includes('Air conditioning'),
      heating: amenitiesArray.includes('Heating'),
      tv: amenitiesArray.includes('TV'),
      washer: amenitiesArray.includes('Washing machine') || amenitiesArray.includes('Washer'),
      balcony: amenitiesArray.includes('Balcony'),
      garden: amenitiesArray.includes('Garden')
    };
  }

  private transformHost(user: any, hostProfile: any) {
    if (!user) return null;
    
    // Format join date directly here instead of using separate method
    let joinedDate = hostProfile?.joined_date;
    if (!joinedDate && user.created_at) {
      const date = new Date(user.created_at);
      const months = ['jan', 'feb', 'mrt', 'apr', 'mei', 'jun', 'jul', 'aug', 'sep', 'okt', 'nov', 'dec'];
      joinedDate = `${months[date.getMonth()]} ${date.getFullYear()}`;
    }
    
    return {
      id: user.id,
      name: user.first_name || user.username,
      avatar: hostProfile?.avatar_url || null,
      joinedDate: joinedDate || null,
      isVerified: hostProfile?.is_verified || null,
      isSuperhost: hostProfile?.is_superhost || null,
      responseRate: hostProfile?.response_rate || null,
      responseTime: hostProfile?.response_time || null,
      bio: hostProfile?.bio || null
    };
  }

  private transformReviews(reviews: any[]) {
    return reviews.map(review => ({
      id: review.id,
      guestName: review.guest_name,
      guestAvatar: review.guest_avatar_url || `https://picsum.photos/seed/${review.guest_name}/100/100`,
      rating: review.rating,
      comment: review.comment,
      date: review.review_date
    }));
  }

  private transformAvailability(availability: any[]) {
    const availabilityMap: Record<string, { available: boolean; price: number }> = {};
    
    // If no availability data, generate basic availability
    if (!availability || availability.length === 0) {
      const today = new Date();
      
      for (let i = 0; i < 10; i++) {
        const date = new Date(today);
        date.setDate(today.getDate() + i);
        const dateStr = date.toISOString().split('T')[0];
        
        availabilityMap[dateStr] = {
          available: true,
          price: 700
        };
      }
      
      return availabilityMap;
    }
    
    // Use database availability data
    availability.forEach(day => {
      availabilityMap[day.date] = {
        available: day.available,
        price: day.price || 700
      };
    });
    
    return availabilityMap;
  }

  private transformBookingRules(propertyDetails: any) {
    if (!propertyDetails) {
      return {
        checkIn: "16:00",
        checkOut: "11:00",
        cancellationPolicy: "Flexible",
        minimumStay: 1,
        instantBook: false
      };
    }

    return {
      checkIn: propertyDetails.check_in_time || "16:00",
      checkOut: propertyDetails.check_out_time || "11:00",
      cancellationPolicy: propertyDetails.cancellation_policy || "Flexible",
      minimumStay: propertyDetails.minimum_stay || 1,
      instantBook: propertyDetails.instant_book || false
    };
  }

  private transformTrustIndicators(propertyDetails: any) {
    // For now, return standard trust indicators
    // This could be enhanced to use actual property features from the database
    return {
      freeBookingChanges: true,
      directCommunication: true,
      trustedReviews: true,
      securePayment: true
    };
  }

}

export const propertyController = new PropertyController();