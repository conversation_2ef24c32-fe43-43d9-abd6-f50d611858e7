import { Request, Response } from 'express';
import { Logger } from '../../utils/logger';
import { storage } from '../../storage';
import { insertGuestBookingSchema } from '../../../shared/schema';
import { z } from 'zod';

export class GuestBookingController {
  
  async getBookings(req: Request, res: Response) {
    const startTime = Date.now();
    try {
      const guestId = req.params.guestId;
      const requestingUserId = (req as any).userId; // From auth middleware
      
      if (!guestId) {
        return res.status(400).json({ error: 'Guest ID is required' });
      }
      
      // RLS-style check: Users can only access their own bookings
      if (guestId !== requestingUserId) {
        return res.status(403).json({ 
          error: 'Access denied - You can only access your own bookings' 
        });
      }
      
      Logger.info(`Guest bookings requested for guest: ${guestId}`);
      
      const bookings = await storage.getGuestBookingsByGuest(guestId);
      
      Logger.api('GET', `/api/guest/bookings/${guestId}`, 200, Date.now() - startTime);
      res.json(bookings);
    } catch (error) {
      Logger.error('Error fetching guest bookings', error);
      Logger.api('GET', `/api/guest/bookings/${req.params.guestId}`, 500, Date.now() - startTime);
      res.status(500).json({ error: 'Failed to fetch guest bookings' });
    }
  }

  async getBooking(req: Request, res: Response) {
    const startTime = Date.now();
    try {
      const bookingId = req.params.bookingId;
      
      if (!bookingId) {
        return res.status(400).json({ error: 'Booking ID is required' });
      }
      
      Logger.info(`Guest booking requested: ${bookingId}`);
      
      const booking = await storage.getGuestBooking(bookingId);
      
      if (!booking) {
        return res.status(404).json({ error: 'Booking not found' });
      }
      
      Logger.api('GET', `/api/guest/booking/${bookingId}`, 200, Date.now() - startTime);
      res.json(booking);
    } catch (error) {
      Logger.error('Error fetching guest booking', error);
      Logger.api('GET', `/api/guest/booking/${req.params.bookingId}`, 500, Date.now() - startTime);
      res.status(500).json({ error: 'Failed to fetch guest booking' });
    }
  }

  async createBooking(req: Request, res: Response) {
    const startTime = Date.now();
    try {
      const validation = insertGuestBookingSchema.safeParse(req.body);
      
      if (!validation.success) {
        return res.status(400).json({ 
          error: 'Invalid booking data', 
          details: validation.error.errors 
        });
      }
      
      const bookingData = {
        ...validation.data,
        check_in: new Date(validation.data.check_in),
        check_out: new Date(validation.data.check_out)
      };
      Logger.info(`Creating guest booking for property: ${bookingData.host_property_id}`);
      
      const booking = await storage.createGuestBooking(bookingData);
      
      Logger.api('POST', '/api/guest/booking', 201, Date.now() - startTime);
      res.status(201).json(booking);
    } catch (error) {
      Logger.error('Error creating guest booking', error);
      Logger.api('POST', '/api/guest/booking', 500, Date.now() - startTime);
      res.status(500).json({ error: 'Failed to create guest booking' });
    }
  }

  async updateBooking(req: Request, res: Response) {
    const startTime = Date.now();
    try {
      const bookingId = req.params.bookingId;
      
      if (!bookingId) {
        return res.status(400).json({ error: 'Booking ID is required' });
      }
      
      const updateSchema = insertGuestBookingSchema.partial();
      const validation = updateSchema.safeParse(req.body);
      
      if (!validation.success) {
        return res.status(400).json({ 
          error: 'Invalid booking data', 
          details: validation.error.errors 
        });
      }
      
      const updates = validation.data;
      Logger.info(`Updating guest booking: ${bookingId}`);
      
      const booking = await storage.updateGuestBooking(bookingId, updates);
      
      if (!booking) {
        return res.status(404).json({ error: 'Booking not found' });
      }
      
      Logger.api('PUT', `/api/guest/booking/${bookingId}`, 200, Date.now() - startTime);
      res.json(booking);
    } catch (error) {
      Logger.error('Error updating guest booking', error);
      Logger.api('PUT', `/api/guest/booking/${req.params.bookingId}`, 500, Date.now() - startTime);
      res.status(500).json({ error: 'Failed to update guest booking' });
    }
  }
}

export const guestBookingController = new GuestBookingController();