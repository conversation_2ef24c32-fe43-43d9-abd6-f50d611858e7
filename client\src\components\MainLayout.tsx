import { Header } from "./Header";
import { Footer } from "./Footer";
import { ReactNode } from "react";

interface MainLayoutProps {
  children: ReactNode;
  showFooter?: boolean;
  className?: string;
  style?: React.CSSProperties;
  hideOverlay?: boolean;
}

export function MainLayout({ children, showFooter = true, className = "", style = {}, hideOverlay = false }: MainLayoutProps) {
  return (
    <div className="min-h-screen relative bg-background">
      {/* Purple gradient background */}
      <div className="absolute inset-0 bg-gradient-to-b from-purple-100/50 via-purple-50/30 to-transparent pointer-events-none" />

      <div className="relative z-10">
        <Header />

        {/* Main content area with blue villa background */}
        <main 
          className={`relative bg-cover bg-center bg-no-repeat ${className}`}
          style={{
            backgroundImage: `url(https://bapymeimutdxrngejohd.supabase.co/storage/v1/object/public/public-index//istockphoto-94279168-2048x2048.jpg)`,
            minHeight: '80vh',
            ...style
          }}
        >
          {/* Overlay for better content readability */}
          {!hideOverlay && <div className="absolute inset-0 bg-black/20"></div>}
          
          <div className="relative z-10">
            {children}
          </div>
        </main>

        {showFooter && <Footer />}
      </div>
    </div>
  );
}