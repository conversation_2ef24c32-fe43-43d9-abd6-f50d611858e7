{"version": "2.0.0", "tasks": [{"label": "TypeScript Check", "type": "shell", "command": "npx", "args": ["tsc", "--noEmit", "--skip<PERSON><PERSON><PERSON><PERSON><PERSON>"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": false, "clear": true}, "problemMatcher": "$tsc", "runOptions": {"runOn": "folderOpen"}}, {"label": "Start Dev Server", "type": "shell", "command": "npm", "args": ["run", "dev"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": false}, "isBackground": true, "problemMatcher": {"owner": "typescript", "source": "ts", "fileLocation": ["relative", "${workspaceFolder}"], "pattern": "$tsc", "background": {"activeOnStart": true, "beginsPattern": ".*serving on port.*", "endsPattern": ".*Local:.*"}}}, {"label": "Health Check", "type": "shell", "command": "curl", "args": ["-s", "http://localhost:5000/api/health"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": false, "clear": true}}, {"label": "ESLint Check", "type": "shell", "command": "npx", "args": ["eslint", ".", "--ext", ".ts,.tsx,.js,.jsx"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": false, "clear": true}, "problemMatcher": "$eslint-stylish"}, {"label": "Build Frontend", "type": "shell", "command": "npm", "args": ["run", "build"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": false, "clear": true}, "problemMatcher": "$tsc"}, {"label": "Production Build", "type": "shell", "command": "node", "args": ["scripts/build/build-production.js"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": false, "clear": true}, "dependsOn": ["Build Frontend"]}, {"label": "Full Development Check", "dependsOrder": "sequence", "dependsOn": ["TypeScript Check", "ESLint Check", "Health Check"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": false, "clear": true}}, {"label": "Docker Build", "type": "shell", "command": "docker", "args": ["build", "-t", "villawise:latest", "."], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": false, "clear": true}}, {"label": "<PERSON><PERSON>", "type": "shell", "command": "docker", "args": ["run", "-p", "5000:5000", "villawise:latest"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": false}, "dependsOn": ["Docker Build"]}, {"label": "Clean Install", "type": "shell", "command": "rm", "args": ["-rf", "node_modules", "package-lock.json"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": false, "clear": true}}, {"label": "Reinstall Dependencies", "type": "shell", "command": "npm", "args": ["install"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": false, "clear": true}, "dependsOn": ["Clean Install"]}]}