
import { UserSession } from '../auth/session'

export interface UserData {
  id: string
  email: string
  full_name?: string
  phone?: string
  role: 'admin' | 'host' | 'guest'
  profile_picture?: string
  bio?: string
  languages?: string[]
  created_at: string
  updated_at: string
  email_verified?: boolean
  phone_verified?: boolean
  host_verified?: boolean
}

export class UserDTO {
  id: string
  email?: string
  fullName?: string
  role: string
  profilePicture?: string
  bio?: string
  languages?: string[]
  createdAt: string
  emailVerified?: boolean
  phoneVerified?: boolean
  hostVerified?: boolean
  phone?: string

  constructor(user: UserData, viewerRole?: UserSession['role'], viewerId?: string) {
    this.id = user.id
    this.fullName = user.full_name
    this.role = user.role
    this.profilePicture = user.profile_picture
    this.bio = user.bio
    this.languages = user.languages
    this.createdAt = user.created_at
    this.emailVerified = user.email_verified
    this.phoneVerified = user.phone_verified
    this.hostVerified = user.host_verified

    // Only include sensitive data based on permissions
    const isAdmin = viewerRole === 'admin'
    const isSelf = viewerId === user.id
    const canViewPrivateData = isAdmin || isSelf

    if (canViewPrivateData) {
      this.email = user.email
      this.phone = user.phone
    }

    // Public profile for hosts/guests
    if (user.role === 'host' || user.role === 'guest') {
      // Always show name and profile picture for hosts
      if (user.role === 'host') {
        this.fullName = user.full_name || 'Host'
      }
    }
  }

  static fromArray(users: UserData[], viewerRole?: UserSession['role'], viewerId?: string): UserDTO[] {
    return users.map(user => new UserDTO(user, viewerRole, viewerId))
  }

  // Create minimal public profile
  toPublicProfile(): Pick<UserDTO, 'id' | 'fullName' | 'profilePicture' | 'bio' | 'languages' | 'role'> {
    return {
      id: this.id,
      fullName: this.fullName,
      profilePicture: this.profilePicture,
      bio: this.bio,
      languages: this.languages,
      role: this.role
    }
  }
}

export interface UserFilters {
  role?: string
  verified?: boolean
  search?: string
}

export interface UserStats {
  total: number
  guests: number
  hosts: number
  admins: number
  verified: number
}