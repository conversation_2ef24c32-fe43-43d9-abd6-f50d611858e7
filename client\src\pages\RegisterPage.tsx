import React, { useState } from "react";
import { useLocation } from "wouter";
import { useTranslations } from "@/lib/translations";
import { MainLayout } from "@/components/MainLayout";
import { SocialLoginButtons } from "@/components/auth/SocialLoginButtons";
import { PasswordInput } from "@/components/auth/PasswordInput";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { Link } from "wouter";
import { Loader2, ArrowLeft } from "lucide-react";
import { TranslatedErrorMessage } from "@/components/ui/TranslatedErrorMessage";
import { useRegister } from "@/features/shared/auth/hooks/useAuth";

export function RegisterPage() {
  const [, navigate] = useLocation();
  const { toast } = useToast();
  const t = useTranslations('auth.register');
  const tValidation = useTranslations('validation');
  const [agreeToTerms, setAgreeToTerms] = useState(false);
  const [formData, setFormData] = useState({
    email: "",
    password: "",
    confirmPassword: "",
    firstName: "",
    lastName: "",
    username: "",
  });
  
  const registerMutation = useRegister();
  const { isPending: isLoading } = registerMutation;

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const validateForm = () => {
    if (formData.password !== formData.confirmPassword) {
      toast({
        title: t('passwordMismatch'),
        description: t('passwordMismatchDescription'),
        variant: "destructive",
      });
      return false;
    }
    
    if (formData.password.length < 8) {
      toast({
        title: t('passwordTooShort'),
        description: t('passwordTooShortDescription'),
        variant: "destructive",
      });
      return false;
    }
    
    if (!agreeToTerms) {
      toast({
        title: t('termsRequired'),
        description: t('termsRequiredDescription'),
        variant: "destructive",
      });
      return false;
    }
    
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    try {
      console.log("🚀 Starting registration process:");
      console.log("  - Email:", formData.email);
      console.log("  - Username:", formData.username);
      console.log("  - First Name:", formData.firstName);
      console.log("  - Last Name:", formData.lastName);

      // Use the mutation hook for registration
      const result = await registerMutation.mutateAsync({
        email: formData.email,
        password: formData.password,
        username: formData.username,
        firstName: formData.firstName,
        lastName: formData.lastName,
      });

      console.log("✅ Registration successful:", result);
      console.log("🔍 Full result object:", JSON.stringify(result, null, 2));
      console.log("🔍 Checking requiresEmailConfirmation flag:", (result as any).requiresEmailConfirmation);
      console.log("🔍 Result type:", typeof result);
      console.log("🔍 Result keys:", Object.keys(result || {}));

      // Check if email confirmation is required
      if ((result as any).requiresEmailConfirmation) {
        console.log("📧 Email confirmation required - redirecting to login with success message");
        // Store email and success message for login page
        localStorage.setItem('registration_email', formData.email);
        localStorage.setItem('registration_success', 'true');
        // Navigate to login page with success message
        navigate('/login');
      } else {
        // Regular registration with immediate login
        toast({
          title: t('accountCreated'),
          description: t('accountCreatedDescription'),
        });

        console.log("🕐 Waiting 300ms before navigation...");
        setTimeout(() => {
          console.log("🏠 Navigating to home page");
          navigate('/');
          
          console.log("🔄 Reloading page in 100ms...");
          setTimeout(() => {
            window.location.reload();
          }, 100);
        }, 300);
      }
    } catch (error) {
      console.error('Registration error:', error);
      toast({
        title: t('registrationFailed'),
        description: error instanceof Error ? error.message : t('registrationFailedDescription'),
        variant: "destructive",
      });
    }
  };

  return (
    <MainLayout showFooter={false}>
      <div className="flex items-center justify-center min-h-[80vh] py-12 px-4">
        <Card className="w-full max-w-lg mx-auto bg-white/95 backdrop-blur-sm border shadow-xl">
          <CardHeader className="text-center relative">
            <Link href="/">
              <Button
                variant="ghost"
                size="sm"
                className="absolute left-0 top-0 h-8 w-8 p-0 text-gray-500 hover:text-gray-700"
              >
                <ArrowLeft className="h-4 w-4" />
              </Button>
            </Link>
            <CardTitle className="text-2xl font-bold text-gray-900">
              {t('title')}
            </CardTitle>
            <p className="text-gray-600 mt-2">
              {t('subtitle')}
            </p>
          </CardHeader>
          <CardContent className="space-y-6">
            <SocialLoginButtons />
            
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="bg-white px-2 text-gray-500">{t('orContinueWith')}</span>
              </div>
            </div>
            
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="firstName">{t('firstNameLabel')}</Label>
                  <Input
                    id="firstName"
                    name="firstName"
                    type="text"
                    placeholder={t('firstNamePlaceholder')}
                    value={formData.firstName}
                    onChange={handleInputChange}
                    required
                    disabled={isLoading}
                    className="h-12"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="lastName">{t('lastNameLabel')}</Label>
                  <Input
                    id="lastName"
                    name="lastName"
                    type="text"
                    placeholder={t('lastNamePlaceholder')}
                    value={formData.lastName}
                    onChange={handleInputChange}
                    required
                    disabled={isLoading}
                    className="h-12"
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="username">{t('usernameLabel')}</Label>
                <Input
                  id="username"
                  name="username"
                  type="text"
                  placeholder={t('usernamePlaceholder')}
                  value={formData.username}
                  onChange={handleInputChange}
                  required
                  disabled={isLoading}
                  className="h-12"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="email">{t('emailLabel')}</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  placeholder={t('emailPlaceholder')}
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                  disabled={isLoading}
                  className="h-12"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="password">{t('passwordLabel')}</Label>
                <PasswordInput
                  id="password"
                  name="password"
                  placeholder={t('passwordPlaceholder')}
                  value={formData.password}
                  onChange={handleInputChange}
                  required
                  disabled={isLoading}
                  className="h-12"
                  showStrength={true}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="confirmPassword">{t('confirmPasswordLabel')}</Label>
                <PasswordInput
                  id="confirmPassword"
                  name="confirmPassword"
                  placeholder={t('confirmPasswordPlaceholder')}
                  value={formData.confirmPassword}
                  onChange={handleInputChange}
                  required
                  disabled={isLoading}
                  className="h-12"
                />
              </div>
              
              <div className="flex items-start space-x-2">
                <Checkbox
                  id="terms"
                  checked={agreeToTerms}
                  onCheckedChange={(checked) => setAgreeToTerms(checked === true)}
                  disabled={isLoading}
                />
                <Label htmlFor="terms" className="text-sm text-gray-600 leading-5">
                  {t('agreeToTerms')}
                </Label>
              </div>
              
              <Button
                type="submit"
                className="w-full h-12"
                disabled={registerMutation.isPending}
              >
                {registerMutation.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {t('creatingAccount')}
                  </>
                ) : (
                  t('createAccountButton')
                )}
              </Button>
            </form>
            
            <div className="text-center">
              <p className="text-sm text-gray-600">
                {t('alreadyHaveAccount')}{' '}
                <Link href="/login">
                  <Button variant="link" className="p-0 h-auto text-sm">
                    {t('signIn')}
                  </Button>
                </Link>
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
}