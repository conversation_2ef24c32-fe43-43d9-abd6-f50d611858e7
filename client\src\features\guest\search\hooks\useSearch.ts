import { UseQueryResult } from '@tanstack/react-query';
import { useDebouncedSearch } from '@/hooks/useDebouncedSearch';
import { searchService } from '../services/searchApi';
import { SearchFilters, SearchResponse } from '../types';

export function useSearch(
  filters: SearchFilters,
  options?: { enabled?: boolean }
): UseQueryResult<SearchResponse, Error> {
  return useDebouncedSearch(
    (filters, signal) => searchService.searchProperties(filters, signal),
    filters,
    {
      enabled: options?.enabled ?? true,
      debounceMs: 300 // Debounce search requests by 300ms
    }
  );
}

export function useSearchWithParams(
  location: string,
  checkIn: string,
  checkOut: string,
  guests: SearchFilters['guests']
) {
  const filters: SearchFilters = {
    location,
    checkIn,
    checkOut,
    guests,
  };

  return useSearch(filters, { enabled: Boolean(location || checkIn || checkOut) });
}