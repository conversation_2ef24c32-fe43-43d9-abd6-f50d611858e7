# VillaWise VSCode Development Setup

This directory contains optimized VSCode configurations specifically tailored for the VillaWise vacation rental platform development.

## 📋 Overview

The configuration has been optimized for our full-stack TypeScript architecture:
- **Frontend**: React 18 + TypeScript + Tailwind CSS + shadcn/ui
- **Backend**: Express.js + Supabase PostgreSQL
- **Build**: Vite with HMR and production Docker builds
- **Deployment**: Railway.app

## 🚀 Key Features

### Chat Mode Configuration
- **File**: `chat-mode-villawise.md`
- **Purpose**: Specialized AI development assistant for VillaWise
- **Key Features**:
  - Mandatory TypeScript compilation checks
  - VillaWise-specific tech stack knowledge
  - Autonomous problem-solving workflow
  - Production-ready code practices

### Development Environment
- **TypeScript**: Enhanced IntelliSense with type hints
- **ESLint**: Automatic code fixing and organization
- **Prettier**: Consistent code formatting
- **Tailwind CSS**: Intelligent class completion
- **File Nesting**: Organized project structure

### Integrated Tasks
- **TypeScript Check**: `Ctrl+Shift+P` → "Tasks: Run Task" → "TypeScript Check"
- **Health Check**: Verify server status
- **ESLint Check**: Code quality validation
- **Full Development Check**: Complete validation suite
- **Docker Build**: Production container building

### Debug Configurations
- **Debug Server**: Backend Node.js debugging
- **Launch Chrome**: Frontend debugging
- **Full Debug Setup**: Combined frontend + backend debugging

## 🛠️ Quick Setup

### 1. Install Recommended Extensions
```bash
# VSCode will automatically prompt to install recommended extensions
# Or run: code --install-extension <extension-id>
```

### 2. Open Workspace
```bash
# Option 1: Open workspace file
code .vscode/VillaWise.code-workspace

# Option 2: Open folder with configurations
code .
```

### 3. Configure Environment
```bash
# Ensure development environment variables are set
cp .env.example .env
# Edit .env with your configuration
```

## 📋 Essential Commands

### TypeScript Development
```bash
# Manual TypeScript check (matches VSCode task)
npx tsc --noEmit --skipLibCheck

# Run development server
npm run dev

# Health check
curl -s http://localhost:5000/api/health
```

### Code Quality
```bash
# ESLint check
npx eslint . --ext .ts,.tsx,.js,.jsx

# Prettier format
npx prettier --write .
```

### Production Build
```bash
# Frontend build
npm run build

# Production build (includes server optimization)
node scripts/build/build-production.js
```

## 🎯 VillaWise-Specific Features

### Mandatory Development Practices
1. **TypeScript Compilation**: Always passes before commits
2. **Health Endpoint**: Server must respond correctly
3. **Documentation**: Update `replit.md` for architectural changes
4. **Testing**: Comprehensive verification of all changes

### Project Structure Integration
- **Feature-based architecture**: 8 self-contained modules
- **Shared types**: Centralized in `shared/schema.ts`
- **Database**: Supabase with 11 tables
- **Authentication**: OAuth + JWT with role-based routing

### Performance Optimizations
- **Leaflet Maps**: Optimized for 100+ properties
- **TanStack Query**: Efficient data fetching
- **Image Processing**: Supabase Storage integration
- **Bundle Optimization**: Tree shaking and code splitting

## 🔧 Advanced Configuration

### Custom Tailwind Class Detection
```typescript
// Supports advanced class detection patterns
["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"],
["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]
```

### File Nesting Patterns
```json
{
  "*.ts": "${capture}.js",
  "*.tsx": "${capture}.js",
  "package.json": "package-lock.json,yarn.lock,pnpm-lock.yaml",
  "docker-compose.yml": "docker-compose.*.yml,Dockerfile*"
}
```

### TypeScript IntelliSense
- Parameter names and types
- Variable type hints
- Function return types
- Property declaration types
- Enum member values

## 🚨 Troubleshooting

### Common Issues

**TypeScript Compilation Errors**
```bash
# Check specific errors
npx tsc --noEmit --skipLibCheck

# Common fixes
# 1. Check null/undefined handling
# 2. Verify import paths
# 3. Ensure proper type annotations
```

**ESLint Configuration Issues**
```bash
# Reset ESLint cache
npx eslint --cache-location .eslintcache --cache

# Check configuration
npx eslint --print-config client/src/App.tsx
```

**Tailwind CSS Not Working**
```bash
# Verify configuration
npx tailwindcss -i ./client/src/index.css -o ./dist/output.css --watch

# Check PostCSS configuration
cat postcss.config.js
```

**Extension Conflicts**
- Disable conflicting TypeScript extensions
- Use recommended extensions list
- Reload VSCode window after changes

### Health Check Failures
```bash
# Verify server is running
curl -s http://localhost:5000/api/health

# Check server logs
npm run dev

# Verify environment variables
cat .env
```

## 📈 Performance Tips

1. **Use Workspace**: Better performance with large projects
2. **Enable File Nesting**: Reduces visual clutter
3. **Exclude Large Directories**: Faster search and indexing
4. **Use TypeScript IntelliSense**: Leverage advanced features
5. **Optimize Extensions**: Only install what you need

## 🔄 Maintenance

### Regular Updates
- Update extensions monthly
- Review and update tasks as project evolves
- Maintain workspace configuration
- Keep chat mode configuration current

### Project Evolution
- Update file nesting patterns for new file types
- Adjust TypeScript configuration for new features
- Modify tasks for new build processes
- Update debugging configurations for new services

## 🤝 Team Collaboration

### Shared Configuration
- All team members use same VSCode setup
- Consistent code formatting and linting
- Shared debugging configurations
- Standardized development workflow

### Version Control
- `.vscode/` directory included in repository
- Workspace-specific settings documented
- Extension recommendations synchronized
- Task configurations shared across team

---

This configuration provides a comprehensive development environment optimized for VillaWise's full-stack TypeScript architecture, ensuring consistency, productivity, and code quality across the entire development team.