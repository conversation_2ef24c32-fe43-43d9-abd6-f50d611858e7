import { useState } from 'react';
import { Heart, Star, Users, Bed, Bath, Wifi, Car, Waves, ChevronLeft, ChevronRight, ExternalLink } from 'lucide-react';
import { useTranslations } from '@/lib/translations';
import { useLocation } from 'wouter';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Property } from '@/lib/apiClient';

interface PropertyCardProps {
  property: Property;
}

export function PropertyCard({ property }: PropertyCardProps) {
  const t = useTranslations('propertyCard');
  const [, setLocation] = useLocation();
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isFavorite, setIsFavorite] = useState(false);

  // Safe property validation
  if (!property || !property.id) {
    return null;
  }

  // Convert Unsplash photo IDs to proper URLs
  const getImageUrl = (photoId: string) => {
    try {
      if (!photoId || typeof photoId !== 'string') return '';
      if (photoId.startsWith('photo-')) {
        const unsplashId = photoId.replace('photo-', '');
        // Use picsum for reliable image loading
        const imageUrl = `https://picsum.photos/id/${Math.abs(parseInt(unsplashId.slice(-6), 16) % 1000)}/800/600`;
        return imageUrl;
      }
      return photoId;
    } catch (error) {
      console.error('Error processing image URL:', error);
      return 'https://picsum.photos/800/600';
    }
  };

  const images = property?.images?.filter(Boolean).map(getImageUrl).filter(Boolean) || [];
  
  // Add fallback image if no images available
  const displayImages = images.length > 0 ? images : ['https://picsum.photos/800/600?grayscale&random=1'];

  const nextImage = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (displayImages.length > 0) {
      setCurrentImageIndex((prev) => 
        prev === displayImages.length - 1 ? 0 : prev + 1
      );
    }
  };

  const prevImage = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (displayImages.length > 0) {
      setCurrentImageIndex((prev) => 
        prev === 0 ? displayImages.length - 1 : prev - 1
      );
    }
  };

  const toggleFavorite = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsFavorite(!isFavorite);
  };

  const handleCardClick = () => {
    // Open property details in new tab to preserve search state
    window.open(`/property/${property.id}`, '_blank');
  };

  const amenityIcons = {
    wifi: Wifi,
    parking: Car,
    pool: Waves,
  };

  return (
    <Card className="group cursor-pointer hover:shadow-lg transition-shadow duration-200" onClick={handleCardClick}>
      <CardContent className="p-0">
        {/* Image carousel */}
        <div className="relative overflow-hidden rounded-t-lg">
          <div className="aspect-square relative">
            <img
              src={displayImages[currentImageIndex]}
              alt={property.title || 'Property image'}
              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                // Create a simple inline SVG placeholder
                const placeholderSvg = `data:image/svg+xml;base64,${btoa(`
                  <svg width="300" height="200" xmlns="http://www.w3.org/2000/svg">
                    <rect width="100%" height="100%" fill="#f0f0f0"/>
                    <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="16" fill="#666" text-anchor="middle" dy=".3em">
                      No Image
                    </text>
                  </svg>
                `)}`;
                target.src = placeholderSvg;
              }}
            />
            
            {/* Navigation arrows */}
            {displayImages.length > 1 && (
              <>
                <button
                  onClick={prevImage}
                  className="absolute left-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <ChevronLeft className="h-3 w-3" />
                </button>
                <button
                  onClick={nextImage}
                  className="absolute right-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <ChevronRight className="h-3 w-3" />
                </button>
              </>
            )}

            {/* Image indicators */}
            {displayImages.length > 1 && (
              <div className="absolute bottom-2 left-1/2 -translate-x-1/2 flex space-x-1">
                {displayImages.map((_, index) => (
                  <div
                    key={index}
                    className={`w-1 h-1 rounded-full ${
                      index === currentImageIndex ? 'bg-white' : 'bg-white/50'
                    }`}
                  />
                ))}
              </div>
            )}

            {/* Favorite button */}
            <button
              onClick={toggleFavorite}
              className="absolute top-1.5 right-1.5 p-1 rounded-full bg-white/80 hover:bg-white transition-colors"
            >
              <Heart
                className={`h-2.5 w-2.5 ${
                  isFavorite ? 'fill-red-500 text-red-500' : 'text-gray-600'
                }`}
              />
            </button>

            {/* Badge */}
            {(property.badge || (property.badges && property.badges.length > 0)) && (
              <div className="absolute top-1.5 left-1.5">
                <Badge variant="secondary" className="bg-white/90 text-gray-900 text-[10px] px-1.5 py-0.5">
                  {(() => {
                    try {
                      // Use single badge if available, otherwise use first badge from array
                      const badgeValue = property.badge || (property.badges && property.badges[0]);
                      if (!badgeValue) return '';
                      
                      const badgeKey = badgeValue.toLowerCase().replace(/\s+/g, '-');
                      return t(`badges.${badgeKey}`);
                    } catch (error) {
                      return property.badge || (property.badges && property.badges[0]) || '';
                    }
                  })()}
                </Badge>
              </div>
            )}
          </div>
        </div>

        {/* Content */}
        <div className="p-3 space-y-1.5">
          {/* Title and Rating */}
          <div className="flex items-center justify-between">
            <h3 className="font-medium text-sm text-foreground line-clamp-1 leading-tight flex-1 min-w-0 pr-2">
              {property.title}
            </h3>
            <div className="flex items-center space-x-1 flex-shrink-0">
              <Star className="h-3 w-3 fill-current text-yellow-400" />
              <span className="text-xs font-medium">{property.rating}</span>
              <ExternalLink className="h-3 w-3 text-muted-foreground/60" />
            </div>
          </div>
          
          {/* Location and Property details in one row */}
          <div className="flex items-center justify-between text-xs text-muted-foreground leading-tight">
            <span>{property.hostType}</span>
            
            {/* Property details - only show if available */}
            {(property.maxGuests || property.bedrooms || property.bathrooms) && (
              <div className="flex items-center space-x-2">
                {property.maxGuests && (
                  <div className="flex items-center space-x-1">
                    <Users className="h-3 w-3" />
                    <span>{property.maxGuests}</span>
                  </div>
                )}
                {property.bedrooms && (
                  <div className="flex items-center space-x-1">
                    <Bed className="h-3 w-3" />
                    <span>{property.bedrooms}</span>
                  </div>
                )}
                {property.bathrooms && (
                  <div className="flex items-center space-x-1">
                    <Bath className="h-3 w-3" />
                    <span>{property.bathrooms}</span>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Price */}
          <div className="flex flex-col pt-1">
            <div className="text-xs text-muted-foreground">{t('priceFrom')}</div>
            <div className="text-sm font-semibold text-foreground">
              {property.price}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}