import React from 'react';
import { useLocation } from 'wouter';
import { HostDashboardLayout } from '../features/host/dashboard';
import { MainLayout } from '../components/MainLayout';
import { useUser } from '../features/shared/auth/hooks/useAuth';
import { Card, CardContent } from '../components/ui/card';
import { Loader2, ShieldAlert } from 'lucide-react';
import { Button } from '../components/ui/button';
import { useTranslations } from '../lib/translations';

const HostDashboardPage: React.FC = () => {
  const { data: user, isLoading } = useUser();
  const [, navigate] = useLocation();
  const t = useTranslations('authErrors');

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <MainLayout className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-96">
          <CardContent className="p-8 text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
            <p className="text-gray-600">{t('checkingAuthorization')}</p>
          </CardContent>
        </Card>
      </MainLayout>
    );
  }

  // Redirect to login if not authenticated
  if (!user) {
    navigate('/login');
    return null;
  }

  // Show host upgrade message if not a host
  if (!user.is_host) {
    return (
      <MainLayout className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-96">
          <CardContent className="p-8 text-center">
            <ShieldAlert className="h-12 w-12 mx-auto mb-4 text-amber-500" />
            <h1 className="text-xl font-semibold mb-2">{t('hostAccessRequired')}</h1>
            <p className="text-gray-600 mb-6">
              {t('hostUpgradeNeeded')}
            </p>
            <div className="space-y-3">
              <Button 
                onClick={() => navigate('/host/upgrade')}
                className="w-full"
              >
                {t('upgradeToHost')}
              </Button>
              <Button 
                variant="outline"
                onClick={() => navigate('/')}
                className="w-full"
              >
                {t('backToHome')}
              </Button>
            </div>
          </CardContent>
        </Card>
      </MainLayout>
    );
  }

  // User is authenticated and is a host - show dashboard
  return (
    <MainLayout 
      showFooter={false} 
      hideOverlay={true}
      className="bg-gray-50"
      style={{ backgroundImage: 'none', minHeight: '100vh' }}
    >
      <HostDashboardLayout />
    </MainLayout>
  );
};

export default HostDashboardPage;