import { supabase } from '../../supabase';
import { Logger } from '../../utils/logger';
import { cacheBackend } from '../../dal/cache/redisCache';

export interface MessageData {
  id: string;
  conversation_id: string;
  sender_id: string;
  sender_type: 'guest' | 'host';
  content: string;
  message_type: 'text' | 'template' | 'system' | 'automated';
  message_status: 'sent' | 'delivered' | 'read';
  response_time_ms?: number;
  read_at?: string;
  edited_at?: string;
  original_content?: string;
  is_flagged: boolean;
  template_id?: string;
  scheduled_for?: string;
  created_at: string;
  updated_at: string;
  sender?: {
    id: string;
    name: string;
    avatar_url?: string;
    user_type: string;
  };
}

export interface SendMessageData {
  conversationId: string;
  senderId: string;
  senderType: 'guest' | 'host';
  content: string;
  messageType?: 'text' | 'template' | 'system' | 'automated';
  templateId?: string;
  scheduledFor?: string;
}

export class MessageService {
  
  /**
   * Get messages for a conversation with pagination
   */
  async getMessages(
    conversationId: string,
    userId: string,
    options: {
      limit?: number;
      offset?: number;
      beforeMessageId?: string;
    } = {}
  ): Promise<{ messages: MessageData[]; hasMore: boolean }> {
    try {
      const { limit = 50, offset = 0, beforeMessageId } = options;
      
      // Verify user has access to this conversation
      const { data: conversation } = await supabase
        .from('conversations')
        .select('guest_id, host_id')
        .eq('id', conversationId)
        .single();
      
      if (!conversation || (conversation.guest_id !== userId && conversation.host_id !== userId)) {
        throw new Error('Unauthorized access to conversation');
      }
      
      let query = supabase
        .from('messages')
        .select(`
          *,
          users!sender_id (
            id,
            username,
            first_name,
            last_name,
            avatar_url
          )
        `)
        .eq('conversation_id', conversationId)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);
      
      if (beforeMessageId) {
        const { data: beforeMessage } = await supabase
          .from('messages')
          .select('created_at')
          .eq('id', beforeMessageId)
          .single();
        
        if (beforeMessage) {
          query = query.lt('created_at', beforeMessage.created_at);
        }
      }
      
      const { data: messages, error } = await query;
      
      if (error) {
        Logger.error('Error fetching messages:', error);
        throw new Error(`Failed to fetch messages: ${error.message}`);
      }
      
      // Transform messages with sender info
      const transformedMessages = (messages || []).map(msg => ({
        ...msg,
        sender: msg.users ? {
          id: msg.users.id,
          name: `${msg.users.first_name || ''} ${msg.users.last_name || ''}`.trim() || msg.users.username,
          avatar_url: msg.users.avatar_url,
          user_type: msg.sender_type
        } : undefined
      }));
      
      return {
        messages: transformedMessages.reverse(), // Return in chronological order
        hasMore: messages?.length === limit
      };
    } catch (error) {
      Logger.error('MessageService.getMessages error:', error);
      throw error;
    }
  }
  
  /**
   * Send a new message
   */
  async sendMessage(data: SendMessageData): Promise<MessageData> {
    try {
      const startTime = Date.now();
      
      // Verify conversation exists and user has access
      const { data: conversation, error: convError } = await supabase
        .from('conversations')
        .select('*')
        .eq('id', data.conversationId)
        .single();
      
      if (convError || !conversation) {
        throw new Error('Conversation not found');
      }
      
      if (conversation.guest_id !== data.senderId && conversation.host_id !== data.senderId) {
        throw new Error('Unauthorized to send message in this conversation');
      }
      
      // Calculate response time if this is a response to the last message
      let responseTimeMs: number | undefined;
      const { data: lastMessage } = await supabase
        .from('messages')
        .select('created_at, sender_id')
        .eq('conversation_id', data.conversationId)
        .neq('sender_id', data.senderId)
        .order('created_at', { ascending: false })
        .limit(1)
        .single();
      
      if (lastMessage) {
        responseTimeMs = Date.now() - new Date(lastMessage.created_at).getTime();
      }
      
      // Insert message
      const { data: message, error } = await supabase
        .from('messages')
        .insert({
          conversation_id: data.conversationId,
          sender_id: data.senderId,
          sender_type: data.senderType,
          content: data.content,
          message_type: data.messageType || 'text',
          message_status: 'sent',
          response_time_ms: responseTimeMs,
          template_id: data.templateId,
          scheduled_for: data.scheduledFor,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select(`
          *,
          users!sender_id (
            id,
            username,
            first_name,
            last_name,
            avatar_url
          )
        `)
        .single();
      
      if (error) {
        Logger.error('Error sending message:', error);
        throw new Error(`Failed to send message: ${error.message}`);
      }
      
      // Update conversation last_message_at
      await supabase
        .from('conversations')
        .update({ 
          last_message_at: message.created_at,
          updated_at: new Date().toISOString()
        })
        .eq('id', data.conversationId);
      
      // Update response metrics for hosts
      if (data.senderType === 'host' && responseTimeMs) {
        await this.updateHostResponseMetrics(data.senderId, responseTimeMs);
      }
      
      // Invalidate cache
      await cacheBackend.delete(`conversation:${data.conversationId}:messages`);
      await cacheBackend.delete(`user:${conversation.guest_id}:conversations`);
      await cacheBackend.delete(`user:${conversation.host_id}:conversations`);
      
      const transformedMessage = {
        ...message,
        sender: message.users ? {
          id: message.users.id,
          name: `${message.users.first_name || ''} ${message.users.last_name || ''}`.trim() || message.users.username,
          avatar_url: message.users.avatar_url,
          user_type: message.sender_type
        } : undefined
      };
      
      Logger.info(`Message sent: ${message.id} in conversation ${data.conversationId}`);
      return transformedMessage;
    } catch (error) {
      Logger.error('MessageService.sendMessage error:', error);
      throw error;
    }
  }
  
  /**
   * Edit a message (within time limit)
   */
  async editMessage(
    messageId: string,
    userId: string,
    newContent: string,
    timeLimit: number = 15 * 60 * 1000 // 15 minutes
  ): Promise<MessageData | null> {
    try {
      const { data: message, error } = await supabase
        .from('messages')
        .select('*')
        .eq('id', messageId)
        .eq('sender_id', userId)
        .single();
      
      if (error || !message) {
        throw new Error('Message not found or unauthorized');
      }
      
      const timeSinceCreated = Date.now() - new Date(message.created_at).getTime();
      if (timeSinceCreated > timeLimit) {
        throw new Error('Message edit time limit exceeded');
      }
      
      const { data: updatedMessage, error: updateError } = await supabase
        .from('messages')
        .update({
          content: newContent,
          original_content: message.original_content || message.content,
          edited_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', messageId)
        .select(`
          *,
          users!sender_id (
            id,
            username,
            first_name,
            last_name,
            avatar_url
          )
        `)
        .single();
      
      if (updateError) {
        Logger.error('Error editing message:', updateError);
        throw new Error(`Failed to edit message: ${updateError.message}`);
      }
      
      // Invalidate cache
      await cacheBackend.delete(`conversation:${message.conversation_id}:messages`);
      
      Logger.info(`Message edited: ${messageId} by ${userId}`);
      return updatedMessage;
    } catch (error) {
      Logger.error('MessageService.editMessage error:', error);
      throw error;
    }
  }
  
  /**
   * Delete/unsend a message (within time limit)
   */
  async deleteMessage(
    messageId: string,
    userId: string,
    timeLimit: number = 24 * 60 * 60 * 1000 // 24 hours
  ): Promise<boolean> {
    try {
      const { data: message, error } = await supabase
        .from('messages')
        .select('*')
        .eq('id', messageId)
        .eq('sender_id', userId)
        .single();
      
      if (error || !message) {
        throw new Error('Message not found or unauthorized');
      }
      
      const timeSinceCreated = Date.now() - new Date(message.created_at).getTime();
      if (timeSinceCreated > timeLimit) {
        throw new Error('Message delete time limit exceeded');
      }
      
      const { error: deleteError } = await supabase
        .from('messages')
        .delete()
        .eq('id', messageId);
      
      if (deleteError) {
        Logger.error('Error deleting message:', deleteError);
        return false;
      }
      
      // Invalidate cache
      await cacheBackend.delete(`conversation:${message.conversation_id}:messages`);
      
      Logger.info(`Message deleted: ${messageId} by ${userId}`);
      return true;
    } catch (error) {
      Logger.error('MessageService.deleteMessage error:', error);
      return false;
    }
  }
  
  /**
   * Flag a message for review
   */
  async flagMessage(
    messageId: string,
    reporterId: string,
    reporterType: 'guest' | 'host',
    reason: string,
    description?: string
  ): Promise<boolean> {
    try {
      // Flag the message
      await supabase
        .from('messages')
        .update({ 
          is_flagged: true,
          updated_at: new Date().toISOString()
        })
        .eq('id', messageId);
      
      // Create report
      const { error } = await supabase
        .from('message_reports')
        .insert({
          message_id: messageId,
          reporter_id: reporterId,
          reporter_type: reporterType,
          reason,
          description,
          status: 'pending',
          created_at: new Date().toISOString()
        });
      
      if (error) {
        Logger.error('Error creating message report:', error);
        return false;
      }
      
      Logger.info(`Message flagged: ${messageId} by ${reporterId}`);
      return true;
    } catch (error) {
      Logger.error('MessageService.flagMessage error:', error);
      return false;
    }
  }
  
  /**
   * Search messages in conversations for a user
   */
  async searchMessages(
    userId: string,
    userType: 'guest' | 'host',
    query: string,
    options: {
      conversationId?: string;
      limit?: number;
      offset?: number;
    } = {}
  ): Promise<{ messages: MessageData[]; total: number }> {
    try {
      const { limit = 20, offset = 0, conversationId } = options;
      
      let messagesQuery = supabase
        .from('messages')
        .select(`
          *,
          conversations!conversation_id (
            guest_id,
            host_id,
            property_id
          ),
          users!sender_id (
            id,
            username,
            first_name,
            last_name,
            avatar_url
          )
        `, { count: 'exact' })
        .ilike('content', `%${query}%`)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);
      
      if (conversationId) {
        messagesQuery = messagesQuery.eq('conversation_id', conversationId);
      } else {
        // Filter by conversations user has access to
        messagesQuery = messagesQuery.or(
          userType === 'guest' 
            ? `conversations.guest_id.eq.${userId}`
            : `conversations.host_id.eq.${userId}`
        );
      }
      
      const { data: messages, error, count } = await messagesQuery;
      
      if (error) {
        Logger.error('Error searching messages:', error);
        throw new Error(`Failed to search messages: ${error.message}`);
      }
      
      const transformedMessages = (messages || []).map(msg => ({
        ...msg,
        sender: msg.users ? {
          id: msg.users.id,
          name: `${msg.users.first_name || ''} ${msg.users.last_name || ''}`.trim() || msg.users.username,
          avatar_url: msg.users.avatar_url,
          user_type: msg.sender_type
        } : undefined
      }));
      
      return {
        messages: transformedMessages,
        total: count || 0
      };
    } catch (error) {
      Logger.error('MessageService.searchMessages error:', error);
      throw error;
    }
  }
  
  /**
   * Update host response metrics
   */
  private async updateHostResponseMetrics(hostId: string, responseTimeMs: number): Promise<void> {
    try {
      const responseTimeMinutes = Math.round(responseTimeMs / (1000 * 60));
      
      // Get current metrics
      const { data: currentMetrics } = await supabase
        .from('host_response_metrics')
        .select('*')
        .eq('host_id', hostId)
        .single();
      
      if (currentMetrics) {
        // Update existing metrics
        const totalResponses = currentMetrics.total_messages_sent + 1;
        const newAvgResponseTime = Math.round(
          (currentMetrics.avg_response_time_minutes * currentMetrics.total_messages_sent + responseTimeMinutes) / totalResponses
        );
        const respondedWithin24h = responseTimeMs <= 24 * 60 * 60 * 1000 
          ? currentMetrics.responded_within_24h + 1 
          : currentMetrics.responded_within_24h;
        
        await supabase
          .from('host_response_metrics')
          .update({
            avg_response_time_minutes: newAvgResponseTime,
            total_messages_sent: totalResponses,
            responded_within_24h: respondedWithin24h,
            response_rate_24h: Math.round((respondedWithin24h / totalResponses) * 100),
            last_calculated: new Date().toISOString()
          })
          .eq('host_id', hostId);
      } else {
        // Create new metrics
        await supabase
          .from('host_response_metrics')
          .insert({
            host_id: hostId,
            avg_response_time_minutes: responseTimeMinutes,
            total_messages_sent: 1,
            responded_within_24h: responseTimeMs <= 24 * 60 * 60 * 1000 ? 1 : 0,
            response_rate_24h: responseTimeMs <= 24 * 60 * 60 * 1000 ? 100 : 0,
            last_calculated: new Date().toISOString()
          });
      }
    } catch (error) {
      Logger.error('Error updating host response metrics:', error);
    }
  }
}

export const messageService = new MessageService();