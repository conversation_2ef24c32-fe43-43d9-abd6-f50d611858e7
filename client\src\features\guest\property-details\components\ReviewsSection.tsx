import { Star } from 'lucide-react';
import { useTranslations } from '@/lib/translations';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import type { PropertyDetails } from '../types';

interface ReviewsSectionProps {
  property: PropertyDetails;
}

export function ReviewsSection({ property }: ReviewsSectionProps) {
  const t = useTranslations('propertyDetails');

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('nl-NL', { 
      year: 'numeric', 
      month: 'long' 
    });
  };

  return (
    <Card>
      <CardHeader className="px-4 lg:px-6">
        <CardTitle className="flex items-center space-x-2 text-lg lg:text-xl">
          <Star className="h-4 w-4 lg:h-5 lg:w-5 fill-current text-yellow-400" />
          <span className="text-base lg:text-lg">{property.rating} · {property.reviewCount} {t('reviews')}</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="px-4 lg:px-6">
        <div className="grid lg:grid-cols-2 gap-4 lg:gap-6">
          {property.reviews.slice(0, 6).map((review) => (
            <div key={review.id} className="space-y-3">
              {/* Reviewer Info */}
              <div className="flex items-center space-x-3">
                <img
                  src={review.guestAvatar}
                  alt={review.guestName}
                  className="w-10 h-10 rounded-full object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = 'https://picsum.photos/100/100';
                  }}
                />
                <div>
                  <h4 className="font-medium">{review.guestName}</h4>
                  <p className="text-sm text-gray-600">{formatDate(review.date)}</p>
                </div>
              </div>

              {/* Review Rating */}
              <div className="flex items-center space-x-1">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`h-3 w-3 ${
                      i < review.rating 
                        ? 'fill-current text-yellow-400' 
                        : 'text-gray-300'
                    }`}
                  />
                ))}
              </div>

              {/* Review Comment */}
              <p className="text-gray-700 text-sm leading-relaxed">
                {review.comment}
              </p>
            </div>
          ))}
        </div>

        {property.reviews.length > 6 && (
          <div className="mt-6 pt-6 border-t">
            <Button variant="outline">
              {t('showAllReviews', { count: property.reviewCount })}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}