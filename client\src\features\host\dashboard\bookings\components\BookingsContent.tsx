import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useTranslations } from '@/lib/translations';
import { CalendarDays, Users } from 'lucide-react';

interface BookingsContentProps {
  bookings: any[];
}

const BookingsContent: React.FC<BookingsContentProps> = ({ bookings }) => {
  const t = useTranslations('hostDashboard');

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-semibold text-gray-900">{t('navigation.bookings')}</h2>
        <Button variant="outline" className="mt-4">
          {t('recentBookings.newBooking')}
        </Button>
      </div>

      {bookings.length > 0 ? (
        <div className="space-y-4">
          {bookings.map((booking: any) => (
            <Card key={booking.id}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                      <Users className="h-6 w-6 text-gray-500" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-lg">{booking.guest_name || 'Guest'}</h3>
                      <p className="text-gray-600">{booking.property?.title || 'Property'}</p>
                      <p className="text-sm text-gray-500">
                        {formatDate(booking.check_in_date)} - {formatDate(booking.check_out_date)}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <Badge variant="outline" className={
                      booking.status === 'confirmed' ? 'bg-green-50 text-green-700' :
                      booking.status === 'pending' ? 'bg-yellow-50 text-yellow-700' :
                      'bg-gray-50 text-gray-700'
                    }>
                      {booking.status === 'confirmed' ? t('recentBookings.confirmed') : 
                       booking.status === 'pending' ? t('recentBookings.pending') : 
                       booking.status}
                    </Badge>
                    <p className="text-sm text-gray-600 mt-1">
                      {booking.guest_count} {t('recentBookings.guests')} • {formatCurrency(booking.total_amount)}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <CalendarDays className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600">{t('recentBookings.noBookings')}</p>
        </div>
      )}
    </div>
  );
};

export default BookingsContent;