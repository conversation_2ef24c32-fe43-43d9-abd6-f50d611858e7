import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { authService } from '../services/authApi';
import { User, LoginCredentials, RegisterCredentials } from '../types';

// Query key constants
const AUTH_KEYS = {
  user: ['auth', 'user'] as const,
};

export function useUser() {
  return useQuery({
    queryKey: AUTH_KEYS.user,
    queryFn: authService.getCurrentUser,
    staleTime: 1 * 60 * 1000, // 1 minute (shorter for OAuth scenarios)
    retry: false, // Don't retry if user is not authenticated
    refetchOnWindowFocus: true, // Refetch when window gets focus
    refetchOnMount: true, // Always refetch on mount
    throwOnError: false, // Don't throw errors, just return null
  });
}

export function useLogin() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (credentials: LoginCredentials) => authService.login(credentials),
    onSuccess: (user: User) => {
      // Update the user cache
      queryClient.setQueryData(AUTH_KEYS.user, user);
    },
  });
}

export function useRegister() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (credentials: RegisterCredentials) => authService.register(credentials),
    onSuccess: (response: any) => {
      // If the response has a user property, update the cache
      if (response.user) {
        queryClient.setQueryData(AUTH_KEYS.user, response.user);
        queryClient.invalidateQueries({ queryKey: AUTH_KEYS.user });
      }
    },
  });
}

export function useLogout() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: authService.logout,
    onSuccess: () => {
      // Clear the user cache
      queryClient.setQueryData(AUTH_KEYS.user, null);
      // Optionally clear all cached data
      queryClient.clear();
    },
  });
}