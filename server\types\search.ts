import { z } from 'zod';

// Property schema
export const propertySchema = z.object({
  id: z.string(),
  title: z.string(),
  location: z.object({
    city: z.string(),
    region: z.string(),
    country: z.string(),
  }),
  price: z.object({
    amount: z.number(),
    currency: z.string(),
    period: z.string(),
  }),
  rating: z.number().nullable(),
  reviewCount: z.number(),
  images: z.array(z.string()).nullable(),
  amenities: z.array(z.string()),
  bedrooms: z.number(),
  bathrooms: z.number(),
  maxGuests: z.number(),
  propertyType: z.string(),
  badges: z.array(z.string()).optional(),
  features: z.array(z.string()).optional(),
  isInstantBook: z.boolean().optional(),
});

// Search response schema
export const searchResponseSchema = z.object({
  properties: z.array(propertySchema),
  total: z.number(),
  filters: z.object({
    cities: z.array(z.object({
      id: z.string(),
      name: z.string(),
      count: z.number(),
    })).optional(),
    regions: z.array(z.object({
      id: z.string(),
      name: z.string(),
      count: z.number(),
    })).optional(),
    propertyTypes: z.array(z.object({
      id: z.string(),
      name: z.string(),
      count: z.number(),
    })).optional(),
  }).optional(),
});

// Type exports
export type Property = z.infer<typeof propertySchema>;
export type SearchResponse = z.infer<typeof searchResponseSchema>;