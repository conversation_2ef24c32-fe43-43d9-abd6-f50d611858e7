/**
 * Enhanced translation caching system with build version support
 * Provides automatic cache invalidation when new app versions are deployed
 */

interface BuildMeta {
  version: string;
  buildTime: string;
  buildHash: string;
  env: string;
}

interface CacheEntry {
  data: Record<string, any>;
  version: string;
  timestamp: number;
  buildHash: string;
}

interface TranslationCacheManager {
  get(locale: string): Promise<Record<string, any> | null>;
  set(locale: string, data: Record<string, any>): Promise<void>;
  clear(): Promise<void>;
  clearLocale(locale: string): Promise<void>;
  isValidCache(locale: string): Promise<boolean>;
  getCurrentVersion(): Promise<BuildMeta | null>;
}

class EnhancedTranslationCache implements TranslationCacheManager {
  private static instance: EnhancedTranslationCache;
  private memoryCache: Map<string, CacheEntry> = new Map();
  private currentBuildMeta: BuildMeta | null = null;
  private readonly CACHE_PREFIX = 'villawise_translations_';
  private readonly META_CACHE_KEY = 'villawise_build_meta';
  private readonly CACHE_EXPIRY_MS = 24 * 60 * 60 * 1000; // 24 hours
  private readonly MAX_CACHE_SIZE = 10; // Maximum number of locale caches

  private constructor() {}

  static getInstance(): EnhancedTranslationCache {
    if (!EnhancedTranslationCache.instance) {
      EnhancedTranslationCache.instance = new EnhancedTranslationCache();
    }
    return EnhancedTranslationCache.instance;
  }

  /**
   * Get current build metadata from server
   */
  async getCurrentVersion(): Promise<BuildMeta | null> {
    try {
      // Check memory cache first
      if (this.currentBuildMeta) {
        return this.currentBuildMeta;
      }

      // Check localStorage cache
      const cachedMeta = localStorage.getItem(this.META_CACHE_KEY);
      if (cachedMeta) {
        const parsed = JSON.parse(cachedMeta);
        // Cache meta for 5 minutes
        if (Date.now() - parsed.timestamp < 5 * 60 * 1000) {
          this.currentBuildMeta = parsed.data;
          return this.currentBuildMeta;
        }
      }

      // Fetch from server
      const response = await fetch('/meta.json?' + Date.now(), {
        cache: 'no-cache',
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate'
        }
      });

      if (!response.ok) {
        console.warn('Failed to fetch build metadata, using fallback');
        return null;
      }

      const buildMeta: BuildMeta = await response.json();
      
      // Cache in memory and localStorage
      this.currentBuildMeta = buildMeta;
      localStorage.setItem(this.META_CACHE_KEY, JSON.stringify({
        data: buildMeta,
        timestamp: Date.now()
      }));

      return buildMeta;
    } catch (error) {
      console.error('Error fetching build metadata:', error);
      return null;
    }
  }

  /**
   * Check if cached translation is still valid
   */
  async isValidCache(locale: string): Promise<boolean> {
    const currentMeta = await this.getCurrentVersion();
    if (!currentMeta) return false;

    // Check memory cache
    const memoryEntry = this.memoryCache.get(locale);
    if (memoryEntry) {
      return (
        memoryEntry.buildHash === currentMeta.buildHash &&
        memoryEntry.version === currentMeta.version &&
        Date.now() - memoryEntry.timestamp < this.CACHE_EXPIRY_MS
      );
    }

    // Check localStorage cache
    const cacheKey = this.CACHE_PREFIX + locale;
    const cachedItem = localStorage.getItem(cacheKey);
    if (!cachedItem) return false;

    try {
      const cacheEntry: CacheEntry = JSON.parse(cachedItem);
      return (
        cacheEntry.buildHash === currentMeta.buildHash &&
        cacheEntry.version === currentMeta.version &&
        Date.now() - cacheEntry.timestamp < this.CACHE_EXPIRY_MS
      );
    } catch (error) {
      console.error('Error parsing cached translation:', error);
      return false;
    }
  }

  /**
   * Get translations from cache
   */
  async get(locale: string): Promise<Record<string, any> | null> {
    const isValid = await this.isValidCache(locale);
    if (!isValid) return null;

    // Try memory cache first
    const memoryEntry = this.memoryCache.get(locale);
    if (memoryEntry) {
      return memoryEntry.data;
    }

    // Try localStorage cache
    const cacheKey = this.CACHE_PREFIX + locale;
    const cachedItem = localStorage.getItem(cacheKey);
    if (cachedItem) {
      try {
        const cacheEntry: CacheEntry = JSON.parse(cachedItem);
        // Also store in memory for faster access
        this.memoryCache.set(locale, cacheEntry);
        return cacheEntry.data;
      } catch (error) {
        console.error('Error parsing cached translation:', error);
        localStorage.removeItem(cacheKey);
      }
    }

    return null;
  }

  /**
   * Set translations in cache
   */
  async set(locale: string, data: Record<string, any>): Promise<void> {
    const currentMeta = await this.getCurrentVersion();
    if (!currentMeta) return;

    const cacheEntry: CacheEntry = {
      data,
      version: currentMeta.version,
      timestamp: Date.now(),
      buildHash: currentMeta.buildHash
    };

    // Store in memory cache
    this.memoryCache.set(locale, cacheEntry);

    // Store in localStorage with size management
    const cacheKey = this.CACHE_PREFIX + locale;
    try {
      localStorage.setItem(cacheKey, JSON.stringify(cacheEntry));
      
      // Clean up old cache entries if we exceed max size
      this.cleanupOldCaches();
    } catch (error) {
      console.error('Error storing translation in localStorage:', error);
      // If localStorage is full, try to clean up and retry
      this.cleanupOldCaches();
      try {
        localStorage.setItem(cacheKey, JSON.stringify(cacheEntry));
      } catch (retryError) {
        console.error('Failed to store translation even after cleanup:', retryError);
      }
    }
  }

  /**
   * Clear all translation caches
   */
  async clear(): Promise<void> {
    // Clear memory cache
    this.memoryCache.clear();

    // Clear localStorage cache
    const keysToRemove: string[] = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith(this.CACHE_PREFIX)) {
        keysToRemove.push(key);
      }
    }

    keysToRemove.forEach(key => localStorage.removeItem(key));
    console.log('Translation cache cleared');
  }

  /**
   * Clear cache for specific locale
   */
  async clearLocale(locale: string): Promise<void> {
    this.memoryCache.delete(locale);
    localStorage.removeItem(this.CACHE_PREFIX + locale);
  }

  /**
   * Clean up old cache entries to prevent localStorage from filling up
   */
  private cleanupOldCaches(): void {
    const cacheKeys: Array<{ key: string; timestamp: number }> = [];
    
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith(this.CACHE_PREFIX)) {
        try {
          const cached = localStorage.getItem(key);
          if (cached) {
            const entry: CacheEntry = JSON.parse(cached);
            cacheKeys.push({ key, timestamp: entry.timestamp });
          }
        } catch (error) {
          // Remove invalid cache entries
          localStorage.removeItem(key);
        }
      }
    }

    // Sort by timestamp (oldest first) and remove excess entries
    cacheKeys.sort((a, b) => a.timestamp - b.timestamp);
    
    const keysToRemove = cacheKeys.slice(0, Math.max(0, cacheKeys.length - this.MAX_CACHE_SIZE));
    keysToRemove.forEach(({ key }) => localStorage.removeItem(key));
  }
}

// Export singleton instance
export const translationCache = EnhancedTranslationCache.getInstance();

// Export types for use in other modules
export type { BuildMeta, CacheEntry, TranslationCacheManager };