# Fuzzy Search Implementation Guide

## Overview

VillaWise implements advanced fuzzy search capabilities for location names using PostgreSQL's `pg_trgm` extension and custom similarity algorithms. This enables users to find locations even with typos, partial matches, or alternative spellings.

## Implementation Details

### Core Technology Stack

- **PostgreSQL pg_trgm Extension**: Trigram similarity matching
- **unaccent Extension**: Diacritic normalization (ç, ñ, etc.)
- **Custom SQL Functions**: Optimized fuzzy search queries
- **Multi-strategy Approach**: Combines exact, fuzzy, and partial matching

### Search Strategies

#### 1. Trigram Similarity Search
Uses PostgreSQL's `similarity()` function to calculate trigram-based similarity scores:

```sql
SELECT similarity('Valencia D''Aneu', 'valenc ane');
-- Returns: 0.5625 (56.25% similarity)
```

#### 2. Exact Match Prioritization
Highest priority for exact matches (case-insensitive):

```sql
WHEN LOWER(gl.name) = LOWER($1) THEN 'exact'
```

#### 3. Partial Pattern Matching
Fallback for substring matches:

```sql
LOWER(gl.name) LIKE '%' || LOWER($1) || '%'
```

### Key Features

#### Multi-Level Scoring System
- **Exact Score**: 1.0 for perfect matches
- **Fuzzy Score**: 0.0-1.0 based on trigram similarity
- **Partial Score**: 0.8 for substring matches
- **Match Quality**: Classification (exact, high_similarity, medium_similarity, low_similarity)

#### Performance Optimizations
- **GIN Indexes**: Fast trigram searches on large datasets
- **Population Sorting**: Results ranked by location importance
- **Result Limiting**: Configurable limits with intelligent ranking

## Real-World Examples

### Test Cases That Work

1. **"valenc ane" → "Valencia D'Aneu"**
   - Similarity Score: 0.5625
   - Match Quality: medium_similarity
   - Use Case: Handling spaces in compound names

2. **"barselona" → "Barcelona"**
   - Handles common misspellings
   - High fuzzy match score

3. **"san fran" → "San Francisco"**
   - Partial prefix matching
   - Common abbreviation support

### Search Parameters

```typescript
interface FuzzySearchParams {
  query: string;           // Search term
  limit: number;           // Max results (default: 10)
  minSimilarity: number;   // Threshold (default: 0.25)
  country?: string;        // Optional country filter
}
```

## Technical Implementation

### SimpleFuzzySearchService

Primary service for production use:

```typescript
const results = await simpleFuzzySearchService.fuzzyLocationSearch(
  'valenc ane',  // query
  5,             // limit
  0.25           // min similarity
);
```

### Database Function

Optimized SQL function for fuzzy search:

```sql
CREATE FUNCTION execute_fuzzy_search(
  search_query TEXT,
  min_similarity FLOAT DEFAULT 0.25,
  result_limit INT DEFAULT 10
) RETURNS TABLE(...);
```

### API Integration

Fuzzy search is enabled via query parameter:

```
GET /api/public/locations/search?q=valenc%20ane&fuzzy=true&limit=5
```

Response includes similarity metadata:

```json
{
  "data": [
    {
      "name": "Valencia D'Aneu",
      "relevance": 56,
      "similarity_score": 0.5625,
      "coordinates": { "lat": 42.6342, "lng": 1.1094 }
    }
  ]
}
```

## Performance Characteristics

### Database Performance
- **Index Type**: GIN (Generalized Inverted Index)
- **Query Time**: Sub-100ms for similarity searches
- **Scalability**: Handles 35,000+ locations efficiently

### Similarity Thresholds
- **High Quality**: 0.6+ similarity score
- **Medium Quality**: 0.4-0.6 similarity score  
- **Low Quality**: 0.25-0.4 similarity score
- **Minimum Threshold**: 0.25 (configurable)

## Configuration

### Environment Variables
```
# Enable fuzzy search features
ENABLE_FUZZY_SEARCH=true

# Default similarity threshold
DEFAULT_FUZZY_THRESHOLD=0.25
```

### Database Extensions Required
```sql
CREATE EXTENSION IF NOT EXISTS pg_trgm;
CREATE EXTENSION IF NOT EXISTS unaccent;
```

## Best Practices

### Query Optimization
1. **Use appropriate similarity thresholds** (0.25-0.3 for permissive, 0.4+ for strict)
2. **Limit result sets** to prevent performance issues
3. **Combine with geographic filtering** when possible

### Error Handling
- Graceful fallback to basic ILIKE matching
- Logging of fuzzy search performance metrics
- Cache fuzzy search results for repeated queries

### User Experience
- Display similarity scores as relevance percentages
- Highlight matched terms in results
- Sort by relevance, then by population/importance

## Future Enhancements

### Planned Improvements
1. **Multi-language support** for non-Latin scripts
2. **Phonetic matching** using Soundex/Metaphone
3. **Machine learning relevance scoring**
4. **Real-time search suggestions**

### Advanced Features
- **Geospatial proximity** in similarity scoring
- **Contextual search** based on user location
- **Search analytics** and improvement feedback loops

## Troubleshooting

### Common Issues
1. **Low similarity scores**: Adjust threshold parameters
2. **Missing results**: Check pg_trgm extension installation
3. **Performance issues**: Verify GIN indexes are created

### Debug Queries
```sql
-- Test trigram similarity
SELECT similarity('input', 'target_location');

-- Check extension status
SELECT * FROM pg_extension WHERE extname IN ('pg_trgm', 'unaccent');

-- Verify indexes
\d geonames_locations
```

This fuzzy search implementation provides robust, scalable location matching that handles real-world user input patterns while maintaining excellent performance.