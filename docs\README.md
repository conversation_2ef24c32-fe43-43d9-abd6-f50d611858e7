# VillaWise Documentation

This directory contains all project documentation consolidated into organized files.

## Main Documentation Files

- [**CONSOLIDATED_DEPLOYMENT_GUIDE.md**](./CONSOLIDATED_DEPLOYMENT_GUIDE.md) - Complete deployment guide covering local, Docker, and Railway deployment
- [**CONSOLIDATED_DEVELOPMENT_GUIDE.md**](./CONSOLIDATED_DEVELOPMENT_GUIDE.md) - Development practices, TypeScript, translations, and workflow
- [**API_REFERENCE.md**](./API_REFERENCE.md) - Complete API documentation
- [**TRANSLATION_SYSTEM_COMPREHENSIVE_GUIDE.md**](./TRANSLATION_SYSTEM_COMPREHENSIVE_GUIDE.md) - Translation system architecture

## Specialized Guides

- [**GEONAMES_IMPLEMENTATION_GUIDE.md**](./GEONAMES_IMPLEMENTATION_GUIDE.md) - GeoNames location search system
- [**FUZ<PERSON>Y_SEARCH_IMPLEMENTATION.md**](./FUZZY_SEARCH_IMPLEMENTATION.md) - PostgreSQL fuzzy search implementation
- [**CACHE_COVERAGE_ANALYSIS.md**](./CACHE_COVERAGE_ANALYSIS.md) - Redis caching architecture
- [**COMPONENT_NAMING_CONVENTIONS.md**](./COMPONENT_NAMING_CONVENTIONS.md) - React component organization
- [**TESTING_STRATEGY.md**](./TESTING_STRATEGY.md) - Testing approaches and strategies

## Quick Reference

- [**GETTING_STARTED.md**](./GETTING_STARTED.md) - Quick start guide for new developers
- [**RAILWAY_REDIS_SETUP.md**](./RAILWAY_REDIS_SETUP.md) - Railway Redis configuration
- [**UPSTASH_REDIS_RAILWAY_SETUP.md**](./UPSTASH_REDIS_RAILWAY_SETUP.md) - Upstash Redis setup

## Architecture Documentation

- [**architecture/DAL_ARCHITECTURE.md**](./architecture/DAL_ARCHITECTURE.md) - Data Access Layer documentation
- [**architecture/FRONTEND_BACKEND_COMMUNICATION.md**](./architecture/FRONTEND_BACKEND_COMMUNICATION.md) - Communication patterns

## Documentation Policy

- **All documentation must be placed in this  folder**
- **No documentation files should be created in the root directory**
- **Always ask permission before creating new documentation**
- **Keep documentation updated with architectural changes**


