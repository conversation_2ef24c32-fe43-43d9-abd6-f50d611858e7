#!/usr/bin/env node

/**
 * Security Audit Script for VillaWise
 * 
 * This script performs comprehensive security audits and fails CI/CD pipelines
 * when vulnerabilities are detected.
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

// Configuration
const AUDIT_CONFIG = {
  failOnModerate: true,
  failOnHigh: true,
  failOnCritical: true,
  generateReport: true,
  reportPath: './security-audit-report.json'
};

// ANSI colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function logHeader(message) {
  log(`\n${colors.bold}${colors.cyan}=== ${message} ===${colors.reset}`);
}

function logSuccess(message) {
  log(`${colors.green}✅ ${message}${colors.reset}`);
}

function logError(message) {
  log(`${colors.red}❌ ${message}${colors.reset}`);
}

function logWarning(message) {
  log(`${colors.yellow}⚠️  ${message}${colors.reset}`);
}

function logInfo(message) {
  log(`${colors.blue}ℹ️  ${message}${colors.reset}`);
}

function runCommand(command, options = {}) {
  try {
    const result = execSync(command, { 
      encoding: 'utf8',
      stdio: options.silent ? 'pipe' : 'inherit',
      ...options 
    });
    return { success: true, output: result };
  } catch (error) {
    return { 
      success: false, 
      error: error.message, 
      output: error.stdout,
      exitCode: error.status 
    };
  }
}

function parseAuditResults(output) {
  try {
    const auditData = JSON.parse(output);
    return {
      vulnerabilities: auditData.vulnerabilities || {},
      metadata: auditData.metadata || {},
      auditReportVersion: auditData.auditReportVersion || 2
    };
  } catch (error) {
    logError(`Failed to parse audit results: ${error.message}`);
    return null;
  }
}

function categorizeVulnerabilities(vulnerabilities) {
  const categories = {
    critical: [],
    high: [],
    moderate: [],
    low: [],
    info: []
  };

  Object.entries(vulnerabilities).forEach(([id, vuln]) => {
    const severity = vuln.severity || 'info';
    if (categories[severity]) {
      categories[severity].push({ id, ...vuln });
    }
  });

  return categories;
}

function generateSecurityReport(auditResults, categorizedVulns) {
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      total: Object.keys(auditResults.vulnerabilities).length,
      critical: categorizedVulns.critical.length,
      high: categorizedVulns.high.length,
      moderate: categorizedVulns.moderate.length,
      low: categorizedVulns.low.length,
      info: categorizedVulns.info.length
    },
    vulnerabilities: categorizedVulns,
    metadata: auditResults.metadata
  };

  if (AUDIT_CONFIG.generateReport) {
    fs.writeFileSync(AUDIT_CONFIG.reportPath, JSON.stringify(report, null, 2));
    logInfo(`Security report generated: ${AUDIT_CONFIG.reportPath}`);
  }

  return report;
}

function displaySummary(report) {
  logHeader('Security Audit Summary');
  
  log(`${colors.bold}Total vulnerabilities found: ${report.summary.total}${colors.reset}`);
  
  if (report.summary.critical > 0) {
    logError(`Critical: ${report.summary.critical}`);
  }
  
  if (report.summary.high > 0) {
    logError(`High: ${report.summary.high}`);
  }
  
  if (report.summary.moderate > 0) {
    logWarning(`Moderate: ${report.summary.moderate}`);
  }
  
  if (report.summary.low > 0) {
    logInfo(`Low: ${report.summary.low}`);
  }
  
  if (report.summary.info > 0) {
    logInfo(`Info: ${report.summary.info}`);
  }
}

function shouldFailPipeline(report) {
  const { critical, high, moderate } = report.summary;
  
  if (AUDIT_CONFIG.failOnCritical && critical > 0) {
    logError(`Pipeline failing due to ${critical} critical vulnerabilities`);
    return true;
  }
  
  if (AUDIT_CONFIG.failOnHigh && high > 0) {
    logError(`Pipeline failing due to ${high} high-severity vulnerabilities`);
    return true;
  }
  
  if (AUDIT_CONFIG.failOnModerate && moderate > 0) {
    logError(`Pipeline failing due to ${moderate} moderate-severity vulnerabilities`);
    return true;
  }
  
  return false;
}

function main() {
  logHeader('VillaWise Security Audit');
  
  // Check if package.json exists
  if (!fs.existsSync('./package.json')) {
    logError('package.json not found. Please run this script from the project root.');
    process.exit(1);
  }
  
  // Run npm audit with JSON output
  logInfo('Running npm audit...');
  const auditResult = runCommand('npm audit --json', { silent: true });
  
  if (!auditResult.success) {
    // npm audit returns non-zero exit code when vulnerabilities are found
    if (auditResult.exitCode === 1 && auditResult.output) {
      // Parse the audit results
      const auditData = parseAuditResults(auditResult.output);
      
      if (!auditData) {
        logError('Failed to parse audit results');
        process.exit(1);
      }
      
      const categorizedVulns = categorizeVulnerabilities(auditData.vulnerabilities);
      const report = generateSecurityReport(auditData, categorizedVulns);
      
      displaySummary(report);
      
      if (shouldFailPipeline(report)) {
        logError('Security audit failed - vulnerabilities must be fixed before deployment');
        process.exit(1);
      } else {
        logSuccess('Security audit passed - no blocking vulnerabilities found');
      }
    } else {
      logError(`npm audit failed with exit code ${auditResult.exitCode}`);
      if (auditResult.output) {
        log(auditResult.output);
      }
      process.exit(1);
    }
  } else {
    logSuccess('Security audit passed - no vulnerabilities found');
  }
  
  logInfo('Security audit completed successfully');
}

// Run the audit
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { main, AUDIT_CONFIG };