import React from 'react';
import { useTranslations } from '@/lib/translations';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  Wifi, 
  Car, 
  Waves, 
  Tv, 
  Utensils, 
  Wind, 
  Shirt, 
  Coffee, 
  Dumbbell, 
  Baby, 
  Flame, 
  Snowflake,
  TreePine,
  Mountain,
  Camera,
  Shield,
  Users,
  Bath,
  Bed,
  Home,
  UtensilsCrossed,
  Sofa,
  Flower2,
  Dog,
  Cigarette,
  Volume2,
  Gamepad2,
  Laptop,
  Music,
  Accessibility,
  Heart
} from 'lucide-react';
import { PropertyWizardData } from '../../types/property';

interface AmenitiesStepProps {
  data: Partial<PropertyWizardData>;
  onUpdate: (data: Partial<PropertyWizardData>) => void;
}

const AMENITY_CATEGORIES = [
  {
    id: 'essentials',
    amenities: [
      { id: 'wifi', icon: Wifi, popular: true },
      { id: 'kitchen', icon: Utensils, popular: true },
      { id: 'parking', icon: Car, popular: true },
      { id: 'tv', icon: Tv, popular: false },
      { id: 'air_conditioning', icon: Wind, popular: true },
      { id: 'heating', icon: Flame, popular: false },
      { id: 'washer', icon: Shirt, popular: false },
      { id: 'dryer', icon: Shirt, popular: false }
    ]
  },
  {
    id: 'features',
    amenities: [
      { id: 'pool', icon: Waves, popular: true },
      { id: 'hot_tub', icon: Bath, popular: false },
      { id: 'gym', icon: Dumbbell, popular: false },
      { id: 'bbq_grill', icon: Flame, popular: false },
      { id: 'garden', icon: Flower2, popular: false },
      { id: 'balcony', icon: Home, popular: false },
      { id: 'terrace', icon: Home, popular: false },
      { id: 'fireplace', icon: Flame, popular: false }
    ]
  },
  {
    id: 'location',
    amenities: [
      { id: 'beach_access', icon: Waves, popular: true },
      { id: 'mountain_view', icon: Mountain, popular: false },
      { id: 'sea_view', icon: Waves, popular: true },
      { id: 'lake_access', icon: Waves, popular: false },
      { id: 'ski_in_out', icon: TreePine, popular: false },
      { id: 'city_view', icon: Home, popular: false }
    ]
  },
  {
    id: 'entertainment',
    amenities: [
      { id: 'game_console', icon: Gamepad2, popular: false },
      { id: 'ping_pong', icon: Gamepad2, popular: false },
      { id: 'pool_table', icon: Gamepad2, popular: false },
      { id: 'sound_system', icon: Music, popular: false },
      { id: 'piano', icon: Music, popular: false },
      { id: 'exercise_equipment', icon: Dumbbell, popular: false }
    ]
  },
  {
    id: 'family',
    amenities: [
      { id: 'suitable_for_children', icon: Baby, popular: false },
      { id: 'suitable_for_infants', icon: Baby, popular: false },
      { id: 'high_chair', icon: Baby, popular: false },
      { id: 'crib', icon: Baby, popular: false },
      { id: 'children_toys', icon: Baby, popular: false },
      { id: 'children_books', icon: Baby, popular: false }
    ]
  },
  {
    id: 'accessibility',
    amenities: [
      { id: 'step_free_access', icon: Accessibility, popular: false },
      { id: 'wide_doorway', icon: Accessibility, popular: false },
      { id: 'accessible_bathroom', icon: Accessibility, popular: false },
      { id: 'wide_hallway', icon: Accessibility, popular: false },
      { id: 'wide_entryway', icon: Accessibility, popular: false }
    ]
  },
  {
    id: 'safety',
    amenities: [
      { id: 'smoke_detector', icon: Shield, popular: false },
      { id: 'fire_extinguisher', icon: Shield, popular: false },
      { id: 'first_aid_kit', icon: Heart, popular: false },
      { id: 'security_cameras', icon: Camera, popular: false },
      { id: 'carbon_monoxide_detector', icon: Shield, popular: false }
    ]
  }
];

export const AmenitiesStep = ({ data, onUpdate }: AmenitiesStepProps) => {
  const t = useTranslations('hostOnboarding.amenities');
  const selectedAmenities = data.amenities || [];

  const handleAmenityToggle = (amenityId: string) => {
    const isSelected = selectedAmenities.includes(amenityId);
    const newAmenities = isSelected
      ? selectedAmenities.filter(id => id !== amenityId)
      : [...selectedAmenities, amenityId];
    
    onUpdate({ amenities: newAmenities });
  };

  const getSelectedCount = () => selectedAmenities.length;

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          {t('title')}
        </h2>
        <p className="text-gray-600 dark:text-gray-400 mb-4">
          {t('description')}
        </p>
        <div className="text-sm text-gray-500 dark:text-gray-400">
          {getSelectedCount()} {t('selectedCount')}
        </div>
      </div>

      {/* Amenities Grid */}
      <div className="space-y-8">
        {AMENITY_CATEGORIES.map((category) => (
          <div key={category.id}>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              {t(`categories.${category.id}`)}
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {category.amenities.map((amenity) => {
                const isSelected = selectedAmenities.includes(amenity.id);
                return (
                  <Card
                    key={amenity.id}
                    className={`cursor-pointer transition-all hover:shadow-md ${
                      isSelected
                        ? 'ring-2 ring-primary border-primary bg-primary/5'
                        : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                    }`}
                    onClick={() => handleAmenityToggle(amenity.id)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <amenity.icon className="h-5 w-5 text-primary" />
                          <div className="flex-1">
                            <div className="flex items-center space-x-2">
                              <h4 className="font-medium text-gray-900 dark:text-white text-sm">
                                {t(`items.${amenity.id}`)}
                              </h4>
                              {amenity.popular && (
                                <Badge variant="secondary" className="text-xs">
                                  {t('popular')}
                                </Badge>
                              )}
                            </div>
                          </div>
                        </div>
                        <Checkbox 
                          checked={isSelected} 
                          onChange={() => handleAmenityToggle(amenity.id)}
                          className="flex-shrink-0"
                        />
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>
        ))}
      </div>

      {/* Tips */}
      <div className="bg-yellow-50 dark:bg-yellow-900/20 p-6 rounded-lg">
        <h4 className="font-medium text-yellow-800 dark:text-yellow-200 mb-2">
          {t('tipsTitle')}
        </h4>
        <ul className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
          <li>• {t('tip1')}</li>
          <li>• {t('tip2')}</li>
          <li>• {t('tip3')}</li>
          <li>• {t('tip4')}</li>
        </ul>
      </div>
    </div>
  );
};