// GeoNames synchronization service
import { SupabaseClient } from '@supabase/supabase-js';
import { GeoNamesDownloader } from './downloader';
import { GeoNamesParser } from './parser';
import { TourismRegionProcessor } from './tourism-processor';
import { GeoNamesConfigManager } from '../../config/geonames-scope';
import { SyncResult, ProcessedGeoNamesData, GeoNamesPlace, GeoNamesAlternateName, SyncHealthStatus } from './types';
import { supabaseAdmin } from '../../supabase';

export class GeoNamesSyncService {
  private supabase: SupabaseClient;
  private downloader: GeoNamesDownloader;
  private parser: GeoNamesParser;
  private tourismProcessor: TourismRegionProcessor;
  private configManager: GeoNamesConfigManager;
  private isEnabled: boolean;

  constructor() {
    this.supabase = supabaseAdmin;
    this.downloader = new GeoNamesDownloader();
    this.parser = new GeoNamesParser();
    this.configManager = new GeoNamesConfigManager();
    
    // Check if Supabase credentials are available
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    if (!supabaseUrl || !supabaseKey) {
      console.warn('[SYNC] Supabase credentials not available - GeoNames sync disabled');
      this.isEnabled = false;
      this.tourismProcessor = new TourismRegionProcessor('', ''); // Empty credentials will disable tourism processor
    } else {
      this.isEnabled = true;
      this.tourismProcessor = new TourismRegionProcessor(supabaseUrl, supabaseKey);
    }
  }

  async performFullSync(): Promise<SyncResult> {
    if (!this.isEnabled) {
      console.log('[SYNC] Skipping GeoNames sync - Supabase not available');
      return {
        countries: 0,
        languages: 0,
        locationsProcessed: 0,
        alternateNamesProcessed: 0,
        processingTime: 0,
        errors: ['Sync disabled - Supabase credentials not available']
      };
    }

    const startTime = Date.now();
    console.log('[SYNC] Starting full GeoNames synchronization');

    try {
      // Step 1: Download all configured data
      console.log('[SYNC] Step 1: Downloading data files...');
      const { countryFiles, alternateNamesFile } = await this.downloader.downloadAllConfiguredData();

      // Step 2: Parse and filter data
      console.log('[SYNC] Step 2: Parsing data files...');
      const processedData = await this.parser.processAllData(countryFiles, alternateNamesFile);
      
      // Log processing stats
      const stats = this.parser.getProcessingStats(processedData);
      console.log('[SYNC] Processing stats:', JSON.stringify(stats, null, 2));

      // Step 3: Clear existing data
      console.log('[SYNC] Step 3: Clearing existing data...');
      await this.clearExistingData();

      // Step 4: Insert new data
      console.log('[SYNC] Step 4: Inserting new data...');
      await this.insertProcessedData(processedData);

      // Step 5: Process tourism regions
      console.log('[SYNC] Step 5: Processing tourism regions...');
      await this.tourismProcessor.processTourismRegions();

      // Step 6: Update search vectors and indexes
      console.log('[SYNC] Step 6: Building search indexes...');
      await this.buildSearchIndexes();

      // Step 7: Cleanup temporary files
      console.log('[SYNC] Step 7: Cleaning up...');
      await this.downloader.cleanup();

      const processingTime = Date.now() - startTime;
      
      const result: SyncResult = {
        countries: this.configManager.getAllCountryCodes().length,
        languages: this.configManager.getAllLanguages().length,
        locationsProcessed: processedData.locations.length,
        alternateNamesProcessed: processedData.alternateNames.length,
        processingTime
      };

      console.log(`[SYNC] ✅ Full sync completed in ${Math.round(processingTime / 1000)}s`);
      console.log('[SYNC] Results:', JSON.stringify(result, null, 2));

      return result;

    } catch (error) {
      console.error('[SYNC] ❌ Full sync failed:', error);
      throw error;
    }
  }

  private async clearExistingData(): Promise<void> {
    console.log('[SYNC] Clearing geonames_location_names table...');
    const { error: namesError } = await this.supabase
      .from('geonames_location_names')
      .delete()
      .neq('id', '00000000-0000-0000-0000-000000000000'); // Delete all

    if (namesError) {
      throw new Error(`Failed to clear geonames_location_names: ${namesError.message}`);
    }

    console.log('[SYNC] Clearing geonames_locations table...');
    const { error: locationsError } = await this.supabase
      .from('geonames_locations')
      .delete()
      .neq('id', '00000000-0000-0000-0000-000000000000'); // Delete all

    if (locationsError) {
      throw new Error(`Failed to clear geonames_locations: ${locationsError.message}`);
    }

    console.log('[SYNC] ✅ Existing data cleared');
  }

  private async insertProcessedData(data: ProcessedGeoNamesData): Promise<void> {
    const batchSize = parseInt(process.env.SYNC_BATCH_SIZE || '1000');
    let totalLocationInserted = 0;
    let totalLocationSkipped = 0;
    let totalNameInserted = 0;
    let totalNameSkipped = 0;
    
    // Insert locations in batches
    console.log(`[SYNC] Inserting ${data.locations.length} locations in batches of ${batchSize}...`);
    
    for (let i = 0; i < data.locations.length; i += batchSize) {
      const batch = data.locations.slice(i, i + batchSize);
      const locationRows = batch.map((place) => {
        return this.convertPlaceToRow.call(this, place);
      });
      
      const { error } = await this.supabase
        .from('geonames_locations')
        .upsert(locationRows, {
          onConflict: 'geonames_id',
          ignoreDuplicates: false
        });

      if (error) {
        console.error(`[SYNC] Failed to upsert locations batch ${i + 1}-${Math.min(i + batchSize, data.locations.length)}:`, {
          code: error.code,
          message: error.message,
          details: error.details
        });
        
        // For constraint violations (duplicate keys, foreign key, etc.)
        if (error.code === '23505' || error.code === '23503' || error.code === '23514') {
          console.log(`[SYNC] Constraint violation detected (${error.code}), trying individual inserts for this batch...`);
          const { inserted, skipped } = await this.insertLocationsIndividually(locationRows);
          console.log(`[SYNC] Individual insert results: ${inserted} inserted, ${skipped} skipped`);
          totalLocationInserted += inserted;
          totalLocationSkipped += skipped;
        } else {
          console.log(`[SYNC] Non-constraint error (${error.code}), continuing with next batch...`);
          totalLocationSkipped += locationRows.length;
        }
        continue;
      }

      console.log(`[SYNC] Inserted locations batch ${i + 1}-${Math.min(i + batchSize, data.locations.length)}`);
      totalLocationInserted += locationRows.length;
    }

    // Insert alternate names in batches
    console.log(`[SYNC] Inserting ${data.alternateNames.length} alternate names in batches of ${batchSize}...`);
    
    // First, get location ID mapping
    const { data: locationMappings, error: mappingError } = await this.supabase
      .from('geonames_locations')
      .select('id, geonames_id');

    if (mappingError) {
      throw new Error(`Failed to get location mappings: ${mappingError.message}`);
    }

    const geonamesIdToUuid = new Map(
      locationMappings.map(l => [l.geonames_id, l.id])
    );

    for (let i = 0; i < data.alternateNames.length; i += batchSize) {
      const batch = data.alternateNames.slice(i, i + batchSize);
      const nameRows = batch
        .map((name) => {
          return this.convertAlternateNameToRow.call(this, name, geonamesIdToUuid);
        })
        .filter(row => row !== null);
      
      if (nameRows.length === 0) continue;

      const { error } = await this.supabase
        .from('geonames_location_names')
        .insert(nameRows);

      if (error) {
        console.error(`[SYNC] Failed to upsert names batch ${i + 1}-${Math.min(i + batchSize, nameRows.length)}:`, {
          code: error.code,
          message: error.message,
          details: error.details
        });
        
        // For constraint violations, try individual inserts
        if (error.code === '23505' || error.code === '23503' || error.code === '23514') {
          console.log(`[SYNC] Constraint violation detected (${error.code}), trying individual inserts for alternate names...`);
          const { inserted, skipped } = await this.insertAlternateNamesIndividually(nameRows);
          console.log(`[SYNC] Individual alternate names insert results: ${inserted} inserted, ${skipped} skipped`);
          totalNameInserted += inserted;
          totalNameSkipped += skipped;
        } else {
          console.log(`[SYNC] Non-constraint error (${error.code}), continuing with next batch...`);
          totalNameSkipped += nameRows.length;
        }
        continue;
      }

      console.log(`[SYNC] Inserted names batch ${i + 1}-${Math.min(i + batchSize, data.alternateNames.length)}`);
      totalNameInserted += nameRows.length;
    }

    console.log(`[SYNC] ✅ Data insertion completed:`);
    console.log(`  - Locations: ${totalLocationInserted} inserted, ${totalLocationSkipped} skipped`);
    console.log(`  - Alternate Names: ${totalNameInserted} inserted, ${totalNameSkipped} skipped`);
    console.log(`[SYNC] Success rate: ${Math.round((totalLocationInserted + totalNameInserted) / (totalLocationInserted + totalLocationSkipped + totalNameInserted + totalNameSkipped) * 100)}%`);
  }

  private async insertLocationsIndividually(locationRows: any[]): Promise<{ inserted: number; skipped: number }> {
    let inserted = 0;
    let skipped = 0;

    for (const row of locationRows) {
      try {
        const { error } = await this.supabase
          .from('geonames_locations')
          .upsert([row], {
            onConflict: 'geonames_id',
            ignoreDuplicates: true
          });

        if (error) {
          console.warn(`[SYNC] Skipping location ${row.geonames_id} (${row.name}): ${error.message}`);
          skipped++;
        } else {
          inserted++;
        }
      } catch (err) {
        console.warn(`[SYNC] Failed to insert location ${row.geonames_id}: ${err}`);
        skipped++;
      }
    }

    return { inserted, skipped };
  }

  private async insertAlternateNamesIndividually(nameRows: any[]): Promise<{ inserted: number; skipped: number }> {
    let inserted = 0;
    let skipped = 0;

    for (const row of nameRows) {
      try {
        const { error } = await this.supabase
          .from('geonames_location_names')
          .upsert([row], {
            onConflict: 'location_id,language_code,name',
            ignoreDuplicates: true
          });

        if (error) {
          console.warn(`[SYNC] Skipping alternate name ${row.name} (${row.language_code}): ${error.message}`);
          skipped++;
        } else {
          inserted++;
        }
      } catch (err) {
        console.warn(`[SYNC] Failed to insert alternate name ${row.name}: ${err}`);
        skipped++;
      }
    }

    return { inserted, skipped };
  }

  private convertPlaceToRow(place: GeoNamesPlace): any {
    return {
      geonames_id: place.geonames_id,
      name: place.name,
      ascii_name: place.ascii_name || place.name,
      country_code: place.country_code,
      admin1_code: place.admin1_code || '',
      admin2_code: place.admin2_code || '',
      feature_class: place.feature_class,
      feature_code: place.feature_code,
      latitude: place.latitude,
      longitude: place.longitude,
      population: place.population || 0,
      elevation: place.elevation || null,
      timezone: place.timezone || 'Europe/Madrid',
      popularity_score: this.calculatePopularityScore(place),
      property_count: 0 // Will be updated by property sync later
    };
  }

  private convertAlternateNameToRow(name: GeoNamesAlternateName, locationMapping: Map<number, string>): any | null {
    const locationId = locationMapping.get(name.geonames_id);
    if (!locationId) {
      return null; // Skip if we don't have this location
    }

    return {
      location_id: locationId,
      language_code: name.language_code,
      name: name.name,
      is_preferred: name.is_preferred,
      is_short: name.is_short,
      is_colloquial: name.is_colloquial,
      is_historic: name.is_historic
    };
  }

  private calculatePopularityScore(place: GeoNamesPlace): number {
    let score = 50; // Base score
    
    // Population factor
    if (place.population > 1000000) score += 30;
    else if (place.population > 100000) score += 20;
    else if (place.population > 10000) score += 10;
    else if (place.population > 1000) score += 5;
    
    // Feature importance
    if (place.feature_code === 'P.PPLC') score += 25; // Capital
    else if (place.feature_code === 'P.PPLA') score += 15; // Admin center
    else if (place.feature_code === 'P.PPLA2') score += 10; // Regional center
    
    // Limit to 0-100 range
    return Math.max(0, Math.min(100, score));
  }

  private async buildSearchIndexes(): Promise<void> {
    const languages = this.configManager.getAllLanguages();
    
    console.log(`[SYNC] Building search vectors for languages: ${languages.join(', ')}`);
    
    // Build language-specific search vectors
    for (const lang of languages) {
      await this.buildLanguageSearchVector(lang);
    }
    
    // Build combined multilingual search vector
    await this.buildMultilingualSearchVector(languages);
    
    console.log('[SYNC] ✅ Search indexes built successfully');
  }

  private async buildLanguageSearchVector(language: string): Promise<void> {
    // Skip - simplified schema uses direct search without pre-built vectors
    console.log(`[SYNC] ℹ️ Skipping search vector for ${language} (using direct search)`);
  }

  private async buildMultilingualSearchVector(languages: string[]): Promise<void> {
    // Skip - simplified schema uses direct ILIKE/similarity search
    console.log('[SYNC] ℹ️ Skipping multilingual search vector (using trigram similarity)');
  }

  private getTextSearchConfig(language: string): string {
    const configs: Record<string, string> = {
      'en': 'english',
      'es': 'spanish',
      'fr': 'french',
      'de': 'german',
      'it': 'italian',
      'pt': 'portuguese',
      'nl': 'dutch',
      'ca': 'simple', // Catalan - use simple
      'eu': 'simple'  // Basque - use simple
    };
    
    return configs[language] || 'simple';
  }

  async getHealthStatus(): Promise<SyncHealthStatus> {
    const checks: Array<{ name: string; status: 'pass' | 'fail'; message?: string }> = [];
    
    // If sync service is disabled, return minimal status
    if (!this.isEnabled) {
      return {
        status: 'degraded',
        checks: [{ 
          name: 'sync_service', 
          status: 'fail', 
          message: 'Supabase credentials not available - sync disabled' 
        }],
        metrics: {
          total_locations: 0,
          countries: 0,
          total_names: 0,
          languages: 0,
          last_update: 'N/A - service disabled'
        }
      };
    }
    
    try {
      // Check database connection
      const { error: connError } = await this.supabase.from('geonames_locations').select('id').limit(1);
      checks.push({
        name: 'database_connection',
        status: connError ? 'fail' : 'pass',
        message: connError?.message
      });

      // Check data freshness
      const { data: lastLocation, error: dataError } = await this.supabase
        .from('geonames_locations')
        .select('created_at')
        .order('created_at', { ascending: false })
        .limit(1);

      if (dataError || !lastLocation?.length) {
        checks.push({
          name: 'data_freshness',
          status: 'fail',
          message: 'No location data found'
        });
      } else {
        const lastUpdate = new Date(lastLocation[0].created_at);
        const hoursOld = (Date.now() - lastUpdate.getTime()) / (1000 * 60 * 60);
        
        checks.push({
          name: 'data_freshness',
          status: hoursOld < 48 ? 'pass' : 'fail',
          message: `Data is ${Math.round(hoursOld)} hours old`
        });
      }

      // Get metrics
      const metrics = await this.getSyncMetrics();

      const overallStatus = checks.every(c => c.status === 'pass') ? 'healthy' : 'degraded';

      return {
        status: overallStatus,
        checks,
        metrics
      };

    } catch (error) {
      return {
        status: 'error',
        checks: [{ name: 'health_check', status: 'fail', message: (error as Error).message }],
        metrics: {
          total_locations: 0,
          countries: 0,
          total_names: 0,
          languages: 0,
          last_update: 'unknown'
        }
      };
    }
  }

  private async getSyncMetrics(): Promise<any> {
    const { data: locationsCount } = await this.supabase
      .from('geonames_locations')
      .select('id', { count: 'exact', head: true });

    const { data: namesCount } = await this.supabase
      .from('geonames_location_names')
      .select('id', { count: 'exact', head: true });

    const { data: countriesData } = await this.supabase
      .from('geonames_locations')
      .select('country_code')
      .limit(1000);

    const { data: languagesData } = await this.supabase
      .from('geonames_location_names')
      .select('language_code')
      .limit(1000);

    const { data: lastUpdate } = await this.supabase
      .from('geonames_locations')
      .select('created_at')
      .order('created_at', { ascending: false })
      .limit(1);

    return {
      total_locations: locationsCount?.length || 0,
      countries: new Set(countriesData?.map(d => d.country_code) || []).size,
      total_names: namesCount?.length || 0,
      languages: new Set(languagesData?.map(d => d.language_code) || []).size,
      last_update: lastUpdate?.[0]?.created_at || 'unknown'
    };
  }
}