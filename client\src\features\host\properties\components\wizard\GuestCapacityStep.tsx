import React from 'react';
import { useTranslations } from '@/lib/translations';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Users, Bed, Bath, Plus, Minus } from 'lucide-react';
import { PropertyWizardData } from '../../types/property';

interface GuestCapacityStepProps {
  data: Partial<PropertyWizardData>;
  onUpdate: (data: Partial<PropertyWizardData>) => void;
}

const CAPACITY_ITEMS = [
  {
    id: 'maxGuests',
    icon: Users,
    min: 1,
    max: 16,
    step: 1
  },
  {
    id: 'bedrooms',
    icon: Bed,
    min: 0,
    max: 10,
    step: 1
  },
  {
    id: 'bathrooms',
    icon: Bath,
    min: 0.5,
    max: 10,
    step: 0.5
  }
];

export const GuestCapacityStep = ({ data, onUpdate }: GuestCapacityStepProps) => {
  const t = useTranslations('hostOnboarding.capacity');

  const handleValueChange = (field: keyof PropertyWizardData, value: number) => {
    onUpdate({ [field]: value });
  };

  const incrementValue = (field: keyof PropertyWizardData, item: typeof CAPACITY_ITEMS[0]) => {
    const currentValue = (data[field] as number) || item.min;
    const newValue = Math.min(currentValue + item.step, item.max);
    handleValueChange(field, newValue);
  };

  const decrementValue = (field: keyof PropertyWizardData, item: typeof CAPACITY_ITEMS[0]) => {
    const currentValue = (data[field] as number) || item.min;
    const newValue = Math.max(currentValue - item.step, item.min);
    handleValueChange(field, newValue);
  };

  const getValue = (field: keyof PropertyWizardData, defaultValue: number) => {
    return (data[field] as number) || defaultValue;
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          {t('title')}
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          {t('description')}
        </p>
      </div>

      {/* Capacity Controls */}
      <div className="space-y-6">
        {CAPACITY_ITEMS.map((item) => {
          const currentValue = getValue(item.id as keyof PropertyWizardData, item.min);
          const isMinimum = currentValue <= item.min;
          const isMaximum = currentValue >= item.max;

          return (
            <Card key={item.id} className="border-gray-200 dark:border-gray-700">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center justify-center w-12 h-12 bg-primary/10 rounded-full">
                      <item.icon className="h-6 w-6 text-primary" />
                    </div>
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                        {t(item.id)}
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {t(`${item.id}Description`)}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-4">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => decrementValue(item.id as keyof PropertyWizardData, item)}
                      disabled={isMinimum}
                      className="h-10 w-10 p-0"
                    >
                      <Minus className="h-4 w-4" />
                    </Button>
                    
                    <span className="text-lg font-semibold text-gray-900 dark:text-white min-w-[3rem] text-center">
                      {currentValue % 1 === 0 ? Math.floor(currentValue) : currentValue}
                    </span>
                    
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => incrementValue(item.id as keyof PropertyWizardData, item)}
                      disabled={isMaximum}
                      className="h-10 w-10 p-0"
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Popular Configurations */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          {t('popularConfigurations')}
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[
            { guests: 2, bedrooms: 1, bathrooms: 1, key: 'couples_retreat' },
            { guests: 4, bedrooms: 2, bathrooms: 1, key: 'small_family' },
            { guests: 6, bedrooms: 3, bathrooms: 2, key: 'large_family' },
            { guests: 8, bedrooms: 4, bathrooms: 2, key: 'group_getaway' },
            { guests: 10, bedrooms: 5, bathrooms: 3, key: 'large_villa' },
            { guests: 12, bedrooms: 6, bathrooms: 4, key: 'luxury_estate' }
          ].map((config, index) => (
            <Card
              key={index}
              className="cursor-pointer transition-all hover:shadow-md border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600"
              onClick={() => onUpdate({
                maxGuests: config.guests,
                bedrooms: config.bedrooms,
                bathrooms: config.bathrooms
              })}
            >
              <CardContent className="p-4">
                <div className="text-center">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                    {t(`configurations.${config.key}`)}
                  </h4>
                  <div className="flex justify-center space-x-4 text-sm text-gray-600 dark:text-gray-400">
                    <span>{t(`configurationDetails.${config.key}`)}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Tips */}
      <div className="bg-green-50 dark:bg-green-900/20 p-6 rounded-lg">
        <h4 className="font-medium text-green-800 dark:text-green-200 mb-2">
          {t('tipsTitle')}
        </h4>
        <ul className="text-sm text-green-700 dark:text-green-300 space-y-1">
          <li>• {t('tip1')}</li>
          <li>• {t('tip2')}</li>
          <li>• {t('tip3')}</li>
          <li>• {t('tip4')}</li>
        </ul>
      </div>

      {/* Illustration */}
      <div className="text-center py-8">
        <div className="inline-flex items-center justify-center w-32 h-32 bg-primary/10 rounded-full mb-4">
          <Users className="h-16 w-16 text-primary" />
        </div>
        <p className="text-sm text-gray-600 dark:text-gray-400 max-w-md mx-auto">
          {t('illustrationText')}
        </p>
      </div>
    </div>
  );
};