import { useRef } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { useTranslations } from '@/lib/translations';
import { PropertyCard } from '../../search/components/PropertyCard';
import { ExplorationSectionProps } from '../types';
import { ErrorBoundary } from '@/components/ErrorBoundary';

export function ExplorationSection({ title, properties }: ExplorationSectionProps) {
  const t = useTranslations('exploration');
  const scrollRef = useRef<HTMLDivElement>(null);

  const scrollLeft = () => {
    if (scrollRef.current) {
      scrollRef.current.scrollBy({ left: -320, behavior: 'smooth' });
    }
  };

  const scrollRight = () => {
    if (scrollRef.current) {
      scrollRef.current.scrollBy({ left: 320, behavior: 'smooth' });
    }
  };

  // Convert exploration properties to search PropertyCard format
  const convertedProperties = properties.map(prop => ({
    id: prop.id,
    title: prop.title,
    hostType: prop.hostType,
    price: prop.price,
    rating: prop.rating,
    reviewCount: prop.reviewCount,
    images: prop.images,
    badge: prop.badge,
    location: {
      city: 'City',
      region: 'Region', 
      country: 'Country'
    },
    maxGuests: 4,
    bedrooms: 2,
    bathrooms: 1
  }));

  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between mb-10">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900">{title}</h2>
          <div className="hidden md:flex items-center space-x-3">
            <button
              onClick={scrollLeft}
              className="p-3 rounded-full bg-white border border-gray-200 shadow-sm hover:shadow-md hover:border-gray-300 transition-all duration-200"
              aria-label={t('scrollLeft')}
            >
              <ChevronLeft className="h-5 w-5 text-gray-700" />
            </button>
            <button
              onClick={scrollRight}
              className="p-3 rounded-full bg-white border border-gray-200 shadow-sm hover:shadow-md hover:border-gray-300 transition-all duration-200"
              aria-label={t('scrollRight')}
            >
              <ChevronRight className="h-5 w-5 text-gray-700" />
            </button>
          </div>
        </div>

        <div
          ref={scrollRef}
          className="flex space-x-6 overflow-x-auto scrollbar-hide pb-4 snap-x snap-mandatory"
          style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
        >
          {convertedProperties.map((property) => (
            <div key={property.id} className="flex-shrink-0 w-80 snap-start">
              <ErrorBoundary>
                <PropertyCard property={property} />
              </ErrorBoundary>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}