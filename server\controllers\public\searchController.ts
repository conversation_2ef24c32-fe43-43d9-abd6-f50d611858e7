import { Request, Response } from 'express';
import { z } from 'zod';
import { searchResponseSchema, propertySchema } from '../../types/search';
import { storage } from '../../storage';
import { resolveImageUrls } from '../../utils/imageResolver';
import { geoSearchService } from '../../services/geoSearch';

// Enhanced request validation schema with geographic search support
const searchParamsSchema = z.object({
  location: z.string().optional(),
  lat: z.coerce.number().optional(),
  lng: z.coerce.number().optional(),
  radius: z.coerce.number().min(1000).max(200000).default(50000), // 1km to 200km radius
  checkIn: z.string().optional(),
  checkOut: z.string().optional(),
  adults: z.coerce.number().min(1).default(1),
  children: z.coerce.number().min(0).default(0),
  infants: z.coerce.number().min(0).default(0),
  pets: z.coerce.number().min(0).default(0),
  minPrice: z.coerce.number().min(0).optional(),
  maxPrice: z.coerce.number().min(0).optional(),
  propertyTypes: z.string().optional(),
  amenities: z.string().optional(),
  useGeoSearch: z.coerce.boolean().default(true),
  filters: z.string().optional(),
});

export class SearchController {
  async searchProperties(req: Request, res: Response) {
    try {
      // Validate request parameters
      const params = searchParamsSchema.parse(req.query);
      
      console.log(`[SEARCH] Geographic search requested:`, {
        location: params.location,
        coordinates: params.lat && params.lng ? { lat: params.lat, lng: params.lng } : null,
        radius: params.radius,
        useGeoSearch: params.useGeoSearch
      });

      // Parse additional filters
      let propertyTypes: string[] = [];
      let amenities: string[] = [];
      
      if (params.propertyTypes) {
        try {
          propertyTypes = JSON.parse(params.propertyTypes);
        } catch {
          propertyTypes = params.propertyTypes.split(',').map(t => t.trim());
        }
      }
      
      if (params.amenities) {
        try {
          amenities = JSON.parse(params.amenities);
        } catch {
          amenities = params.amenities.split(',').map(a => a.trim());
        }
      }

      // Use geographic search if enabled and location/coordinates provided
      if (params.useGeoSearch && (params.location || (params.lat && params.lng))) {
        const geoSearchResult = await geoSearchService.searchPropertiesGeographically({
          location: params.location,
          coordinates: params.lat && params.lng ? { lat: params.lat, lng: params.lng } : undefined,
          radius: params.radius,
          checkIn: params.checkIn ? new Date(params.checkIn) : undefined,
          checkOut: params.checkOut ? new Date(params.checkOut) : undefined,
          maxGuests: params.adults + params.children + params.infants,
          minPrice: params.minPrice,
          maxPrice: params.maxPrice,
          propertyTypes,
          amenities
        });

        console.log(`[SEARCH] Geographic search found ${geoSearchResult.total} properties within ${params.radius}m of ${geoSearchResult.searchLocation}`);

        // Transform geographic search results
        const transformedResults = geoSearchResult.results.map(result => {
          const property = result.property;
          const transformed = {
            id: property.id.toString(),
            title: property.title,
            location: { 
              city: property.city, 
              region: property.location || property.city, 
              country: property.country 
            },
            price: { 
              amount: Math.round(Number(property.price_per_night) * 7), 
              currency: "€", 
              period: "week" 
            },
            rating: property.rating ? Number(property.rating) : null,
            reviewCount: property.review_count || 0,
            images: resolveImageUrls(property.images),
            amenities: Array.isArray(property.amenities) ? property.amenities : [],
            bedrooms: property.bedrooms,
            bathrooms: property.bathrooms,
            maxGuests: property.max_guests,
            propertyType: property.property_type,
            badges: [],
            features: Array.isArray(property.amenities) ? property.amenities : [],
            distance: result.distance, // Add distance from search center
            coordinates: result.coordinates
          };

          try {
            return propertySchema.parse(transformed);
          } catch (validationError) {
            console.error('Property validation error:', validationError);
            return null;
          }
        }).filter(property => property !== null);

        const geoSearchResponse = {
          properties: transformedResults,
          total: transformedResults.length,
          center: geoSearchResult.center,
          radius: geoSearchResult.radius,
          searchLocation: geoSearchResult.searchLocation,
          filters: {
            cities: Array.from(new Set(transformedResults.map(p => p.location.city))),
            regions: Array.from(new Set(transformedResults.map(p => p.location.region))),
            propertyTypes: Array.from(new Set(transformedResults.map(p => p.propertyType)))
          }
        };

        return res.json(geoSearchResponse);
      }

      // Fallback to traditional text-based search
      const properties = await storage.searchHostProperties({
        location: params.location,
        checkIn: params.checkIn ? new Date(params.checkIn) : undefined,
        checkOut: params.checkOut ? new Date(params.checkOut) : undefined,
        maxGuests: params.adults + params.children + params.infants,
        minPrice: params.minPrice,
        maxPrice: params.maxPrice
      });

      console.log(`[SEARCH] Traditional search found ${properties.length} properties for location: ${params.location}`);

      // Transform properties to match frontend format
      const transformedProperties = properties.map(property => {
        const transformed = {
          id: property.id.toString(),
          title: property.title,
          location: { 
            city: property.city, 
            region: property.location || property.city, 
            country: property.country 
          },
          price: { 
            amount: Math.round(Number(property.price_per_night) * 7), 
            currency: "€", 
            period: "week" 
          },
          rating: property.rating ? Number(property.rating) : null,
          reviewCount: property.review_count || 0,
          images: resolveImageUrls(property.images),
          amenities: Array.isArray(property.amenities) ? property.amenities : [],
          bedrooms: property.bedrooms,
          bathrooms: property.bathrooms,
          maxGuests: property.max_guests,
          propertyType: property.property_type,
          badges: [],
          features: Array.isArray(property.amenities) ? property.amenities : []
        };

        // Validate individual property
        try {
          return propertySchema.parse(transformed);
        } catch (validationError) {
          console.error('Property validation error:', validationError, 'Property:', transformed);
          throw validationError;
        }
      });

      // Apply location filtering if specified
      let filteredProperties = transformedProperties;
      if (params.location) {
        const locationLower = params.location.toLowerCase();
        filteredProperties = transformedProperties.filter(property => 
          property.location.city.toLowerCase().includes(locationLower) ||
          property.location.region.toLowerCase().includes(locationLower) ||
          property.location.country.toLowerCase().includes(locationLower)
        );
      }

      console.log(`After filtering: ${filteredProperties.length} properties`);

      // Create search response with metadata
      const searchResponse = {
        properties: filteredProperties,
        total: filteredProperties.length,
        filters: {
          cities: [],
          regions: [],
          propertyTypes: []
        }
      };

      // Validate response format
      const validatedResponse = searchResponseSchema.parse(searchResponse);
      
      res.json(validatedResponse);
    } catch (error) {
      console.error('Search error:', error);
      if (error instanceof z.ZodError) {
        return res.status(400).json({ 
          error: 'Invalid search parameters',
          details: error.errors 
        });
      }
      res.status(500).json({ error: 'Internal server error' });
    }
  }
}

export const searchController = new SearchController();