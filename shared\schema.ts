import { z } from "zod";

// Validation schemas for API requests
export const insertUserSchema = z.object({
  id: z.string().optional(), // Optional for auto-generated IDs, required for Supabase Auth IDs
  email: z.string().email(),
  username: z.string().min(3),
  first_name: z.string().optional(),
  last_name: z.string().optional(),
  phone: z.string().optional(),
  is_host: z.boolean().optional(),
  avatar_url: z.string().url().optional(),
  oauth_provider: z.string().optional(),
  oauth_id: z.string().optional(),
  locale: z.string().optional()
});

// Host-centric schemas
export const insertHostPropertySchema = z.object({
  host_id: z.string(),
  title: z.string().min(1),
  description: z.string().min(1),
  location: z.string().min(1),
  city: z.string().min(1),
  country: z.string().min(1),
  coordinates: z.object({
    lat: z.number(),
    lng: z.number()
  }),
  price_per_night: z.string(),
  max_guests: z.number().min(1),
  bedrooms: z.number().min(0),
  bathrooms: z.number().min(0),
  amenities: z.array(z.string()).optional(),
  images: z.array(z.string()).optional(),
  property_type: z.string().min(1),
  rating: z.string().optional(),
  review_count: z.number().optional()
});

// Guest-centric schemas
export const insertGuestBookingSchema = z.object({
  host_property_id: z.string(),
  guest_id: z.string(),
  check_in: z.string(),
  check_out: z.string(),
  guests: z.object({
    adults: z.number().min(1),
    children: z.number().min(0),
    infants: z.number().min(0),
    pets: z.number().min(0)
  }),
  total_price: z.string(),
  status: z.string(),
  special_requests: z.string().optional()
});

export const insertGuestReviewSchema = z.object({
  booking_id: z.string(),
  guest_id: z.string(),
  host_property_id: z.string(),
  rating: z.number().min(1).max(5),
  comment: z.string().optional()
});

export const insertGuestSearchHistorySchema = z.object({
  user_id: z.string().optional(),
  session_id: z.string().optional(),
  location: z.string().min(1),
  check_in: z.string().optional(),
  check_out: z.string().optional(),
  guests: z.object({
    adults: z.number().min(1),
    children: z.number().min(0),
    infants: z.number().min(0),
    pets: z.number().min(0)
  }).optional()
});

export const insertGuestProfileSchema = z.object({
  user_id: z.string(),
  bio: z.string().optional(),
  avatar_url: z.string().url().optional(),
  travel_preferences: z.array(z.string()).optional(),
  identity_verified: z.boolean().optional(),
  email_verified: z.boolean().optional(),
  phone_verified: z.boolean().optional(),
  emergency_contact: z.any().optional()
});

export const insertGuestWishlistSchema = z.object({
  guest_id: z.string(),
  name: z.string().min(1),
  description: z.string().optional(),
  is_public: z.boolean().optional()
});

export const insertGuestWishlistPropertySchema = z.object({
  wishlist_id: z.string(),
  host_property_id: z.string()
});

// Communication schemas
export const insertCommConversationSchema = z.object({
  guest_id: z.string(),
  host_id: z.string(),
  host_property_id: z.string().optional(),
  subject: z.string().optional()
});

export const insertCommMessageSchema = z.object({
  conversation_id: z.string(),
  sender_id: z.string(),
  sender_type: z.enum(['guest', 'host']),
  content: z.string().min(1),
  message_type: z.enum(['text', 'image', 'system']).optional()
});

export const insertHelpArticleSchema = z.object({
  title: z.string().min(1),
  content: z.string().min(1),
  category: z.string().min(1),
  subcategory: z.string().optional(),
  is_faq: z.boolean().optional(),
  display_order: z.number().optional(),
  is_active: z.boolean().optional()
});

// Legacy schemas for backward compatibility
export const insertPropertySchema = insertHostPropertySchema;
export const insertBookingSchema = insertGuestBookingSchema;
export const insertReviewSchema = insertGuestReviewSchema;
export const insertSearchHistorySchema = insertGuestSearchHistorySchema;

// Type exports (these match the Supabase types)
export type InsertUser = z.infer<typeof insertUserSchema>;
export type InsertHostProperty = z.infer<typeof insertHostPropertySchema>;
export type InsertGuestBooking = z.infer<typeof insertGuestBookingSchema>;
export type InsertGuestReview = z.infer<typeof insertGuestReviewSchema>;
export type InsertGuestSearchHistory = z.infer<typeof insertGuestSearchHistorySchema>;
export type InsertGuestProfile = z.infer<typeof insertGuestProfileSchema>;
export type InsertGuestWishlist = z.infer<typeof insertGuestWishlistSchema>;
export type InsertGuestWishlistProperty = z.infer<typeof insertGuestWishlistPropertySchema>;
export type InsertCommConversation = z.infer<typeof insertCommConversationSchema>;
export type InsertCommMessage = z.infer<typeof insertCommMessageSchema>;
export type InsertHelpArticle = z.infer<typeof insertHelpArticleSchema>;

// Legacy type exports for backward compatibility
export type InsertProperty = z.infer<typeof insertPropertySchema>;
export type InsertBooking = z.infer<typeof insertBookingSchema>;
export type InsertReview = z.infer<typeof insertReviewSchema>;
export type InsertSearchHistory = z.infer<typeof insertSearchHistorySchema>;

// Re-export types from Supabase module for consistency
export type { 
  User, 
  HostProperty, 
  GuestBooking, 
  GuestReview, 
  GuestSearchHistory,
  GuestProfile,
  GuestWishlist,
  GuestWishlistProperty,
  CommConversation,
  CommMessage,
  HelpArticle,
  // Legacy types
  Property, 
  Booking, 
  Review, 
  SearchHistory 
} from "../server/supabase";