{"name": "VillaWise Development", "dockerComposeFile": "../docker-compose.dev.yml", "service": "villawise-dev", "workspaceFolder": "/app", "customizations": {"vscode": {"extensions": ["bradlc.vscode-tailwindcss", "esbenp.prettier-vscode", "ms-typescript.typescript-hero", "formulahendry.auto-rename-tag", "christian-kohler.path-intellisense", "ms-vscode.vscode-typescript-next", "ms-vscode.vscode-json", "ms-vscode.vscode-eslint"], "settings": {"typescript.preferences.includePackageJsonAutoImports": "on", "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "tailwindCSS.includeLanguages": {"typescript": "typescript", "typescriptreact": "typescript"}, "typescript.suggest.autoImports": true, "editor.codeActionsOnSave": {"source.organizeImports": true}}}}, "forwardPorts": [5000], "postCreateCommand": "sudo chown -R node:node /app /app/node_modules && npm install && npm run build", "remoteUser": "node"}