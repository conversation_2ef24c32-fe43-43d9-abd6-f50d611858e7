# Getting Started with Villa<PERSON><PERSON>

## Overview

VillaWise is a modern vacation rental platform featuring 100+ authentic Spanish properties in Costa Blanca region. Built with React 18, TypeScript, Express.js, and Supabase, it offers complete authentication, property management, and booking systems.

**Current Status:** Production-ready with OAuth authentication, comprehensive host dashboard, and security audit pipeline.

## Quick Setup

### Prerequisites

- Node.js 18+ (use `.nvmrc` file)
- npm or yarn
- Supabase account

### Installation

```bash
# Clone and install dependencies
git clone <repository-url>
cd villawise
npm install

# Environment setup
cp .env.example .env
# Configure environment variables (see Environment Configuration below)

# Start development server
npm run dev
```

The application will be available at `http://localhost:5000`

## Environment Configuration

### Required Variables

```env
# Supabase Database
SUPABASE_URL=your-supabase-project-url
SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# Application
NODE_ENV=development
PORT=5000

# OAuth (optional for development)
OAUTH_REDIRECT_URL=http://localhost:5000/auth/callback
```

### Supabase Setup

1. Create a new Supabase project at [supabase.com](https://supabase.com)
2. Go to **Settings → API** and copy:
   - Project URL → `SUPABASE_URL`
   - anon public key → `SUPABASE_ANON_KEY`
   - service_role secret key → `SUPABASE_SERVICE_ROLE_KEY`
3. Enable authentication providers:
   - **Authentication → Providers → Google OAuth**
   - **Authentication → Providers → Email**
4. Configure OAuth redirect URLs:
   - Add `http://localhost:5000/auth/callback` for development
   - Add production URLs for deployment

### OAuth Configuration

#### Google OAuth Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing
3. Enable Google+ API
4. Go to **Credentials → OAuth 2.0 Client IDs**
5. Add authorized redirect URIs:
   - `https://your-supabase-project.supabase.co/auth/v1/callback`
   - `http://localhost:5000/auth/callback` (development)
6. Copy Client ID and Client Secret to Supabase:
   - **Supabase → Authentication → Providers → Google**
   - Enable Google provider
   - Add Client ID and Client Secret

#### Facebook OAuth Setup

1. Go to [Facebook Developers](https://developers.facebook.com/)
2. Create a new app
3. Add Facebook Login product
4. Configure OAuth redirect URIs:
   - `https://your-supabase-project.supabase.co/auth/v1/callback`
5. Copy App ID and App Secret to Supabase:
   - **Supabase → Authentication → Providers → Facebook**
   - Enable Facebook provider
   - Add App ID and App Secret

## Development Commands

```bash
# Start development server
npm run dev

# Type checking
npm run check
# OR with timeout for faster feedback
timeout 30s npx tsc --noEmit --skipLibCheck

# Database operations
npm run db:push

# Testing
npm test                    # Run all tests
npm run test:auth          # Run authentication tests
npm run test:coverage      # Run with coverage
npm run test:e2e           # Run end-to-end tests

# Security
npm run audit:security     # Run security audit
npm run audit:fix          # Fix security issues
```

## Key Features

### Authentication System

- **OAuth Integration**: Google and Facebook login
- **Email/Password**: Traditional registration with email verification
- **Role-based Access**: Guest and host dashboards
- **Session Management**: Secure JWT tokens with refresh

### Property Management

- **Host Dashboard**: Complete property CRUD operations
- **Image Upload**: Supabase storage integration
- **Search & Filtering**: Location-based search with maps
- **Booking System**: Availability management and reservations

### Technical Highlights

- **Frontend**: React 18 + TypeScript + Tailwind CSS + shadcn/ui
- **Backend**: Express.js with feature-based architecture
- **Database**: Supabase PostgreSQL with Row Level Security
- **Authentication**: Supabase Auth with hybrid approach
- **Maps**: Leaflet with OpenStreetMap and clustering
- **Internationalization**: API-based translations (English/Dutch)

## Project Structure

```
├── client/
│   ├── src/
│   │   ├── components/        # Shared UI components
│   │   ├── features/          # Feature-based modules
│   │   │   ├── guest/         # Guest-specific features
│   │   │   │   ├── dashboard/ # Guest dashboard (overview, bookings, wishlists)
│   │   │   │   ├── exploration/ # Property discovery
│   │   │   │   ├── inspiration/ # Travel content
│   │   │   │   ├── property-details/ # Property viewing
│   │   │   │   └── search/    # Property search with maps
│   │   │   ├── host/          # Host-specific features
│   │   │   │   ├── dashboard/ # Host dashboard (overview, bookings, properties)
│   │   │   │   ├── properties/ # Property management
│   │   │   │   └── components/ # Host-specific components
│   │   │   ├── shared/        # Shared features across user types
│   │   │   │   ├── auth/      # Authentication system
│   │   │   │   ├── home/      # Landing page
│   │   │   │   ├── messaging/ # Communication system
│   │   │   │   └── navigation/ # Navigation components
│   │   │   └── admin/         # Admin panel features
│   │   ├── pages/             # Route-based page components
│   │   ├── hooks/             # Shared React hooks
│   │   ├── lib/               # Utility libraries
│   │   └── types/             # TypeScript definitions
├── server/
│   ├── controllers/           # Feature-organized controllers
│   ├── dal/                   # Data Access Layer
│   ├── routes/                # Express route definitions
│   ├── middleware/            # Authentication and validation
│   └── types/                 # Backend type definitions
├── database/                  # Schema, migrations, seeds
├── shared/                    # Shared types and schemas
├── docs/                      # Comprehensive documentation
└── deployment/                # Docker and Railway configs
```

## Architecture Overview

### Frontend Architecture

- **Feature-based organization**: Organized by user types (guest, host, shared, admin)
- **Component library**: shadcn/ui + custom components
- **State management**: TanStack Query + React Context
- **Routing**: Wouter for lightweight routing
- **Styling**: Tailwind CSS with yellow accent theme

### Backend Architecture

- **Controller structure**: Feature-organized Express controllers
- **Database layer**: Supabase with typed interfaces
- **Authentication**: Supabase Auth with custom middleware
- **API design**: RESTful endpoints with Zod validation

### Data Flow

```
Frontend (React) → TanStack Query → Express Controllers → Supabase
                                                       ↓
                                  Row Level Security → PostgreSQL
```

## Key URLs & Endpoints

### Frontend Routes

- **Home**: `/`
- **Authentication**: `/login`, `/register`
- **Search**: `/search`
- **Property**: `/property/{id}`
- **Host Dashboard**: `/host/dashboard`
- **Guest Dashboard**: `/guest/dashboard`

### API Endpoints

- **Authentication**: `/api/auth/*`
- **Properties**: `/api/properties/*`
- **Users**: `/api/users/*`
- **Bookings**: `/api/bookings/*`
- **Translations**: `/api/translations/{locale}`
- **Health Check**: `/api/health`

## Database Schema

### Core Tables

- **users**: User profiles and authentication
- **host_properties**: Property listings and details
- **guest_bookings**: Booking records and status
- **guest_reviews**: Property reviews and ratings
- **guest_search_history**: Search tracking
- **comm_conversations**: Host-guest messaging
- **guest_wishlists**: Saved properties

### Key Relationships

- Users → Host Properties (one-to-many)
- Properties → Bookings (one-to-many)
- Bookings → Reviews (one-to-one)
- Users → Search History (one-to-many)

## Common Issues & Solutions

### Authentication Issues

- **OAuth callback errors**: Check redirect URLs in provider settings
- **Token validation failures**: Verify Supabase keys and permissions
- **Session persistence**: Clear browser storage and check token expiration

### Development Issues

- **Port conflicts**: Change PORT in .env file
- **TypeScript errors**: Run `npm run check` to identify issues
- **Database connection**: Verify Supabase credentials and RLS policies

### Performance Issues

- **Slow map rendering**: Check clustering settings and data volume
- **Large bundle size**: Analyze with `npm run build` and optimize imports
- **Database queries**: Review query patterns and add indexes

## Next Steps

1. **Complete Setup**: Configure all environment variables
2. **Database Setup**: Run migrations and seed data
3. **OAuth Configuration**: Set up Google and Facebook authentication
4. **Testing**: Run test suite to verify functionality
5. **Deployment**: Follow deployment guide for production setup

For detailed information, see:

- [API Documentation](API_REFERENCE.md)
- [Development Guide](DEVELOPMENT_GUIDE.md)
- [Testing Strategy](TESTING_STRATEGY.md)
- [Deployment Guide](DEPLOYMENT_GUIDE.md)
