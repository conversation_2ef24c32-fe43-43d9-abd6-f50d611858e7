import { useQuery } from '@tanstack/react-query';
import { useUser } from '@/features/shared/auth/hooks/useAuth';

export const useBookings = () => {
  const { data: user } = useUser();

  const { data: bookings = [], isLoading, error } = useQuery({
    queryKey: ['/api/bookings/host'],
    enabled: !!user,
  });

  // Cast to proper type
  const bookingsArray = bookings as any[];

  // Filter bookings by status
  const confirmedBookings = bookingsArray.filter((b: any) => b.status === 'confirmed');
  const pendingBookings = bookingsArray.filter((b: any) => b.status === 'pending');
  const completedBookings = bookingsArray.filter((b: any) => b.status === 'completed');
  const cancelledBookings = bookingsArray.filter((b: any) => b.status === 'cancelled');

  // Get upcoming bookings (check-in date is in the future)
  const upcomingBookings = bookingsArray.filter((b: any) => {
    const checkInDate = new Date(b.check_in_date);
    return checkInDate > new Date() && (b.status === 'confirmed' || b.status === 'pending');
  });

  // Get current bookings (guests are currently staying)
  const currentBookings = bookingsArray.filter((b: any) => {
    const checkInDate = new Date(b.check_in_date);
    const checkOutDate = new Date(b.check_out_date);
    const now = new Date();
    return checkInDate <= now && checkOutDate >= now && b.status === 'confirmed';
  });

  return {
    bookings: bookingsArray,
    confirmedBookings,
    pendingBookings,
    completedBookings,
    cancelledBookings,
    upcomingBookings,
    currentBookings,
    isLoading,
    error,
    bookingCounts: {
      total: bookingsArray.length,
      confirmed: confirmedBookings.length,
      pending: pendingBookings.length,
      completed: completedBookings.length,
      cancelled: cancelledBookings.length,
      upcoming: upcomingBookings.length,
      current: currentBookings.length
    }
  };
};