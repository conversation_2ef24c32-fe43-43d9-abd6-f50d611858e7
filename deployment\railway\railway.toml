[build]
builder = "dockerfile"
dockerfilePath = "deployment/railway/Dockerfile.railway"

[deploy]
healthcheckPath = "/api/health"
healthcheckTimeout = 300
restartPolicyType = "on_failure"
restartPolicyMaxRetries = 3

[variables]
NODE_ENV = "production"
USE_REDIS_CACHE = "true"

# GeoNames Configuration
GEONAMES_COUNTRIES = "ES"
GEONAMES_LANGUAGES = "en,nl,es,ca"
SYNC_BATCH_SIZE = "1000"

# Redis Configuration (set your actual values)
# UPSTASH_REDIS_REST_URL = "https://your-redis-url"
# UPSTASH_REDIS_REST_TOKEN = "your-redis-token"