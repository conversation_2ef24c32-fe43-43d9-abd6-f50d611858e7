import { useParams, useLocation } from 'wouter';
import { PropertyDetails as PropertyDetailsComponent } from '@/features/public/property-details';

export default function PropertyDetails() {
  const { id } = useParams<{ id: string }>();
  const [, setLocation] = useLocation();

  const handleBack = () => {
    // Navigate back to search page
    setLocation('/search');
  };

  if (!id) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900">Property not found</h2>
          <p className="text-gray-600 mt-2">The property ID is missing or invalid.</p>
        </div>
      </div>
    );
  }

  return (
    <PropertyDetailsComponent
      propertyId={id}
      onBack={handleBack}
    />
  );
}