
import { supabase } from '../../supabase'
import { getCurrentUser, requireAuth, requireRole } from '../auth/session'
import { UserDTO, UserData, UserFilters, UserStats } from '../dto/user.dto'
import { memoryCache } from '../cache/memoryCache'

export const getUserById = async (
  userId: string,
  authHeader?: string
): Promise<UserDTO | null> => {
  const viewer = await getCurrentUser(authHeader)
  
  const cacheKey = `user:${userId}:${viewer?.userId || 'anonymous'}`
  const cached = memoryCache.get(cacheKey) as UserDTO | null
  if (cached) return cached
  
  try {
    const { data: user, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single()
    
    if (error || !user) {
      return null
    }
    
    const result = new UserDTO(user, viewer?.role, viewer?.userId)
    memoryCache.set(cacheKey, result, 600)
    return result
  } catch (error) {
    console.error('Error fetching user:', error)
    return null
  }
}

export const getCurrentUserProfile = async (authHeader: string): Promise<UserDTO | null> => {
  const user = await requireAuth(authHeader)
  
  const cacheKey = `user:profile:${user.userId}`
  const cached = memoryCache.get(cacheKey) as UserDTO | null
  if (cached) return cached
  
  try {
    const { data: userData, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', user.userId)
      .single()
    
    if (error || !userData) {
      return null
    }
    
    const result = new UserDTO(userData, user.role, user.userId)
    memoryCache.set(cacheKey, result, 300)
    return result
  } catch (error) {
    console.error('Error fetching user profile:', error)
    return null
  }
}

export const updateUserProfile = async (
  authHeader: string,
  updates: Partial<UserData>
): Promise<UserDTO> => {
  const user = await requireAuth(authHeader)
  
  try {
    const { id, role, created_at, email_verified, phone_verified, host_verified, ...allowedUpdates } = updates
    
    const { data: userData, error } = await supabase
      .from('users')
      .update({
        ...allowedUpdates,
        updated_at: new Date().toISOString()
      })
      .eq('id', user.userId)
      .select()
      .single()
    
    if (error) {
      throw new Error(`Failed to update user profile: ${error.message}`)
    }
    
    memoryCache.invalidatePattern(`user:${user.userId}:*`)
    memoryCache.invalidatePattern(`user:profile:${user.userId}`)
    
    return new UserDTO(userData, user.role, user.userId)
  } catch (error) {
    console.error('Error updating user profile:', error)
    throw error
  }
}

export const getUserStats = async (authHeader: string): Promise<UserStats> => {
  const user = await requireRole(authHeader, 'admin')
  
  const cacheKey = 'user-stats'
  const cached = memoryCache.get(cacheKey) as UserStats | null
  if (cached) return cached
  
  try {
    const { data: users, error } = await supabase
      .from('users')
      .select('role, created_at, email_verified, host_verified')
    
    if (error) {
      throw new Error(`Failed to fetch user stats: ${error.message}`)
    }
    
    const stats: UserStats = {
      total: users?.length || 0,
      guests: users?.filter(u => u.role === 'guest').length || 0,
      hosts: users?.filter(u => u.role === 'host').length || 0,
      admins: users?.filter(u => u.role === 'admin').length || 0,
      verified: users?.filter(u => u.email_verified).length || 0
    }
    
    memoryCache.set(cacheKey, stats, 600)
    return stats
  } catch (error) {
    console.error('Error fetching user stats:', error)
    throw error
  }
}