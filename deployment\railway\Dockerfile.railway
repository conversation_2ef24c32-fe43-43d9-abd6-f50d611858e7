# Railway-optimized Dockerfile for VillaWise
# Optimized for Railway's infrastructure and constraints

FROM node:20-alpine AS builder

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY tsconfig.json ./
COPY vite.config.ts ./
COPY tailwind.config.ts ./
COPY postcss.config.js ./

# Install dependencies with Railway optimizations
RUN npm ci --production=false --verbose

# Copy source code
COPY client/ ./client/
COPY server/ ./server/
COPY shared/ ./shared/
COPY scripts/ ./scripts/

# Build with memory optimization for Railway
ENV NODE_OPTIONS="--max-old-space-size=4096"
RUN npm run build && node scripts/build-production.js

# Production stage
FROM node:20-alpine AS production

WORKDIR /app

# Install production dependencies only
COPY package*.json ./
RUN npm ci --production=true && npm cache clean --force

# Copy built application
COPY --from=builder /app/dist ./dist

# Copy only production-needed server files (exclude development-only files)
COPY --from=builder /app/server/routes ./server/routes
COPY --from=builder /app/server/static-server.ts ./server/static-server.ts
COPY --from=builder /app/server/storage.ts ./server/storage.ts
COPY --from=builder /app/server/supabase.ts ./server/supabase.ts
COPY --from=builder /app/server/types.d.ts ./server/types.d.ts
COPY --from=builder /app/server/middleware ./server/middleware
COPY --from=builder /app/server/dal ./server/dal
COPY --from=builder /app/server/controllers ./server/controllers
COPY --from=builder /app/server/services ./server/services
COPY --from=builder /app/server/config ./server/config

# Create data directory for GeoNames files
RUN mkdir -p /app/data && chown -R 1001:1001 /app/data

# Copy shared files
COPY --from=builder /app/shared ./shared

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && adduser -S villawise -u 1001

# Set proper permissions
RUN chown -R villawise:nodejs /app
USER villawise

# Railway uses PORT environment variable
EXPOSE $PORT

# Health check endpoint
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "const http = require('http'); const port = process.env.PORT || 3000; http.get(\`http://localhost:\${port}/api/health\`, (res) => { process.exit(res.statusCode === 200 ? 0 : 1) }).on('error', () => process.exit(1))"

# Start application
CMD ["npm", "run", "start"]