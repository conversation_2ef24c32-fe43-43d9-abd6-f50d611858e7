#!/usr/bin/env node

/**
 * GitHub Actions Build Status Checker
 * Run this script to check build results if you have GitHub CLI installed
 */

import { execSync } from 'child_process';
import fs from 'fs';

async function checkBuildStatus() {
  console.log('🔍 Checking GitHub Actions build status...\n');

  try {
    // Check if GitHub CLI is available
    execSync('gh --version', { stdio: 'pipe' });
    console.log('✅ GitHub CLI detected');

    // Get latest workflow runs
    console.log('\n📋 Recent workflow runs:');
    const runs = execSync('gh run list --limit 10 --json status,conclusion,name,createdAt,url', { 
      encoding: 'utf8' 
    });
    
    const runData = JSON.parse(runs);
    
    if (runData.length === 0) {
      console.log('No workflow runs found. Make sure you have pushed the GitHub Actions workflows to your repository.');
      return;
    }

    runData.forEach((run, index) => {
      const status = run.status === 'completed' ? 
        (run.conclusion === 'success' ? '✅' : '❌') : '🔄';
      
      console.log(`${index + 1}. ${status} ${run.name}`);
      console.log(`   Status: ${run.status} | Conclusion: ${run.conclusion || 'N/A'}`);
      console.log(`   Created: ${new Date(run.createdAt).toLocaleString()}`);
      console.log(`   URL: ${run.url}\n`);
    });

    // Get artifacts from latest run
    console.log('📦 Checking for artifacts...');
    try {
      const artifacts = execSync('gh api repos/:owner/:repo/actions/artifacts --jq ".artifacts[] | {name, size_in_bytes, created_at, download_url}"', {
        encoding: 'utf8'
      });
      
      if (artifacts.trim()) {
        console.log('Available artifacts:');
        console.log(artifacts);
      } else {
        console.log('No artifacts found yet.');
      }
    } catch (artifactError) {
      console.log('Could not fetch artifacts. This might be normal if no workflows have completed yet.');
    }

  } catch (error) {
    console.log('❌ GitHub CLI not found or not authenticated.');
    console.log('\nTo check build results manually:');
    console.log('1. Go to your GitHub repository');
    console.log('2. Click the "Actions" tab');
    console.log('3. View workflow runs and download artifacts');
    console.log('\nTo install GitHub CLI: https://cli.github.com/');
    console.log('To authenticate: gh auth login');
  }
}

// Check if package.json exists to confirm we're in the right directory
if (!fs.existsSync('package.json')) {
  console.log('❌ Run this script from the project root directory (where package.json is located)');
  process.exit(1);
}

checkBuildStatus().catch(console.error);