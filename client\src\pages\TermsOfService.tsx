import React from 'react';
import { Link } from 'wouter';

export default function TermsOfService() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 py-12">
        <div className="bg-white rounded-lg shadow-sm p-8">
          <Link href="/">
            <button className="text-primary hover:text-primary/80 mb-6 flex items-center gap-2">
              ← Back to VillaWise
            </button>
          </Link>
          
          <h1 className="text-3xl font-bold mb-8">Terms of Service</h1>
          
          <div className="prose max-w-none space-y-6">
            <p className="text-gray-600">
              Last updated: {new Date().toLocaleDateString()}
            </p>
            
            <section>
              <h2 className="text-xl font-semibold mb-3">1. Acceptance of Terms</h2>
              <p>
                By accessing and using VillaWise, you accept and agree to be bound by the terms 
                and provision of this agreement.
              </p>
            </section>

            <section>
              <h2 className="text-xl font-semibold mb-3">2. Description of Service</h2>
              <p>
                VillaWise is a vacation rental platform that connects travelers with property owners 
                in the Costa Blanca region of Spain. We provide search, booking, and property 
                management services.
              </p>
            </section>

            <section>
              <h2 className="text-xl font-semibold mb-3">3. User Accounts</h2>
              <p>
                To access certain features of VillaWise, you may need to create an account. You are 
                responsible for maintaining the confidentiality of your account credentials.
              </p>
              
              <h3 className="text-lg font-medium mt-4 mb-2">Google OAuth</h3>
              <p>
                When using Google OAuth to sign in, you agree to Google's terms of service and 
                privacy policy in addition to ours.
              </p>
            </section>

            <section>
              <h2 className="text-xl font-semibold mb-3">4. Property Listings</h2>
              <p>
                Property owners are responsible for the accuracy of their listings. VillaWise does 
                not guarantee the accuracy of property descriptions, availability, or pricing.
              </p>
            </section>

            <section>
              <h2 className="text-xl font-semibold mb-3">5. Booking and Payments</h2>
              <p>
                Bookings are subject to availability and confirmation by the property owner. 
                Payment terms and cancellation policies are specified for each property.
              </p>
            </section>

            <section>
              <h2 className="text-xl font-semibold mb-3">6. Prohibited Uses</h2>
              <p>You may not use VillaWise for:</p>
              <ul className="list-disc list-inside ml-4 space-y-1">
                <li>Any unlawful purpose or activity</li>
                <li>Posting false or misleading information</li>
                <li>Interfering with the platform's operation</li>
                <li>Violating any applicable laws or regulations</li>
              </ul>
            </section>

            <section>
              <h2 className="text-xl font-semibold mb-3">7. Limitation of Liability</h2>
              <p>
                VillaWise provides the platform "as is" without warranties. We are not liable for 
                any damages arising from your use of the platform or any booking arrangements.
              </p>
            </section>

            <section>
              <h2 className="text-xl font-semibold mb-3">8. Contact Information</h2>
              <p>
                For questions about these Terms of Service, contact us at:
                <br />
                Email: <EMAIL>
              </p>
            </section>
          </div>
        </div>
      </div>
    </div>
  );
}