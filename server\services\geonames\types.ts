// GeoNames service types and interfaces
export interface GeoNamesPlace {
  geonames_id: number;
  name: string;
  ascii_name: string;
  latitude: number;
  longitude: number;
  country_code: string;
  admin1_code?: string;
  admin2_code?: string;
  feature_class: string;
  feature_code: string;
  population: number;
  elevation?: number;
  timezone?: string;
}

export interface GeoNamesAlternateName {
  alternate_name_id: number;
  geonames_id: number;
  language_code: string;
  name: string;
  is_preferred: boolean;
  is_short: boolean;
  is_colloquial: boolean;
  is_historic: boolean;
}

export interface ProcessedGeoNamesData {
  locations: GeoNamesPlace[];
  alternateNames: GeoNamesAlternateName[];
}

export interface SyncResult {
  countries: number;
  languages: number;
  locationsProcessed: number;
  alternateNamesProcessed: number;
  processingTime: number;
  errors?: string[];
}

export interface SyncHealthStatus {
  status: 'healthy' | 'degraded' | 'error';
  lastSync?: {
    timestamp: number;
    duration: number;
    locationsCount: number;
    namesCount: number;
  };
  checks: Array<{
    name: string;
    status: 'pass' | 'fail';
    message?: string;
  }>;
  metrics: SyncMetrics;
}

export interface SyncMetrics {
  total_locations: number;
  countries: number;
  total_names: number;
  languages: number;
  last_update: string;
}

export interface LocationResult {
  id: string;
  geonames_id: number;
  name: string;
  local_name?: string;
  localized_name?: string;
  display_name?: string;
  ascii_name?: string;
  country_code: string;
  country_name?: string;
  region_name?: string;
  admin1_code?: string;
  admin1_name?: string;
  admin2_code?: string;
  admin2_name?: string;
  feature_class: string;
  feature_code: string;
  coordinates: { lat: number; lng: number };
  latitude: number;
  longitude: number;
  population: number;
  elevation?: number;
  timezone?: string;
  popularity_score: number;
  property_count: number;
  distance_km?: number;
  relevance_score?: number;
  tourism_region?: string;
  tourism_region_type?: string;
  is_tourism_region?: boolean;
  similarity_score?: number;
  match_type?: string;
  match_source?: string;
  type?: string;
}

export interface LocationWithNames extends LocationResult {
  alternate_names: Array<{
    language: string;
    name: string;
    is_preferred: boolean;
  }>;
}

export interface LocationSearchParams {
  query: string;
  language?: string;
  limit?: number;
  country?: string;
  userCoordinates?: { lat: number; lng: number };
  minPopulation?: number;
}