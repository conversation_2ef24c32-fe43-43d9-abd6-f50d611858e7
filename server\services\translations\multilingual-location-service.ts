// Comprehensive multilingual location translation service
// Uses multiple data sources for robust translation coverage

import { cacheBackend } from '../../dal/cache/redisCache.js';

export interface MultilingualLocationResult {
  name: string;
  localizedName: string;
  displayName: string;
  translationSource: 'cldr' | 'nominatim' | 'regional' | 'fallback';
  confidence: number;
}

export interface NominatimNameDetails {
  name?: string;
  'name:en'?: string;
  'name:es'?: string;
  'name:fr'?: string;
  'name:de'?: string;
  'name:it'?: string;
  'name:nl'?: string;
  'name:ca'?: string;
  'name:eu'?: string;
  [key: string]: string | undefined;
}

/**
 * Comprehensive multilingual location translation service
 * Combines multiple data sources for maximum coverage
 */
export class MultilingualLocationService {
  private nominatimCache = new Map<string, NominatimNameDetails>();
  private readonly CACHE_TTL = 24 * 60 * 60; // 24 hours
  private readonly NOMINATIM_DELAY = 1000; // 1 second rate limit
  private lastNominatimCall = 0;

  /**
   * Get the best translation for a location
   */
  async getLocationTranslation(
    locationName: string,
    countryCode: string,
    regionName?: string,
    locale: string = 'en',
    coordinates?: { lat: number, lng: number }
  ): Promise<MultilingualLocationResult> {
    // Try different translation sources in priority order
    
    // 1. Try Nominatim API for city-level translations
    if (coordinates) {
      const nominatimResult = await this.getNominatimTranslation(
        locationName, 
        coordinates, 
        locale
      );
      if (nominatimResult) {
        return {
          name: locationName,
          localizedName: nominatimResult,
          displayName: this.buildDisplayName(nominatimResult, regionName, countryCode, locale),
          translationSource: 'nominatim',
          confidence: 0.9
        };
      }
    }

    // 2. Try cached translations (from previous Nominatim calls)
    const cachedTranslation = await this.getCachedTranslation(locationName, locale);
    if (cachedTranslation) {
      return {
        name: locationName,
        localizedName: cachedTranslation,
        displayName: this.buildDisplayName(cachedTranslation, regionName, countryCode, locale),
        translationSource: 'nominatim',
        confidence: 0.8
      };
    }

    // 3. Try built-in regional translations
    const regionalTranslation = this.getRegionalTranslation(locationName, locale);
    if (regionalTranslation !== locationName) {
      return {
        name: locationName,
        localizedName: regionalTranslation,
        displayName: this.buildDisplayName(regionalTranslation, regionName, countryCode, locale),
        translationSource: 'regional',
        confidence: 0.7
      };
    }

    // 4. Fallback to original name with translated regions/country
    return {
      name: locationName,
      localizedName: locationName,
      displayName: this.buildDisplayName(locationName, regionName, countryCode, locale),
      translationSource: 'fallback',
      confidence: 0.6
    };
  }

  /**
   * Get translation from OpenStreetMap Nominatim API
   */
  private async getNominatimTranslation(
    locationName: string,
    coordinates: { lat: number, lng: number },
    locale: string
  ): Promise<string | null> {
    try {
      // Rate limiting
      const now = Date.now();
      const timeSinceLastCall = now - this.lastNominatimCall;
      if (timeSinceLastCall < this.NOMINATIM_DELAY) {
        await new Promise(resolve => setTimeout(resolve, this.NOMINATIM_DELAY - timeSinceLastCall));
      }
      this.lastNominatimCall = Date.now();

      // Check cache first
      const cacheKey = `nominatim:${coordinates.lat}:${coordinates.lng}`;
      const cached = await cacheBackend.get(cacheKey);
      if (cached && typeof cached === 'string') {
        const nameDetails = JSON.parse(cached) as NominatimNameDetails;
        return this.extractNameFromDetails(nameDetails, locale);
      }

      // Call Nominatim API
      const url = `https://nominatim.openstreetmap.org/reverse?` +
        `lat=${coordinates.lat}&lon=${coordinates.lng}&` +
        `format=json&namedetails=1&accept-language=${locale}`;

      const response = await fetch(url, {
        headers: {
          'User-Agent': 'VillaWise-Search/1.0 (vacation-rental-platform)'
        }
      });

      if (!response.ok) {
        console.warn(`[NOMINATIM] API error: ${response.status} ${response.statusText}`);
        return null;
      }

      const data = await response.json();
      const nameDetails = data.namedetails as NominatimNameDetails;

      if (nameDetails) {
        // Cache the result
        await cacheBackend.set(cacheKey, JSON.stringify(nameDetails), this.CACHE_TTL);
        return this.extractNameFromDetails(nameDetails, locale);
      }

      return null;
    } catch (error) {
      console.warn(`[NOMINATIM] Translation failed for ${locationName}:`, error);
      return null;
    }
  }

  /**
   * Extract localized name from Nominatim name details
   */
  private extractNameFromDetails(nameDetails: NominatimNameDetails, locale: string): string | null {
    // Try exact locale match first
    const localeKey = `name:${locale}`;
    if (nameDetails[localeKey]) {
      return nameDetails[localeKey]!;
    }

    // Try fallback hierarchy
    const fallbacks = this.getLocaleFallbacks(locale);
    for (const fallbackLocale of fallbacks) {
      const fallbackKey = `name:${fallbackLocale}`;
      if (nameDetails[fallbackKey]) {
        return nameDetails[fallbackKey]!;
      }
    }

    // Default name
    return nameDetails.name || null;
  }

  /**
   * Get cached translation from previous API calls
   */
  private async getCachedTranslation(locationName: string, locale: string): Promise<string | null> {
    const cacheKey = `location_translation:${locationName}:${locale}`;
    const cached = await cacheBackend.get(cacheKey);
    return (cached && typeof cached === 'string') ? cached : null;
  }

  /**
   * Set cached translation
   */
  private async setCachedTranslation(locationName: string, locale: string, translation: string): Promise<void> {
    const cacheKey = `location_translation:${locationName}:${locale}`;
    await cacheBackend.set(cacheKey, translation, this.CACHE_TTL);
  }

  /**
   * Get regional translation (existing service)
   */
  private getRegionalTranslation(locationName: string, locale: string): string {
    try {
      // Dynamic import to avoid circular dependencies
      const regionalTranslations = require('../geonames/regional-translations.js');
      return regionalTranslations.getRegionTranslation(locationName, locale);
    } catch (error) {
      console.warn('[MULTILINGUAL] Regional translation failed:', error);
      return locationName;
    }
  }

  /**
   * Build complete display name with translated components
   */
  private buildDisplayName(
    cityName: string,
    regionName?: string,
    countryCode?: string,
    locale: string = 'en'
  ): string {
    const parts = [cityName];

    if (regionName) {
      const translatedRegion = this.getRegionalTranslation(regionName, locale);
      parts.push(translatedRegion);
    }

    if (countryCode) {
      const translatedCountry = this.getCountryTranslation(countryCode, locale);
      parts.push(translatedCountry);
    }

    return parts.join(', ');
  }

  /**
   * Get country translation using CLDR data
   */
  private getCountryTranslation(countryCode: string, locale: string): string {
    try {
      // Dynamic import to avoid circular dependencies
      const regionalTranslations = require('../geonames/regional-translations.js');
      return regionalTranslations.getCountryTranslation(countryCode, locale);
    } catch (error) {
      console.warn('[MULTILINGUAL] Country translation failed:', error);
      return countryCode;
    }
  }

  /**
   * Get locale fallback hierarchy
   */
  private getLocaleFallbacks(locale: string): string[] {
    const fallbacks: { [key: string]: string[] } = {
      'nl': ['en', 'de', 'fr'],
      'de': ['en', 'fr', 'nl'],
      'fr': ['en', 'de', 'nl'],
      'it': ['en', 'fr', 'de'],
      'es': ['en', 'ca', 'fr'],
      'ca': ['es', 'en', 'fr'],
      'eu': ['es', 'en', 'fr']
    };

    return fallbacks[locale] || ['en'];
  }

  /**
   * Batch translate multiple locations efficiently
   */
  async batchTranslateLocations(
    locations: Array<{
      name: string;
      countryCode: string;
      regionName?: string;
      coordinates?: { lat: number, lng: number };
    }>,
    locale: string
  ): Promise<MultilingualLocationResult[]> {
    const results: MultilingualLocationResult[] = [];

    for (const location of locations) {
      const result = await this.getLocationTranslation(
        location.name,
        location.countryCode,
        location.regionName,
        locale,
        location.coordinates
      );
      results.push(result);

      // Small delay between API calls to respect rate limits
      if (location.coordinates) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    return results;
  }

  /**
   * Get service health status
   */
  async getHealthStatus(): Promise<{
    nominatimAvailable: boolean;
    cacheAvailable: boolean;
    lastError?: string;
  }> {
    try {
      // Test Nominatim availability
      const testResponse = await fetch('https://nominatim.openstreetmap.org/status', {
        headers: {
          'User-Agent': 'VillaWise-Search/1.0 (health-check)'
        }
      });

      // Test cache availability
      const cacheTest = await cacheBackend.get('health_test');

      return {
        nominatimAvailable: testResponse.ok,
        cacheAvailable: true // If we got here, cache is working
      };
    } catch (error) {
      return {
        nominatimAvailable: false,
        cacheAvailable: false,
        lastError: (error as Error).message
      };
    }
  }
}

// Export singleton instance
export const multilingualLocationService = new MultilingualLocationService();