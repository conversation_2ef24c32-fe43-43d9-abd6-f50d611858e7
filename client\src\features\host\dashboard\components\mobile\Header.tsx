import React from 'react';
import { Button } from '@/components/ui/button';
import { Bell, Plus, Search } from 'lucide-react';
import { useTranslations } from '@/lib/translations';

interface MobileHeaderProps {
  activeTab: string;
  onAddProperty?: () => void;
  user?: any;
}

export const Header: React.FC<MobileHeaderProps> = ({
  activeTab,
  onAddProperty,
  user
}) => {
  const t = useTranslations('hostDashboard');

  const getTabTitle = (tab: string) => {
    switch (tab) {
      case 'dashboard': return t('navigation.dashboard');
      case 'bookings': return t('navigation.bookings');
      case 'properties': return t('navigation.properties');
      case 'messages': return t('navigation.messages');
      case 'guests': return t('navigation.guests');
      case 'earnings': return t('navigation.earnings');
      case 'analytics': return t('navigation.analytics');
      case 'tasks': return t('navigation.tasks');
      case 'inventory': return t('navigation.inventory');
      case 'settings': return t('navigation.settings');
      case 'help': return t('navigation.help');
      default: return t('title');
    }
  };

  return (
    <div className="sticky top-0 bg-white border-b border-gray-200 z-40 md:hidden">
      <div className="flex items-center justify-between px-4 py-3">
        <div className="flex items-center">
          <div>
            <h1 className="text-lg font-semibold text-gray-900">
              {getTabTitle(activeTab)}
            </h1>
            {user?.name && (
              <p className="text-sm text-gray-600">
                {t('greeting')}
              </p>
            )}
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          {activeTab === 'properties' && (
            <Button
              variant="ghost"
              size="icon"
              onClick={onAddProperty}
              className="h-8 w-8 text-primary"
            >
              <Plus className="h-4 w-4" />
            </Button>
          )}
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 text-gray-600"
          >
            <Search className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 text-gray-600 relative"
          >
            <Bell className="h-4 w-4" />
            {/* Notification dot */}
            <span className="absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full"></span>
          </Button>
        </div>
      </div>
    </div>
  );
};