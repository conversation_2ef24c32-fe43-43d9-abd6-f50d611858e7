
import { supabase } from '../../supabase'
import { memoryCache } from '../cache/memoryCache'

export interface UserSession {
  userId: string
  email: string
  role: 'admin' | 'host' | 'guest'
  isAuthenticated: boolean
}

export const verifySession = async (token: string): Promise<UserSession | null> => {
  if (!token) return null
  
  // Check cache first
  const cacheKey = `session:${token}`
  const cached = memoryCache.get(cacheKey) as UserSession | null
  if (cached) return cached
  
  try {
    const { data: { user }, error } = await supabase.auth.getUser(token)
    
    if (error || !user) {
      return null
    }
    
    const session: UserSession = {
      userId: user.id,
      email: user.email || '',
      role: user.user_metadata?.role || 'guest',
      isAuthenticated: true
    }
    
    // Cache for 5 minutes
    memoryCache.set(cacheKey, session, 300)
    
    return session
  } catch (error) {
    console.error('Session verification failed:', error)
    return null
  }
}

export const getCurrentUser = async (authHeader?: string): Promise<UserSession | null> => {
  if (!authHeader) return null
  
  const token = authHeader.replace('Bearer ', '')
  return await verifySession(token)
}

export const requireAuth = async (authHeader?: string): Promise<UserSession> => {
  const user = await getCurrentUser(authHeader)
  
  if (!user || !user.isAuthenticated) {
    throw new Error('Authentication required')
  }
  
  return user
}

export const requireRole = async (
  authHeader: string | undefined, 
  requiredRole: UserSession['role']
): Promise<UserSession> => {
  const user = await requireAuth(authHeader)
  
  if (user.role !== requiredRole && user.role !== 'admin') {
    throw new Error(`${requiredRole} role required`)
  }
  
  return user
}

export const invalidateUserSession = (token: string): void => {
  const cacheKey = `session:${token}`
  memoryCache.delete(cacheKey)
}