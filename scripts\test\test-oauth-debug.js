#!/usr/bin/env node

/**
 * OAuth Debug Test Script
 * This script simulates the OAuth login flow to identify where the issue occurs
 */

const testOAuthFlow = async () => {
  console.log("🔍 OAuth Flow Debug Test Started");
  console.log("========================================");
  
  try {
    // Step 1: Test redirect URL detection
    console.log("\n📍 Step 1: Testing redirect URL detection...");
    const redirectResponse = await fetch("http://localhost:5000/api/auth/test-redirect");
    const redirectData = await redirectResponse.json();
    console.log("✅ Redirect URL detection:", redirectData.redirectUrl);
    
    // Step 2: Test Google OAuth initiation
    console.log("\n🔗 Step 2: Testing Google OAuth initiation...");
    const oauthResponse = await fetch("http://localhost:5000/api/auth/google");
    const oauthData = await oauthResponse.json();
    
    if (oauthData.success) {
      console.log("✅ Google OAuth URL generated successfully");
      console.log("🔗 OAuth URL:", oauthData.url);
    } else {
      console.log("❌ Google OAuth initiation failed:", oauthData.message);
    }
    
    // Step 3: Test OAuth user validation endpoint (with invalid token)
    console.log("\n🔐 Step 3: Testing OAuth user validation endpoint...");
    const validationResponse = await fetch("http://localhost:5000/api/auth/oauth-user", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer invalid_test_token"
      }
    });
    
    const validationData = await validationResponse.json();
    console.log("📝 Validation response status:", validationResponse.status);
    console.log("📝 Validation response:", validationData);
    
    if (validationResponse.status === 401) {
      console.log("✅ OAuth validation endpoint correctly rejects invalid tokens");
    } else {
      console.log("❌ Unexpected validation response");
    }
    
    // Step 4: Test current user endpoint
    console.log("\n👤 Step 4: Testing current user endpoint...");
    const currentUserResponse = await fetch("http://localhost:5000/api/auth/me");
    const currentUserData = await currentUserResponse.json();
    
    console.log("📝 Current user response status:", currentUserResponse.status);
    console.log("📝 Current user response:", currentUserData);
    
    if (currentUserResponse.status === 401) {
      console.log("✅ Current user endpoint correctly requires authentication");
    } else {
      console.log("❌ Unexpected current user response");
    }
    
  } catch (error) {
    console.error("❌ OAuth flow test failed:", error);
  }
  
  console.log("\n========================================");
  console.log("🏁 OAuth Flow Debug Test Completed");
  console.log("\n💡 Next Steps:");
  console.log("1. Try a real Google OAuth login via the browser");
  console.log("2. Check the browser console for OAuth callback logs");
  console.log("3. Monitor server logs during the OAuth flow");
  console.log("4. Look for specific error messages in the frontend OAuth callback");
};

testOAuthFlow();