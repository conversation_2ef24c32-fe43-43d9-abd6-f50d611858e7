import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useTranslations } from '@/lib/translations';
import { MapPin, Plus, Eye } from 'lucide-react';
import { PropertyWizardModal } from '@/features/host/properties/components/PropertyWizardModal';

interface PropertiesContentProps {
  hostProperties: any[];
  bookings: any[];
}

const PropertiesContent: React.FC<PropertiesContentProps> = ({ 
  hostProperties, 
  bookings 
}) => {
  const t = useTranslations('hostDashboard');
  const [isWizardOpen, setIsWizardOpen] = useState(false);

  return (
    <div className="p-6 w-full max-w-full overflow-x-hidden">
      <div className="mb-6 w-full">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4">{t('properties.title')}</h2>
        <Button variant="outline" onClick={() => setIsWizardOpen(true)} className="w-full sm:w-auto">
          <Plus className="h-4 w-4 mr-2" />
          {t('properties.newProperty')}
        </Button>
      </div>

      {hostProperties.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 w-full">
          {hostProperties.map((property: any) => (
            <Card key={property.id} className="cursor-pointer hover:shadow-lg transition-shadow w-full max-w-full">
              <CardContent className="p-4 w-full">
                <div className="aspect-video bg-gray-200 rounded-lg mb-4 flex items-center justify-center w-full">
                  <MapPin className="h-8 w-8 text-gray-400" />
                </div>
                <h3 className="font-semibold text-lg mb-2 break-words">{property.title}</h3>
                <p className="text-gray-600 text-sm mb-4 break-words">{property.location}</p>
                <div className="flex items-center justify-between flex-wrap gap-2">
                  <Badge variant="outline" className="bg-blue-50 text-blue-700 text-xs">
                    {bookings.filter((b: any) => b.property_id === property.id).length} {t('properties.bookings')}
                  </Badge>
                  <Button variant="outline" size="sm" className="text-xs">
                    <Eye className="h-4 w-4 mr-1" />
                    View
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center py-12 w-full">
          <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600 break-words">{t('properties.noProperties')}</p>
          <Button variant="outline" className="mt-4 w-full sm:w-auto" onClick={() => setIsWizardOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            {t('properties.addProperty')}
          </Button>
        </div>
      )}
      
      {/* Property Wizard Modal */}
      <PropertyWizardModal 
        open={isWizardOpen} 
        onOpenChange={setIsWizardOpen}
      />
    </div>
  );
};

export default PropertiesContent;