# Testing Strategy

## Overview

Comprehensive testing strategy for VillaWise authentication flows (OAuth, email/password registration), property management, and user workflows with 60% unit tests, 30% integration tests, and 10% E2E tests.

## Testing Stack

- **Unit Testing**: Vitest + React Testing Library
- **Integration Testing**: Supertest + MSW (Mock Service Worker)
- **E2E Testing**: Playwright
- **Mocking**: MSW for API mocking
- **Coverage**: NYC/Istanbul for coverage reports

## Setup & Installation

### Dependencies

```bash
npm install --save-dev \
  vitest \
  @testing-library/react \
  @testing-library/jest-dom \
  @testing-library/user-event \
  @playwright/test \
  msw \
  supertest \
  @types/supertest
```

### Configuration

```typescript
// vitest.config.ts
import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react';

export default defineConfig({
  plugins: [react()],
  test: {
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.ts'],
    globals: true,
  },
  resolve: {
    alias: { '@': path.resolve(__dirname, './src') },
  },
});
```

```typescript
// src/test/setup.ts
import '@testing-library/jest-dom';
import { server } from './mocks/server';

beforeAll(() => server.listen());
afterEach(() => server.resetHandlers());
afterAll(() => server.close());
```

## Authentication Testing

### OAuth Testing Challenges

OAuth flows present unique challenges:
- External dependencies (Google, Facebook)
- Dynamic consent screens
- Rate limiting and security measures
- Token expiration

### OAuth Testing Solutions

#### Google OAuth Testing
```typescript
describe('Google OAuth Service', () => {
  test('extracts user data from Google profile', () => {
    const googleUser = {
      sub: 'google-oauth2|123456789',
      email: '<EMAIL>',
      email_verified: true,
      name: 'Test User',
      given_name: 'Test',
      family_name: 'User',
      picture: 'https://example.com/avatar.jpg',
      locale: 'en'
    };

    const result = extractGoogleUserData(googleUser);
    
    expect(result).toEqual({
      id: 'google-oauth2|123456789',
      email: '<EMAIL>',
      username: 'test',
      first_name: 'Test',
      last_name: 'User',
      avatar_url: 'https://example.com/avatar.jpg',
      oauth_provider: 'google',
      oauth_id: 'google-oauth2|123456789',
      locale: 'en'
    });
  });
});
```

#### Integration Testing
```typescript
describe('Google OAuth Integration', () => {
  test('creates user from Google OAuth callback', async () => {
    const mockOAuthUser = {
      id: 'google-oauth2|test-123',
      email: '<EMAIL>',
      user_metadata: {
        name: 'Test User',
        picture: 'https://example.com/avatar.jpg'
      },
      app_metadata: { provider: 'google' }
    };

    jest.spyOn(supabase.auth, 'getUser').mockResolvedValue({
      data: { user: mockOAuthUser },
      error: null
    });

    const response = await request(app)
      .post('/api/auth/oauth/google')
      .set('Authorization', 'Bearer mock-token')
      .expect(200);

    expect(response.body.success).toBe(true);
    expect(response.body.user.email).toBe('<EMAIL>');
  });
});
```

### Email/Password Registration Testing

#### Form Validation Testing
```typescript
describe('RegistrationForm', () => {
  test('validates email format', async () => {
    const user = userEvent.setup();
    render(<RegistrationForm />);
    
    const emailInput = screen.getByLabelText(/email/i);
    await user.type(emailInput, 'invalid-email');
    await user.tab();
    
    await waitFor(() => {
      expect(screen.getByText(/please enter a valid email/i)).toBeInTheDocument();
    });
  });

  test('enforces password strength requirements', async () => {
    const user = userEvent.setup();
    render(<RegistrationForm />);
    
    const passwordInput = screen.getByLabelText(/password/i);
    await user.type(passwordInput, 'weak');
    await user.tab();
    
    await waitFor(() => {
      expect(screen.getByText(/password must be at least 8 characters/i)).toBeInTheDocument();
    });
  });
});
```

#### API Testing
```typescript
describe('Registration API', () => {
  test('POST /api/auth/register creates new user', async () => {
    const userData = {
      email: '<EMAIL>',
      username: 'newuser',
      password: 'StrongPass123!',
      firstName: 'New',
      lastName: 'User'
    };

    const response = await request(app)
      .post('/api/auth/register')
      .send(userData)
      .expect(201);

    expect(response.body).toEqual({
      success: true,
      message: expect.stringContaining('Please check your email'),
      requiresEmailConfirmation: true,
      user: expect.objectContaining({
        email: '<EMAIL>',
        username: 'newuser',
        email_confirmed: false
      })
    });
  });
});
```

## E2E Testing

### Authentication Flow Testing
```typescript
// __tests__/e2e/auth/login.spec.ts
import { test, expect } from '@playwright/test';

test.describe('Authentication Flows', () => {
  test('user can register with email and password', async ({ page }) => {
    await page.goto('/register');
    
    await page.fill('[data-testid="email"]', '<EMAIL>');
    await page.fill('[data-testid="username"]', 'newuser');
    await page.fill('[data-testid="password"]', 'StrongPass123!');
    await page.fill('[data-testid="confirm-password"]', 'StrongPass123!');
    await page.fill('[data-testid="first-name"]', 'Test');
    await page.fill('[data-testid="last-name"]', 'User');
    
    await page.click('[data-testid="register-button"]');
    
    await expect(page).toHaveURL('/login');
    await expect(page.locator('[data-testid="success-banner"]')).toContainText('Please check your email');
  });

  test('user can login with valid credentials', async ({ page }) => {
    await page.goto('/login');
    
    await page.fill('[data-testid="email"]', '<EMAIL>');
    await page.fill('[data-testid="password"]', 'password123');
    await page.click('[data-testid="login-button"]');
    
    await expect(page).toHaveURL('/dashboard');
    await expect(page.locator('[data-testid="user-profile"]')).toBeVisible();
  });
});
```

## Mock Service Worker (MSW)

### API Mocking Setup
```typescript
// src/test/mocks/server.ts
import { setupServer } from 'msw/node';
import { rest } from 'msw';

export const handlers = [
  rest.post('/api/auth/register', (req, res, ctx) => {
    return res(ctx.json({ success: true, user: { id: '123' } }));
  }),
  
  rest.post('/api/auth/login', (req, res, ctx) => {
    return res(ctx.json({ success: true, token: 'mock-token' }));
  }),
  
  rest.post('/api/auth/oauth/google', (req, res, ctx) => {
    return res(ctx.json({ success: true, user: { id: '123' } }));
  }),
];

export const server = setupServer(...handlers);
```

### Test Utilities

```typescript
// test/helpers/auth-helpers.ts
export const createTestOAuthUser = async (userData: {
  email: string;
  oauth_provider: string;
  oauth_id?: string;
  first_name?: string;
  last_name?: string;
}) => {
  const user = await storage.createUser({
    id: userData.oauth_id || `${userData.oauth_provider}-${Date.now()}`,
    email: userData.email,
    username: userData.email.split('@')[0],
    first_name: userData.first_name || null,
    last_name: userData.last_name || null,
    oauth_provider: userData.oauth_provider,
    oauth_id: userData.oauth_id || userData.email
  });
  
  return user;
};

export const createTestUserData = (overrides = {}) => ({
  email: '<EMAIL>',
  username: 'testuser',
  password: 'TestPass123!',
  firstName: 'Test',
  lastName: 'User',
  isHost: false,
  ...overrides
});
```

## Test Commands

```bash
# Run all tests
npm test

# Run authentication tests only
npm run test:auth

# Run with coverage
npm run test:coverage

# Run E2E tests
npm run test:e2e

# Run E2E tests with UI
npm run test:e2e:ui

# Run specific test file
npm test -- LoginForm.test.tsx
```

## CI/CD Integration

```yaml
# .github/workflows/test.yml
name: Test Suite
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
      
      - run: npm ci
      - run: npm run test:coverage
      - run: npm run test:e2e
      
      - name: Upload coverage
        uses: codecov/codecov-action@v4
        with:
          file: ./coverage/lcov.info
```

## Package.json Scripts

```json
{
  "scripts": {
    "test": "vitest",
    "test:ui": "vitest --ui",
    "test:coverage": "vitest --coverage",
    "test:unit": "vitest --testPathPattern=unit",
    "test:integration": "vitest --testPathPattern=integration",
    "test:e2e": "playwright test",
    "test:e2e:ui": "playwright test --ui",
    "test:auth": "vitest --testPathPattern=auth"
  }
}
```

This comprehensive testing strategy ensures VillaWise authentication flows are thoroughly tested across all levels, providing confidence in the security and reliability of the authentication system.