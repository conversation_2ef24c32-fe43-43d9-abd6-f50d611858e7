import { Request, Response } from 'express';
import { Logger } from '../../utils/logger';
import { messageService } from '../services/messageService';
import { templateService } from '../services/templateService';
import { realtimeService } from '../services/realtimeService';
import { z } from 'zod';

// Validation schemas
const getMessagesSchema = z.object({
  limit: z.coerce.number().min(1).max(100).default(50),
  offset: z.coerce.number().min(0).default(0),
  beforeMessageId: z.string().optional()
});

const sendMessageSchema = z.object({
  content: z.string().min(1).max(5000),
  messageType: z.enum(['text', 'template', 'system', 'automated']).default('text'),
  templateId: z.string().optional(),
  scheduledFor: z.string().datetime().optional()
});

const editMessageSchema = z.object({
  content: z.string().min(1).max(5000)
});

const flagMessageSchema = z.object({
  reason: z.enum(['inappropriate', 'spam', 'harassment', 'outside_platform']),
  description: z.string().optional()
});

const searchMessagesSchema = z.object({
  query: z.string().min(1),
  conversationId: z.string().optional(),
  limit: z.coerce.number().min(1).max(50).default(20),
  offset: z.coerce.number().min(0).default(0)
});

const typingIndicatorSchema = z.object({
  isTyping: z.boolean()
});

export class MessageController {
  
  /**
   * GET /api/messaging/conversations/:conversationId/messages
   * Get messages for a conversation
   */
  async getMessages(req: Request, res: Response): Promise<void> {
    const startTime = Date.now();
    try {
      const userId = (req as any).userId;
      const conversationId = req.params.conversationId;
      
      if (!conversationId) {
        res.status(400).json({
          success: false,
          message: 'Conversation ID is required'
        });
        return;
      }
      
      const validation = getMessagesSchema.safeParse(req.query);
      if (!validation.success) {
        res.status(400).json({
          success: false,
          message: 'Invalid query parameters',
          errors: validation.error.errors
        });
        return;
      }
      
      const options = validation.data;
      
      const result = await messageService.getMessages(
        conversationId,
        userId,
        options
      );
      
      Logger.api('GET', `/api/messaging/conversations/${conversationId}/messages`, 200, Date.now() - startTime);
      res.json({
        success: true,
        data: result.messages,
        pagination: {
          limit: options.limit,
          offset: options.offset,
          hasMore: result.hasMore
        }
      });
    } catch (error) {
      Logger.error('MessageController.getMessages error:', error);
      Logger.api('GET', `/api/messaging/conversations/${req.params.conversationId}/messages`, 500, Date.now() - startTime);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch messages',
        error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
      });
    }
  }
  
  /**
   * POST /api/messaging/conversations/:conversationId/messages
   * Send a new message
   */
  async sendMessage(req: Request, res: Response): Promise<void> {
    const startTime = Date.now();
    try {
      const userId = (req as any).userId;
      const userType = (req as any).user?.user_metadata?.role || 'guest';
      const conversationId = req.params.conversationId;
      
      if (!conversationId) {
        res.status(400).json({
          success: false,
          message: 'Conversation ID is required'
        });
        return;
      }
      
      const validation = sendMessageSchema.safeParse(req.body);
      if (!validation.success) {
        res.status(400).json({
          success: false,
          message: 'Invalid message data',
          errors: validation.error.errors
        });
        return;
      }
      
      const { content, messageType, templateId, scheduledFor } = validation.data;
      
      // Process template if provided
      let processedContent = content;
      if (templateId && userType === 'host') {
        try {
          const templateResult = await templateService.processTemplate(
            templateId,
            userId,
            req.body.variables || {}
          );
          processedContent = templateResult.content;
        } catch (templateError) {
          Logger.error('Template processing error:', templateError);
          res.status(400).json({
            success: false,
            message: 'Failed to process template'
          });
          return;
        }
      }
      
      const message = await messageService.sendMessage({
        conversationId,
        senderId: userId,
        senderType: userType === 'host' ? 'host' : 'guest',
        content: processedContent,
        messageType,
        templateId,
        scheduledFor
      });
      
      Logger.api('POST', `/api/messaging/conversations/${conversationId}/messages`, 201, Date.now() - startTime);
      res.status(201).json({
        success: true,
        data: message,
        message: 'Message sent successfully'
      });
    } catch (error) {
      Logger.error('MessageController.sendMessage error:', error);
      Logger.api('POST', `/api/messaging/conversations/${req.params.conversationId}/messages`, 500, Date.now() - startTime);
      res.status(500).json({
        success: false,
        message: 'Failed to send message',
        error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
      });
    }
  }
  
  /**
   * PUT /api/messaging/messages/:messageId
   * Edit a message
   */
  async editMessage(req: Request, res: Response): Promise<void> {
    const startTime = Date.now();
    try {
      const userId = (req as any).userId;
      const messageId = req.params.messageId;
      
      if (!messageId) {
        res.status(400).json({
          success: false,
          message: 'Message ID is required'
        });
        return;
      }
      
      const validation = editMessageSchema.safeParse(req.body);
      if (!validation.success) {
        res.status(400).json({
          success: false,
          message: 'Invalid message content',
          errors: validation.error.errors
        });
        return;
      }
      
      const { content } = validation.data;
      
      const editedMessage = await messageService.editMessage(
        messageId,
        userId,
        content
      );
      
      if (!editedMessage) {
        res.status(404).json({
          success: false,
          message: 'Message not found, unauthorized, or edit time limit exceeded'
        });
        return;
      }
      
      Logger.api('PUT', `/api/messaging/messages/${messageId}`, 200, Date.now() - startTime);
      res.json({
        success: true,
        data: editedMessage,
        message: 'Message edited successfully'
      });
    } catch (error) {
      Logger.error('MessageController.editMessage error:', error);
      Logger.api('PUT', `/api/messaging/messages/${req.params.messageId}`, 500, Date.now() - startTime);
      res.status(500).json({
        success: false,
        message: 'Failed to edit message',
        error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
      });
    }
  }
  
  /**
   * DELETE /api/messaging/messages/:messageId
   * Delete/unsend a message
   */
  async deleteMessage(req: Request, res: Response): Promise<void> {
    const startTime = Date.now();
    try {
      const userId = (req as any).userId;
      const messageId = req.params.messageId;
      
      if (!messageId) {
        res.status(400).json({
          success: false,
          message: 'Message ID is required'
        });
        return;
      }
      
      const success = await messageService.deleteMessage(messageId, userId);
      
      if (!success) {
        res.status(404).json({
          success: false,
          message: 'Message not found, unauthorized, or delete time limit exceeded'
        });
        return;
      }
      
      Logger.api('DELETE', `/api/messaging/messages/${messageId}`, 200, Date.now() - startTime);
      res.json({
        success: true,
        message: 'Message deleted successfully'
      });
    } catch (error) {
      Logger.error('MessageController.deleteMessage error:', error);
      Logger.api('DELETE', `/api/messaging/messages/${req.params.messageId}`, 500, Date.now() - startTime);
      res.status(500).json({
        success: false,
        message: 'Failed to delete message',
        error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
      });
    }
  }
  
  /**
   * POST /api/messaging/messages/:messageId/flag
   * Flag a message for review
   */
  async flagMessage(req: Request, res: Response): Promise<void> {
    const startTime = Date.now();
    try {
      const userId = (req as any).userId;
      const userType = (req as any).user?.user_metadata?.role || 'guest';
      const messageId = req.params.messageId;
      
      if (!messageId) {
        res.status(400).json({
          success: false,
          message: 'Message ID is required'
        });
        return;
      }
      
      const validation = flagMessageSchema.safeParse(req.body);
      if (!validation.success) {
        res.status(400).json({
          success: false,
          message: 'Invalid flag data',
          errors: validation.error.errors
        });
        return;
      }
      
      const { reason, description } = validation.data;
      
      const success = await messageService.flagMessage(
        messageId,
        userId,
        userType === 'host' ? 'host' : 'guest',
        reason,
        description
      );
      
      if (!success) {
        res.status(404).json({
          success: false,
          message: 'Message not found or already flagged'
        });
        return;
      }
      
      Logger.api('POST', `/api/messaging/messages/${messageId}/flag`, 200, Date.now() - startTime);
      res.json({
        success: true,
        message: 'Message flagged for review'
      });
    } catch (error) {
      Logger.error('MessageController.flagMessage error:', error);
      Logger.api('POST', `/api/messaging/messages/${req.params.messageId}/flag`, 500, Date.now() - startTime);
      res.status(500).json({
        success: false,
        message: 'Failed to flag message',
        error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
      });
    }
  }
  
  /**
   * GET /api/messaging/search
   * Search messages across conversations
   */
  async searchMessages(req: Request, res: Response): Promise<void> {
    const startTime = Date.now();
    try {
      const userId = (req as any).userId;
      const userType = (req as any).user?.user_metadata?.role || 'guest';
      
      const validation = searchMessagesSchema.safeParse(req.query);
      if (!validation.success) {
        res.status(400).json({
          success: false,
          message: 'Invalid search parameters',
          errors: validation.error.errors
        });
        return;
      }
      
      const { query, conversationId, limit, offset } = validation.data;
      
      const result = await messageService.searchMessages(
        userId,
        userType === 'host' ? 'host' : 'guest',
        query,
        { conversationId, limit, offset }
      );
      
      Logger.api('GET', '/api/messaging/search', 200, Date.now() - startTime);
      res.json({
        success: true,
        data: result.messages,
        pagination: {
          total: result.total,
          limit,
          offset,
          hasMore: result.total > offset + limit
        }
      });
    } catch (error) {
      Logger.error('MessageController.searchMessages error:', error);
      Logger.api('GET', '/api/messaging/search', 500, Date.now() - startTime);
      res.status(500).json({
        success: false,
        message: 'Message search failed',
        error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
      });
    }
  }
  
  /**
   * POST /api/messaging/conversations/:conversationId/typing
   * Send typing indicator
   */
  async sendTypingIndicator(req: Request, res: Response): Promise<void> {
    const startTime = Date.now();
    try {
      const userId = (req as any).userId;
      const conversationId = req.params.conversationId;
      
      if (!conversationId) {
        res.status(400).json({
          success: false,
          message: 'Conversation ID is required'
        });
        return;
      }
      
      const validation = typingIndicatorSchema.safeParse(req.body);
      if (!validation.success) {
        res.status(400).json({
          success: false,
          message: 'Invalid typing indicator data',
          errors: validation.error.errors
        });
        return;
      }
      
      const { isTyping } = validation.data;
      
      await realtimeService.sendTypingIndicator(
        conversationId,
        userId,
        isTyping
      );
      
      Logger.api('POST', `/api/messaging/conversations/${conversationId}/typing`, 200, Date.now() - startTime);
      res.json({
        success: true,
        message: 'Typing indicator sent'
      });
    } catch (error) {
      Logger.error('MessageController.sendTypingIndicator error:', error);
      Logger.api('POST', `/api/messaging/conversations/${req.params.conversationId}/typing`, 500, Date.now() - startTime);
      res.status(500).json({
        success: false,
        message: 'Failed to send typing indicator',
        error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
      });
    }
  }
}

export const messageController = new MessageController();