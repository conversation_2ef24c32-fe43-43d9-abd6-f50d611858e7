interface CacheEntry {
  data: unknown;
  timestamp: number;
  ttl: number;
}

class SafeMemoryCache {
  private cache = new Map<string, CacheEntry>();
  private maxSize = 1000; // Maximum entries
  private maxMemory = 50 * 1024 * 1024; // 50MB limit

  set(key: string, value: unknown, ttl: number = 3600): void {
    // Check memory limits and evict if necessary
    this.checkLimitsAndEvict();

    this.cache.set(key, {
      data: value,
      timestamp: Date.now(),
      ttl: ttl * 1000, // Convert to milliseconds
    });
  }

  get(key: string): unknown {
    const entry = this.cache.get(key);

    if (!entry) {
      return null;
    }

    // Check if entry has expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  delete(key: string): void {
    this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }

  invalidatePattern(pattern: string): void {
    const regex = new RegExp(pattern.replace("*", ".*"));
    for (const key of this.cache.keys()) {
      if (regex.test(key)) {
        this.cache.delete(key);
      }
    }
  }

  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      memoryUsage: this.getMemoryUsage(),
      maxMemory: this.maxMemory,
      hitRate: this.calculateHitRate(),
    };
  }

  private checkLimitsAndEvict(): void {
    // Memory limit check
    if (this.getMemoryUsage() > this.maxMemory) {
      this.evictOldest(Math.floor(this.cache.size * 0.1)); // Evict 10%
    }

    // Size limit check
    if (this.cache.size >= this.maxSize) {
      this.evictOldest(Math.floor(this.maxSize * 0.1)); // Evict 10%
    }
  }

  private evictOldest(count: number = 1): void {
    const entries = Array.from(this.cache.entries()).sort(
      ([, a], [, b]) => a.timestamp - b.timestamp
    );

    for (let i = 0; i < Math.min(count, entries.length); i++) {
      this.cache.delete(entries[i][0]);
    }
  }

  private getMemoryUsage(): number {
    return process.memoryUsage().heapUsed;
  }

  private calculateHitRate(): number {
    // Simple hit rate calculation - would need request tracking for accuracy
    return this.cache.size > 0 ? 0.85 : 0; // Placeholder
  }
}

// Global cache instance
export const memoryCache = new SafeMemoryCache();

// Cache wrapper function for DAL methods
export function withCache<T>(key: string, ttl: number = 3600) {
  return function (
    target: object,
    propertyName: string,
    descriptor: PropertyDescriptor
  ) {
    const method = descriptor.value;

    descriptor.value = async function (...args: unknown[]): Promise<T> {
      const cacheKey = `${key}:${JSON.stringify(args)}`;
      const cached = memoryCache.get(cacheKey);

      if (cached !== null) {
        return cached as T;
      }

      const result = await method.apply(this, args);
      memoryCache.set(cacheKey, result, ttl);

      return result;
    };
  };
}
