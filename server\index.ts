import "dotenv/config";
import express from "express";
import { errorHandler } from "./middleware/errorHandler";
import { registerRoutes } from "./routes";

// Enable Redis cache if Redis credentials are available
if (process.env.UPSTASH_REDIS_REST_URL && process.env.UPSTASH_REDIS_REST_TOKEN && !process.env.USE_REDIS_CACHE) {
  console.log('🔧 Auto-enabling Redis cache (Redis credentials detected)');
  process.env.USE_REDIS_CACHE = 'true';
}

// Debug current environment settings
console.log('🔍 Environment check at startup:');
console.log(`  - USE_REDIS_CACHE: "${process.env.USE_REDIS_CACHE}"`);
console.log(`  - UPSTASH_REDIS_REST_URL: ${process.env.UPSTASH_REDIS_REST_URL ? 'Present' : 'Missing'}`);
console.log(`  - UPSTASH_REDIS_REST_TOKEN: ${process.env.UPSTASH_REDIS_REST_TOKEN ? 'Present' : 'Missing'}`);

// Initialize cache backend early to see configuration logs
import { cacheBackend } from "./dal/cache/redisCache";

// GeoNames initialization
import { GeoNamesSyncService } from "./services/geonames/sync-service";

// Production-safe logging function
function log(message: string, source = "express") {
  const formattedTime = new Date().toLocaleTimeString("en-US", {
    hour: "numeric",
    minute: "2-digit",
    second: "2-digit",
    hour12: true,
  });

  console.log(`${formattedTime} [${source}] ${message}`);
}

// Override Vite configuration for Replit compatibility
if (process.env.NODE_ENV !== "production") {
  const replatDomain = process.env.REPLIT_DOMAINS;
  if (replatDomain) {
    // Support wildcard patterns for dynamic Replit domains
    process.env.VITE_ALLOWED_HOSTS = `${replatDomain},*.picard.replit.dev,*.replit.dev,localhost,127.0.0.1`;
  }
}

const app = express();
app.use(express.json());
app.use(express.urlencoded({ extended: false }));

// Request logging middleware
app.use((req, res, next) => {
  const start = Date.now();
  const path = req.path;
  let capturedJsonResponse:
    | Record<string, unknown>
    | unknown[]
    | string
    | number
    | boolean
    | null
    | undefined = undefined;

  const originalResJson = res.json;
  res.json = function (
    bodyJson:
      | Record<string, unknown>
      | unknown[]
      | string
      | number
      | boolean
      | null
      | undefined
  ) {
    capturedJsonResponse = bodyJson;
    return originalResJson.call(res, bodyJson);
  };

  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path.startsWith("/api")) {
      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }

      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "…";
      }

      log(logLine);
    }
  });

  next();
});

// Health check endpoint for Railway
app.get("/api/health", (req, res) => {
  res.status(200).json({
    status: "healthy",
    timestamp: new Date().toISOString(),
    version: "1.0.0",
    environment: process.env.NODE_ENV || "development",
  });
});

// Error handling middleware
app.use(errorHandler);

// Initialize GeoNames data on startup if needed
async function initializeGeoNames(): Promise<void> {
  if (process.env.NODE_ENV === 'production') {
    try {
      console.log('[STARTUP] Checking GeoNames data...');
      const syncService = new GeoNamesSyncService();
      const health = await syncService.getHealthStatus();
      
      // Check if database connection is working and has data
      const dbConnected = health.checks.find(c => c.name === 'database_connection')?.status === 'pass';
      const hasData = health.metrics && health.metrics.total_locations > 0;
      
      if (dbConnected && !hasData) {
        console.log('[STARTUP] 🔄 GeoNames database empty, starting initial sync...');
        
        // Start sync in background (non-blocking)
        syncService.performFullSync()
          .then(result => {
            console.log('[STARTUP] ✅ Initial GeoNames sync completed:', {
              locations: result.locationsProcessed,
              names: result.alternateNamesProcessed,
              duration: `${Math.round(result.processingTime / 1000)}s`
            });
          })
          .catch(error => {
            console.error('[STARTUP] ❌ Initial GeoNames sync failed:', error);
          });
      } else if (hasData) {
        console.log(`[STARTUP] ✅ GeoNames data present (${health.metrics?.total_locations || 0} locations)`);
      } else {
        console.log('[STARTUP] ⚠️ Database connection issue - skipping GeoNames sync');
      }
    } catch (error) {
      console.error('[STARTUP] ⚠️ GeoNames initialization check failed:', error);
      // Continue startup even if GeoNames check fails
    }
  }
}

registerRoutes(app).then(async (server) => {
  const PORT = parseInt(process.env.PORT || "5000", 10);
  
  server.listen(PORT, "0.0.0.0", async () => {
    log(`serving on port ${PORT}`, "express");
    
    // Initialize GeoNames after server starts
    await initializeGeoNames();
  });
});
