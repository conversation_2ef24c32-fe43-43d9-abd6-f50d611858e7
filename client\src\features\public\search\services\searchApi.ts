import { SearchFilters, SearchResponse, searchResponseSchema } from '../types';

class SearchService {
  async searchProperties(params: SearchFilters, signal?: AbortSignal): Promise<SearchResponse> {
    try {
      // Call backend search endpoint
      const queryParams = new URLSearchParams();
      if (params.location) queryParams.append('location', params.location);
      if (params.dateRange?.from) queryParams.append('checkIn', params.dateRange.from.toISOString());
      if (params.dateRange?.to) queryParams.append('checkOut', params.dateRange.to.toISOString());
      if (params.guests) {
        queryParams.append('adults', params.guests.adults.toString());
        queryParams.append('children', params.guests.children.toString());
        queryParams.append('infants', params.guests.infants.toString());
        queryParams.append('pets', params.guests.pets.toString());
      }
      if (params.priceRange) {
        queryParams.append('minPrice', params.priceRange[0].toString());
        queryParams.append('maxPrice', params.priceRange[1].toString());
      }
      if (params.propertyTypes?.length) {
        params.propertyTypes.forEach(type => queryParams.append('propertyTypes', type));
      }
      if (params.amenities?.length) {
        params.amenities.forEach(amenity => queryParams.append('amenities', amenity));
      }

      const response = await fetch(`/api/search?${queryParams.toString()}`, {
        signal // Pass the AbortSignal to fetch for request cancellation
      });
      
      if (!response.ok) {
        throw new Error(`Search failed: ${response.statusText}`);
      }
      
      return response.json();
    } catch (error) {
      // Re-throw the error, including AbortError for cancelled requests
      throw error;
    }
  }
}

export const searchService = new SearchService();