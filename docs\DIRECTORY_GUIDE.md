# VillaWise Directory Guide

This guide explains the purpose and contents of each directory in the VillaWise project.

## Root Directory Structure

```
villawise/
├── client/                     # Frontend React application
├── server/                     # Backend Express.js API
├── database/                   # Database schema, migrations, and utilities
├── docs/                       # Consolidated documentation (THIS FOLDER)
├── deployment/                 # Docker and Railway deployment configs
├── data/                       # GeoNames data files (excluded from Git)
├── shared/                     # Shared types and schemas
├── scripts/                    # Build and automation scripts
├── tools/                      # Development tools and utilities
├── configs/                    # Configuration files
└── .github/                    # GitHub workflows and templates
```

## Directory Purposes

### `/client/` - Frontend Application
- **Purpose**: React 18 + TypeScript frontend with Tailwind CSS
- **Key Features**: Feature-based architecture, mobile-responsive dashboards
- **Structure**: `src/features/` organized by public/auth/guest/host/admin

### `/server/` - Backend API
- **Purpose**: Express.js API with Supabase database integration
- **Key Features**: Authentication, caching, translation system
- **Structure**: Feature-organized controllers, DAL layer, middleware

### `/database/` - Database Management
- **Purpose**: PostgreSQL schema, migrations, and utilities
- **Contains**: 
  - `schema.sql` - Database structure
  - `migrations/` - Schema changes
  - `seeds/` - Sample data
  - `utils/` - Database helper scripts

### `/docs/` - Consolidated Documentation
- **Purpose**: All project documentation in one organized location
- **Policy**: No documentation files outside this folder
- **Key Files**: 
  - `CONSOLIDATED_DEPLOYMENT_GUIDE.md` - Complete deployment guide
  - `CONSOLIDATED_DEVELOPMENT_GUIDE.md` - Development workflow and practices

### `/deployment/` - Deployment Configuration
- **Purpose**: Docker and Railway deployment files
- **Contains**: Dockerfiles, docker-compose, Railway configs
- **Structure**: Railway-specific configs in `railway/` subfolder

### `/data/` - Data Files (Git Excluded)
- **Purpose**: GeoNames data files and large datasets
- **Important**: ALL files excluded from Git except `.gitkeep`
- **Size**: Can contain files up to 724MB (alternateNames.txt)

### `/shared/` - Shared Code
- **Purpose**: TypeScript types and Zod schemas shared between client/server
- **Key File**: `schema.ts` - Data model definitions

### `/scripts/` - Automation Scripts
- **Purpose**: Build scripts, testing utilities, deployment automation
- **Platform Support**: Windows batch files, Unix shell scripts

### `/tools/` - Development Tools
- **Purpose**: GitHub issue templates, development utilities
- **Contains**: Project management tools, automation scripts

## Documentation Policy

### Consolidated Structure
- **Primary Guides**: 2 comprehensive guides cover all deployment and development needs
- **Specialized Guides**: Feature-specific documentation (GeoNames, translations, caching)
- **No Root Docs**: All `.md` files must be in `/docs/` folder

### File Organization
- **CONSOLIDATED_*** files contain merged content from multiple sources
- **Specialized guides** cover specific technical implementations
- **Quick reference** files for immediate needs

## Quick Navigation

### For New Developers
1. Start with `/docs/GETTING_STARTED.md`
2. Read `/docs/CONSOLIDATED_DEVELOPMENT_GUIDE.md`
3. Review project architecture in `/docs/architecture/`

### For Deployment
1. Follow `/docs/CONSOLIDATED_DEPLOYMENT_GUIDE.md`
2. Check environment requirements
3. Configure Redis caching if needed

### For Feature Development
1. Review feature-specific guides in `/docs/`
2. Follow TypeScript and translation requirements
3. Update documentation when making architectural changes

This directory guide helps navigate the VillaWise project structure and find relevant documentation quickly.