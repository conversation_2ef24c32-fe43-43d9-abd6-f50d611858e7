import { Request, Response } from 'express';
import { z } from 'zod';
import { supabase } from '../../supabase';
import { Logger } from '../../utils/logger';
import { resolveImageUrls } from '../../utils/imageResolver';
import { cacheBackend } from '../../dal/cache/redisCache';

// Property schema matching frontend expectations
const propertySchema = z.object({
  id: z.string(),
  title: z.string(),
  hostType: z.string(),
  price: z.string(),
  rating: z.number(),
  reviewCount: z.number(),
  images: z.array(z.string()).nullable(),
  badge: z.string().optional(),
});

// Inspiration item schema
const inspirationItemSchema = z.object({
  id: z.string(),
  title: z.string(),
  subtitle: z.string(),
  image: z.string(),
  size: z.enum(['large', 'medium', 'small']),
});

// Inspiration section schema
const inspirationSectionSchema = z.object({
  id: z.string(),
  header: z.string(),
  items: z.array(inspirationItemSchema),
});

// Helper function to transform database properties to frontend format
function transformProperty(prop: any) {
  return {
    id: prop.id,
    title: prop.title,
    hostType: prop.host_type,
    price: prop.price,
    rating: prop.rating,
    reviewCount: prop.review_count,
    images: Array.isArray(prop.images) && prop.images.length > 0 ? prop.images : null,
    badge: prop.badge !== null ? prop.badge : undefined
  };
}

export class ContentController {
  async getPopularInSpain(req: Request, res: Response) {
    const startTime = Date.now();
    try {
      Logger.info('Popular Spain properties request received', { ip: req.ip });
      
      // Check cache first
      const cacheKey = 'content:popular-spain';
      const cached = await cacheBackend.get(cacheKey) as any[] | null;
      if (cached) {
        const responseTime = Date.now() - startTime;
        Logger.api('GET', '/api/content/popular-spain', 200, responseTime);
        return res.json(cached);
      }
      
      if (!supabase) {
        // Fallback to empty array when supabase is not available
        Logger.warn('Supabase client not available, returning empty properties');
        return res.json([]);
      }
      
      const { data: properties, error } = await supabase
        .from('content_properties')
        .select('*')
        .eq('category', 'popular-spain')
        .eq('is_active', true)
        .order('display_order');

      if (error) {
        throw error;
      }

      // Transform database response to frontend format
      const transformedProperties = (properties || []).map(transformProperty);
      const validatedProperties = z.array(propertySchema).parse(transformedProperties);

      // Cache for 1 hour (content doesn't change frequently)
      await cacheBackend.set(cacheKey, validatedProperties, 3600);

      const responseTime = Date.now() - startTime;
      Logger.info(`Returning ${transformedProperties.length} popular Spain properties`);
      Logger.api('GET', '/api/content/popular-spain', 200, responseTime);

      res.json(validatedProperties);
    } catch (error) {
      const responseTime = Date.now() - startTime;
      Logger.error('Error fetching popular Spain properties', error);
      Logger.api('GET', '/api/content/popular-spain', 500, responseTime);
      console.error('Error fetching popular properties in Spain:', error);
      res.status(500).json({ error: 'Failed to fetch properties' });
    }
  }

  async getNewInFrance(req: Request, res: Response) {
    try {
      // Check cache first
      const cacheKey = 'content:new-france';
      const cached = await cacheBackend.get(cacheKey) as any[] | null;
      if (cached) {
        return res.json(cached);
      }
      
      if (!supabase) {
        Logger.warn('Supabase client not available, returning empty properties');
        return res.json([]);
      }
      
      const { data: properties, error } = await supabase
        .from('content_properties')
        .select('*')
        .eq('category', 'new-france')
        .eq('is_active', true)
        .order('display_order');

      if (error) {
        throw error;
      }

      // Transform database response to frontend format
      const transformedProperties = (properties || []).map(transformProperty);
      const validatedProperties = z.array(propertySchema).parse(transformedProperties);

      // Cache for 1 hour
      await cacheBackend.set(cacheKey, validatedProperties, 3600);
      
      res.json(validatedProperties);
    } catch (error) {
      console.error('Error fetching new properties in France:', error);
      res.status(500).json({ error: 'Failed to fetch properties' });
    }
  }

  async getGuestFavorites(req: Request, res: Response) {
    try {
      // Check cache first
      const cacheKey = 'content:guest-favorites';
      const cached = await cacheBackend.get(cacheKey) as any[] | null;
      if (cached) {
        return res.json(cached);
      }
      
      if (!supabase) {
        Logger.warn('Supabase client not available, returning empty properties');
        return res.json([]);
      }
      
      const { data: properties, error } = await supabase
        .from('content_properties')
        .select('*')
        .eq('category', 'guest-favorites')
        .eq('is_active', true)
        .order('display_order');

      if (error) {
        throw error;
      }

      // Transform database response to frontend format
      const transformedProperties = (properties || []).map(transformProperty);
      const validatedProperties = z.array(propertySchema).parse(transformedProperties);

      // Cache for 1 hour
      await cacheBackend.set(cacheKey, validatedProperties, 3600);

      res.json(validatedProperties);
    } catch (error) {
      console.error('Error fetching guest favorite properties:', error);
      res.status(500).json({ error: 'Failed to fetch properties' });
    }
  }

  async getInspirations(req: Request, res: Response) {
    try {
      // Check cache first
      const cacheKey = 'content:inspirations';
      const cached = await cacheBackend.get(cacheKey) as any[] | null;
      if (cached) {
        return res.json(cached);
      }
      
      if (!supabase) {
        Logger.warn('Supabase client not available, returning empty inspirations');
        return res.json([]);
      }
      
      const { data: inspirations, error } = await supabase
        .from('inspiration_content')
        .select('*')
        .eq('is_active', true)
        .order('display_order');

      if (error) {
        throw error;
      }

      // Transform the database structure to match frontend expectations
      const sections = (inspirations || []).map(section => ({
        id: section.id,
        header: section.header,
        items: section.items.map((item: any, index: number) => ({
          id: `${section.id}-${index}`,
          title: item.title,
          subtitle: item.desc,
          image: item.image,
          size: index === 0 ? 'large' : 'medium' // First item large, rest medium
        }))
      }));

      const validatedSections = z.array(inspirationSectionSchema).parse(sections);
      
      // Cache for 2 hours (inspirations change less frequently)
      await cacheBackend.set(cacheKey, validatedSections, 7200);
      
      res.json(validatedSections);
    } catch (error) {
      console.error('Error fetching inspirations:', error);
      res.status(500).json({ error: 'Failed to fetch inspirations' });
    }
  }
}

export const contentController = new ContentController();