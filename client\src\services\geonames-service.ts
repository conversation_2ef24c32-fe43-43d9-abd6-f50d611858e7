// GeoNames API service for frontend
export interface LocationResult {
  id: string;
  geonames_id: number;
  name: string;
  local_name?: string;
  ascii_name?: string;
  country_code: string;
  admin1_code?: string;
  admin2_code?: string;
  feature_class: string;
  feature_code: string;
  coordinates: { lat: number; lng: number };
  latitude: number;
  longitude: number;
  population: number;
  elevation?: number;
  timezone?: string;
  popularity_score: number;
  property_count: number;
  distance_km?: number;
  relevance_score?: number;
}

export interface LocationWithNames extends LocationResult {
  alternate_names: Array<{
    language: string;
    name: string;
    is_preferred: boolean;
  }>;
}

export interface GeoNamesConfig {
  countries: Array<{
    code: string;
    name: string;
    languages: string[];
    minPopulation: number;
  }>;
  globalLanguages: string[];
  featureCodes: string[];
  supportedLanguages: string[];
  estimatedData: {
    totalLocations: string;
    totalNames: string;
    storageSize: string;
    syncTime: string;
  };
}

export interface SyncStatus {
  status: 'healthy' | 'degraded' | 'error';
  checks: Array<{
    name: string;
    status: 'pass' | 'fail';
    message?: string;
  }>;
  metrics: {
    total_locations: number;
    countries: number;
    total_names: number;
    languages: number;
    last_update: string;
  };
}

export class GeoNamesService {
  private baseUrl = '/api/public/geonames';

  // Public search methods
  async searchLocations(
    query: string,
    options: {
      language?: string;
      limit?: number;
      country?: string;
      userLocation?: { lat: number; lng: number };
      minPopulation?: number;
    } = {}
  ): Promise<LocationResult[]> {
    const params = new URLSearchParams({
      q: query,
      lang: options.language || 'en',
      limit: (options.limit || 10).toString(),
    });

    if (options.country) params.append('country', options.country);
    if (options.userLocation) {
      params.append('lat', options.userLocation.lat.toString());
      params.append('lng', options.userLocation.lng.toString());
    }
    if (options.minPopulation) {
      params.append('minPopulation', options.minPopulation.toString());
    }

    const response = await fetch(`${this.baseUrl}/search?${params}`, {
      headers: { 'Content-Type': 'application/json' },
    });

    if (!response.ok) {
      throw new Error(`Search failed: ${response.statusText}`);
    }

    const result = await response.json();
    return result.success ? result.data : [];
  }

  async autocomplete(
    query: string,
    options: {
      language?: string;
      limit?: number;
    } = {}
  ): Promise<LocationResult[]> {
    if (!query || query.length < 2) return [];

    const params = new URLSearchParams({
      q: query,
      lang: options.language || 'en',
      limit: (options.limit || 5).toString(),
    });

    const response = await fetch(`${this.baseUrl}/autocomplete?${params}`, {
      headers: { 'Content-Type': 'application/json' },
    });

    if (!response.ok) {
      throw new Error(`Autocomplete failed: ${response.statusText}`);
    }

    const result = await response.json();
    return result.success ? result.data : [];
  }

  async getLocationDetails(
    locationId: string,
    language = 'en'
  ): Promise<LocationWithNames | null> {
    const response = await fetch(`${this.baseUrl}/location/${locationId}?lang=${language}`, {
      headers: { 'Content-Type': 'application/json' },
    });

    if (!response.ok) {
      if (response.status === 404) return null;
      throw new Error(`Failed to get location details: ${response.statusText}`);
    }

    const result = await response.json();
    return result.success ? result.data : null;
  }

  async getPopularDestinations(
    options: {
      country?: string;
      language?: string;
      limit?: number;
    } = {}
  ): Promise<LocationResult[]> {
    const params = new URLSearchParams({
      lang: options.language || 'en',
      limit: (options.limit || 20).toString(),
    });

    if (options.country) params.append('country', options.country);

    const response = await fetch(`${this.baseUrl}/popular?${params}`, {
      headers: { 'Content-Type': 'application/json' },
    });

    if (!response.ok) {
      throw new Error(`Failed to get popular destinations: ${response.statusText}`);
    }

    const result = await response.json();
    return result.success ? result.data : [];
  }

  // Configuration and status methods
  async getConfiguration(): Promise<GeoNamesConfig> {
    const response = await fetch(`${this.baseUrl}/config`, {
      headers: { 'Content-Type': 'application/json' },
    });

    if (!response.ok) {
      throw new Error(`Failed to get configuration: ${response.statusText}`);
    }

    const result = await response.json();
    return result.data;
  }

  async getSyncStatus(): Promise<SyncStatus> {
    const response = await fetch(`${this.baseUrl}/admin/sync/status`, {
      headers: { 'Content-Type': 'application/json' },
    });

    if (!response.ok) {
      throw new Error(`Failed to get sync status: ${response.statusText}`);
    }

    const result = await response.json();
    return result.data;
  }

  async triggerSync(): Promise<{ message: string; estimatedDuration: string }> {
    const response = await fetch(`${this.baseUrl}/admin/sync`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
    });

    if (!response.ok) {
      throw new Error(`Failed to trigger sync: ${response.statusText}`);
    }

    const result = await response.json();
    return {
      message: result.message,
      estimatedDuration: result.estimatedDuration
    };
  }

  // Utility methods
  formatLocationName(location: LocationResult, language = 'en'): string {
    // Use local name if available, otherwise fall back to main name
    return location.local_name || location.name || location.ascii_name || `Location ${location.id}`;
  }

  formatLocationWithContext(location: LocationResult): string {
    const parts = [this.formatLocationName(location)];
    
    if (location.admin1_code && location.admin1_code !== location.name) {
      parts.push(location.admin1_code);
    }
    
    parts.push(location.country_code);
    
    return parts.join(', ');
  }

  isCapitalCity(location: LocationResult): boolean {
    return location.feature_code === 'P.PPLC';
  }

  isMajorCity(location: LocationResult): boolean {
    return ['P.PPLC', 'P.PPLA', 'P.PPLA2'].includes(location.feature_code) ||
           location.population > 100000;
  }

  getLocationTypeLabel(location: LocationResult): string {
    const typeMap: Record<string, string> = {
      'P.PPLC': 'Capital',
      'P.PPLA': 'Administrative Center',
      'P.PPLA2': 'Regional Center',
      'P.PPL': 'City',
      'A.ADM1': 'Region',
      'A.ADM2': 'Province'
    };

    return typeMap[location.feature_code] || 'Location';
  }
}

// Export singleton instance
export const geoNamesService = new GeoNamesService();