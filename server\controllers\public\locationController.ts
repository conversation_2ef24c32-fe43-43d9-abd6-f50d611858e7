import { Request, Response } from 'express';
import { z } from 'zod';
import { locationService } from '../../services/locationService';
import { GeoNamesSearchService } from '../../services/geonames/search-service';
import { LocationSearchService } from '../../services/geonames/location-search';
import { enhancedLocationSearchService } from '../../services/translations/enhanced-location-search';
import { Logger } from '../../utils/logger';
import { cacheBackend } from '../../dal/cache/redisCache';
import { multilingualLocationService } from '../../services/translations/multilingual-location-service';

// Request validation schemas
const autocompleteRequestSchema = z.object({
  q: z.string().min(1, 'Query is required'),
  limit: z.coerce.number().min(1).max(20).default(10),
  locale: z.string().optional().default('en'),
  type: z.string().optional(),
  country: z.string().optional(),
  category: z.string().optional(),
  fuzzy: z.coerce.boolean().default(true),
  fuzzyThreshold: z.coerce.number().min(0).max(1).default(0.25),
});

const locationByIdSchema = z.object({
  id: z.string().min(1, 'Location ID is required'),
});

export class LocationController {
  private geoNamesService: GeoNamesSearchService;

  constructor() {
    this.geoNamesService = new GeoNamesSearchService();
  }

  // Extract locale from Accept-Language header or query parameter
  private getRequestLocale(req: Request, queryLocale?: string): string {
    if (queryLocale && ['en', 'nl', 'es', 'ca', 'eu', 'fr', 'de', 'it'].includes(queryLocale)) {
      return queryLocale;
    }
    
    const acceptLanguage = req.get('Accept-Language');
    if (acceptLanguage) {
      // Parse Accept-Language header (e.g., "en-US,en;q=0.9,nl;q=0.8")
      const preferredLang = acceptLanguage.split(',')[0].split('-')[0].toLowerCase();
      if (['en', 'nl', 'es', 'ca', 'eu', 'fr', 'de', 'it'].includes(preferredLang)) {
        return preferredLang;
      }
    }
    
    return 'en'; // Default fallback
  }
  
  // Helper methods for enhanced location disambiguation
  private getRegionName(admin1Code: string): string {
    const spanishRegions: { [key: string]: string } = {
      'AN': 'Andalucía', 'AR': 'Aragón', 'AS': 'Asturias', 'IB': 'Baleares',
      'PV': 'País Vasco', 'CN': 'Canarias', 'CB': 'Cantabria', 'CM': 'Castilla-La Mancha',
      'CL': 'Castilla y León', 'CT': 'Cataluña', 'EX': 'Extremadura', 'GA': 'Galicia',
      'MD': 'Madrid', 'MC': 'Murcia', 'NC': 'Navarra', 'RI': 'La Rioja',
      'VC': 'Valencia', 'CE': 'Ceuta', 'ML': 'Melilla'
    };
    return spanishRegions[admin1Code] || admin1Code || '';
  }

  private getCountryName(countryCode: string): string {
    const countryNames: { [key: string]: string } = {
      'ES': 'España', 'FR': 'France', 'IT': 'Italia', 'PT': 'Portugal',
      'DE': 'Deutschland', 'GB': 'United Kingdom', 'US': 'United States', 'CA': 'Canada'
    };
    return countryNames[countryCode] || countryCode || '';
  }

  private createEnhancedDisplayName(location: any): string {
    let displayName = location.name;
    const regionParts: string[] = [];
    
    if (location.admin1_code) {
      const regionName = this.getRegionName(location.admin1_code);
      if (regionName && regionName !== location.name) {
        regionParts.push(regionName);
      }
    }
    
    const countryName = this.getCountryName(location.country_code);
    if (countryName) {
      regionParts.push(countryName);
    }
    
    if (regionParts.length > 0) {
      displayName += ', ' + regionParts.join(', ');
    }
    
    return displayName;
  }

  private getLocationTypeFromFeature(featureClass: string, featureCode: string): string {
    if (featureClass === 'P') {
      if (featureCode === 'PPLC') return 'capital';
      if (featureCode === 'PPLA') return 'admin_capital';
      if (featureCode === 'PPL') return 'city';
      return 'town';
    }
    if (featureClass === 'A') return 'region';
    if (featureClass === 'T') return 'terrain';
    return 'location';
  }

  /**
   * Enhance locations with multilingual translations
   */
  private async enhanceLocationsWithTranslations(locations: any[], locale: string): Promise<any[]> {
    const enhancedLocations = [];
    
    for (const location of locations) {
      try {
        // Get translation for the location
        const translation = await multilingualLocationService.getLocationTranslation(
          location.name,
          location.country_code || 'ES',
          location.region_name || this.getRegionName(location.admin1_code || ''),
          locale,
          location.latitude && location.longitude ? {
            lat: location.latitude,
            lng: location.longitude
          } : undefined
        );

        // Enhanced location with translation
        const enhanced = {
          ...location,
          localizedName: translation.localizedName,
          display_name: translation.displayName,
          translationSource: translation.translationSource,
          translationConfidence: translation.confidence
        };

        enhancedLocations.push(enhanced);
      } catch (error) {
        Logger.warn(`[TRANSLATION] Failed to translate ${location.name}:`, error);
        enhancedLocations.push(location); // Fallback to original
      }
    }
    
    return enhancedLocations;
  }
  /**
   * Location autocomplete search - Database first approach
   * Returns locations with coordinates from database
   */
  async autocomplete(req: Request, res: Response) {
    const startTime = Date.now();
    
    try {
      const params = autocompleteRequestSchema.parse(req.query);
      const requestLocale = this.getRequestLocale(req, params.locale);
      
      // Create cache key with locale for multi-language support
      const cacheKey = `location_autocomplete:${params.q.toLowerCase()}:${requestLocale}:${params.limit}:${params.fuzzyThreshold}`;
      
      // Check cache first
      try {
        const cachedResult = await cacheBackend.get(cacheKey);
        if (cachedResult) {
          Logger.info('[CACHE-HIT] Location autocomplete served from cache', {
            query: params.q,
            locale: requestLocale,
            cacheKey
          });
          
          const responseTime = Date.now() - startTime;
          return res.json({
            ...cachedResult,
            response_time_ms: responseTime,
            cached: true
          });
        }
      } catch (cacheError) {
        Logger.warn('[CACHE-ERROR] Cache lookup failed, proceeding with database query', { cacheError });
      }
      
      Logger.info('Location autocomplete request', { 
        query: params.q, 
        limit: params.limit,
        ip: req.ip 
      });

      // Use Simple Fuzzy Search with trigram similarity for enhanced user experience
      let locations: any[] = [];
      
      if (params.fuzzy) {
        Logger.info('[FUZZY-SEARCH-I18N] Using PostgreSQL multilingual fuzzy search', { 
          query: params.q, 
          locale: requestLocale 
        });
        const locationSearchService = new LocationSearchService();
        locations = await locationSearchService.fuzzyLocationSearchI18n(
          params.q,
          requestLocale,
          params.limit,
          params.fuzzyThreshold || 0.25
        );
      } else {
        locations = await this.geoNamesService.searchLocations({
          query: params.q,
          limit: params.limit,
          language: 'en',
          country: params.country,
        });
      }

      const responseTime = Date.now() - startTime;
      Logger.api('GET', '/api/locations/autocomplete', 200, responseTime);

      // The locations are already enhanced with multilingual translations from the database function
      // The fuzzyLocationSearchI18n method provides localized display names automatically

      const responseData = {
        success: true,
        data: locations.map((location: any) => ({
          id: location.id,
          name: location.name,
          type: this.getLocationTypeFromFeature(location.feature_class || 'P', location.feature_code || 'PPL'),
          country: location.country_name || this.getCountryName(location.country_code || 'ES'),
          region: location.region_name || this.getRegionName(location.admin1_code || ''),
          coordinates: location.coordinates || { lat: location.latitude || 0, lng: location.longitude || 0 },
          popularity: location.popularity_score || 50,
          property_count: location.property_count || 0,
          display_name: location.display_name || this.createEnhancedDisplayName(location),
          // Enhanced fields for disambiguation - now properly populated
          region_name: location.region_name || this.getRegionName(location.admin1_code || ''),
          country_name: location.country_name || this.getCountryName(location.country_code || 'ES'),
          admin1_code: location.admin1_code,
          admin2_code: location.admin2_code,
          // Fuzzy search metadata (when using fuzzy search)
          ...(params.fuzzy && location.relevance_score !== undefined ? {
            relevance: Math.round((location.relevance_score || 0) * 100),
            similarity_score: location.relevance_score,
          } : {}),
        })),
        total: locations.length,
        query: params.q,
        locale: requestLocale,
        response_time_ms: responseTime
      };

      // Cache the result for 2 hours with locale-specific caching
      try {
        await cacheBackend.set(cacheKey, responseData, 7200); // 2 hours TTL
        Logger.info('[CACHE-SET] Location autocomplete cached', {
          cacheKey,
          resultCount: locations.length,
          locale: requestLocale
        });
      } catch (cacheError) {
        Logger.warn('[CACHE-ERROR] Failed to cache autocomplete results', { cacheError });
      }

      res.json(responseData);
    } catch (error) {
      const responseTime = Date.now() - startTime;
      Logger.error('Location autocomplete error', error);
      Logger.api('GET', '/api/locations/autocomplete', 500, responseTime);

      if (error instanceof z.ZodError) {
        res.status(400).json({
          success: false,
          error: 'Invalid request parameters',
          details: error.errors
        });
      } else {
        res.status(500).json({
          success: false,
          error: 'Internal server error',
          message: 'Failed to search locations'
        });
      }
    }
  }

  /**
   * Get location by ID with coordinates
   */
  async getLocationById(req: Request, res: Response) {
    const startTime = Date.now();
    
    try {
      const { id } = locationByIdSchema.parse(req.params);
      
      Logger.info('Get location by ID', { locationId: id, ip: req.ip });

      const location = await locationService.getLocationById(id);

      const responseTime = Date.now() - startTime;
      Logger.api('GET', `/api/locations/${id}`, location ? 200 : 404, responseTime);

      if (location) {
        res.json({
          success: true,
          data: {
            id: location.id,
            name: location.name,
            type: location.type,
            country: location.country,
            region: location.region,
            coordinates: location.coordinates,
            popularity: location.popularity_score,
            property_count: location.property_count,
            search_terms: location.search_terms,
            category: location.category,
            display_name: location.region ? 
              `${location.name}, ${location.region}` : 
              `${location.name}, ${location.country}`,
          }
        });
      } else {
        res.status(404).json({
          success: false,
          error: 'Location not found',
          message: `Location with ID ${id} not found`
        });
      }
    } catch (error) {
      const responseTime = Date.now() - startTime;
      Logger.error('Get location by ID error', error);
      Logger.api('GET', '/api/locations/:id', 500, responseTime);

      if (error instanceof z.ZodError) {
        res.status(400).json({
          success: false,
          error: 'Invalid request parameters',
          details: error.errors
        });
      } else {
        res.status(500).json({
          success: false,
          error: 'Internal server error',
          message: 'Failed to get location'
        });
      }
    }
  }

  /**
   * Get popular destinations for homepage suggestions
   */
  async getPopularDestinations(req: Request, res: Response) {
    const startTime = Date.now();
    
    try {
      const limit = parseInt(req.query.limit as string) || 20;
      
      Logger.info('Get popular destinations', { limit, ip: req.ip });

      const locations = await locationService.getPopularDestinations(limit);

      const responseTime = Date.now() - startTime;
      Logger.api('GET', '/api/locations/popular', 200, responseTime);

      res.json({
        success: true,
        data: locations.map(location => ({
          id: location.id,
          name: location.name,
          type: location.type,
          country: location.country,
          region: location.region,
          coordinates: location.coordinates,
          popularity: location.popularity_score,
          property_count: location.property_count,
          display_name: location.region ? 
            `${location.name}, ${location.region}` : 
            `${location.name}, ${location.country}`,
        })),
        total: locations.length,
        response_time_ms: responseTime
      });
    } catch (error) {
      const responseTime = Date.now() - startTime;
      Logger.error('Get popular destinations error', error);
      Logger.api('GET', '/api/locations/popular', 500, responseTime);

      res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: 'Failed to get popular destinations'
      });
    }
  }

  /**
   * Get locations within radius of coordinates
   */
  async getLocationsByRadius(req: Request, res: Response) {
    const startTime = Date.now();
    
    try {
      const lat = parseFloat(req.query.lat as string);
      const lng = parseFloat(req.query.lng as string);
      const radius = parseInt(req.query.radius as string) || 50;

      if (isNaN(lat) || isNaN(lng)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid coordinates',
          message: 'Valid latitude and longitude are required'
        });
      }

      Logger.info('Get locations by radius', { lat, lng, radius, ip: req.ip });

      const locations = await locationService.getLocationsByRadius(
        { lat, lng }, 
        radius
      );

      const responseTime = Date.now() - startTime;
      Logger.api('GET', '/api/locations/radius', 200, responseTime);

      res.json({
        success: true,
        data: locations.map(location => ({
          id: location.id,
          name: location.name,
          type: location.type,
          country: location.country,
          region: location.region,
          coordinates: location.coordinates,
          popularity: location.popularity_score,
          property_count: location.property_count,
          display_name: location.region ? 
            `${location.name}, ${location.region}` : 
            `${location.name}, ${location.country}`,
        })),
        total: locations.length,
        center: { lat, lng },
        radius,
        response_time_ms: responseTime
      });
    } catch (error) {
      const responseTime = Date.now() - startTime;
      Logger.error('Get locations by radius error', error);
      Logger.api('GET', '/api/locations/radius', 500, responseTime);

      res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: 'Failed to get locations by radius'
      });
    }
  }
}

export const locationController = new LocationController();