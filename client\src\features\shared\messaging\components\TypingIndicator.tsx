import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Card } from '@/components/ui/card';
import { useTranslations } from '@/lib/translations';

interface TypingIndicatorProps {
  users: string[];
  getUserInfo?: (userId: string) => { name: string; avatar_url?: string } | null;
}

export const TypingIndicator = ({ users, getUserInfo }: TypingIndicatorProps) => {
  const t = useTranslations('messaging');
  
  if (users.length === 0) return null;
  
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };
  
  const formatTypingText = () => {
    if (users.length === 1) {
      const userInfo = getUserInfo?.(users[0]);
      const name = userInfo?.name || 'Someone';
      return `${name} ${t('isTyping')}`;
    } else if (users.length === 2) {
      const user1 = getUserInfo?.(users[0])?.name || 'Someone';
      const user2 = getUserInfo?.(users[1])?.name || 'Someone';
      return `${user1} ${t('and')} ${user2} ${t('areTyping')}`;
    } else {
      return `${users.length} ${t('peopleTyping')}`;
    }
  };
  
  return (
    <div className="flex gap-2 mb-3">
      {/* Avatar */}
      <Avatar className="h-8 w-8 flex-shrink-0">
        {users.length === 1 && getUserInfo ? (
          <>
            <AvatarImage src={getUserInfo(users[0])?.avatar_url} />
            <AvatarFallback className="bg-muted text-muted-foreground text-xs">
              {getUserInfo(users[0])?.name ? getInitials(getUserInfo(users[0])!.name) : '?'}
            </AvatarFallback>
          </>
        ) : (
          <AvatarFallback className="bg-muted text-muted-foreground text-xs">
            {users.length}
          </AvatarFallback>
        )}
      </Avatar>
      
      {/* Typing Bubble */}
      <Card className="p-3 bg-muted/50 border-dashed max-w-xs">
        <div className="flex items-center gap-2">
          <span className="text-sm text-muted-foreground">
            {formatTypingText()}
          </span>
          
          {/* Animated typing dots */}
          <div className="flex gap-1">
            <div className="w-1 h-1 bg-muted-foreground rounded-full animate-bounce" />
            <div className="w-1 h-1 bg-muted-foreground rounded-full animate-bounce delay-100" />
            <div className="w-1 h-1 bg-muted-foreground rounded-full animate-bounce delay-200" />
          </div>
        </div>
      </Card>
    </div>
  );
};