import { LocaleSwitcher } from "@/components/LocaleSwitcher";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Progress } from "@/components/ui/progress";
import { useTranslations } from "@/lib/translations";
import { ChevronLeft, ChevronRight, Eye, Save } from "lucide-react";
import React, { useEffect, useRef } from "react";
import { usePropertyDraft } from "../hooks/usePropertyDraft";
import { usePropertyWizard } from "../hooks/usePropertyWizard";
import { PropertyDraft } from "../types/property";

// Import wizard step components
import { AmenitiesStep } from "./wizard/AmenitiesStep";
import { GuestCapacityStep } from "./wizard/GuestCapacityStep";
import { LocationStep } from "./wizard/LocationStep";
import { PhotosStep } from "./wizard/PhotosStep";
import { PolicyStep } from "./wizard/PolicyStep";
import { PricingStep } from "./wizard/PricingStep";
import { PropertyTypeStep } from "./wizard/PropertyTypeStep";
import { ReviewStep } from "./wizard/ReviewStep";
import { TitleDescriptionStep } from "./wizard/TitleDescriptionStep";

interface PropertyWizardModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  draftId?: string;
}

export const PropertyWizardModal: React.FC<PropertyWizardModalProps> = ({
  open,
  onOpenChange,
  draftId,
}) => {
  const t = useTranslations("hostOnboarding");
  const isLoadingFromDraftRef = useRef(false);
  const hasCreatedDraftRef = useRef(false);

  // Initialize hooks
  const {
    formData,
    currentStep,
    currentStepData,
    isFirstStep,
    isLastStep,
    canGoToNextStep,
    progress,
    completedSteps,
    updateFormData,
    goToStep,
    goToNextStep,
    goToPreviousStep,
    resetWizard,
  } = usePropertyWizard();

  const {
    draft,
    currentDraftId,
    isLoading,
    hasUnsavedChanges,
    isSaving,
    isPublishing,
    createDraft,
    updateData,
    saveStep,
    publishProperty,
    deleteDraft,
  } = usePropertyDraft(draftId);

  // Create draft when modal opens (if no existing draft)
  useEffect(() => {
    if (open && !currentDraftId && !isLoading && !hasCreatedDraftRef.current) {
      hasCreatedDraftRef.current = true;
      createDraft(formData);
    }
    // Reset flag when modal closes
    if (!open) {
      hasCreatedDraftRef.current = false;
    }
  }, [open, currentDraftId, isLoading, createDraft, formData]);

  // Load draft data when draft is fetched
  useEffect(() => {
    if (draft && typeof draft === "object" && "data" in draft) {
      const draftData = draft as PropertyDraft;
      isLoadingFromDraftRef.current = true;
      updateFormData(draftData.data);
      if (draftData.currentStep !== undefined) {
        goToStep(draftData.currentStep);
      }
      // Reset the flag after a short delay to allow the effect to complete
      setTimeout(() => {
        isLoadingFromDraftRef.current = false;
      }, 100);
    }
  }, [draft, updateFormData, goToStep]);

  // Auto-save data changes - but only when user makes changes, not when loading from draft
  useEffect(() => {
    if (
      currentDraftId &&
      Object.keys(formData).length > 0 &&
      !isLoading &&
      !isLoadingFromDraftRef.current
    ) {
      // Only save if we have a draft loaded and the data differs from draft data
      if (
        draft &&
        typeof draft === "object" &&
        "data" in draft &&
        JSON.stringify(formData) !== JSON.stringify(draft.data)
      ) {
        updateData(formData, currentStep);
      } else if (!draft) {
        // No draft loaded yet, save current form data
        updateData(formData, currentStep);
      }
    }
  }, [formData, currentStep, currentDraftId, updateData, draft, isLoading]);

  // Handle step navigation
  const handleNext = () => {
    if (canGoToNextStep) {
      goToNextStep();
      if (currentDraftId) {
        saveStep(currentStep + 1);
      }
    }
  };

  const handlePrevious = () => {
    goToPreviousStep();
    if (currentDraftId) {
      saveStep(currentStep - 1);
    }
  };

  const handleStepClick = (stepIndex: number) => {
    // Allow navigation to completed steps or current step
    const step = completedSteps[stepIndex];
    if (step.isCompleted || step.isCurrent) {
      goToStep(stepIndex);
      if (currentDraftId) {
        saveStep(stepIndex);
      }
    }
  };

  const handlePublish = () => {
    if (currentDraftId && isLastStep) {
      publishProperty();
      onOpenChange(false);
    }
  };

  const handleClose = () => {
    if (hasUnsavedChanges) {
      // In a real app, you might want to show a confirmation dialog
      // For now, we'll just close since auto-save handles it
    }
    onOpenChange(false);
  };

  const renderStep = () => {
    const stepId = currentStepData.id;

    switch (stepId) {
      case "property-type":
        return <PropertyTypeStep data={formData} onUpdate={updateFormData} />;
      case "location":
        return <LocationStep data={formData} onUpdate={updateFormData} />;
      case "capacity":
        return <GuestCapacityStep data={formData} onUpdate={updateFormData} />;
      case "amenities":
        return <AmenitiesStep data={formData} onUpdate={updateFormData} />;
      case "photos":
        return <PhotosStep data={formData} onUpdate={updateFormData} />;
      case "listing":
        return (
          <TitleDescriptionStep data={formData} onUpdate={updateFormData} />
        );
      case "pricing":
        return <PricingStep data={formData} onUpdate={updateFormData} />;
      case "policies":
        return <PolicyStep data={formData} onUpdate={updateFormData} />;
      case "review":
        return <ReviewStep data={formData} onUpdate={updateFormData} />;
      default:
        return null;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="border-b pb-4 mb-6">
          <div className="flex items-center justify-between">
            <DialogTitle className="text-2xl font-bold">
              {t("title")}
            </DialogTitle>
            <div className="flex items-center space-x-4">
              <LocaleSwitcher />
              {(isSaving || hasUnsavedChanges) && (
                <Badge
                  variant="secondary"
                  className="flex items-center space-x-1"
                >
                  <Save className="h-3 w-3" />
                  <span>{isSaving ? "Saving..." : "Auto-saved"}</span>
                </Badge>
              )}
              <div className="text-sm text-gray-600 dark:text-gray-400">
                {t("step")} {currentStep + 1} {t("of")} {completedSteps.length}
              </div>
            </div>
          </div>

          {/* Progress bar */}
          <div className="mt-4">
            <Progress value={progress} className="h-2 mb-4" />

            {/* Step indicators */}
            <div className="flex items-center justify-between text-xs">
              {completedSteps.map((step, index) => (
                <button
                  key={step.id}
                  onClick={() => handleStepClick(index)}
                  className={`flex flex-col items-center space-y-1 p-2 rounded-lg transition-colors ${
                    step.isCurrent
                      ? "bg-primary/10 text-primary"
                      : step.isCompleted
                      ? "text-green-600 cursor-pointer hover:bg-green-50"
                      : "text-gray-400 cursor-not-allowed"
                  }`}
                  disabled={!step.isCompleted && !step.isCurrent}
                >
                  {React.createElement(step.icon, {
                    className: `h-4 w-4 ${
                      step.isCompleted ? "text-green-600" : ""
                    }`,
                  })}
                  <span className="hidden sm:block">
                    {t(`steps.${step.titleKey}`)}
                  </span>
                </button>
              ))}
            </div>
          </div>
        </DialogHeader>

        {/* Main Content */}
        <div className="px-2">
          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4" />
                <p className="text-gray-600">Loading...</p>
              </div>
            </div>
          ) : (
            renderStep()
          )}
        </div>

        {/* Navigation Footer */}
        <div className="border-t pt-4 mt-6">
          <div className="flex justify-between items-center">
            <Button
              type="button"
              variant="outline"
              onClick={handlePrevious}
              disabled={isFirstStep}
              className="flex items-center space-x-2"
            >
              <ChevronLeft className="h-4 w-4" />
              <span>{t("previous")}</span>
            </Button>

            <div className="flex items-center space-x-2">
              {currentDraftId && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    // Preview functionality could be added here
                    console.log("Preview property:", formData);
                  }}
                  className="flex items-center space-x-1"
                >
                  <Eye className="h-4 w-4" />
                  <span>Preview</span>
                </Button>
              )}

              {isLastStep ? (
                <Button
                  onClick={handlePublish}
                  disabled={!canGoToNextStep || isPublishing}
                  className="flex items-center space-x-2"
                >
                  {isPublishing ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white" />
                      <span>Publishing...</span>
                    </>
                  ) : (
                    <>
                      <span>{t("publish")}</span>
                    </>
                  )}
                </Button>
              ) : (
                <Button
                  onClick={handleNext}
                  disabled={!canGoToNextStep}
                  className="flex items-center space-x-2"
                >
                  <span>{t("next")}</span>
                  <ChevronRight className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
