#!/usr/bin/env node
import { build } from 'esbuild';
import fs from 'fs';
import path from 'path';

// Create a temporary server file without vite dependencies for production
const tempServerFile = 'server/index.production.ts';
const originalServerFile = 'server/index.ts';

// Read the original server file
const originalContent = fs.readFileSync(originalServerFile, 'utf8');

// Create production version of routes.ts without vite imports
const routesContent = fs.readFileSync('server/routes.ts', 'utf8');
const productionRoutesContent = routesContent.replace(
  /const viteDevServer = await import\("\.\/vite-dev-server\.js"\);[\s\S]*?await viteDevServer\.setupViteWithCustomConfig\(app, server, allowedHosts\);/,
  'throw new Error("Vite development server not available in production");'
);

// Write temporary production routes file
fs.writeFileSync('server/routes.production.ts', productionRoutesContent);

// Create production server file that imports production routes
const productionServerContent = originalContent.replace(
  'import { registerRoutes } from "./routes";',
  'import { registerRoutes } from "./routes.production";'
);

fs.writeFileSync(tempServerFile, productionServerContent);

try {
  // Build the production server
  await build({
    entryPoints: [tempServerFile],
    bundle: true,
    platform: 'node',
    format: 'esm',
    outfile: 'dist/index.js',
    packages: 'external',
    external: ['vite', '@vitejs/plugin-react', '@replit/vite-plugin-runtime-error-modal', '@replit/vite-plugin-cartographer'],
    minify: false,
    sourcemap: false,
  });

  console.log('✅ Production server built successfully');
  console.log('📦 Output: dist/index.js');
  
  // Check the output size
  const stats = fs.statSync('dist/index.js');
  console.log(`📊 Bundle size: ${(stats.size / 1024).toFixed(1)}kb`);
  
  // Verify no vite references in production build
  const bundleContent = fs.readFileSync('dist/index.js', 'utf8');
  const viteRefs = bundleContent.match(/import.*vite/g);
  if (viteRefs) {
    console.warn('⚠️  Warning: Found vite references in production build:', viteRefs);
  } else {
    console.log('✅ No vite references found in production build');
  }
  
} catch (error) {
  console.error('❌ Production build failed:', error);
  process.exit(1);
} finally {
  // Clean up temporary files
  if (fs.existsSync(tempServerFile)) {
    fs.unlinkSync(tempServerFile);
  }
  if (fs.existsSync('server/routes.production.ts')) {
    fs.unlinkSync('server/routes.production.ts');
  }
}