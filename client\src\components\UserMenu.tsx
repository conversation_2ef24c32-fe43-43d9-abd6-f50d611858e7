import React from 'react';
import { useTranslations } from '@/lib/translations';
import { useLocation } from 'wouter';
import {
  Heart,
  Compass,
  MessageSquare,
  User as UserIcon,
  Settings,
  Globe,
  HelpCircle,
  Home,
  UserPlus,
  Menu as MenuIcon,
  LogIn,
  LogOut
} from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from './ui/avatar';
import { Button } from './ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from './ui/dropdown-menu';
import { useUser, useLogout } from '@/features/shared/auth/hooks/useAuth';
import { useQueryClient } from '@tanstack/react-query';

import type { User } from '@/features/shared/auth/types';

export function UserMenu() {
  const t = useTranslations('userMenu');
  const { data: currentUser, isLoading } = useUser();
  const logoutMutation = useLogout();
  const [, navigate] = useLocation();

  const handleBecomeHost = () => {
    navigate('/host/upgrade');
  };

  const handleLogout = async () => {
    try {
      await logoutMutation.mutateAsync();
      // Silent logout without toast notification
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const handleAuthNavigation = (path: string) => {
    navigate(path);
  };

  if (isLoading) {
    return (
      <div className="flex items-center space-x-1 sm:space-x-2">
        <Button variant="ghost" size="sm" disabled className="text-xs sm:text-sm font-medium px-2 sm:px-3">
          {t('loading')}
        </Button>
      </div>
    );
  }

  // Unified login/profile button for better mobile experience
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className="flex items-center gap-1.5 sm:gap-2 px-2 sm:px-3 py-2 hover:bg-gray-100 rounded-full border border-gray-300 transition-colors"
        >
          <Avatar className="h-8 w-8">
            <AvatarImage src={currentUser?.avatar_url || ''} alt={currentUser?.username || 'User'} />
            <AvatarFallback className="bg-primary text-primary-foreground text-sm font-medium">
              {currentUser?.first_name ? currentUser.first_name.charAt(0).toUpperCase() : 
               currentUser?.username ? currentUser.username.charAt(0).toUpperCase() : 
               <UserIcon className="h-4 w-4 text-gray-600" />}
            </AvatarFallback>
          </Avatar>
          <MenuIcon className="h-4 w-4 text-gray-600" />
        </Button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent 
        className="w-64 !right-0 !left-auto !transform-none !translate-x-0" 
        align="end" 
        side="bottom"
        sideOffset={8}
        alignOffset={0}
        avoidCollisions={false}
        collisionPadding={0}
      >
        {!currentUser ? (
          // Not logged in - show login/register options (without welcome header)
          <>
            <DropdownMenuItem 
              onClick={() => handleAuthNavigation('/login')}
              className="cursor-pointer"
            >
              <LogIn className="mr-2 h-4 w-4" />
              <span>{t('login')}</span>
            </DropdownMenuItem>
            
            <DropdownMenuItem 
              onClick={() => handleAuthNavigation('/register')}
              className="cursor-pointer"
            >
              <UserPlus className="mr-2 h-4 w-4" />
              <span>{t('signup')}</span>
            </DropdownMenuItem>
            
            <DropdownMenuSeparator />
            
            <DropdownMenuItem>
              <HelpCircle className="mr-2 h-4 w-4" />
              <span>{t('help')}</span>
            </DropdownMenuItem>
          </>
        ) : (
          // Logged in - show user menu
          <>
            <DropdownMenuLabel>
              <div className="flex items-center space-x-3">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={currentUser.avatar_url || ''} alt={currentUser.username || 'User'} />
                  <AvatarFallback className="bg-primary text-primary-foreground text-sm font-medium">
                    {currentUser.first_name ? currentUser.first_name.charAt(0).toUpperCase() : 
                     currentUser.username ? currentUser.username.charAt(0).toUpperCase() : 
                     <UserIcon className="h-4 w-4" />}
                  </AvatarFallback>
                </Avatar>
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium">
                    {currentUser.first_name && currentUser.last_name 
                      ? `${currentUser.first_name} ${currentUser.last_name}`
                      : currentUser.username
                    }
                  </p>
                  <p className="text-xs text-muted-foreground">{currentUser.email || 'No email'}</p>
                </div>
              </div>
            </DropdownMenuLabel>

            <DropdownMenuSeparator />

            <DropdownMenuGroup>
              <DropdownMenuItem 
                onClick={() => window.location.href = '/guest/dashboard'}
                className="cursor-pointer"
              >
                <Compass className="mr-2 h-4 w-4" />
                <span>Dashboard</span>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Heart className="mr-2 h-4 w-4" />
                <span>{t('favorites')}</span>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <MessageSquare className="mr-2 h-4 w-4" />
                <span>{t('messages')}</span>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Settings className="mr-2 h-4 w-4" />
                <span>{t('settings')}</span>
              </DropdownMenuItem>
            </DropdownMenuGroup>

            <DropdownMenuSeparator />

            {/* Host/Guest conditional menu */}
            {currentUser.is_host ? (
              // User is a host - show Host Dashboard
              <DropdownMenuGroup>
                <DropdownMenuItem 
                  onClick={() => window.location.href = '/host/dashboard'}
                  className="cursor-pointer"
                >
                  <Home className="mr-2 h-4 w-4" />
                  <span>Host Dashboard</span>
                </DropdownMenuItem>
              </DropdownMenuGroup>
            ) : (
              // User is not a host - show Become Host option
              <DropdownMenuGroup>
                <DropdownMenuItem 
                  onClick={handleBecomeHost}
                  className="cursor-pointer"
                >
                  <Home className="mr-2 h-4 w-4" />
                  <span>{t('becomeHost')}</span>
                </DropdownMenuItem>
              </DropdownMenuGroup>
            )}

            <DropdownMenuSeparator />

            <DropdownMenuItem onClick={handleLogout}>
              <LogOut className="mr-2 h-4 w-4" />
              <span>Sign Out</span>
            </DropdownMenuItem>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
