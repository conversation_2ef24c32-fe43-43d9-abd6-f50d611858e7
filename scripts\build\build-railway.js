#!/usr/bin/env node
/**
 * Railway-specific build script
 * Handles ES module compatibility and memory optimization for Railway deployment
 */

import { execSync } from 'child_process';
import path from 'path';
import fs from 'fs';

// Set memory optimization for Node.js
process.env.NODE_OPTIONS = '--max-old-space-size=4096';

console.log('🚀 Starting Railway build process...');

try {
  // Ensure we're in the project root
  const projectRoot = process.cwd();
  console.log('📁 Project root:', projectRoot);

  // Run the main build script
  console.log('🔨 Running production build...');
  execSync('node scripts/build-production.js', { 
    stdio: 'inherit', 
    cwd: projectRoot 
  });

  // Verify build output
  const distPath = path.join(projectRoot, 'dist');
  if (fs.existsSync(distPath)) {
    console.log('✅ Build completed successfully');
    console.log('📦 Build output available at:', distPath);
  } else {
    throw new Error('Build failed - dist directory not found');
  }

} catch (error) {
  console.error('❌ Railway build failed:', error.message);
  process.exit(1);
}