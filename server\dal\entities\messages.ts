
import { supabase } from '../../supabase'
import { getCurrentUser, requireAuth } from '../auth/session'
import { MessageDTO, MessageData, MessageFilters, MessageStats, Conversation } from '../dto/message.dto'
import { memoryCache } from '../cache/memoryCache'

export const getMessages = async (
  authHeader: string,
  userType: 'host' | 'guest',
  filters?: MessageFilters
): Promise<MessageDTO[]> => {
  const user = await requireAuth(authHeader)
  
  const cacheKey = `messages:${userType}:${user.userId}:${JSON.stringify(filters)}`
  const cached = memoryCache.get(cacheKey) as MessageDTO[] | null
  if (cached) return cached
  
  try {
    let query = supabase
      .from('messages')
      .select(`
        *,
        sender:sender_id (
          id,
          full_name,
          email,
          profile_picture,
          role
        ),
        receiver:receiver_id (
          id,
          full_name,
          email,
          profile_picture,
          role
        ),
        property:property_id (
          id,
          title,
          location
        ),
        booking:booking_id (
          id,
          check_in_date,
          check_out_date,
          status
        )
      `)
    
    query = query.or(`sender_id.eq.${user.userId},receiver_id.eq.${user.userId}`)
    
    if (filters?.conversationWith) {
      query = query.or(
        `and(sender_id.eq.${user.userId},receiver_id.eq.${filters.conversationWith}),` +
        `and(sender_id.eq.${filters.conversationWith},receiver_id.eq.${user.userId})`
      )
    }
    
    if (filters?.unreadOnly) {
      query = query.eq('read', false).eq('receiver_id', user.userId)
    }
    
    const { data: messages, error } = await query
      .order('created_at', { ascending: false })
      .limit(100)
    
    if (error) {
      throw new Error(`Failed to fetch messages: ${error.message}`)
    }
    
    const result = MessageDTO.fromArray(messages || [], user.role, user.userId)
    memoryCache.set(cacheKey, result, 60)
    return result
  } catch (error) {
    console.error('Error fetching messages:', error)
    throw error
  }
}

export const sendMessage = async (
  authHeader: string,
  messageData: {
    receiverId: string
    content: string
    propertyId?: string
    bookingId?: string
    messageType?: 'text' | 'booking_inquiry' | 'booking_update' | 'system'
  }
): Promise<MessageDTO> => {
  const user = await requireAuth(authHeader)
  
  try {
    const { data: receiver, error: receiverError } = await supabase
      .from('users')
      .select('id, role')
      .eq('id', messageData.receiverId)
      .single()
    
    if (receiverError || !receiver) {
      throw new Error('Receiver not found')
    }
    
    const { data: message, error } = await supabase
      .from('messages')
      .insert({
        sender_id: user.userId,
        receiver_id: messageData.receiverId,
        content: messageData.content,
        property_id: messageData.propertyId,
        booking_id: messageData.bookingId,
        message_type: messageData.messageType || 'text',
        read: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select(`
        *,
        sender:sender_id (
          id,
          full_name,
          email,
          profile_picture,
          role
        ),
        receiver:receiver_id (
          id,
          full_name,
          email,
          profile_picture,
          role
        )
      `)
      .single()
    
    if (error) {
      throw new Error(`Failed to send message: ${error.message}`)
    }
    
    memoryCache.invalidatePattern(`messages:*:${user.userId}:*`)
    memoryCache.invalidatePattern(`messages:*:${messageData.receiverId}:*`)
    
    return new MessageDTO(message, user.role, user.userId)
  } catch (error) {
    console.error('Error sending message:', error)
    throw error
  }
}

export const getMessageStats = async (
  authHeader: string,
  userType: 'host' | 'guest'
): Promise<MessageStats> => {
  const user = await requireAuth(authHeader)
  
  const cacheKey = `message-stats:${userType}:${user.userId}`
  const cached = memoryCache.get(cacheKey) as MessageStats | null
  if (cached) return cached
  
  try {
    let query = supabase
      .from('messages')
      .select('read, created_at, sender_id, receiver_id')
      .or(`sender_id.eq.${user.userId},receiver_id.eq.${user.userId}`)
    
    const { data: messages, error } = await query
    
    if (error) {
      throw new Error(`Failed to fetch message stats: ${error.message}`)
    }
    
    const stats: MessageStats = {
      total: messages?.length || 0,
      unread: messages?.filter(m => !m.read).length || 0,
      sent: messages?.filter(m => m.sender_id === user.userId).length || 0,
      received: messages?.filter(m => m.receiver_id === user.userId).length || 0
    }
    
    memoryCache.set(cacheKey, stats, 300)
    return stats
  } catch (error) {
    console.error('Error fetching message stats:', error)
    throw error
  }
}

export const getConversations = async (
  authHeader: string
): Promise<Conversation[]> => {
  const user = await requireAuth(authHeader)
  
  const cacheKey = `conversations:${user.userId}`
  const cached = memoryCache.get(cacheKey) as Conversation[] | null
  if (cached) return cached
  
  try {
    const { data: messages, error } = await supabase
      .from('messages')
      .select(`
        *,
        sender:sender_id (
          id,
          full_name,
          profile_picture
        ),
        receiver:receiver_id (
          id,
          full_name,
          profile_picture
        )
      `)
      .or(`sender_id.eq.${user.userId},receiver_id.eq.${user.userId}`)
      .order('created_at', { ascending: false })
    
    if (error) {
      throw new Error(`Failed to fetch conversations: ${error.message}`)
    }
    
    const conversationMap = new Map<string, Conversation>()
    
    messages?.forEach(message => {
      const otherUserId = message.sender_id === user.userId ? message.receiver_id : message.sender_id
      const otherUser = message.sender_id === user.userId ? message.receiver : message.sender
      
      if (!conversationMap.has(otherUserId)) {
        conversationMap.set(otherUserId, {
          participantId: otherUserId,
          participantName: otherUser?.full_name || 'Unknown User',
          participantAvatar: otherUser?.profile_picture,
          lastMessage: new MessageDTO(message, user.role, user.userId),
          unreadCount: 0
        })
      }
      
      if (message.receiver_id === user.userId && !message.read) {
        const conversation = conversationMap.get(otherUserId)!
        conversation.unreadCount++
      }
    })
    
    const result = Array.from(conversationMap.values())
    memoryCache.set(cacheKey, result, 300)
    return result
  } catch (error) {
    console.error('Error fetching conversations:', error)
    throw error
  }
}

export const markMessageAsRead = async (
  authHeader: string,
  messageId: string
): Promise<void> => {
  const user = await requireAuth(authHeader)
  
  try {
    const { error } = await supabase
      .from('messages')
      .update({ read: true })
      .eq('id', messageId)
      .eq('receiver_id', user.userId)
    
    if (error) {
      throw new Error(`Failed to mark message as read: ${error.message}`)
    }
    
    memoryCache.invalidatePattern(`messages:*:${user.userId}:*`)
    memoryCache.invalidatePattern(`conversations:${user.userId}`)
  } catch (error) {
    console.error('Error marking message as read:', error)
    throw error
  }
}