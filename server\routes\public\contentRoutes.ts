import { Router } from 'express';
import { contentController } from '../../controllers/public/contentController';

const router = Router();

// Homepage content routes
router.get('/popular-spain', contentController.getPopularInSpain.bind(contentController));
router.get('/new-france', contentController.getNewInFrance.bind(contentController));
router.get('/guest-favorites', contentController.getGuestFavorites.bind(contentController));
router.get('/inspirations', contentController.getInspirations.bind(contentController));

export default router;