import React, { useState } from 'react';
import { useTranslations } from '@/lib/translations';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Lightbulb, Sparkles, Eye, Users } from 'lucide-react';
import { PropertyWizardData } from '../../types/property';

interface TitleDescriptionStepProps {
  data: Partial<PropertyWizardData>;
  onUpdate: (data: Partial<PropertyWizardData>) => void;
}

const TITLE_SUGGESTIONS = [
  'Stunning villa with pool and sea views in Costa Blanca',
  'Luxury beachfront apartment in the heart of Benidorm',
  'Charming traditional Spanish villa with mountain views',
  'Modern penthouse with panoramic Mediterranean views',
  'Family-friendly villa with private pool and garden',
  'Elegant townhouse minutes from pristine beaches',
  'Romantic getaway villa with sunset terrace views',
  'Spacious holiday home perfect for large groups'
];

const DESCRIPTION_TEMPLATES = [
  {
    typeKey: 'villaWithPool',
    templateKey: 'villaWithPoolDescription'
  },
  {
    typeKey: 'beachfrontApartment',
    templateKey: 'beachfrontApartmentDescription'
  },
  {
    typeKey: 'mountainRetreat',
    templateKey: 'mountainRetreatDescription'
  }
];

export const TitleDescriptionStep = ({ data, onUpdate }: TitleDescriptionStepProps) => {
  const t = useTranslations('hostOnboarding.listing');
  const [selectedTemplate, setSelectedTemplate] = useState<number | null>(null);
  const [showSuggestions, setShowSuggestions] = useState(false);

  const title = data.title || '';
  const description = data.description || '';

  const handleTitleChange = (newTitle: string) => {
    onUpdate({ title: newTitle });
  };

  const handleDescriptionChange = (newDescription: string) => {
    onUpdate({ description: newDescription });
  };

  const applyTemplate = (template: typeof DESCRIPTION_TEMPLATES[0]) => {
    let processedTemplate = t(template.templateKey)
      .replace('[property_type]', data.propertyType || 'property')
      .replace('[location]', data.city || 'Costa Blanca')
      .replace('[bedrooms]', (data.bedrooms || 1).toString())
      .replace('[bathrooms]', (data.bathrooms || 1).toString())
      .replace('[guests]', (data.maxGuests || 2).toString())
      .replace('[view_type]', 'sea')
      .replace('[local_attraction]', 'the beach');

    handleDescriptionChange(processedTemplate);
    setSelectedTemplate(DESCRIPTION_TEMPLATES.indexOf(template));
  };

  const generateAITitle = () => {
    const propertyType = data.propertyType || 'villa';
    const location = data.city || 'Costa Blanca';
    const guests = data.maxGuests || 4;
    
    const aiTitle = `Beautiful ${propertyType} for ${guests} guests in ${location}`;
    handleTitleChange(aiTitle);
  };

  const titleCharacterCount = title.length;
  const descriptionWordCount = description.split(/\s+/).filter(word => word.length > 0).length;
  const isFormValid = title.length >= 10 && description.length >= 100;

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          {t('title')}
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          {t('description')}
        </p>
      </div>

      {/* Title Section */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Label htmlFor="title" className="text-lg font-semibold">
            {t('propertyTitle')}
          </Label>
          <Badge variant={titleCharacterCount >= 10 ? "default" : "secondary"}>
            {titleCharacterCount}/50 {t('charactersCount')}
          </Badge>
        </div>
        
        <Input
          id="title"
          value={title}
          onChange={(e) => handleTitleChange(e.target.value)}
          placeholder={t('titlePlaceholder')}
          maxLength={50}
          className="text-lg"
        />
        
        <div className="flex space-x-2">
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={() => setShowSuggestions(!showSuggestions)}
            className="flex items-center space-x-2"
          >
            <Lightbulb className="h-4 w-4" />
            <span>{t('titleSuggestions')}</span>
          </Button>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={generateAITitle}
            className="flex items-center space-x-2"
          >
            <Sparkles className="h-4 w-4" />
            <span>{t('generateTitle')}</span>
          </Button>
        </div>

        {showSuggestions && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            {TITLE_SUGGESTIONS.map((suggestion, index) => (
              <Card
                key={index}
                className="cursor-pointer hover:shadow-md transition-all border-gray-200 dark:border-gray-700"
                onClick={() => {
                  handleTitleChange(suggestion);
                  setShowSuggestions(false);
                }}
              >
                <CardContent className="p-3">
                  <p className="text-sm text-gray-700 dark:text-gray-300">
                    {suggestion}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>

      {/* Description Section */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Label htmlFor="description" className="text-lg font-semibold">
            {t('propertyDescription')}
          </Label>
          <Badge variant={descriptionWordCount >= 50 ? "default" : "secondary"}>
            {descriptionWordCount} words
          </Badge>
        </div>
        
        <Textarea
          id="description"
          value={description}
          onChange={(e) => handleDescriptionChange(e.target.value)}
          placeholder={t('descriptionPlaceholder')}
          rows={8}
          className="min-h-[200px]"
        />
        
        <div className="space-y-4">
          <h4 className="font-medium text-gray-900 dark:text-white">
            {t('descriptionTemplates')}
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {DESCRIPTION_TEMPLATES.map((template, index) => (
              <Card
                key={index}
                className={`cursor-pointer transition-all hover:shadow-md ${
                  selectedTemplate === index
                    ? 'ring-2 ring-primary border-primary'
                    : 'border-gray-200 dark:border-gray-700'
                }`}
                onClick={() => applyTemplate(template)}
              >
                <CardContent className="p-4">
                  <h5 className="font-medium text-gray-900 dark:text-white mb-2">
                    {t(template.typeKey)}
                  </h5>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    {t(template.templateKey).substring(0, 100)}...
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>

      {/* Preview */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center space-x-2">
          <Eye className="h-5 w-5" />
          <span>{t('preview')}</span>
        </h3>
        
        <Card className="border-gray-200 dark:border-gray-700">
          <CardContent className="p-6">
            <div className="aspect-video bg-gray-100 dark:bg-gray-800 rounded-lg mb-4 flex items-center justify-center">
              <span className="text-gray-500 dark:text-gray-400">
                {t('photoPlaceholder')}
              </span>
            </div>
            
            <h4 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              {title || t('titlePlaceholder')}
            </h4>
            
            <div className="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400 mb-4">
              <span className="flex items-center space-x-1">
                <Users className="h-4 w-4" />
                <span>{data.maxGuests || 2} guests</span>
              </span>
              <span>•</span>
              <span>{data.bedrooms || 1} bedrooms</span>
              <span>•</span>
              <span>{data.bathrooms || 1} bathrooms</span>
            </div>
            
            <div className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-line">
              {description || t('descriptionPlaceholder')}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Writing Tips */}
      <div className="bg-purple-50 dark:bg-purple-900/20 p-6 rounded-lg">
        <h4 className="font-medium text-purple-800 dark:text-purple-200 mb-2">
          {t('writingTips')}
        </h4>
        <ul className="text-sm text-purple-700 dark:text-purple-300 space-y-1">
          <li>• {t('tip1')}</li>
          <li>• {t('tip2')}</li>
          <li>• {t('tip3')}</li>
          <li>• {t('tip4')}</li>
          <li>• {t('tip5')}</li>
        </ul>
      </div>

      {/* Validation Status */}
      {!isFormValid && (
        <div className="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg">
          <h4 className="font-medium text-orange-800 dark:text-orange-200 mb-2">
            {t('completionRequired')}
          </h4>
          <ul className="text-sm text-orange-700 dark:text-orange-300 space-y-1">
            {title.length < 10 && <li>• {t('completionRequiredTitle')}</li>}
            {description.length < 100 && <li>• {t('completionRequiredDescription')}</li>}
          </ul>
        </div>
      )}
    </div>
  );
};