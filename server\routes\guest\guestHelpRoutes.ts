import { Router } from 'express';
import { guestHelpController } from '../../controllers/guest/guestHelpController';

const router = Router();

// Guest help routes
router.get('/help/articles', guestHelpController.getHelpArticles.bind(guestHelpController));
router.get('/help/article/:articleId', guestHelpController.getHelpArticle.bind(guestHelpController));
router.get('/help/faqs', guestHelpController.getFAQs.bind(guestHelpController));
router.post('/help/article', guestHelpController.createHelpArticle.bind(guestHelpController));
router.put('/help/article/:articleId', guestHelpController.updateHelpArticle.bind(guestHelpController));
router.delete('/help/article/:articleId', guestHelpController.deleteHelpArticle.bind(guestHelpController));

export default router;