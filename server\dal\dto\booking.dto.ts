
import { UserSession } from '../auth/session'

export interface BookingData {
  id: string
  property_id: string
  guest_id: string
  host_id: string
  check_in_date: string
  check_out_date: string
  guest_count: number
  total_amount: number
  status: 'pending' | 'confirmed' | 'cancelled' | 'completed'
  special_requests?: string
  created_at: string
  updated_at: string
  property?: {
    id: string
    title: string
    location: string
    images: string[]
  }
  guest?: {
    id: string
    full_name: string
    email: string
    profile_picture?: string
  }
  host?: {
    id: string
    full_name: string
    email: string
    profile_picture?: string
  }
}

export class BookingDTO {
  id: string
  propertyId: string
  checkInDate: string
  checkOutDate: string
  guestCount: number
  totalAmount: number
  status: string
  specialRequests?: string
  createdAt: string
  nights: number
  property?: {
    id: string
    title: string
    location: string
    images: string[]
  }
  guest?: {
    id: string
    name: string
    email?: string
  }
  host?: {
    id: string
    name: string
    email?: string
  }

  constructor(booking: BookingData, userRole?: UserSession['role'], userId?: string) {
    this.id = booking.id
    this.propertyId = booking.property_id
    this.checkInDate = booking.check_in_date
    this.checkOutDate = booking.check_out_date
    this.guestCount = booking.guest_count
    this.totalAmount = booking.total_amount
    this.status = booking.status
    this.specialRequests = booking.special_requests
    this.createdAt = booking.created_at
    
    // Calculate nights
    const checkIn = new Date(booking.check_in_date)
    const checkOut = new Date(booking.check_out_date)
    this.nights = Math.ceil((checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60 * 24))

    // Include property details if available
    if (booking.property) {
      this.property = {
        id: booking.property.id,
        title: booking.property.title,
        location: booking.property.location,
        images: booking.property.images || []
      }
    }

    // Include user details based on role and permissions
    if (userRole === 'admin' || userRole === 'host') {
      if (booking.guest) {
        this.guest = {
          id: booking.guest.id,
          name: booking.guest.full_name || booking.guest.email,
          email: booking.guest.email
        }
      }
    }

    if (userRole === 'admin' || userRole === 'guest') {
      if (booking.host) {
        this.host = {
          id: booking.host.id,
          name: booking.host.full_name || booking.host.email,
          email: userRole === 'admin' ? booking.host.email : undefined
        }
      }
    }
  }

  static fromArray(bookings: BookingData[], userRole?: UserSession['role'], userId?: string): BookingDTO[] {
    return bookings.map(booking => new BookingDTO(booking, userRole, userId))
  }
}

export interface BookingFilters {
  status?: string[]
  dateRange?: {
    start: string
    end: string
  }
  propertyId?: string
  hostId?: string
  guestId?: string
}

export interface BookingStats {
  total: number
  confirmed: number
  pending: number
  cancelled: number
  completed: number
  totalRevenue: number
}