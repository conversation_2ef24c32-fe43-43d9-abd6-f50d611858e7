// Image upload API client helpers

interface UploadImageResponse {
  success: boolean;
  imageUrl?: string;
  message: string;
}

interface DeleteImageResponse {
  success: boolean;
  message: string;
}

interface ReorderImagesResponse {
  success: boolean;
  message: string;
}

interface GetImagesResponse {
  success: boolean;
  images: string[];
}

export const imageApi = {
  async uploadImage(propertyId: string, file: File): Promise<UploadImageResponse> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = async () => {
        try {
          const response = await fetch(`/api/images/properties/${propertyId}/images`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              file: reader.result as string,
              fileName: file.name,
            }),
          });

          if (!response.ok) {
            throw new Error('Upload failed');
          }

          const data = await response.json();
          resolve(data);
        } catch (error) {
          reject(error);
        }
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  },

  async deleteImage(propertyId: string, imageUrl: string): Promise<DeleteImageResponse> {
    const response = await fetch(`/api/images/properties/${propertyId}/images`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ imageUrl }),
    });

    if (!response.ok) {
      throw new Error('Delete failed');
    }

    return response.json();
  },

  async reorderImages(propertyId: string, imageUrls: string[]): Promise<ReorderImagesResponse> {
    const response = await fetch(`/api/images/properties/${propertyId}/images/reorder`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ imageUrls }),
    });

    if (!response.ok) {
      throw new Error('Reorder failed');
    }

    return response.json();
  },

  async getImages(propertyId: string): Promise<GetImagesResponse> {
    const response = await fetch(`/api/images/properties/${propertyId}/images`);

    if (!response.ok) {
      throw new Error('Failed to fetch images');
    }

    return response.json();
  },
};