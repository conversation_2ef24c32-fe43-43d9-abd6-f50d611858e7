import { Request, Response } from 'express';
import { Logger } from '../../utils/logger';
import { storage } from '../../storage';
import { insertCommConversationSchema, insertCommMessageSchema } from '../../../shared/schema';
import { z } from 'zod';

export class GuestCommunicationController {
  
  async getConversations(req: Request, res: Response) {
    const startTime = Date.now();
    try {
      const guestId = req.params.guestId;
      
      if (!guestId) {
        return res.status(400).json({ error: 'Guest ID is required' });
      }
      
      Logger.info(`Guest conversations requested for guest: ${guestId}`);
      
      const conversations = await storage.getConversations(guestId, 'guest');
      
      Logger.api('GET', `/api/guest/conversations/${guestId}`, 200, Date.now() - startTime);
      res.json(conversations);
    } catch (error) {
      Logger.error('Error fetching guest conversations', error);
      Logger.api('GET', `/api/guest/conversations/${req.params.guestId}`, 500, Date.now() - startTime);
      res.status(500).json({ error: 'Failed to fetch guest conversations' });
    }
  }

  async getConversation(req: Request, res: Response) {
    const startTime = Date.now();
    try {
      const conversationId = req.params.conversationId;
      
      if (!conversationId) {
        return res.status(400).json({ error: 'Conversation ID is required' });
      }
      
      Logger.info(`Guest conversation requested: ${conversationId}`);
      
      const conversation = await storage.getConversation(conversationId);
      
      if (!conversation) {
        return res.status(404).json({ error: 'Conversation not found' });
      }
      
      Logger.api('GET', `/api/guest/conversation/${conversationId}`, 200, Date.now() - startTime);
      res.json(conversation);
    } catch (error) {
      Logger.error('Error fetching guest conversation', error);
      Logger.api('GET', `/api/guest/conversation/${req.params.conversationId}`, 500, Date.now() - startTime);
      res.status(500).json({ error: 'Failed to fetch guest conversation' });
    }
  }

  async createConversation(req: Request, res: Response) {
    const startTime = Date.now();
    try {
      const validation = insertCommConversationSchema.safeParse(req.body);
      
      if (!validation.success) {
        return res.status(400).json({ 
          error: 'Invalid conversation data', 
          details: validation.error.errors 
        });
      }
      
      const conversationData = validation.data;
      Logger.info(`Creating guest conversation between guest ${conversationData.guest_id} and host ${conversationData.host_id}`);
      
      const conversation = await storage.createConversation(conversationData);
      
      Logger.api('POST', '/api/guest/conversation', 201, Date.now() - startTime);
      res.status(201).json(conversation);
    } catch (error) {
      Logger.error('Error creating guest conversation', error);
      Logger.api('POST', '/api/guest/conversation', 500, Date.now() - startTime);
      res.status(500).json({ error: 'Failed to create guest conversation' });
    }
  }

  async getMessages(req: Request, res: Response) {
    const startTime = Date.now();
    try {
      const conversationId = req.params.conversationId;
      
      if (!conversationId) {
        return res.status(400).json({ error: 'Conversation ID is required' });
      }
      
      Logger.info(`Guest messages requested for conversation: ${conversationId}`);
      
      const messages = await storage.getMessages(conversationId);
      
      Logger.api('GET', `/api/guest/conversation/${conversationId}/messages`, 200, Date.now() - startTime);
      res.json(messages);
    } catch (error) {
      Logger.error('Error fetching guest messages', error);
      Logger.api('GET', `/api/guest/conversation/${req.params.conversationId}/messages`, 500, Date.now() - startTime);
      res.status(500).json({ error: 'Failed to fetch guest messages' });
    }
  }

  async sendMessage(req: Request, res: Response) {
    const startTime = Date.now();
    try {
      const conversationId = req.params.conversationId;
      const messageData = {
        ...req.body,
        conversation_id: conversationId
      };
      
      const validation = insertCommMessageSchema.safeParse(messageData);
      
      if (!validation.success) {
        return res.status(400).json({ 
          error: 'Invalid message data', 
          details: validation.error.errors 
        });
      }
      
      const validatedMessageData = validation.data;
      Logger.info(`Sending guest message in conversation: ${conversationId}`);
      
      const message = await storage.sendMessage(validatedMessageData);
      
      Logger.api('POST', `/api/guest/conversation/${conversationId}/messages`, 201, Date.now() - startTime);
      res.status(201).json(message);
    } catch (error) {
      Logger.error('Error sending guest message', error);
      Logger.api('POST', `/api/guest/conversation/${req.params.conversationId}/messages`, 500, Date.now() - startTime);
      res.status(500).json({ error: 'Failed to send guest message' });
    }
  }

  async markMessagesAsRead(req: Request, res: Response) {
    const startTime = Date.now();
    try {
      const conversationId = req.params.conversationId;
      const guestId = req.body.guestId;
      
      if (!conversationId || !guestId) {
        return res.status(400).json({ error: 'Conversation ID and guest ID are required' });
      }
      
      Logger.info(`Marking messages as read for conversation: ${conversationId}, guest: ${guestId}`);
      
      await storage.markMessagesAsRead(conversationId, guestId);
      
      Logger.api('PUT', `/api/guest/conversation/${conversationId}/read`, 200, Date.now() - startTime);
      res.json({ success: true });
    } catch (error) {
      Logger.error('Error marking messages as read', error);
      Logger.api('PUT', `/api/guest/conversation/${req.params.conversationId}/read`, 500, Date.now() - startTime);
      res.status(500).json({ error: 'Failed to mark messages as read' });
    }
  }
}

export const guestCommunicationController = new GuestCommunicationController();