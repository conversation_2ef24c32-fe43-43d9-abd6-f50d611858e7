import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Minus, Plus, Users, ChevronDown } from "lucide-react";
import { useTranslations } from "@/lib/translations";
import { useSearchLocalStorage } from "@/features/guest/search/hooks";

interface GuestSelectorProps {
  guests: {
    adults: number;
    children: number;
    infants: number;
    pets: number;
  };
  onChange: (guests: {
    adults: number;
    children: number;
    infants: number;
    pets: number;
  }) => void;
  compact?: boolean;
  placeholder?: string;
  autoOpen?: boolean;
  onFocus?: () => void;
}

export function GuestSelector({ guests, onChange, compact = false, placeholder, autoOpen = false, onFocus }: GuestSelectorProps) {
  const t = useTranslations("guestSelector");
  const [open, setOpen] = useState(false);
  const { updateDefaultGuests } = useSearchLocalStorage();

  const totalGuests = guests.adults + guests.children;

  const handleFocus = () => {
    onFocus?.();
    setOpen(true);
  };

  useEffect(() => {
    if (autoOpen) {
      setOpen(true);
    }
  }, [autoOpen]);

  const categories = [
    {
      id: "adults",
      label: t("adults.title"),
      description: t("adults.description"),
      value: guests.adults,
      min: 1,
      max: 16,
    },
    {
      id: "children",
      label: t("children.title"),
      description: t("children.description"),
      value: guests.children,
      min: 0,
      max: 5,
    },
    {
      id: "infants",
      label: t("infants.title"),
      description: t("infants.description"),
      value: guests.infants,
      min: 0,
      max: 5,
    },
    {
      id: "pets",
      label: t("pets.title"),
      description: t("pets.description"),
      value: guests.pets,
      min: 0,
      max: 5,
    },
  ];

  const updateGuests = (category: string, newValue: number) => {
    const updatedGuests = {
      ...guests,
      [category]: newValue,
    };
    onChange(updatedGuests);
  };

  const handleDone = () => {
    // Save guest preferences to localStorage when user confirms selection
    updateDefaultGuests(guests);
    setOpen(false);
  };

  const formatGuestText = () => {
    if (totalGuests === 0) return placeholder || t("guests");
    
    if (totalGuests === 1) {
      return `1 ${t("guest")}`;
    }
    
    return `${totalGuests} ${t("guests")}`;
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant={compact ? "ghost" : "outline"}
          className={compact 
            ? "w-full justify-start text-left font-medium p-0 h-auto text-sm text-gray-900 hover:bg-transparent"
            : "w-full justify-between font-normal"
          }
          onClick={handleFocus}
        >
          {compact ? (
            <span className="truncate">{formatGuestText()}</span>
          ) : (
            <>
              <div className="flex items-center">
                <Users className="mr-2 h-4 w-4" />
                <span className="truncate">{formatGuestText()}</span>
              </div>
              <ChevronDown className="h-4 w-4 opacity-50" />
            </>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 sm:w-96" align="start" sideOffset={8}>
        <div className="space-y-4">
          {categories.map((category) => (
            <div
              key={category.id}
              className="flex items-center justify-between"
            >
              <div className="flex-1">
                <div className="font-medium text-sm">{category.label}</div>
                <div className="text-xs text-muted-foreground">
                  {category.description}
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="icon"
                  className="h-8 w-8 rounded-full"
                  onClick={() =>
                    updateGuests(
                      category.id,
                      Math.max(category.min, category.value - 1),
                    )
                  }
                  disabled={category.value <= category.min}
                >
                  <Minus className="h-4 w-4" />
                </Button>
                <span className="w-8 text-center text-sm font-medium">
                  {category.value}
                </span>
                <Button
                  variant="outline"
                  size="icon"
                  className="h-8 w-8 rounded-full"
                  onClick={() =>
                    updateGuests(
                      category.id,
                      Math.min(category.max, category.value + 1),
                    )
                  }
                  disabled={category.value >= category.max}
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
            </div>
          ))}
        </div>
        <div className="mt-4 pt-4 border-t">
          <Button className="w-full" onClick={handleDone}>
            {t("done")}
          </Button>
        </div>
      </PopoverContent>
    </Popover>
  );
}