/**
 * Debug script to test storage retrieval logic
 * This will help identify why the user lookup is failing
 */

import { storage } from './dist/index.js';
import { supabase, supabaseAdmin } from './dist/index.js';

async function testStorageRetrieval() {
  console.log('🔍 Testing storage retrieval logic...');
  
  const userId = '50c1b056-db73-40c5-ae5f-80117d798cfb';
  const userEmail = '<EMAIL>';
  
  console.log('\n📊 Test 1: Direct Supabase query with regular client');
  try {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId);
    
    console.log('Regular client query result:');
    console.log('  Data:', data);
    console.log('  Error:', error);
    console.log('  Data length:', data?.length || 0);
  } catch (err) {
    console.error('Regular client error:', err);
  }
  
  console.log('\n📊 Test 2: Direct Supabase query with admin client');
  try {
    const { data, error } = await supabaseAdmin
      .from('users')
      .select('*')
      .eq('id', userId);
    
    console.log('Admin client query result:');
    console.log('  Data:', data);
    console.log('  Error:', error);
    console.log('  Data length:', data?.length || 0);
  } catch (err) {
    console.error('Admin client error:', err);
  }
  
  console.log('\n📊 Test 3: Storage.getUser method');
  try {
    const user = await storage.getUser(userId);
    console.log('Storage.getUser result:', user);
  } catch (err) {
    console.error('Storage.getUser error:', err);
  }
  
  console.log('\n📊 Test 4: Storage.getUserByEmail method');
  try {
    const user = await storage.getUserByEmail(userEmail);
    console.log('Storage.getUserByEmail result:', user);
  } catch (err) {
    console.error('Storage.getUserByEmail error:', err);
  }
  
  console.log('\n📊 Test 5: List all users (first 5)');
  try {
    const { data, error } = await supabaseAdmin
      .from('users')
      .select('id, email, username')
      .limit(5);
    
    console.log('All users query result:');
    console.log('  Data:', data);
    console.log('  Error:', error);
  } catch (err) {
    console.error('All users error:', err);
  }
  
  console.log('\n📊 Test 6: Check if user exists with different query');
  try {
    const { data, error } = await supabaseAdmin
      .from('users')
      .select('id, email, username')
      .eq('email', userEmail);
    
    console.log('User by email query result:');
    console.log('  Data:', data);
    console.log('  Error:', error);
  } catch (err) {
    console.error('User by email error:', err);
  }
  
  console.log('\n✅ Storage debugging complete');
}

testStorageRetrieval().catch(console.error);