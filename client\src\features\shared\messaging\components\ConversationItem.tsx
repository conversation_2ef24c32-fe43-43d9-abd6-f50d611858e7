import { formatDistanceToNow } from 'date-fns';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { MapPin, Calendar, Users } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ConversationItemProps {
  conversation: {
    id: string;
    guest_id: string;
    host_id: string;
    property_id?: string;
    subject?: string;
    status: string;
    last_message_at: string;
    unread_count: number;
    last_message?: {
      content: string;
      sender_type: string;
      created_at: string;
    };
    property?: {
      id: string;
      title: string;
      location: string;
    };
    participant?: {
      id: string;
      name: string;
      avatar_url?: string;
    };
  };
  isSelected: boolean;
  onClick: () => void;
  currentUserId?: string;
}

export const ConversationItem = ({ 
  conversation, 
  isSelected, 
  onClick, 
  currentUserId 
}: ConversationItemProps) => {
  const isFromCurrentUser = conversation.last_message?.sender_type === 
    (currentUserId === conversation.guest_id ? 'guest' : 'host');
  
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };
  
  const truncateMessage = (content: string, maxLength: number = 50) => {
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  };
  
  return (
    <Card 
      className={cn(
        "mb-2 cursor-pointer transition-all duration-200 hover:shadow-md",
        isSelected && "ring-2 ring-primary bg-primary/5",
        conversation.unread_count > 0 && !isSelected && "border-primary/30"
      )}
      onClick={onClick}
    >
      <div className="p-3">
        <div className="flex items-start gap-3">
          {/* Avatar */}
          <Avatar className="h-10 w-10 flex-shrink-0">
            <AvatarImage src={conversation.participant?.avatar_url} />
            <AvatarFallback className="bg-primary text-primary-foreground text-sm">
              {conversation.participant ? getInitials(conversation.participant.name) : '?'}
            </AvatarFallback>
          </Avatar>
          
          <div className="flex-1 min-w-0">
            {/* Header */}
            <div className="flex items-center justify-between mb-1">
              <div className="flex items-center gap-2 min-w-0">
                <h4 className={cn(
                  "font-medium truncate text-sm",
                  conversation.unread_count > 0 && "font-semibold"
                )}>
                  {conversation.participant?.name || 'Unknown User'}
                </h4>
                {conversation.unread_count > 0 && (
                  <Badge variant="destructive" className="h-5 px-2 text-xs flex-shrink-0">
                    {conversation.unread_count}
                  </Badge>
                )}
              </div>
              <span className="text-xs text-muted-foreground flex-shrink-0">
                {formatDistanceToNow(new Date(conversation.last_message_at), { addSuffix: true })}
              </span>
            </div>
            
            {/* Property Info */}
            {conversation.property && (
              <div className="flex items-center gap-1 mb-2 text-xs text-muted-foreground">
                <MapPin className="h-3 w-3" />
                <span className="truncate">{conversation.property.title}</span>
              </div>
            )}
            
            {/* Subject/Last Message */}
            <div className="space-y-1">
              {conversation.subject && (
                <p className="text-xs font-medium text-foreground truncate">
                  {conversation.subject}
                </p>
              )}
              
              {conversation.last_message && (
                <div className="flex items-center gap-1">
                  {isFromCurrentUser && (
                    <span className="text-xs text-muted-foreground">You:</span>
                  )}
                  <p className={cn(
                    "text-xs text-muted-foreground truncate",
                    conversation.unread_count > 0 && !isFromCurrentUser && "font-medium text-foreground"
                  )}>
                    {truncateMessage(conversation.last_message.content)}
                  </p>
                </div>
              )}
            </div>
            
            {/* Status Indicators */}
            <div className="flex items-center justify-between mt-2">
              <div className="flex items-center gap-2">
                {conversation.status === 'archived' && (
                  <Badge variant="secondary" className="text-xs">
                    Archived
                  </Badge>
                )}
              </div>
              
              {/* Online Indicator - Placeholder for now */}
              <div className="w-2 h-2 rounded-full bg-green-500 opacity-0" />
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
};