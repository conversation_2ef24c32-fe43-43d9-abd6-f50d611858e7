import { supabase } from '../supabase';
import { memoryCache } from '../dal/cache/memoryCache';

export interface LocationResult {
  id: string;
  name: string;
  type: string;
  country: string;
  region?: string;
  coordinates: { lat: number; lng: number };
  popularity_score: number;
  property_count: number;
  search_terms: string[];
  category?: string;
}

export interface LocationSearchParams {
  query: string;
  limit?: number;
  type?: string;
  country?: string;
  category?: string;
}

export class LocationService {
  private static readonly DEFAULT_LIMIT = 10;

  /**
   * Search locations with coordinates from database
   * This replaces external geocoding APIs with direct database lookup
   */
  async searchLocations(params: LocationSearchParams): Promise<LocationResult[]> {
    const { query, limit = LocationService.DEFAULT_LIMIT, type, country, category } = params;

    // Generate cache key based on parameters
    const cacheKey = `locations:search:${JSON.stringify(params)}`;
    const cached = memoryCache.get(cacheKey) as LocationResult[] | null;
    if (cached) return cached;

    if (!supabase) {
      console.warn('[LOCATION] Supabase client not available, returning empty results');
      return [];
    }

    try {
      let queryBuilder = supabase
        .from('locations')
        .select('*')
        .not('coordinates', 'is', null) // Only locations with coordinates
        .limit(limit);

      // Search by name or search terms
      if (query) {
        queryBuilder = queryBuilder.or(
          `name.ilike.%${query}%,search_terms.cs.{${query.toLowerCase()}}`
        );
      }

      // Apply filters
      if (type) {
        queryBuilder = queryBuilder.eq('type', type);
      }
      if (country) {
        queryBuilder = queryBuilder.eq('country', country);
      }
      if (category) {
        queryBuilder = queryBuilder.eq('category', category);
      }

      // Order by popularity score (highest first)
      queryBuilder = queryBuilder.order('popularity_score', { ascending: false });

      const { data, error } = await queryBuilder;

      if (error) {
        console.error('[LOCATION] Database error:', error);
        return [];
      }

      const results = data?.map(this.formatLocationResult) || [];
      // Cache for 30 minutes (location searches are relatively stable)
      memoryCache.set(cacheKey, results, 1800);
      
      return results;
    } catch (error) {
      console.error('[LOCATION] Search error:', error);
      return [];
    }
  }

  /**
   * Get location by ID with coordinates
   */
  async getLocationById(id: string): Promise<LocationResult | null> {
    // Check cache first
    const cacheKey = `location:by-id:${id}`;
    const cached = memoryCache.get(cacheKey) as LocationResult | null;
    if (cached) return cached;

    if (!supabase) {
      console.warn('[LOCATION] Supabase client not available');
      return null;
    }

    try {
      const { data, error } = await supabase
        .from('locations')
        .select('*')
        .eq('id', id)
        .not('coordinates', 'is', null)
        .single();

      if (error || !data) {
        console.warn(`[LOCATION] Location not found: ${id}`);
        return null;
      }

      const result = this.formatLocationResult(data);
      // Cache for 1 hour (individual location data is stable)
      memoryCache.set(cacheKey, result, 3600);
      
      return result;
    } catch (error) {
      console.error(`[LOCATION] Error getting location ${id}:`, error);
      return null;
    }
  }

  /**
   * Get popular destinations for autocomplete suggestions
   */
  async getPopularDestinations(limit: number = 20): Promise<LocationResult[]> {
    if (!supabase) {
      return [];
    }

    try {
      const { data, error } = await supabase
        .from('locations')
        .select('*')
        .not('coordinates', 'is', null)
        .eq('is_popular', true)
        .order('popularity_score', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('[LOCATION] Error getting popular destinations:', error);
        return [];
      }

      return data?.map(this.formatLocationResult) || [];
    } catch (error) {
      console.error('[LOCATION] Error getting popular destinations:', error);
      return [];
    }
  }

  /**
   * Get locations by coordinates within radius
   */
  async getLocationsByRadius(
    coordinates: { lat: number; lng: number },
    radiusKm: number = 50
  ): Promise<LocationResult[]> {
    if (!supabase) {
      return [];
    }

    try {
      const { data, error } = await supabase
        .from('locations')
        .select('*')
        .not('coordinates', 'is', null);

      if (error) {
        console.error('[LOCATION] Error getting locations by radius:', error);
        return [];
      }

      // Filter by distance using Haversine formula
      const locationsWithDistance = data
        ?.map(location => {
          const locationCoords = location.coordinates as { lat: number; lng: number };
          const distance = this.calculateDistance(coordinates, locationCoords);
          return { ...location, distance };
        })
        .filter(location => location.distance <= radiusKm)
        .sort((a, b) => a.distance - b.distance)
        .map(location => this.formatLocationResult(location));

      return locationsWithDistance || [];
    } catch (error) {
      console.error('[LOCATION] Error getting locations by radius:', error);
      return [];
    }
  }

  /**
   * Calculate distance between two coordinates using Haversine formula
   */
  private calculateDistance(
    coord1: { lat: number; lng: number },
    coord2: { lat: number; lng: number }
  ): number {
    const R = 6371; // Earth's radius in kilometers
    const dLat = this.toRad(coord2.lat - coord1.lat);
    const dLon = this.toRad(coord2.lng - coord1.lng);
    const lat1 = this.toRad(coord1.lat);
    const lat2 = this.toRad(coord2.lat);

    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.sin(dLon / 2) * Math.sin(dLon / 2) * Math.cos(lat1) * Math.cos(lat2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return R * c;
  }

  private toRad(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  /**
   * Format database result to LocationResult interface
   */
  private formatLocationResult(data: any): LocationResult {
    return {
      id: data.id,
      name: data.name,
      type: data.type,
      country: data.country,
      region: data.region,
      coordinates: data.coordinates,
      popularity_score: data.popularity_score || 0,
      property_count: data.property_count || 0,
      search_terms: data.search_terms || [],
      category: data.category
    };
  }
}

export const locationService = new LocationService();