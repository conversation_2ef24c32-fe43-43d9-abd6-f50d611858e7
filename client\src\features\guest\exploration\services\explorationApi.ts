import { ExplorationProperty } from '../types';

class ExplorationService {
  async getPopularInSpain(): Promise<ExplorationProperty[]> {
    const response = await fetch('/api/public/content/popular-spain');
    if (!response.ok) {
      throw new Error(`Failed to fetch popular Spain properties: ${response.statusText}`);
    }
    return response.json();
  }

  async getNewInFrance(): Promise<ExplorationProperty[]> {
    const response = await fetch('/api/public/content/new-france');
    if (!response.ok) {
      throw new Error(`Failed to fetch new France properties: ${response.statusText}`);
    }
    return response.json();
  }

  async getGuestFavorites(): Promise<ExplorationProperty[]> {
    const response = await fetch('/api/public/content/guest-favorites');
    if (!response.ok) {
      throw new Error(`Failed to fetch guest favorites: ${response.statusText}`);
    }
    return response.json();
  }
}

export const explorationService = new ExplorationService();