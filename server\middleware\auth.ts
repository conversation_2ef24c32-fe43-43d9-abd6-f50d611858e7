import { NextFunction, Request, Response } from "express";
import { storage } from "../storage";
import { supabase } from "../supabase";

export async function requireAuth(
  req: Request,
  res: Response,
  next: NextFunction
) {
  try {
    // Check for authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return res.status(401).json({
        success: false,
        message: "Authentication required - No valid token provided",
      });
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    // Check if we have Supabase properly configured
    const hasSupabase =
      process.env.SUPABASE_URL && process.env.SUPABASE_ANON_KEY;

    if (hasSupabase) {
      // Verify token with Supabase
      const {
        data: { user },
        error,
      } = await supabase.auth.getUser(token);

      if (error || !user) {
        return res.status(401).json({
          success: false,
          message: "Invalid or expired token",
        });
      }

      // Add user to request object for downstream use
      req.user = user;
      req.userId = user.id;
    } else {
      // Development mode - basic token validation or allow test tokens
      if (
        !token.startsWith("test_oauth_token_") &&
        token !== "development_token"
      ) {
        return res.status(401).json({
          success: false,
          message: "Invalid token format for development mode",
        });
      }

      // For development, set a mock user with proper UUID format
      const mockUser = {
        id: "a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11",
        email: "<EMAIL>",
        user_metadata: {
          name: "Test User",
          avatar_url: null,
        },
      };
      req.user = mockUser;
      req.userId = mockUser.id;
      console.log(
        "🔧 Development mode: Using mock user for property draft operations"
      );
    }

    next();
  } catch (error) {
    console.error("Authentication error:", error);
    res.status(401).json({
      success: false,
      message: "Authentication failed",
      code: "AUTH_FAILED",
      messageKey: "authErrors.authenticationFailed",
    });
  }
}

export function optionalAuth(req: Request, res: Response, next: NextFunction) {
  // This middleware doesn't block the request, just adds user info if available
  next();
}

// Middleware to require host status
export async function requireHost(
  req: Request,
  res: Response,
  next: NextFunction
) {
  try {
    // Development mode - set mock host user
    if (process.env.NODE_ENV === "development") {
      const authHeader = req.headers.authorization;
      if (!authHeader || !authHeader.startsWith("Bearer ")) {
        // Set mock host user for development
        const mockHostUser = {
          id: "a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11",
          email: "<EMAIL>",
          user_metadata: {
            name: "Test Host User",
            avatar_url: null,
          },
        };
        req.user = mockHostUser;
        req.userId = mockHostUser.id;
        console.log("🔧 Development mode: Using mock host user");
        return next();
      }
    }

    // First check authentication
    await new Promise<void>((resolve, reject) => {
      requireAuth(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    // Get user ID from auth middleware
    const userId = req.userId;
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: "Authentication required",
        code: "AUTH_REQUIRED",
        messageKey: "authErrors.authenticationRequired",
      });
    }

    // Check if user has host status
    const user = await storage.getUser(userId);
    if (!user || !user.is_host) {
      return res.status(403).json({
        success: false,
        message: "Host privileges required",
        code: "HOST_REQUIRED",
        messageKey: "authErrors.hostPrivilegesRequired",
      });
    }

    next();
  } catch (error) {
    console.error("Host authorization error:", error);
    res.status(403).json({
      success: false,
      message: "Host authorization failed",
      code: "HOST_AUTH_FAILED",
      messageKey: "authErrors.authenticationFailed",
    });
  }
}
