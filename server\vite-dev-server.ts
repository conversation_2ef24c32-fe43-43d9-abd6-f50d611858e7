import type { Express } from "express";
import type { Server } from "http";

// Custom Vite setup function with allowedHosts support (development only)
export async function setupViteWithCustomConfig(app: Express, server: Server, allowedHosts: string[]) {
  try {
    // Dynamic import to avoid loading vite in production
    const { createServer: createViteServer } = await import("vite");
    
    // Import vite config only in development mode
    let viteConfig;
    try {
      viteConfig = await import("../vite.config");
    } catch (error) {
      console.log("Warning: Could not import vite config, using defaults");
      viteConfig = { default: {} };
    }

    const serverOptions = {
      middlewareMode: true,
      hmr: { server },
      allowedHosts: allowedHosts,
    };

    const vite = await createViteServer({
      ...viteConfig.default,
      configFile: false,
      server: serverOptions,
      appType: "custom",
    });

    app.use(vite.middlewares);
    app.use("*", async (req, res, next) => {
      const url = req.originalUrl;
      try {
        const template = await vite.transformIndexHtml(url, `
          <!DOCTYPE html>
          <html lang="en">
            <head>
              <meta charset="UTF-8" />
              <meta name="viewport" content="width=device-width, initial-scale=1.0" />
              <title>VillaWise</title>
            </head>
            <body>
              <div id="root"></div>
              <script type="module" src="/src/main.tsx"></script>
            </body>
          </html>
        `);
        res.status(200).set({ "Content-Type": "text/html" }).end(template);
      } catch (e) {
        next(e);
      }
    });
  } catch (error) {
    console.error("Failed to setup Vite development server:", error);
    throw error;
  }
}