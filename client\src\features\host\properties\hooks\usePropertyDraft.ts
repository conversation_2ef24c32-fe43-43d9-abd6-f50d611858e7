import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useCallback, useEffect, useRef, useState } from "react";
import { PropertyDraft, PropertyWizardData } from "../types/property";

interface UsePropertyDraftOptions {
  autoSaveDelay?: number;
  onSaveSuccess?: (draft: PropertyDraft) => void;
  onSaveError?: (error: Error) => void;
}

export const usePropertyDraft = (
  draftId?: string,
  options: UsePropertyDraftOptions = {}
) => {
  const { autoSaveDelay = 500, onSaveSuccess, onSaveError } = options;
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [currentDraftId, setCurrentDraftId] = useState<string | undefined>(
    draftId
  );
  const saveTimeoutRef = useRef<ReturnType<typeof setTimeout>>();
  const queryClient = useQueryClient();
  const { toast } = useToast();

  // Fetch existing draft
  const { data: draft, isLoading } = useQuery({
    queryKey: ["/api/host/properties/drafts", currentDraftId],
    queryFn: () => apiRequest(`/api/host/properties/drafts/${currentDraftId}`),
    enabled: !!currentDraftId,
  });

  // Create new draft mutation
  const createDraftMutation = useMutation({
    mutationFn: (data: Partial<PropertyWizardData>) =>
      apiRequest("/api/host/properties/drafts", {
        method: "POST",
        body: JSON.stringify({ data, currentStep: 0 }),
        headers: { "Content-Type": "application/json" },
      }),
    onSuccess: (response: { success: boolean; data: PropertyDraft }) => {
      console.log("✅ Draft creation successful:", response);
      const newDraft = response.data;
      setCurrentDraftId(newDraft.id);
      queryClient.setQueryData(
        ["/api/host/properties/drafts", newDraft.id],
        newDraft
      );
      setHasUnsavedChanges(false);
      onSaveSuccess?.(newDraft);
    },
    onError: (error: Error) => {
      console.error("❌ Draft creation failed:", error);
      toast({
        title: "Error creating draft",
        description: `Failed to create property draft: ${error.message}`,
        variant: "destructive",
      });
      onSaveError?.(error);
    },
  });

  // Update draft mutation
  const updateDraftMutation = useMutation({
    mutationFn: (updates: {
      data?: Partial<PropertyWizardData>;
      currentStep?: number;
    }) =>
      apiRequest(`/api/host/properties/drafts/${currentDraftId}`, {
        method: "PATCH",
        body: JSON.stringify(updates),
        headers: { "Content-Type": "application/json" },
      }),
    onSuccess: (response: { success: boolean; data: PropertyDraft }) => {
      const updatedDraft = response.data;
      queryClient.setQueryData(
        ["/api/host/properties/drafts", currentDraftId],
        updatedDraft
      );
      setHasUnsavedChanges(false);
      onSaveSuccess?.(updatedDraft);
    },
    onError: (error: Error) => {
      toast({
        title: "Error saving draft",
        description: "Failed to save changes. Please try again.",
        variant: "destructive",
      });
      onSaveError?.(error);
    },
  });

  // Publish draft mutation
  const publishDraftMutation = useMutation({
    mutationFn: () =>
      apiRequest(`/api/host/properties/drafts/${currentDraftId}/publish`, {
        method: "POST",
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/properties"] });
      queryClient.removeQueries({
        queryKey: ["/api/host/properties/drafts", currentDraftId],
      });
      toast({
        title: "Property published",
        description: "Your property has been successfully published!",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error publishing property",
        description: "Failed to publish property. Please try again.",
        variant: "destructive",
      });
      onSaveError?.(error);
    },
  });

  // Delete draft mutation
  const deleteDraftMutation = useMutation({
    mutationFn: () =>
      apiRequest(`/api/host/properties/drafts/${currentDraftId}`, {
        method: "DELETE",
      }),
    onSuccess: () => {
      queryClient.removeQueries({
        queryKey: ["/api/host/properties/drafts", currentDraftId],
      });
      setCurrentDraftId(undefined);
    },
  });

  // Debounced save function
  const debouncedSave = useCallback(
    (data: Partial<PropertyWizardData>, step?: number) => {
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
      }

      setHasUnsavedChanges(true);

      saveTimeoutRef.current = setTimeout(() => {
        if (currentDraftId) {
          updateDraftMutation.mutate({
            data,
            ...(step !== undefined && { currentStep: step }),
          });
        } else {
          createDraftMutation.mutate(data);
        }
      }, autoSaveDelay);
    },
    [currentDraftId, autoSaveDelay, updateDraftMutation, createDraftMutation]
  );

  // Create new draft
  const createDraft = useCallback(
    (initialData: Partial<PropertyWizardData> = {}) => {
      createDraftMutation.mutate(initialData);
    },
    [createDraftMutation]
  );

  // Save current step
  const saveStep = useCallback(
    (step: number) => {
      if (currentDraftId) {
        updateDraftMutation.mutate({ currentStep: step });
      }
    },
    [currentDraftId, updateDraftMutation]
  );

  // Update data with auto-save
  const updateData = useCallback(
    (data: Partial<PropertyWizardData>, step?: number) => {
      debouncedSave(data, step);
    },
    [debouncedSave]
  );

  // Publish property
  const publishProperty = useCallback(() => {
    if (currentDraftId) {
      publishDraftMutation.mutate();
    }
  }, [currentDraftId, publishDraftMutation]);

  // Delete draft
  const deleteDraft = useCallback(() => {
    if (currentDraftId) {
      deleteDraftMutation.mutate();
    }
  }, [currentDraftId, deleteDraftMutation]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
      }
    };
  }, []);

  return {
    // Data
    draft,
    currentDraftId,

    // States
    isLoading,
    hasUnsavedChanges,
    isCreating: createDraftMutation.isPending,
    isSaving: updateDraftMutation.isPending,
    isPublishing: publishDraftMutation.isPending,
    isDeleting: deleteDraftMutation.isPending,

    // Actions
    createDraft,
    updateData,
    saveStep,
    publishProperty,
    deleteDraft,
  };
};
