import { QueryClient, QueryFunction } from "@tanstack/react-query";

const defaultQueryFn: QueryFunction = async ({ queryKey, signal }) => {
  const url = queryKey[0] as string;
  const params = (queryKey[1] as Record<string, unknown>) || {};

  // Build URL with query parameters
  const searchParams = new URLSearchParams();
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      if (typeof value === "object") {
        searchParams.append(key, JSON.stringify(value));
      } else {
        searchParams.append(key, String(value));
      }
    }
  });

  const fetchUrl = searchParams.toString() ? `${url}?${searchParams}` : url;

  // Add Authorization header if token exists
  const accessToken = localStorage.getItem("sb_access_token");
  const headers: HeadersInit = {
    "Content-Type": "application/json",
  };

  if (accessToken && accessToken !== "missing") {
    headers["Authorization"] = `Bearer ${accessToken}`;
  }
  // In development, we allow unauthenticated requests

  const response = await fetch(fetchUrl, {
    headers,
    credentials: "include",
    signal, // Pass AbortSignal to fetch for request cancellation
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }
  return response.json();
};

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      queryFn: defaultQueryFn,
      staleTime: 5 * 60 * 1000, // 5 minutes
      refetchOnWindowFocus: false,
    },
  },
});

export const apiRequest = async (url: string, options: RequestInit = {}) => {
  // Add Authorization header if token exists
  const accessToken = localStorage.getItem("sb_access_token");
  const headers: Record<string, string> = {
    "Content-Type": "application/json",
    ...(options.headers as Record<string, string>),
  };

  if (accessToken && accessToken !== "missing") {
    headers["Authorization"] = `Bearer ${accessToken}`;
  }
  // In development, we allow unauthenticated requests

  const response = await fetch(url, {
    headers,
    credentials: "include",
    ...options, // This includes the signal if provided
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return response.json();
};
