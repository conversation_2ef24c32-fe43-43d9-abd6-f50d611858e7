import { useState, useEffect, useCallback } from 'react';
import { useSearchLocalStorage } from './useSearchLocalStorage';

export interface SearchParams {
  location: string;
  dateRange?: { from: Date; to: Date };
  dateFlexibility?: number | "exact" | null;
  guests: {
    adults: number;
    children: number;
    infants: number;
    pets: number;
  };
}

export interface SearchHistoryItem {
  id: string;
  location: string;
  dateRange?: { from: Date; to: Date };
  dateFlexibility?: number | "exact" | null;
  guests: {
    adults: number;
    children: number;
    infants: number;
    pets: number;
  };
  timestamp: number;
  resultCount?: number;
}

export function useSearchPersistence() {
  const [isSaving, setIsSaving] = useState(false);
  
  const {
    searchDefaults,
    updateDefaultGuests,
    saveLastSearch,
    getLastSearch,
    clearLastSearch,
  } = useSearchLocalStorage();
  
  // Removed backend search history - only keeping latest search in localStorage

  // Initialize search state from localStorage with effect to handle updates
  const [searchState, setSearchState] = useState<SearchParams>({
    location: '',
    dateRange: undefined,
    dateFlexibility: null,
    guests: searchDefaults.guests,
  });

  // Initialize search state from localStorage when searchDefaults is available
  useEffect(() => {
    // Wait for searchDefaults to be loaded (after migration)
    if (!searchDefaults?.lastSearch && searchDefaults?.guests) {
      // searchDefaults loaded but no lastSearch - use defaults
      console.log('🔍 useSearchPersistence: Using default state');
      setSearchState({
        location: '',
        dateRange: undefined,
        dateFlexibility: null,
        guests: searchDefaults.guests,
      });
    } else if (searchDefaults?.lastSearch) {
      // lastSearch exists - restore it
      const lastSearch = getLastSearch();
      console.log('🔍 useSearchPersistence: Restoring last search:', lastSearch);
      if (lastSearch) {
        setSearchState(lastSearch);
      }
    }
  }, [searchDefaults?.lastSearch, searchDefaults?.guests]); // Only depend on the specific fields we need

  // Save search when it changes
  const persistSearch = useCallback((params: SearchParams) => {
    console.log('💾 useSearchPersistence: Persisting search:', params);
    setIsSaving(true);
    setSearchState(params);
    
    try {
      // Save to localStorage immediately
      saveLastSearch(params);
      console.log('💾 useSearchPersistence: Saved to localStorage');
      
      // Update default guests if they've changed
      if (JSON.stringify(params.guests) !== JSON.stringify(searchDefaults.guests)) {
        updateDefaultGuests(params.guests);
        console.log('💾 useSearchPersistence: Updated default guests');
      }
    } finally {
      setTimeout(() => setIsSaving(false), 300);
    }
  }, [saveLastSearch, updateDefaultGuests, searchDefaults.guests]);

  // Execute search (simplified - just save latest search)
  const executeSearch = useCallback(async (params: SearchParams) => {
    setIsSaving(true);
    
    try {
      persistSearch(params);
    } finally {
      setTimeout(() => setIsSaving(false), 500);
    }
  }, [persistSearch]);

  // Get last search (simplified)
  const getLastSearchData = useCallback(() => {
    return getLastSearch();
  }, [getLastSearch]);

  // Restore last search
  const restoreLastSearch = useCallback(() => {
    const lastSearch = getLastSearch();
    if (lastSearch) {
      setSearchState(lastSearch);
      return lastSearch;
    }
    return null;
  }, [getLastSearch]);

  // Clear search data
  const clearSearchData = useCallback(() => {
    clearLastSearch();
    setSearchState({
      location: '',
      dateRange: undefined,
      dateFlexibility: null,
      guests: searchDefaults.guests,
    });
  }, [clearLastSearch, searchDefaults.guests]);

  // Get smart defaults for new searches
  const getSmartDefaults = useCallback(() => {
    const lastSearch = getLastSearch();
    return {
      location: lastSearch?.location || '',
      dateRange: lastSearch?.dateRange,
      dateFlexibility: lastSearch?.dateFlexibility || null,
      guests: lastSearch?.guests || searchDefaults.guests,
    };
  }, [getLastSearch, searchDefaults.guests]);

  return {
    searchState,
    persistSearch,
    executeSearch,
    getLastSearchData,
    restoreLastSearch,
    clearSearchData,
    getSmartDefaults,
    hasLastSearch: !!getLastSearch(),
    isSaving,
  };
}