import express from 'express'
import { cacheRoute, cacheMonitor } from '../../middleware/cache'
import { getHostDashboardData } from '../../dal/aggregators/dashboard'
import { getHostProperties, createProperty, updateProperty, deleteProperty } from '../../dal/entities/properties'
import { getBookings, updateBookingStatus } from '../../dal/entities/bookings'
import { getMessages, sendMessage, markMessageAsRead } from '../../dal/entities/messages'
import { getCurrentUserProfile, updateUserProfile } from '../../dal/entities/users'

export const hostDashboardRouter = express.Router()

// Apply cache monitoring to all routes
hostDashboardRouter.use(cacheMonitor)

// Host Dashboard Overview
hostDashboardRouter.get('/dashboard', 
  cacheRoute(120), // 2 minute cache
  async (req, res) => {
    try {
      const authHeader = req.headers.authorization
      const dashboardData = await getHostDashboardData(authHeader!)
      
      res.json({
        success: true,
        data: dashboardData,
        cached: true
      })
    } catch (error) {
      console.error('Host dashboard error:', error)
      res.status(401).json({ 
        success: false, 
        message: error instanceof Error ? error.message : 'Failed to load dashboard' 
      })
    }
  }
)

// Host Properties
hostDashboardRouter.get('/properties', 
  cacheRoute(300), // 5 minute cache
  async (req, res) => {
    try {
      const authHeader = req.headers.authorization
      const properties = await getHostProperties(authHeader!)
      
      res.json({
        success: true,
        data: properties
      })
    } catch (error) {
      console.error('Host properties error:', error)
      res.status(401).json({ 
        success: false, 
        message: error instanceof Error ? error.message : 'Failed to load properties' 
      })
    }
  }
)

// Create Property
hostDashboardRouter.post('/properties', async (req, res) => {
  try {
    const authHeader = req.headers.authorization
    const property = await createProperty(authHeader!, req.body)
    
    res.json({
      success: true,
      data: property
    })
  } catch (error) {
    console.error('Create property error:', error)
    res.status(401).json({ 
      success: false, 
      message: error instanceof Error ? error.message : 'Failed to create property' 
    })
  }
})

// Update Property
hostDashboardRouter.put('/properties/:id', async (req, res) => {
  try {
    const authHeader = req.headers.authorization
    const property = await updateProperty(authHeader!, req.params.id, req.body)
    
    res.json({
      success: true,
      data: property
    })
  } catch (error) {
    console.error('Update property error:', error)
    res.status(401).json({ 
      success: false, 
      message: error instanceof Error ? error.message : 'Failed to update property' 
    })
  }
})

// Delete Property
hostDashboardRouter.delete('/properties/:id', async (req, res) => {
  try {
    const authHeader = req.headers.authorization
    await deleteProperty(authHeader!, req.params.id)
    
    res.json({
      success: true,
      message: 'Property deleted successfully'
    })
  } catch (error) {
    console.error('Delete property error:', error)
    res.status(401).json({ 
      success: false, 
      message: error instanceof Error ? error.message : 'Failed to delete property' 
    })
  }
})

// Host Bookings
hostDashboardRouter.get('/bookings', 
  cacheRoute(120), // 2 minute cache
  async (req, res) => {
    try {
      const authHeader = req.headers.authorization
      const { status, propertyId } = req.query
      
      const filters: any = {}
      if (status && typeof status === 'string') {
        filters.status = status.split(',')
      }
      if (propertyId) {
        filters.propertyId = propertyId as string
      }
      
      const bookings = await getBookings(authHeader!, 'host', filters)
      
      res.json({
        success: true,
        data: bookings
      })
    } catch (error) {
      console.error('Host bookings error:', error)
      res.status(401).json({ 
        success: false, 
        message: error instanceof Error ? error.message : 'Failed to load bookings' 
      })
    }
  }
)

// Update Booking Status
hostDashboardRouter.patch('/bookings/:id/status', async (req, res) => {
  try {
    const authHeader = req.headers.authorization
    const { status } = req.body
    
    const booking = await updateBookingStatus(authHeader!, req.params.id, status)
    
    res.json({
      success: true,
      data: booking
    })
  } catch (error) {
    console.error('Update booking status error:', error)
    res.status(401).json({ 
      success: false, 
      message: error instanceof Error ? error.message : 'Failed to update booking status' 
    })
  }
})

// Host Messages
hostDashboardRouter.get('/messages', 
  cacheRoute(60), // 1 minute cache
  async (req, res) => {
    try {
      const authHeader = req.headers.authorization
      const { conversationWith, unreadOnly } = req.query
      
      const filters: any = {}
      if (conversationWith) {
        filters.conversationWith = conversationWith as string
      }
      if (unreadOnly === 'true') {
        filters.unreadOnly = true
      }
      
      const messages = await getMessages(authHeader!, 'host', filters)
      
      res.json({
        success: true,
        data: messages
      })
    } catch (error) {
      console.error('Host messages error:', error)
      res.status(401).json({ 
        success: false, 
        message: error instanceof Error ? error.message : 'Failed to load messages' 
      })
    }
  }
)

// Send Message
hostDashboardRouter.post('/messages', async (req, res) => {
  try {
    const authHeader = req.headers.authorization
    const message = await sendMessage(authHeader!, req.body)
    
    res.json({
      success: true,
      data: message
    })
  } catch (error) {
    console.error('Send message error:', error)
    res.status(401).json({ 
      success: false, 
      message: error instanceof Error ? error.message : 'Failed to send message' 
    })
  }
})

// Mark Message as Read
hostDashboardRouter.patch('/messages/:id/read', async (req, res) => {
  try {
    const authHeader = req.headers.authorization
    await markMessageAsRead(authHeader!, req.params.id)
    
    res.json({
      success: true,
      message: 'Message marked as read'
    })
  } catch (error) {
    console.error('Mark message as read error:', error)
    res.status(401).json({ 
      success: false, 
      message: error instanceof Error ? error.message : 'Failed to mark message as read' 
    })
  }
})

// Host Profile
hostDashboardRouter.get('/profile', 
  cacheRoute(300), // 5 minute cache
  async (req, res) => {
    try {
      const authHeader = req.headers.authorization
      const profile = await getCurrentUserProfile(authHeader!)
      
      res.json({
        success: true,
        data: profile
      })
    } catch (error) {
      console.error('Host profile error:', error)
      res.status(401).json({ 
        success: false, 
        message: error instanceof Error ? error.message : 'Failed to load profile' 
      })
    }
  }
)

// Update Host Profile
hostDashboardRouter.put('/profile', async (req, res) => {
  try {
    const authHeader = req.headers.authorization
    const profile = await updateUserProfile(authHeader!, req.body)
    
    res.json({
      success: true,
      data: profile
    })
  } catch (error) {
    console.error('Update host profile error:', error)
    res.status(401).json({ 
      success: false, 
      message: error instanceof Error ? error.message : 'Failed to update profile' 
    })
  }
})