#!/bin/bash
set -e

echo "☢️  NUCLEAR OPTION: Complete Git history cleanup"
echo "⚠️  This will rewrite Git history to remove large files completely"
echo "⚠️  Use only if the standard fix doesn't work"
echo ""

read -p "Are you sure you want to rewrite Git history? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Cancelled."
    exit 1
fi

echo "🔥 Starting nuclear cleanup..."

# Backup current branch
git branch backup-before-cleanup || true

echo "🗑️ Removing large files from entire Git history..."

# Remove large files from entire Git history
git filter-branch --force --index-filter \
'git rm --cached --ignore-unmatch data/geonames/alternateNamesV2.txt data/geonames/alternateNamesV2.zip data/geonames/ES.txt data/geonames/*.zip data/geonames/*.txt' \
--prune-empty --tag-name-filter cat -- --all

echo "🧹 Cleaning up filter-branch remnants..."
git for-each-ref --format="delete %(refname)" refs/original | git update-ref --stdin || true
git reflog expire --expire=now --all
git gc --aggressive --prune=now

echo "📊 Repository size after nuclear cleanup:"
du -sh .git

echo "🚀 Attempting push..."
if git push origin main --force; then
    echo "✅ SUCCESS! Repository pushed with rewritten history"
    echo "🎉 Nuclear cleanup successful!"
else
    echo "❌ Even nuclear cleanup failed. Contact GitHub support."
fi

echo ""
echo "⚠️  Git history has been rewritten"
echo "⚠️  Other collaborators will need to re-clone the repository"