/**
 * Popular Locations Seeder
 * 
 * Seeds popular Costa Blanca locations for autocomplete and suggestions.
 * Migrates hardcoded POPULAR_LOCATIONS from LocationStep.tsx
 */

export default {
  name: '04_popular_locations',
  description: 'Popular Costa Blanca locations for autocomplete',
  environment: 'shared' as const,
  order: 5,

  async execute(supabase: any): Promise<void> {
    console.log('   📍 Seeding popular locations...');

    // Get Spain country ID and Valencia region ID
    const { data: countryData } = await supabase
      .from('country_codes')
      .select('id')
      .eq('iso_alpha2', 'ES')
      .single();

    const { data: regionData } = await supabase
      .from('region_codes')
      .select('id')
      .eq('geonames_admin1_code', 'VC')
      .single();

    if (!countryData || !regionData) {
      throw new Error('Spain country or Valencia region not found');
    }

    const spainId = countryData.id;
    const valenciaId = regionData.id;

    // Popular Costa Blanca locations (from LocationStep.tsx POPULAR_LOCATIONS)
    const popularLocations = [
      {
        name: '<PERSON><PERSON><PERSON>',
        country_id: spainId,
        region_id: valenciaId,
        coordinates: `(38.5385,-0.1313)`, // PostgreSQL POINT format
        latitude: 38.5385,
        longitude: -0.1313,
        popularity_score: 95,
        display_order: 1,
        is_active: true
      },
      {
        name: 'Javea',
        country_id: spainId,
        region_id: valenciaId,
        coordinates: `(38.7914,0.1616)`,
        latitude: 38.7914,
        longitude: 0.1616,
        popularity_score: 85,
        display_order: 2,
        is_active: true
      },
      {
        name: 'Calpe',
        country_id: spainId,
        region_id: valenciaId,
        coordinates: `(38.6429,0.0420)`,
        latitude: 38.6429,
        longitude: 0.0420,
        popularity_score: 80,
        display_order: 3,
        is_active: true
      },
      {
        name: 'Denia',
        country_id: spainId,
        region_id: valenciaId,
        coordinates: `(38.8408,0.1061)`,
        latitude: 38.8408,
        longitude: 0.1061,
        popularity_score: 82,
        display_order: 4,
        is_active: true
      },
      {
        name: 'Altea',
        country_id: spainId,
        region_id: valenciaId,
        coordinates: `(38.5998,-0.0545)`,
        latitude: 38.5998,
        longitude: -0.0545,
        popularity_score: 78,
        display_order: 5,
        is_active: true
      },
      {
        name: 'Moraira',
        country_id: spainId,
        region_id: valenciaId,
        coordinates: `(38.6858,0.1213)`,
        latitude: 38.6858,
        longitude: 0.1213,
        popularity_score: 75,
        display_order: 6,
        is_active: true
      },
      {
        name: 'Benissa',
        country_id: spainId,
        region_id: valenciaId,
        coordinates: `(38.7186,0.0501)`,
        latitude: 38.7186,
        longitude: 0.0501,
        popularity_score: 70,
        display_order: 7,
        is_active: true
      },
      {
        name: 'Alicante',
        country_id: spainId,
        region_id: valenciaId,
        coordinates: `(38.3452,-0.4815)`,
        latitude: 38.3452,
        longitude: -0.4815,
        popularity_score: 90,
        display_order: 8,
        is_active: true
      },
      {
        name: 'Villajoyosa',
        country_id: spainId,
        region_id: valenciaId,
        coordinates: `(38.5084,-0.2331)`,
        latitude: 38.5084,
        longitude: -0.2331,
        popularity_score: 72,
        display_order: 9,
        is_active: true
      },
      {
        name: 'Torrevieja',
        country_id: spainId,
        region_id: valenciaId,
        coordinates: `(37.9785,-0.6811)`,
        latitude: 37.9785,
        longitude: -0.6811,
        popularity_score: 88,
        display_order: 10,
        is_active: true
      }
    ];

    // Insert popular locations
    const { error: locationError } = await supabase
      .from('popular_locations')
      .upsert(popularLocations, { onConflict: 'name,country_id' });

    if (locationError) {
      throw new Error(`Failed to seed popular locations: ${locationError.message}`);
    }

    console.log(`   ✅ Seeded ${popularLocations.length} popular locations`);

    // Try to link with existing geonames_locations if they exist
    console.log('   🔗 Linking with GeoNames data...');
    
    const linkingQueries = popularLocations.map(location => `
      UPDATE popular_locations 
      SET geonames_id = (
        SELECT geonames_id 
        FROM geonames_locations 
        WHERE name ILIKE '${location.name}%' 
          AND country_code = 'ES'
          AND admin1_code = 'VC'
        ORDER BY similarity(name, '${location.name}') DESC
        LIMIT 1
      )
      WHERE name = '${location.name}' AND country_id = ${spainId}
    `);

    // Execute linking queries (best effort - don't fail if GeoNames data not available)
    for (const query of linkingQueries) {
      try {
        await supabase.rpc('execute_sql', { sql: query });
      } catch (error) {
        console.warn(`   ⚠️ Could not link ${query.match(/name = '([^']+)'/)?.[1]} with GeoNames data`);
      }
    }

    console.log('   ✅ Completed popular locations seeding');
  },

  async rollback(supabase: any): Promise<void> {
    // Get Spain country ID
    const { data: countryData } = await supabase
      .from('country_codes')
      .select('id')
      .eq('iso_alpha2', 'ES')
      .single();

    if (countryData) {
      await supabase
        .from('popular_locations')
        .delete()
        .eq('country_id', countryData.id);
    }
    
    console.log('   🧹 Cleaned popular locations');
  }
};
