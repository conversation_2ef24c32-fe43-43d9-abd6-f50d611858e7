import { supabase } from '../../supabase';
import { Logger } from '../../utils/logger';

export interface ConversationData {
  id: string;
  guest_id: string;
  host_id: string;
  property_id?: string;
  booking_id?: string;
  subject?: string;
  status: string;
  last_message_at: string;
  created_at: string;
  updated_at: string;
  unread_count?: number;
  last_message?: {
    content: string;
    sender_type: string;
    created_at: string;
  };
  property?: {
    id: string;
    title: string;
    location: string;
  };
  participant?: {
    id: string;
    name: string;
    avatar_url?: string;
  };
}

export class ConversationService {
  
  /**
   * Get conversations for a user with pagination and filtering
   */
  async getConversations(
    userId: string, 
    userType: 'guest' | 'host',
    options: {
      status?: string;
      propertyId?: string;
      limit?: number;
      offset?: number;
      search?: string;
    } = {}
  ): Promise<{ conversations: ConversationData[]; total: number }> {
    try {
      const { status = 'active', limit = 20, offset = 0, search, propertyId } = options;
      
      let query = supabase
        .from('conversations')
        .select(`
          *,
          messages!conversations_last_message_fkey (
            content,
            sender_type,
            created_at
          ),
          host_properties!property_id (
            id,
            title,
            location
          )
        `)
        .eq(userType === 'guest' ? 'guest_id' : 'host_id', userId)
        .eq('status', status)
        .order('last_message_at', { ascending: false })
        .range(offset, offset + limit - 1);
      
      if (propertyId) {
        query = query.eq('property_id', propertyId);
      }
      
      if (search) {
        query = query.or(`subject.ilike.%${search}%,messages.content.ilike.%${search}%`);
      }
      
      const { data: conversations, error, count } = await query;
      
      if (error) {
        Logger.error('Error fetching conversations:', error);
        throw new Error(`Failed to fetch conversations: ${error.message}`);
      }
      
      // Get unread counts for each conversation
      const conversationsWithUnread = await Promise.all(
        (conversations || []).map(async (conv) => {
          const { count: unreadCount } = await supabase
            .from('messages')
            .select('*', { count: 'exact', head: true })
            .eq('conversation_id', conv.id)
            .neq('sender_id', userId)
            .eq('message_status', 'sent');
          
          return {
            ...conv,
            unread_count: unreadCount || 0,
            last_message: conv.messages?.[0] || null,
            property: conv.host_properties || null
          };
        })
      );
      
      return {
        conversations: conversationsWithUnread,
        total: count || 0
      };
    } catch (error) {
      Logger.error('ConversationService.getConversations error:', error);
      throw error;
    }
  }
  
  /**
   * Create a new conversation
   */
  async createConversation(data: {
    guestId: string;
    hostId: string;
    propertyId?: string;
    bookingId?: string;
    subject?: string;
  }): Promise<ConversationData> {
    try {
      // Check if conversation already exists
      const { data: existing } = await supabase
        .from('conversations')
        .select('*')
        .eq('guest_id', data.guestId)
        .eq('host_id', data.hostId)
        .eq('property_id', data.propertyId || '')
        .eq('status', 'active')
        .single();
      
      if (existing) {
        return existing;
      }
      
      const { data: conversation, error } = await supabase
        .from('conversations')
        .insert({
          guest_id: data.guestId,
          host_id: data.hostId,
          property_id: data.propertyId,
          booking_id: data.bookingId,
          subject: data.subject || 'Property Inquiry',
          status: 'active',
          last_message_at: new Date().toISOString()
        })
        .select()
        .single();
      
      if (error) {
        Logger.error('Error creating conversation:', error);
        throw new Error(`Failed to create conversation: ${error.message}`);
      }
      
      // Create conversation participants
      await supabase
        .from('conversation_participants')
        .insert([
          {
            conversation_id: conversation.id,
            user_id: data.guestId,
            user_type: 'guest'
          },
          {
            conversation_id: conversation.id,
            user_id: data.hostId,
            user_type: 'host'
          }
        ]);
      
      Logger.info(`New conversation created: ${conversation.id}`);
      return conversation;
    } catch (error) {
      Logger.error('ConversationService.createConversation error:', error);
      throw error;
    }
  }
  
  /**
   * Get single conversation with full details
   */
  async getConversationById(
    conversationId: string, 
    userId: string
  ): Promise<ConversationData | null> {
    try {
      const { data: conversation, error } = await supabase
        .from('conversations')
        .select(`
          *,
          host_properties!property_id (
            id,
            title,
            location,
            images
          ),
          users!guest_id (
            id,
            username,
            first_name,
            last_name,
            avatar_url
          ),
          users!host_id (
            id,
            username,
            first_name,
            last_name,
            avatar_url
          )
        `)
        .eq('id', conversationId)
        .single();
      
      if (error || !conversation) {
        Logger.error('Conversation not found:', conversationId);
        return null;
      }
      
      // Verify user has access to this conversation
      if (conversation.guest_id !== userId && conversation.host_id !== userId) {
        Logger.error('User unauthorized for conversation:', { conversationId, userId });
        return null;
      }
      
      return conversation;
    } catch (error) {
      Logger.error('ConversationService.getConversationById error:', error);
      throw error;
    }
  }
  
  /**
   * Archive conversation
   */
  async archiveConversation(conversationId: string, userId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('conversations')
        .update({ 
          status: 'archived',
          updated_at: new Date().toISOString()
        })
        .eq('id', conversationId)
        .or(`guest_id.eq.${userId},host_id.eq.${userId}`);
      
      if (error) {
        Logger.error('Error archiving conversation:', error);
        return false;
      }
      
      Logger.info(`Conversation archived: ${conversationId} by ${userId}`);
      return true;
    } catch (error) {
      Logger.error('ConversationService.archiveConversation error:', error);
      return false;
    }
  }
  
  /**
   * Update last read timestamp for user
   */
  async markAsRead(conversationId: string, userId: string): Promise<boolean> {
    try {
      // Update participant's last read timestamp
      const { error: participantError } = await supabase
        .from('conversation_participants')
        .update({ last_read_at: new Date().toISOString() })
        .eq('conversation_id', conversationId)
        .eq('user_id', userId);
      
      if (participantError) {
        Logger.error('Error updating participant read timestamp:', participantError);
      }
      
      // Mark messages as read
      const { error: messageError } = await supabase
        .from('messages')
        .update({ 
          message_status: 'read',
          read_at: new Date().toISOString()
        })
        .eq('conversation_id', conversationId)
        .neq('sender_id', userId)
        .eq('message_status', 'sent');
      
      if (messageError) {
        Logger.error('Error marking messages as read:', messageError);
        return false;
      }
      
      return true;
    } catch (error) {
      Logger.error('ConversationService.markAsRead error:', error);
      return false;
    }
  }
}

export const conversationService = new ConversationService();