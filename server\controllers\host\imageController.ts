import { Request, Response } from 'express';
import { supabase } from '../../supabase';
import { v4 as uuidv4 } from 'uuid';

export class ImageController {
  async uploadPropertyImage(req: Request, res: Response) {
    try {
      const { propertyId } = req.params;
      const { file, fileName } = req.body;

      if (!file || !fileName) {
        return res.status(400).json({ 
          success: false, 
          message: 'File and fileName are required' 
        });
      }

      // Generate unique filename
      const fileExtension = fileName.split('.').pop();
      const uniqueFileName = `${propertyId}/${uuidv4()}.${fileExtension}`;

      // Convert base64 to buffer
      const fileBuffer = Buffer.from(file.replace(/^data:image\/\w+;base64,/, ''), 'base64');

      // Upload to Supabase Storage
      if (!supabase) {
        return res.status(500).json({ 
          success: false, 
          message: "Storage service unavailable" 
        });
      }
      
      const { data, error } = await supabase.storage
        .from('property-images')
        .upload(uniqueFileName, fileBuffer, {
          contentType: `image/${fileExtension}`,
          upsert: false
        });

      if (error) {
        console.error('Upload error:', error);
        return res.status(500).json({ 
          success: false, 
          message: 'Failed to upload image' 
        });
      }

      // Get public URL
      const { data: publicUrlData } = supabase.storage
        .from('property-images')
        .getPublicUrl(uniqueFileName);

      const imageUrl = publicUrlData.publicUrl;

      // Update property images array
      const { data: property, error: fetchError } = await supabase
        .from('properties')
        .select('images')
        .eq('id', propertyId)
        .single();

      if (fetchError) {
        console.error('Property fetch error:', fetchError);
        return res.status(500).json({ 
          success: false, 
          message: 'Failed to fetch property' 
        });
      }

      const currentImages = property.images || [];
      const updatedImages = [...currentImages, imageUrl];

      // Update property with new image URL
      const { error: updateError } = await supabase
        .from('properties')
        .update({ images: updatedImages })
        .eq('id', propertyId);

      if (updateError) {
        console.error('Property update error:', updateError);
        return res.status(500).json({ 
          success: false, 
          message: 'Failed to update property' 
        });
      }

      res.json({
        success: true,
        imageUrl,
        message: 'Image uploaded successfully'
      });

    } catch (error) {
      console.error('Upload error:', error);
      res.status(500).json({ 
        success: false, 
        message: 'Internal server error' 
      });
    }
  }

  async deletePropertyImage(req: Request, res: Response) {
    try {
      const { propertyId } = req.params;
      const { imageUrl } = req.body;

      if (!imageUrl) {
        return res.status(400).json({ 
          success: false, 
          message: 'Image URL is required' 
        });
      }

      // Extract filename from URL
      const urlParts = imageUrl.split('/');
      const fileName = urlParts[urlParts.length - 1];
      const filePath = `${propertyId}/${fileName}`;

      // Delete from Supabase Storage
      if (!supabase) {
        return res.status(500).json({ 
          success: false, 
          message: 'Database service unavailable' 
        });
      }

      const { error: deleteError } = await supabase.storage
        .from('property-images')
        .remove([filePath]);

      if (deleteError) {
        console.error('Delete error:', deleteError);
        return res.status(500).json({ 
          success: false, 
          message: 'Failed to delete image from storage' 
        });
      }

      // Update property images array
      const { data: property, error: fetchError } = await supabase
        .from('properties')
        .select('images')
        .eq('id', propertyId)
        .single();

      if (fetchError) {
        console.error('Property fetch error:', fetchError);
        return res.status(500).json({ 
          success: false, 
          message: 'Failed to fetch property' 
        });
      }

      const currentImages = property.images || [];
      const updatedImages = currentImages.filter((img: string) => img !== imageUrl);

      // Update property with new image array
      const { error: updateError } = await supabase
        .from('properties')
        .update({ images: updatedImages })
        .eq('id', propertyId);

      if (updateError) {
        console.error('Property update error:', updateError);
        return res.status(500).json({ 
          success: false, 
          message: 'Failed to update property' 
        });
      }

      res.json({
        success: true,
        message: 'Image deleted successfully'
      });

    } catch (error) {
      console.error('Delete error:', error);
      res.status(500).json({ 
        success: false, 
        message: 'Internal server error' 
      });
    }
  }

  async reorderPropertyImages(req: Request, res: Response) {
    try {
      const { propertyId } = req.params;
      const { imageUrls } = req.body;

      if (!Array.isArray(imageUrls)) {
        return res.status(400).json({ 
          success: false, 
          message: 'imageUrls must be an array' 
        });
      }

      // Update property with reordered images
      if (!supabase) {
        return res.status(500).json({ 
          success: false, 
          message: 'Database service unavailable' 
        });
      }

      const { error: updateError } = await supabase
        .from('properties')
        .update({ images: imageUrls })
        .eq('id', propertyId);

      if (updateError) {
        console.error('Property update error:', updateError);
        return res.status(500).json({ 
          success: false, 
          message: 'Failed to update property' 
        });
      }

      res.json({
        success: true,
        message: 'Images reordered successfully'
      });

    } catch (error) {
      console.error('Reorder error:', error);
      res.status(500).json({ 
        success: false, 
        message: 'Internal server error' 
      });
    }
  }

  async getPropertyImages(req: Request, res: Response) {
    try {
      const { propertyId } = req.params;

      if (!supabase) {
        return res.status(500).json({ 
          success: false, 
          message: 'Database service unavailable' 
        });
      }

      const { data: property, error } = await supabase
        .from('properties')
        .select('images')
        .eq('id', propertyId)
        .single();

      if (error) {
        console.error('Property fetch error:', error);
        return res.status(500).json({ 
          success: false, 
          message: 'Failed to fetch property images' 
        });
      }

      res.json({
        success: true,
        images: property.images || []
      });

    } catch (error) {
      console.error('Fetch error:', error);
      res.status(500).json({ 
        success: false, 
        message: 'Internal server error' 
      });
    }
  }
}

export const imageController = new ImageController();