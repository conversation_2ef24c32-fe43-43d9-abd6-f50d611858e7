import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import { 
  X, 
  Search, 
  Send, 
  FileText, 
  Star,
  Eye,
  Calendar,
  Home,
  MessageSquare,
  Wrench
} from 'lucide-react';
import { useTranslations } from '@/lib/translations';
import { cn } from '@/lib/utils';

interface Template {
  id: string;
  name: string;
  content: string;
  category: 'inquiry' | 'booking' | 'checkin' | 'checkout' | 'support' | 'custom';
  dynamic_fields: Record<string, string>;
  usage_count: number;
  is_active: boolean;
}

interface TemplateSelectorProps {
  templates: Template[];
  onSelect: (templateId: string, variables: Record<string, string>) => Promise<void>;
  onClose: () => void;
  conversationData?: {
    property?: {
      title: string;
      location: string;
    };
    participant?: {
      name: string;
    };
  };
}

const CATEGORY_ICONS = {
  inquiry: MessageSquare,
  booking: Calendar,
  checkin: Home,
  checkout: Home,
  support: Wrench,
  custom: FileText
};

const CATEGORY_COLORS = {
  inquiry: 'bg-blue-500/10 text-blue-700 border-blue-200',
  booking: 'bg-green-500/10 text-green-700 border-green-200',
  checkin: 'bg-purple-500/10 text-purple-700 border-purple-200',
  checkout: 'bg-orange-500/10 text-orange-700 border-orange-200',
  support: 'bg-red-500/10 text-red-700 border-red-200',
  custom: 'bg-gray-500/10 text-gray-700 border-gray-200'
};

export const TemplateSelector = ({ 
  templates, 
  onSelect, 
  onClose, 
  conversationData 
}: TemplateSelectorProps) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);
  const [variables, setVariables] = useState<Record<string, string>>({});
  const [previewContent, setPreviewContent] = useState('');
  const t = useTranslations('messaging');
  
  // Filter templates
  const filteredTemplates = templates.filter(template => {
    if (!template.is_active) return false;
    
    const matchesSearch = !searchQuery || 
      template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      template.content.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesCategory = !selectedCategory || template.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });
  
  // Group templates by category
  const templatesByCategory = filteredTemplates.reduce((acc, template) => {
    if (!acc[template.category]) {
      acc[template.category] = [];
    }
    acc[template.category].push(template);
    return acc;
  }, {} as Record<string, Template[]>);
  
  // Get categories with counts
  const categories = Object.keys(templatesByCategory).map(category => ({
    key: category,
    label: t(`categories.${category}`),
    count: templatesByCategory[category].length
  }));
  
  // Process template content with variables
  useEffect(() => {
    if (!selectedTemplate) {
      setPreviewContent('');
      return;
    }
    
    let content = selectedTemplate.content;
    
    // Replace variables in content
    Object.entries(variables).forEach(([key, value]) => {
      const placeholder = `{${key}}`;
      content = content.replace(new RegExp(placeholder, 'g'), value || placeholder);
    });
    
    setPreviewContent(content);
  }, [selectedTemplate, variables]);
  
  // Initialize variables when template is selected
  useEffect(() => {
    if (!selectedTemplate) {
      setVariables({});
      return;
    }
    
    const initialVariables: Record<string, string> = {};
    
    // Extract variables from template content
    const variableMatches = selectedTemplate.content.match(/{([^}]+)}/g);
    if (variableMatches) {
      variableMatches.forEach(match => {
        const key = match.slice(1, -1);
        
        // Auto-fill from conversation data
        if (key === 'guest_name' && conversationData?.participant?.name) {
          initialVariables[key] = conversationData.participant.name;
        } else if (key === 'property_name' && conversationData?.property?.title) {
          initialVariables[key] = conversationData.property.title;
        } else if (key === 'property_location' && conversationData?.property?.location) {
          initialVariables[key] = conversationData.property.location;
        } else {
          initialVariables[key] = selectedTemplate.dynamic_fields[key] || '';
        }
      });
    }
    
    setVariables(initialVariables);
  }, [selectedTemplate, conversationData]);
  
  const handleSendTemplate = async () => {
    if (!selectedTemplate) return;
    
    try {
      await onSelect(selectedTemplate.id, variables);
      onClose();
    } catch (error) {
      console.error('Failed to send template:', error);
    }
  };
  
  const hasRequiredVariables = () => {
    if (!selectedTemplate) return false;
    
    const variableMatches = selectedTemplate.content.match(/{([^}]+)}/g);
    if (!variableMatches) return true;
    
    return variableMatches.every(match => {
      const key = match.slice(1, -1);
      return variables[key]?.trim();
    });
  };
  
  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center">
      <Card className="w-full max-w-4xl max-h-[90vh] mx-4 flex flex-col">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                {t('templates.title')}
              </CardTitle>
              <p className="text-sm text-muted-foreground mt-1">
                {t('templates.description')}
              </p>
            </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        
        <CardContent className="flex-1 flex gap-4 min-h-0">
          {/* Templates List */}
          <div className="w-1/2 flex flex-col">
            {/* Search and Categories */}
            <div className="space-y-3 mb-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder={t('templates.search')}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-9"
                />
              </div>
              
              <div className="flex flex-wrap gap-2">
                <Button
                  variant={selectedCategory === null ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedCategory(null)}
                >
                  {t('templates.allCategories')} ({templates.length})
                </Button>
                {categories.map(category => {
                  const Icon = CATEGORY_ICONS[category.key as keyof typeof CATEGORY_ICONS];
                  return (
                    <Button
                      key={category.key}
                      variant={selectedCategory === category.key ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setSelectedCategory(category.key)}
                      className="flex items-center gap-1"
                    >
                      <Icon className="h-3 w-3" />
                      {category.label} ({category.count})
                    </Button>
                  );
                })}
              </div>
            </div>
            
            {/* Templates */}
            <ScrollArea className="flex-1">
              <div className="space-y-2">
                {filteredTemplates.map(template => {
                  const Icon = CATEGORY_ICONS[template.category];
                  return (
                    <Card
                      key={template.id}
                      className={cn(
                        "p-3 cursor-pointer transition-all hover:shadow-md",
                        selectedTemplate?.id === template.id && "ring-2 ring-primary"
                      )}
                      onClick={() => setSelectedTemplate(template)}
                    >
                      <div className="flex items-start gap-3">
                        <Icon className="h-4 w-4 mt-0.5 text-muted-foreground" />
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="font-medium text-sm truncate">
                              {template.name}
                            </h4>
                            <Badge 
                              variant="outline" 
                              className={cn("text-xs", CATEGORY_COLORS[template.category])}
                            >
                              {t(`categories.${template.category}`)}
                            </Badge>
                          </div>
                          <p className="text-xs text-muted-foreground line-clamp-2">
                            {template.content}
                          </p>
                          <div className="flex items-center gap-2 mt-2 text-xs text-muted-foreground">
                            <Star className="h-3 w-3" />
                            <span>{template.usage_count} {t('templates.uses')}</span>
                          </div>
                        </div>
                      </div>
                    </Card>
                  );
                })}
                
                {filteredTemplates.length === 0 && (
                  <div className="text-center py-8">
                    <FileText className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                    <p className="text-muted-foreground">
                      {t('templates.noTemplates')}
                    </p>
                  </div>
                )}
              </div>
            </ScrollArea>
          </div>
          
          {/* Preview and Variables */}
          <div className="w-1/2 flex flex-col">
            {selectedTemplate ? (
              <>
                <div className="mb-4">
                  <h3 className="font-medium mb-2 flex items-center gap-2">
                    <Eye className="h-4 w-4" />
                    {t('templates.preview')}
                  </h3>
                  
                  {/* Variables */}
                  {Object.keys(variables).length > 0 && (
                    <div className="space-y-3 mb-4">
                      <h4 className="text-sm font-medium">{t('templates.variables')}</h4>
                      {Object.entries(variables).map(([key, value]) => (
                        <div key={key}>
                          <label className="text-xs text-muted-foreground capitalize">
                            {key.replace(/_/g, ' ')}
                          </label>
                          <Input
                            value={value}
                            onChange={(e) => setVariables(prev => ({
                              ...prev,
                              [key]: e.target.value
                            }))}
                            placeholder={`Enter ${key.replace(/_/g, ' ')}`}
                            className="mt-1"
                          />
                        </div>
                      ))}
                    </div>
                  )}
                  
                  <Separator className="my-3" />
                </div>
                
                {/* Preview */}
                <ScrollArea className="flex-1 mb-4">
                  <Card className="p-4 bg-muted/50">
                    <p className="text-sm whitespace-pre-wrap">
                      {previewContent || selectedTemplate.content}
                    </p>
                  </Card>
                </ScrollArea>
                
                {/* Actions */}
                <div className="flex gap-2">
                  <Button variant="outline" onClick={onClose} className="flex-1">
                    {t('cancel')}
                  </Button>
                  <Button 
                    onClick={handleSendTemplate}
                    disabled={!hasRequiredVariables()}
                    className="flex-1"
                  >
                    <Send className="h-4 w-4 mr-2" />
                    {t('templates.send')}
                  </Button>
                </div>
              </>
            ) : (
              <div className="flex-1 flex items-center justify-center">
                <div className="text-center">
                  <FileText className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                  <p className="text-muted-foreground">
                    {t('templates.selectTemplate')}
                  </p>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};