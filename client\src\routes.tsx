import { Route, Switch } from 'wouter';
import Index from './pages/Index';
import Search from './pages/Search';
import PropertyDetails from './pages/PropertyDetails';
import HostDashboardPage from './pages/HostDashboardPage';
import GuestDashboardPage from './pages/GuestDashboardPage';
// AddProperty component replaced with HostOnboarding flow
import { HostOnboarding } from './pages/HostOnboarding';
import HostUpgradePage from './pages/HostUpgradePage';
import AdminPanel from './pages/AdminPanel';
import NotFound from './pages/NotFound';
import OAuthCallback from './components/auth/OAuthCallback';
import PrivacyPolicy from './pages/PrivacyPolicy';
import TermsOfService from './pages/TermsOfService';
import { LoginPage } from './pages/LoginPage';
import { RegisterPage } from './pages/RegisterPage';
import { ForgotPasswordPage } from './pages/ForgotPasswordPage';
import { ResetPasswordPage } from './pages/ResetPasswordPage';
import EmailConfirmationPage from './pages/EmailConfirmationPage';
import { ProtectedRoute } from './components/auth/ProtectedRoute';

export function AppRoutes() {
  return (
    <Switch>
      <Route path="/" component={Index} />
      <Route path="/search" component={Search} />
      <Route path="/property/:id" component={PropertyDetails} />
      <Route path="/guest/dashboard">
        <ProtectedRoute>
          <GuestDashboardPage />
        </ProtectedRoute>
      </Route>
      <Route path="/host/dashboard">
        <ProtectedRoute>
          <HostDashboardPage />
        </ProtectedRoute>
      </Route>
      <Route path="/host/add-property">
        <ProtectedRoute>
          <HostOnboarding />
        </ProtectedRoute>
      </Route>
      <Route path="/host/onboarding">
        <ProtectedRoute>
          <HostOnboarding />
        </ProtectedRoute>
      </Route>
      <Route path="/host/upgrade">
        <ProtectedRoute>
          <HostUpgradePage />
        </ProtectedRoute>
      </Route>
      <Route path="/admin" component={AdminPanel} />
      <Route path="/auth/callback" component={OAuthCallback} />
      <Route path="/login" component={LoginPage} />
      <Route path="/register" component={RegisterPage} />
      <Route path="/email-confirmation" component={EmailConfirmationPage} />
      <Route path="/forgot-password" component={ForgotPasswordPage} />
      <Route path="/reset-password" component={ResetPasswordPage} />
      <Route path="/privacy" component={PrivacyPolicy} />
      <Route path="/terms" component={TermsOfService} />
      <Route component={NotFound} />
    </Switch>
  );
}