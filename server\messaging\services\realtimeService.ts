import { supabase } from '../../supabase';
import { Logger } from '../../utils/logger';
import { RealtimeChannel } from '@supabase/supabase-js';

export interface TypingIndicator {
  userId: string;
  conversationId: string;
  isTyping: boolean;
  timestamp: string;
}

export interface OnlinePresence {
  userId: string;
  status: 'online' | 'away' | 'offline';
  lastSeen: string;
}

export class RealtimeService {
  private channels: Map<string, RealtimeChannel> = new Map();
  private typingTimeouts: Map<string, NodeJS.Timeout> = new Map();
  
  /**
   * Subscribe to conversation messages for real-time updates
   */
  async subscribeToConversation(
    conversationId: string,
    userId: string,
    onMessage: (message: any) => void,
    onTyping?: (typing: TypingIndicator) => void,
    onPresence?: (presence: OnlinePresence) => void
  ): Promise<RealtimeChannel> {
    try {
      const channelName = `conversation:${conversationId}`;
      
      // Remove existing channel if it exists
      if (this.channels.has(channelName)) {
        await this.unsubscribeFromConversation(conversationId);
      }
      
      const channel = supabase
        .channel(channelName)
        // Listen for new messages
        .on('postgres_changes', {
          event: 'INSERT',
          schema: 'public',
          table: 'messages',
          filter: `conversation_id=eq.${conversationId}`
        }, (payload) => {
          Logger.info(`New message received in conversation ${conversationId}`);
          onMessage(payload.new);
        })
        // Listen for message updates (edits, read status)
        .on('postgres_changes', {
          event: 'UPDATE',
          schema: 'public',
          table: 'messages',
          filter: `conversation_id=eq.${conversationId}`
        }, (payload) => {
          Logger.info(`Message updated in conversation ${conversationId}`);
          onMessage(payload.new);
        })
        // Listen for typing indicators
        .on('broadcast', {
          event: 'typing'
        }, (payload) => {
          if (onTyping && payload.payload.userId !== userId) {
            onTyping(payload.payload as TypingIndicator);
          }
        })
        // Listen for presence updates
        .on('presence', {
          event: 'sync'
        }, () => {
          if (onPresence) {
            const state = channel.presenceState();
            Object.values(state).forEach((presence: any) => {
              if (presence[0]?.userId !== userId) {
                onPresence(presence[0] as OnlinePresence);
              }
            });
          }
        })
        .subscribe((status) => {
          if (status === 'SUBSCRIBED') {
            Logger.info(`Subscribed to conversation ${conversationId}`);
            // Track user presence
            if (onPresence) {
              channel.track({
                userId,
                status: 'online',
                lastSeen: new Date().toISOString()
              });
            }
          }
        });
      
      this.channels.set(channelName, channel);
      return channel;
    } catch (error) {
      Logger.error('RealtimeService.subscribeToConversation error:', error);
      throw error;
    }
  }
  
  /**
   * Unsubscribe from conversation updates
   */
  async unsubscribeFromConversation(conversationId: string): Promise<void> {
    try {
      const channelName = `conversation:${conversationId}`;
      const channel = this.channels.get(channelName);
      
      if (channel) {
        await supabase.removeChannel(channel);
        this.channels.delete(channelName);
        Logger.info(`Unsubscribed from conversation ${conversationId}`);
      }
    } catch (error) {
      Logger.error('RealtimeService.unsubscribeFromConversation error:', error);
    }
  }
  
  /**
   * Send typing indicator
   */
  async sendTypingIndicator(
    conversationId: string,
    userId: string,
    isTyping: boolean
  ): Promise<void> {
    try {
      const channelName = `conversation:${conversationId}`;
      const channel = this.channels.get(channelName);
      
      if (!channel) {
        Logger.warn(`No channel found for conversation ${conversationId}`);
        return;
      }
      
      const payload: TypingIndicator = {
        userId,
        conversationId,
        isTyping,
        timestamp: new Date().toISOString()
      };
      
      await channel.send({
        type: 'broadcast',
        event: 'typing',
        payload
      });
      
      // Auto-clear typing indicator after 3 seconds
      if (isTyping) {
        const timeoutKey = `${conversationId}:${userId}`;
        
        // Clear existing timeout
        if (this.typingTimeouts.has(timeoutKey)) {
          clearTimeout(this.typingTimeouts.get(timeoutKey)!);
        }
        
        // Set new timeout
        const timeout = setTimeout(() => {
          this.sendTypingIndicator(conversationId, userId, false);
          this.typingTimeouts.delete(timeoutKey);
        }, 3000);
        
        this.typingTimeouts.set(timeoutKey, timeout);
      }
    } catch (error) {
      Logger.error('RealtimeService.sendTypingIndicator error:', error);
    }
  }
  
  /**
   * Subscribe to user's conversations for unread count updates
   */
  async subscribeToUserConversations(
    userId: string,
    userType: 'guest' | 'host',
    onConversationUpdate: (conversation: any) => void
  ): Promise<RealtimeChannel> {
    try {
      const channelName = `user:${userId}:conversations`;
      
      // Remove existing channel if it exists
      if (this.channels.has(channelName)) {
        const existingChannel = this.channels.get(channelName);
        await supabase.removeChannel(existingChannel!);
      }
      
      const channel = supabase
        .channel(channelName)
        // Listen for conversation updates
        .on('postgres_changes', {
          event: 'UPDATE',
          schema: 'public',
          table: 'conversations',
          filter: userType === 'guest' 
            ? `guest_id=eq.${userId}`
            : `host_id=eq.${userId}`
        }, (payload) => {
          Logger.info(`Conversation updated for user ${userId}`);
          onConversationUpdate(payload.new);
        })
        // Listen for new conversations
        .on('postgres_changes', {
          event: 'INSERT',
          schema: 'public',
          table: 'conversations',
          filter: userType === 'guest' 
            ? `guest_id=eq.${userId}`
            : `host_id=eq.${userId}`
        }, (payload) => {
          Logger.info(`New conversation created for user ${userId}`);
          onConversationUpdate(payload.new);
        })
        .subscribe((status) => {
          if (status === 'SUBSCRIBED') {
            Logger.info(`Subscribed to conversations for user ${userId}`);
          }
        });
      
      this.channels.set(channelName, channel);
      return channel;
    } catch (error) {
      Logger.error('RealtimeService.subscribeToUserConversations error:', error);
      throw error;
    }
  }
  
  /**
   * Unsubscribe from user conversations
   */
  async unsubscribeFromUserConversations(userId: string): Promise<void> {
    try {
      const channelName = `user:${userId}:conversations`;
      const channel = this.channels.get(channelName);
      
      if (channel) {
        await supabase.removeChannel(channel);
        this.channels.delete(channelName);
        Logger.info(`Unsubscribed from conversations for user ${userId}`);
      }
    } catch (error) {
      Logger.error('RealtimeService.unsubscribeFromUserConversations error:', error);
    }
  }
  
  /**
   * Update user presence status
   */
  async updatePresence(
    conversationId: string,
    userId: string,
    status: 'online' | 'away' | 'offline'
  ): Promise<void> {
    try {
      const channelName = `conversation:${conversationId}`;
      const channel = this.channels.get(channelName);
      
      if (!channel) {
        Logger.warn(`No channel found for conversation ${conversationId}`);
        return;
      }
      
      await channel.track({
        userId,
        status,
        lastSeen: new Date().toISOString()
      });
      
      Logger.info(`Presence updated for user ${userId}: ${status}`);
    } catch (error) {
      Logger.error('RealtimeService.updatePresence error:', error);
    }
  }
  
  /**
   * Cleanup all subscriptions
   */
  async cleanup(): Promise<void> {
    try {
      // Clear all typing timeouts
      this.typingTimeouts.forEach((timeout) => {
        clearTimeout(timeout);
      });
      this.typingTimeouts.clear();
      
      // Remove all channels
      for (const [channelName, channel] of this.channels.entries()) {
        await supabase.removeChannel(channel);
        Logger.info(`Removed channel: ${channelName}`);
      }
      
      this.channels.clear();
      Logger.info('RealtimeService cleanup completed');
    } catch (error) {
      Logger.error('RealtimeService.cleanup error:', error);
    }
  }
  
  /**
   * Get active channels count for monitoring
   */
  getActiveChannelsCount(): number {
    return this.channels.size;
  }
  
  /**
   * Get channel info for debugging
   */
  getChannelInfo(): { name: string; status: string }[] {
    return Array.from(this.channels.entries()).map(([name, channel]) => ({
      name,
      status: channel.state
    }));
  }
}

export const realtimeService = new RealtimeService();