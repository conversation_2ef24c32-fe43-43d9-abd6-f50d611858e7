
import { UserSession } from '../auth/session'

export interface MessageData {
  id: string
  sender_id: string
  receiver_id: string
  property_id?: string
  booking_id?: string
  content: string
  message_type: 'text' | 'booking_inquiry' | 'booking_update' | 'system'
  read: boolean
  created_at: string
  updated_at: string
  sender?: {
    id: string
    full_name: string
    email: string
    profile_picture?: string
    role: string
  }
  receiver?: {
    id: string
    full_name: string
    email: string
    profile_picture?: string
    role: string
  }
  property?: {
    id: string
    title: string
    location: string
  }
  booking?: {
    id: string
    check_in_date: string
    check_out_date: string
    status: string
  }
}

export class MessageDTO {
  id: string
  senderId: string
  receiverId: string
  propertyId?: string
  bookingId?: string
  content: string
  messageType: string
  read: boolean
  createdAt: string
  sender?: {
    id: string
    name: string
    profilePicture?: string
    role: string
  }
  receiver?: {
    id: string
    name: string
    profilePicture?: string
    role: string
  }
  property?: {
    id: string
    title: string
    location: string
  }
  booking?: {
    id: string
    checkInDate: string
    checkOutDate: string
    status: string
  }

  constructor(message: MessageD<PERSON>, viewerRole?: UserSession['role'], viewerId?: string) {
    this.id = message.id
    this.senderId = message.sender_id
    this.receiverId = message.receiver_id
    this.propertyId = message.property_id
    this.bookingId = message.booking_id
    this.content = message.content
    this.messageType = message.message_type
    this.read = message.read
    this.createdAt = message.created_at

    // Include sender details
    if (message.sender) {
      this.sender = {
        id: message.sender.id,
        name: message.sender.full_name || message.sender.email || 'User',
        profilePicture: message.sender.profile_picture,
        role: message.sender.role
      }
    }

    // Include receiver details
    if (message.receiver) {
      this.receiver = {
        id: message.receiver.id,
        name: message.receiver.full_name || message.receiver.email || 'User',
        profilePicture: message.receiver.profile_picture,
        role: message.receiver.role
      }
    }

    // Include property details if available
    if (message.property) {
      this.property = {
        id: message.property.id,
        title: message.property.title,
        location: message.property.location
      }
    }

    // Include booking details if available
    if (message.booking) {
      this.booking = {
        id: message.booking.id,
        checkInDate: message.booking.check_in_date,
        checkOutDate: message.booking.check_out_date,
        status: message.booking.status
      }
    }
  }

  static fromArray(messages: MessageData[], viewerRole?: UserSession['role'], viewerId?: string): MessageDTO[] {
    return messages.map(message => new MessageDTO(message, viewerRole, viewerId))
  }

  // Check if user can view this message
  canView(userId: string): boolean {
    return this.senderId === userId || this.receiverId === userId
  }
}

export interface MessageFilters {
  conversationWith?: string
  propertyId?: string
  bookingId?: string
  messageType?: string
  unreadOnly?: boolean
}

export interface MessageStats {
  total: number
  unread: number
  sent: number
  received: number
}

export interface Conversation {
  participantId: string
  participantName: string
  participantAvatar?: string
  lastMessage: MessageDTO
  unreadCount: number
  propertyId?: string
  propertyTitle?: string
}