import { Toaster as Sonner } from "@/components/ui/sonner";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClientProvider } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import { <PERSON><PERSON>erRouter } from "react-router-dom";
import { DevCacheManager } from "./components/DevCacheManager";
import { VersionChecker } from "./components/VersionChecker";
import { useOAuthHandler } from "./features/auth/hooks/useOAuthHandler";
import {
  Locale,
  LocaleContext,
  defaultLocale,
  getTranslations,
} from "./lib/i18n";
import { queryClient } from "./lib/queryClient";
import { AppRoutes } from "./routes";

const OAuthHandler = () => {
  useOAuthHandler();
  return null;
};

const App = () => {
  const [locale, setLocale] = useState<Locale>(defaultLocale);
  const [messages, setMessages] = useState<any>({});
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadMessages = async () => {
      try {
        setIsLoading(true);
        const newMessages = await getTranslations(locale);
        setMessages(newMessages);
        console.log(
          "App: Messages loaded successfully",
          Object.keys(newMessages)
        );
      } catch (error) {
        console.error("App: Failed to load messages", error);
        setMessages({});
      } finally {
        setIsLoading(false);
      }
    };
    loadMessages();
  }, [locale]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-lg">Loading VillaWise...</div>
      </div>
    );
  }

  return (
    <BrowserRouter>
      <QueryClientProvider client={queryClient}>
        <LocaleContext.Provider value={{ locale, setLocale }}>
          <VersionChecker
            checkInterval={5 * 60 * 1000} // Check every 5 minutes
            onVersionChange={(newVersion, oldVersion) => {
              console.log(`VillaWise updated: ${oldVersion} → ${newVersion}`);
              // Optionally show user notification about update
            }}
          >
            <TooltipProvider>
              <Toaster />
              <Sonner />
              <OAuthHandler />
              <AppRoutes />
              <DevCacheManager />
            </TooltipProvider>
          </VersionChecker>
        </LocaleContext.Provider>
      </QueryClientProvider>
    </BrowserRouter>
  );
};

export default App;
