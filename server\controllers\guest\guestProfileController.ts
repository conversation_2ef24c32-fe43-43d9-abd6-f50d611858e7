import { Request, Response } from 'express';
import { Logger } from '../../utils/logger';
import { storage } from '../../storage';
import { insertGuestProfileSchema } from '../../../shared/schema';
import { z } from 'zod';

export class GuestProfileController {
  
  async getProfile(req: Request, res: Response) {
    const startTime = Date.now();
    try {
      const userId = req.params.userId;
      const requestingUserId = (req as any).userId; // From auth middleware
      
      if (!userId) {
        return res.status(400).json({ error: 'User ID is required' });
      }
      
      // RLS-style check: Users can only access their own profile
      if (userId !== requestingUserId) {
        return res.status(403).json({ 
          error: 'Access denied - You can only access your own profile' 
        });
      }
      
      Logger.info(`Guest profile requested for user: ${userId}`);
      
      const profile = await storage.getGuestProfile(userId);
      
      if (!profile) {
        return res.status(404).json({ error: 'Guest profile not found' });
      }
      
      Logger.api('GET', `/api/guest/profile/${userId}`, 200, Date.now() - startTime);
      res.json(profile);
    } catch (error) {
      Logger.error('Error fetching guest profile', error);
      Logger.api('GET', `/api/guest/profile/${req.params.userId}`, 500, Date.now() - startTime);
      res.status(500).json({ error: 'Failed to fetch guest profile' });
    }
  }

  async createProfile(req: Request, res: Response) {
    const startTime = Date.now();
    try {
      const validation = insertGuestProfileSchema.safeParse(req.body);
      
      if (!validation.success) {
        return res.status(400).json({ 
          error: 'Invalid profile data', 
          details: validation.error.errors 
        });
      }
      
      const profileData = validation.data;
      Logger.info(`Creating guest profile for user: ${profileData.user_id}`);
      
      const profile = await storage.createGuestProfile(profileData);
      
      Logger.api('POST', '/api/guest/profile', 201, Date.now() - startTime);
      res.status(201).json(profile);
    } catch (error) {
      Logger.error('Error creating guest profile', error);
      Logger.api('POST', '/api/guest/profile', 500, Date.now() - startTime);
      res.status(500).json({ error: 'Failed to create guest profile' });
    }
  }

  async updateProfile(req: Request, res: Response) {
    const startTime = Date.now();
    try {
      const userId = req.params.userId;
      
      if (!userId) {
        return res.status(400).json({ error: 'User ID is required' });
      }
      
      const updateSchema = insertGuestProfileSchema.partial().omit({ user_id: true });
      const validation = updateSchema.safeParse(req.body);
      
      if (!validation.success) {
        return res.status(400).json({ 
          error: 'Invalid profile data', 
          details: validation.error.errors 
        });
      }
      
      const updates = validation.data;
      Logger.info(`Updating guest profile for user: ${userId}`);
      
      const profile = await storage.updateGuestProfile(userId, updates);
      
      if (!profile) {
        return res.status(404).json({ error: 'Guest profile not found' });
      }
      
      Logger.api('PUT', `/api/guest/profile/${userId}`, 200, Date.now() - startTime);
      res.json(profile);
    } catch (error) {
      Logger.error('Error updating guest profile', error);
      Logger.api('PUT', `/api/guest/profile/${req.params.userId}`, 500, Date.now() - startTime);
      res.status(500).json({ error: 'Failed to update guest profile' });
    }
  }
}

export const guestProfileController = new GuestProfileController();