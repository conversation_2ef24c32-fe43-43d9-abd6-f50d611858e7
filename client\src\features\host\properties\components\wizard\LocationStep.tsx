import React, { useState } from 'react';
import { useTranslations } from '@/lib/translations';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { MapPin, Search, Check } from 'lucide-react';
import { PropertyWizardData } from '../../types/property';

interface LocationStepProps {
  data: Partial<PropertyWizardData>;
  onUpdate: (data: Partial<PropertyWizardData>) => void;
}

// Popular Costa Blanca locations
const POPULAR_LOCATIONS = [
  { city: 'Benidorm', region: 'Alicante', coordinates: { lat: 38.5385, lng: -0.1313 } },
  { city: 'Javea', region: 'Alicante', coordinates: { lat: 38.7914, lng: 0.1616 } },
  { city: 'Calpe', region: 'Alicante', coordinates: { lat: 38.6429, lng: 0.0420 } },
  { city: 'Denia', region: 'Alicante', coordinates: { lat: 38.8408, lng: 0.1061 } },
  { city: 'Altea', region: 'Alicante', coordinates: { lat: 38.5998, lng: -0.0545 } },
  { city: 'Moraira', region: 'Alicante', coordinates: { lat: 38.6858, lng: 0.1213 } },
  { city: 'Benissa', region: 'Alicante', coordinates: { lat: 38.7186, lng: 0.0501 } },
  { city: 'Alicante', region: 'Alicante', coordinates: { lat: 38.3452, lng: -0.4815 } },
  { city: 'Villajoyosa', region: 'Alicante', coordinates: { lat: 38.5084, lng: -0.2331 } },
  { city: 'Torrevieja', region: 'Alicante', coordinates: { lat: 37.9785, lng: -0.6811 } }
];

export const LocationStep = ({ data, onUpdate }: LocationStepProps) => {
  const t = useTranslations('hostOnboarding.location');
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddressForm, setShowAddressForm] = useState(false);
  const [formData, setFormData] = useState({
    address: data.address || '',
    city: data.city || '',
    postalCode: '',
    province: 'Alicante'
  });

  const filteredLocations = POPULAR_LOCATIONS.filter(location =>
    location.city.toLowerCase().includes(searchTerm.toLowerCase()) ||
    location.region.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleLocationSelect = (location: typeof POPULAR_LOCATIONS[0]) => {
    onUpdate({
      city: location.city,
      coordinates: location.coordinates,
      country: 'Spain'
    });
    setFormData(prev => ({
      ...prev,
      city: location.city
    }));
    setShowAddressForm(true);
  };

  const handleAddressSubmit = () => {
    onUpdate({
      address: formData.address,
      city: formData.city,
      country: 'Spain'
    });
  };

  const isFormValid = formData.address && formData.city;

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          {t('title')}
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          {t('description')}
        </p>
      </div>

      {/* Search Bar */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        <Input
          placeholder={t('searchPlaceholder')}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10"
        />
      </div>

      {/* Popular Locations */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          {t('popularLocations')}
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredLocations.map((location) => (
            <Card
              key={location.city}
              className={`cursor-pointer transition-all hover:shadow-md ${
                data.city === location.city
                  ? 'ring-2 ring-primary border-primary bg-primary/5'
                  : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
              }`}
              onClick={() => handleLocationSelect(location)}
            >
              <CardContent className="p-4">
                <div className="flex items-start justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <MapPin className="h-4 w-4 text-primary" />
                    <h4 className="font-medium text-gray-900 dark:text-white">
                      {location.city}
                    </h4>
                  </div>
                  {data.city === location.city && (
                    <Check className="h-4 w-4 text-primary" />
                  )}
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {location.region}, Spain
                </p>
                <Badge variant="secondary" className="mt-2 text-xs">
                  Costa Blanca
                </Badge>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Address Form */}
      {showAddressForm && (
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              {t('addressDetails')}
            </h3>
            <div className="space-y-4">
              <div>
                <Label htmlFor="address">{t('streetAddress')}</Label>
                <Input
                  id="address"
                  value={formData.address}
                  onChange={(e) => setFormData(prev => ({ ...prev, address: e.target.value }))}
                  placeholder={t('addressPlaceholder')}
                />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="city">{t('city')}</Label>
                  <Input
                    id="city"
                    value={formData.city}
                    onChange={(e) => setFormData(prev => ({ ...prev, city: e.target.value }))}
                    placeholder={t('cityPlaceholder')}
                  />
                </div>
                
                <div>
                  <Label htmlFor="postalCode">{t('postalCode')}</Label>
                  <Input
                    id="postalCode"
                    value={formData.postalCode}
                    onChange={(e) => setFormData(prev => ({ ...prev, postalCode: e.target.value }))}
                    placeholder="03000"
                  />
                </div>
              </div>
              
              <div>
                <Label htmlFor="province">{t('province')}</Label>
                <Input
                  id="province"
                  value={formData.province}
                  onChange={(e) => setFormData(prev => ({ ...prev, province: e.target.value }))}
                  placeholder="Alicante"
                />
              </div>
            </div>
          </div>

          <Button
            type="button"
            onClick={handleAddressSubmit}
            disabled={!isFormValid}
            className="w-full bg-primary hover:bg-primary/90"
          >
            {t('confirmAddress')}
          </Button>
        </div>
      )}

      {/* Privacy Notice */}
      <div className="bg-blue-50 dark:bg-blue-900/20 p-6 rounded-lg">
        <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">
          {t('privacyNoticeTitle')}
        </h4>
        <p className="text-sm text-blue-700 dark:text-blue-300">
          {t('privacyNoticeText')}
        </p>
      </div>

      {/* Illustration */}
      <div className="text-center py-8">
        <div className="inline-flex items-center justify-center w-32 h-32 bg-primary/10 rounded-full mb-4">
          <MapPin className="h-16 w-16 text-primary" />
        </div>
        <p className="text-sm text-gray-600 dark:text-gray-400 max-w-md mx-auto">
          {t('illustrationText')}
        </p>
      </div>
    </div>
  );
};