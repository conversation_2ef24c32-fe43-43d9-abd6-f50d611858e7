import { useTranslations } from "@/lib/translations";

interface TranslatedErrorMessageProps {
  messageKey?: string;
  fallbackMessage?: string;
  params?: Record<string, string | number>;
  className?: string;
}

export function TranslatedErrorMessage({ 
  messageKey, 
  fallbackMessage, 
  params = {}, 
  className = "" 
}: TranslatedErrorMessageProps) {
  const t = useTranslations('validation');
  
  if (!messageKey && !fallbackMessage) {
    return null;
  }
  
  // Try to translate the key, fall back to original message
  let translatedMessage = fallbackMessage || "";
  
  if (messageKey) {
    try {
      // Try to get translation, use fallback if not found
      const translation = t(messageKey, params);
      if (translation !== messageKey) {
        translatedMessage = translation;
      }
    } catch (error) {
      // If translation fails, use fallback message
      console.warn(`Translation key "${messageKey}" not found, using fallback message`);
    }
  }
  
  return (
    <div className={`text-sm text-red-600 ${className}`}>
      {translatedMessage}
    </div>
  );
}