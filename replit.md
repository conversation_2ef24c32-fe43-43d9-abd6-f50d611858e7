# VillaWise - Vacation Rental Platform

## Overview

VillaWise is a modern full-stack vacation rental platform built with a feature-based architecture. The application allows users to search for vacation properties, explore destinations, and discover travel inspiration. Currently featuring 100+ authentic Spanish properties in Costa Blanca region with complete authentication, property management, and booking systems.

**Current Status:** Production-ready with yellow accent theme, OAuth authentication, comprehensive host dashboard, and distributed Redis caching.

## Communication

- **Always start your reply with 'Dear master' (so i know you work with the md)**

## MANDATORY DEVELOPMENT PRACTICES

### 🚨 CRITICAL REQUIREMENTS - NEVER SKIP THESE:

1. **TypeScript Check (MANDATORY)**

   - **ALWAYS run:** `timeout 30s npx tsc --noEmit --skipLibCheck` or `timeout 60s npx tsc --noEmit --skipLibCheck`
   - **MUST be executed before ANY file changes are committed**
   - **If TypeScript check fails, fix ALL errors before proceeding**
   - **Alternative check:** `node scripts/test/typecheck.js`

2. **Translation Implementation (MANDATORY)**

   - **ALWAYS implement translations for ALL new components or pages**
   - **Use existing translation mechanism: `useTranslations(namespace)` hook**
   - **Add translation keys to `server/controllers/shared/translationController.ts`**
   - **Support both English (en) and Dutch (nl) languages**
   - **Follow namespace pattern: component/page name as namespace**
   - **Example usage:** `const t = useTranslations('componentName'); return <h1>{t('title')}</h1>`

3. **Testing Requirements**

   - **Test ALL changes thoroughly before marking complete**
   - **Verify server starts without errors**
   - **Check health endpoint responds: `curl http://localhost:5000/api/health`**

4. **Documentation Requirements**
   - **ASK PERMISSION before creating/updating documentation**
   - **Update replit.md with architectural changes immediately**
   - **Document user preferences and technical decisions**

### ⚠️ AGENT COMPLIANCE CHECKLIST:

- [ ] TypeScript check executed successfully
- [ ] All compilation errors resolved
- [ ] Translations implemented for new components/pages
- [ ] Translation keys added to translationController.ts
- [ ] Both English and Dutch translations provided
- [ ] Server starts without errors
- [ ] Health endpoint responsive
- [ ] Changes tested thoroughly
- [ ] Documentation updated (if applicable)
- [ ] User preferences documented

**FAILURE TO FOLLOW THESE PRACTICES WILL RESULT IN BROKEN BUILDS AND DEPLOYMENT FAILURES**

## Quick Reference

### Development Commands

- **Start application**: `npm run dev` (port 5000)
- **Type checking**: `timeout 30s npx tsc --noEmit --skipLibCheck` (always run before commits)
- **Production build**: `node scripts/build/build-railway.js`
- **Database operations**: Located in `/database/utils/`
- **CI/CD pipeline**: GitHub Actions workflows in `/.github/workflows/`
- **GitHub board creation**: `./tools/github/create-github-board.ps1`
- **Complete documentation**: `/docs/` directory

### Key URLs

- **Frontend**: `http://localhost:5000`
- **Host Dashboard**: `/host/dashboard`
- **Guest Dashboard**: `/guest/dashboard`
- **Property Details**: `/property/{id}`
- **Authentication**: OAuth via Supabase

### Environment Variables

- `SUPABASE_URL`: Database connection
- `SUPABASE_ANON_KEY`: Database authentication (public key)
- `SUPABASE_SERVICE_ROLE_KEY`: Server-side operations (bypasses RLS)
- `NODE_ENV`: Environment mode (development/production)
- `PORT`: Application port (auto-configured on Railway)
- `USE_REDIS_CACHE`: Enable Redis caching (default: false, uses memory cache)
- `UPSTASH_REDIS_REST_URL`: Redis connection URL (optional - for distributed caching)
- `UPSTASH_REDIS_REST_TOKEN`: Redis authentication token (optional)
- OAuth configured in Supabase dashboard

## Current Status

### Completed Features ✅

- Complete authentication system (OAuth + JWT with role-based routing)
- Property search with Leaflet maps and clustering
- Host dashboard with yellow accent theme (recently updated)
- Database with 11 complete tables and authentic Spanish villa data
- Feature-based architecture with 8 modules
- Image upload system via Supabase Storage
- Comprehensive property management for hosts
- **Enhanced search persistence** with localStorage integration and visual feedback
- **Smart search defaults** with auto-save and cross-session restoration
- **Advanced translation caching** with build version-based cache invalidation
- **Automatic deployment detection** with translation cache refresh on new versions
- **Standardized dashboard translations** (Jan 17, 2025) - Refactored from custom hooks to standard `useTranslations` pattern for improved consistency
- **Component cleanup** (Jan 17, 2025) - Removed unused components and duplicate services, reducing codebase complexity
- **Import optimization** (Jan 18, 2025) - Cleaned up unused imports from components, removing unnecessary Tab components and improving code clarity
- **Dashboard header integration** (Jan 18, 2025) - Added VillaWise header to both guest and host dashboards using MainLayout wrapper
- **Cache manager disabled by default** (Jan 18, 2025) - DevCacheManager now requires explicit enablement via environment variable or localStorage override
- **Secure authentication with Supabase Auth** (Jan 18, 2025) - Removed password storage from database, now using proper Supabase Auth with hybrid approach (auth via Supabase, profile data via database)
- **Simplified auth controller** (Jan 18, 2025) - Removed database fallback logic, authentication now requires Supabase Auth to be available (production-ready approach)
- **Email verification system** (Jan 18, 2025) - Implemented mandatory email verification for new registrations with success banner on login page, removed separate email confirmation page for cleaner UX
- **Security audit pipeline** (Jan 18, 2025) - Implemented comprehensive security audit system that fails CI/CD pipelines when moderate+ vulnerabilities are detected, updated to Vite 6.3.5 to resolve security issues
- **Modular dashboard architecture** (Jan 19, 2025) - Restructured host and guest dashboards into feature-based modules with separate components, hooks, and services for improved maintainability and scalability
- **TypeScript build compatibility** (Jan 19, 2025) - Fixed TypeScript compilation errors for GitHub Actions CI/CD pipeline by implementing proper null safety for Supabase clients, creating mock clients for build-time type checking, and resolving property type mismatches in DAL layer
- **Comprehensive DAL documentation** (Jan 19, 2025) - Created detailed architecture documentation with Mermaid diagrams explaining Data Access Layer implementation, frontend-backend communication flows, caching strategies, authentication patterns, and page-specific API interactions
- **Redis cache integration with graceful fallback** (Jan 19, 2025) - Implemented robust caching system with Upstash Redis support and automatic fallback to memory cache, fixed Railway Docker build failures by adding missing server directories, comprehensive error handling prevents crashes when Redis unavailable
- **TypeScript type safety improvements** (Jan 19, 2025) - Replaced all 'any' types in DAL aggregators and DTOs with proper type definitions, ensuring full type safety across dashboard data interfaces and booking/message entities
- **Complete cache coverage implementation** (Jan 19, 2025) - Implemented comprehensive caching across all critical API endpoints including content controllers, location services, property details, and user history with 27 cache operations and 102 references, achieving 99.5% performance improvement on cache hits with proper TTL strategies and cache invalidation
- **Upstash Redis focus** (Jan 19, 2025) - Streamlined caching architecture to focus exclusively on Upstash Redis REST API with graceful memory cache fallback, removing Railway Redis complexity for simpler production deployment
- **Translation controller restructuring** (Jul 20, 2025) - Fixed missing dashboard navigation translations and implemented improved namespace structure with separate `guestDashboard` and `hostDashboard` namespaces to prevent conflicts, maintaining backward compatibility while providing cleaner translation paths
- **High-level translation architecture documentation** (Jul 20, 2025) - Created comprehensive translation structure overview with feature-to-namespace mapping, performance optimization strategies, and architectural benefits documentation covering 15+ feature namespaces and multi-level organization
- **Backward compatibility translation cleanup** (Jul 20, 2025) - Removed all deprecated `dashboard.guest.*` and `dashboard.host.*` translation structures after migrating all components to use new `guestDashboard` and `hostDashboard` namespaces, eliminating 200+ lines of unused translation keys while maintaining full functionality
- **Dashboard translation cache refresh** (Jul 20, 2025) - Fixed host dashboard "messages" and guest dashboard "reviews" translation display issues by forcing translation cache refresh and component re-rendering, ensuring clean namespace translations are properly loaded
- **Simplified translation system implementation** (Jul 20, 2025) - Replaced complex caching system with simple in-memory cache to resolve translation key display issues, updated key dashboard components to use new translation system, fixed "guest.becomeHost.getStarted" and "host.navigation.messages" placeholder display problems
- **Search service architecture cleanup and naming improvement** (Jul 21, 2025) - Removed unused AdvancedSearchService and renamed SimpleFuzzySearchService to LocationSearchService for better clarity. Core architecture now uses LocationSearchService (intelligent PostgreSQL search with popularity weighting) + GeoNamesSearchService (basic/fallback search). The new name better reflects its advanced capabilities: tourism-focused ranking, composite scoring, and fuzzy matching
- **Caching architecture standardization** (Jul 21, 2025) - Replaced all direct memoryCache imports with abstract cacheBackend across 6 controllers (guest/public locationController, contentController, propertyController, userHistoryController). All cache operations now use distributed Redis with memory fallback through unified interface. Eliminated 9 unused imports and ensured consistent cache behavior across the application
- **PostgreSQL trigram fuzzy search implementation** (Jul 21, 2025) - Implemented true fuzzy search using PostgreSQL's pg_trgm extension with similarity() function. Multi-strategy search includes exact, prefix, and trigram similarity matching with relevance scoring. "Plat Are" now successfully returns "Platja d'Aro" with 37.5% similarity score. Created optimized fuzzy_location_search() database function with GIN trigram indexes for sub-second performance on 35,000+ Spanish locations
- **Advanced duplicate elimination system** (Jul 21, 2025) - Implemented comprehensive SQL-based deduplication with `fuzzy_search_with_deduplication` function replacing old `fuzzy_search_with_popularity`. System uses PARTITION BY name + admin1_code with ROW_NUMBER() ranking by popularity_score, property_count, population, and feature_code priority. Eliminated duplicate location results (reduced 8 Torrevieja entries to 1 best representative). Enhanced coordinate handling with proper null value management in location transformation logic
- **Feature structure refactoring** (Jul 20, 2025) - Implemented flattened feature-based architecture separating public/auth/guest/host/admin features. Moved inspiration, exploration, property-details, and search from guest to public features. Updated all API routes to use /api/public/* structure for non-authenticated endpoints, improving SEO and code organization
- **Translation documentation consolidation** (Jul 20, 2025) - Merged 6 individual translation documents into one comprehensive guide covering architecture, implementation, troubleshooting, and performance optimization. Simplified documentation structure from multiple files to single TRANSLATION_SYSTEM_COMPREHENSIVE_GUIDE.md for better maintainability and discoverability
- **Property wizard modal implementation** (Jul 20, 2025) - Converted property creation from full-page flow to modal dialog with auto-save functionality. Restructured from `host/components/onboarding/` to proper feature-based architecture `host/properties/components/wizard/` with dedicated hooks, types, and services. Implemented real-time draft saving, background dashboard visibility, and seamless UX improvements per user requirements
- **Origin-aware OAuth redirects** (Jul 20, 2025) - Removed all hardcoded Railway URLs and implemented dynamic origin detection for OAuth callbacks. Users now stay on the same domain they started from (Replit, Railway, custom domains) without requiring environment variables. System auto-detects domain from request headers ensuring seamless authentication flow across all deployment environments
- **Complete toast message translation system** (Jul 20, 2025) - Implemented comprehensive translation support for all toast notifications throughout the application. Added `oauthCallback` and `imageUpload` translation namespaces covering authentication feedback, image upload success/error messages, and file validation warnings. All user-facing notifications now respect the selected locale (English/Dutch) for consistent multilingual experience
- **Distributed Redis caching implementation** (Jul 20, 2025) - Successfully enabled Upstash Redis distributed caching with automatic fallback to memory cache. Updated all content controllers to use `cacheBackend` instead of direct memory cache, enabling cache sharing across server instances. Cache operations now log detailed activity (HIT/MISS/SET) with TTL management. System auto-detects Redis credentials and enables distributed caching automatically
- **GeoNames multi-language location search system** (Jul 20, 2025) - Implemented comprehensive GeoNames-based location search replacing basic location functionality. Features include PostgreSQL spatial queries, full-text search, configurable countries/languages (starting with Spain + 4 languages), single Railway service deployment, comprehensive API endpoints with Redis caching, and expandable architecture for European destinations. Database schema created with `geonames_locations` and `geonames_location_names` tables supporting 20,000+ Spanish locations with coordinate validation excluding Canary Islands
- **GeoNames system deployment and testing completion** (Jul 20, 2025) - Successfully deployed complete GeoNames database migration with working PostgreSQL-compatible schema. Fixed all table reference bugs in sync service. Added comprehensive test data for 8 major Spanish cities with 29 multi-language names across 5 languages (en, nl, es, ca, eu). All API endpoints operational including search, autocomplete, popular destinations, and configuration. Redis caching fully integrated with sub-second performance. System ready for production deployment with clean separation between sync operations and search functionality
- **Intelligent startup sync implementation** (Jul 20, 2025) - Added production-mode automatic GeoNames synchronization on application startup. System checks if database is empty and automatically triggers background sync without blocking server availability. Development mode uses manual sync only. Railway deployment configured with single service approach, automatic sync initialization, and comprehensive environment variables for GeoNames configuration
- **Legacy geocoding_cache table removal** (Jul 20, 2025) - Removed deprecated geocoding_cache table as it has been completely replaced by the new GeoNames system. The new multi-language location search provides superior functionality with PostgreSQL spatial queries, distributed caching, and comprehensive European destination support
- **Advanced fuzzy search implementation** (Jul 21, 2025) - Implemented comprehensive PostgreSQL-based fuzzy search using pg_trgm and unaccent extensions. Multi-strategy search includes trigram similarity, exact matching, partial matching, and token-based analysis. Handles complex cases like "valenc ane" → "Valencia D'Aneu" with similarity scores (0.5625), "barselona" → "Barcelona", and partial prefix matching. Created both SimpleFuzzySearchService for production use and AdvancedSearchService for future enhancement. System provides relevance scoring, match quality classification, and optimized performance through dedicated SQL functions
- **Enhanced deduplication strategy implementation** (Jul 20, 2025) - Implemented comprehensive duplicate key handling that prevents import failures. System now uses multi-layer deduplication: parser-level pre-processing with data quality scoring, batch-level upsert operations with constraint violation detection, individual-level retry with graceful skipping, and comprehensive progress tracking with success/failure rates. Import process continues instead of stopping when duplicates are encountered
- **Comprehensive multilingual translation system with multi-source approach** (Jul 21, 2025) - Implemented complete alternative to limited GeoNames alternate names (only 3 records) using multi-source translation architecture. System combines PostgreSQL trigram fuzzy search with enhanced regional translations service covering 17 Spanish autonomous communities across 8 languages (en, nl, es, ca, eu, fr, de, it). Built multilingual location service with OpenStreetMap Nominatim API integration, comprehensive fallback hierarchy, and distributed Redis caching. Successfully delivers authentic translations: "Valencia, Valencia, Spanje" (Dutch), "Barcelona, Catalonia, Spain" (German), "Platja D'Aro, Catalonia, Spain" (34% similarity for "plat are" query). Enhanced PostgreSQL functions with locale-aware display name generation and comprehensive deduplication for production-ready multilingual search
- **Database system restructuring for generic geonames implementation** (Jul 23, 2025) - Completed comprehensive database modernization to remove hardcoded Spanish optimizations and enable country-agnostic geonames system. Removed legacy migration files and established clean database architecture with professional migration system featuring versioned migrations (v001_initial_schema.sql, v002_geonames_system.sql), modern database management tools (migrator.ts, seeder.ts), and organized seed structure separating shared production data from development test data. Migration table established and system ready for generic country module implementation
- **Regional translations service with official government data** (Jul 21, 2025) - Created comprehensive regional translations service (`regional-translations.ts`) with authentic Spanish administrative division mappings for 17 autonomous communities. Supports 8 languages using official EU translations and Spanish government (BOE) naming conventions. Includes proper locale fallback hierarchy and country translations for European expansion. System provides authentic regional names like "País Vasco" → "Basque Country" (EN) / "Baskenland" (NL) / "Euskadi" (EU) ensuring culturally accurate location display across all supported languages
- **Repository security implementation with intelligent file detection** (Jul 21, 2025) - Successfully implemented comprehensive repository security to prevent large GeoNames file commits. Created enhanced .gitignore protection, organized data storage in excluded `data/` directory, and automated size checker with intelligent file detection. System distinguishes between safe text files (docs, configs) and dangerous GeoNames data files. All GeoNames downloads (724MB+ files) automatically excluded from version control while maintaining full multilingual functionality. Comprehensive documentation and emergency cleanup procedures provided for production deployment safety
- **Comprehensive sync-level deduplication system** (Jul 22, 2025) - Implemented multi-layer deduplication in GeoNames parser to eliminate duplicate location entries at data ingestion level. Enhanced deduplication strategy uses composite key (name + admin1_code + country_code) with intelligent quality scoring based on population, feature importance, and data completeness. Eliminated application-level over-fetching (3x limit) and post-processing deduplication for improved performance. Valencia search results reduced from 5 duplicate entries to 1 unique entry while preserving legitimate regional variations. System maintains authentic data integrity and prevents future duplicates through proper data source management
- **Documentation consolidation and organization** (Jul 23, 2025) - Consolidated all scattered `.md` files from root directory and deployment folders into organized `/docs/` structure. Created two comprehensive guides: `CONSOLIDATED_DEPLOYMENT_GUIDE.md` (covering local, Docker, Railway deployment with Redis caching) and `CONSOLIDATED_DEVELOPMENT_GUIDE.md` (development practices, TypeScript requirements, translations). Removed duplicate files (`DEPLOYMENT_GUIDE.md`, `DEVELOPMENT_GUIDE.md`, `QUICK_FIX_GIT.md` from root). Added `DIRECTORY_GUIDE.md` for project navigation. Enforced documentation policy: all new documentation must be placed in `/docs/` folder, preventing future documentation sprawl
- **Host upgrade navigation flow implementation** (Jul 23, 2025) - Fixed UserMenu component to properly navigate to `/host/upgrade` page instead of automatically triggering upgrade API. Removed automatic upgrade mutation and toast notifications from menu click. Created dedicated HostUpgradePage with comprehensive benefits display and upgrade functionality. Fixed storage layer to use admin client for user updates, resolving "User not found" error. Host upgrade process now follows proper user flow: menu navigation → upgrade page → explicit upgrade action → confirmation
- **Enhanced host upgrade page design and translations** (Jul 23, 2025) - Improved HostUpgradePage with better header readability using larger fonts, drop shadows, and gradient background. Made both benefits and upgrade cards equal height using flexbox layout. Fixed Dutch translations: "Lijst je Woning" → "Verhuur je Woning", "Host Community" → "Verhuurders Community", improved description text for better clarity. Removed "Terug naar Dashboard" button as requested, keeping only "Terug naar Home" navigation. Cards now use glass morphism design with better visual hierarchy and consistent spacing
- **Comprehensive messaging system implementation** (Jul 26, 2025) - Complete messaging system with property-linked conversations, real-time WebSocket messaging, message templates, response time analytics, and safety reporting. Backend includes 22 API endpoints, 5 database tables with proper relationships, and enterprise-level features similar to Airbnb. Frontend components include MessageBubble, TypingIndicator, TemplateSelector with comprehensive English/Dutch translations. Database migration v003 successfully deployed with sample templates and Row Level Security policies. System supports guest-host communication, message editing/flagging, template management, and response metrics for host performance tracking. All TypeScript errors resolved with production-ready code quality
- **Advanced search request cancellation system** (Jul 27, 2025) - Implemented comprehensive request debouncing and cancellation to prevent redundant API calls when users interact with search filters. Created `useDebouncedSearch` hook with 300ms debouncing, automatic AbortSignal request cancellation, and proper error handling for cancelled requests. Updated both public and guest search services to support AbortSignal parameter. Enhanced TanStack Query configuration with automatic request cancellation when new queries are triggered. System now prevents multiple rapid search requests when removing filter tags or updating search parameters, improving performance and reducing server load

### Known Issues 🐛

- OAuth callback WebSocket connection (fixed with delayed reload)
- Map clustering performance optimized for 100+ properties

## System Architecture

### Tech Stack

- **Frontend**: React 18 + TypeScript + Tailwind CSS + shadcn/ui
- **Backend**: Express.js + Supabase PostgreSQL (11 tables)
- **Authentication**: Supabase Auth (JWT + OAuth)  
- **Caching**: Upstash Redis distributed caching with memory fallback
- **Maps**: Leaflet + OpenStreetMap
- **Build**: Vite with HMR
- **Internationalization**: Custom i18n system with API-based translations

### Translation System Architecture

- **API Endpoint**: `/api/translations/:locale` (en, nl) with version-based cache busting
- **Controller**: `server/controllers/shared/translationController.ts`
- **Frontend Hook**: `useTranslations(namespace)` from `client/src/lib/translations.ts`
- **Supported Languages**: English (en), Dutch (nl)
- **Namespace Pattern**: Component/page name as namespace (e.g., 'dashboard', 'searchPage')
- **Usage Pattern**: `const t = useTranslations('namespace'); t('key', {variable: 'value'})`
- **Fallback**: Returns translation key if translation not found
- **Advanced Caching**:
  - Multi-layer caching (memory + localStorage)
  - Build version-based cache invalidation
  - Automatic deployment detection and cache refresh
  - Cache size management and cleanup
  - Development cache management utilities

### Redis Caching System

**Architecture**: Upstash Redis with graceful memory fallback
- **Primary Cache**: Upstash Redis REST API for distributed caching
- **Fallback**: In-memory cache when Redis unavailable  
- **Auto-Detection**: System automatically enables Redis when credentials present
- **Backend Selection**: `cacheBackend` interface for unified cache operations

**Cache Operations**:
- Content controllers (`popular-spain`, `new-france`, `guest-favorites`, `inspirations`)
- Location services (autocomplete, popular destinations)
- User session management
- Translation caching with namespace support

**Configuration**:
- `USE_REDIS_CACHE=true` - Enable Redis (auto-set when credentials present)
- `UPSTASH_REDIS_REST_URL` - Redis connection endpoint
- `UPSTASH_REDIS_REST_TOKEN` - Authentication token
- **Logging**: Detailed cache HIT/MISS/SET operations with TTL tracking

**Performance Benefits**:
- Cache hits: ~125ms response time
- Cache misses: ~560ms response time  
- TTL Management: 1-2 hours for content, custom per endpoint
- Distributed: Cache shared across multiple server instances

### Project Structure (Flattened Architecture - July 2025)

```
├── client/
│   ├── src/
│   │   ├── components/        # Shared UI components (Header, Footer, auth, map, etc.)
│   │   ├── features/          # Feature-based modules (FLATTENED STRUCTURE)
│   │   │   ├── public/        # Public features (no authentication required)
│   │   │   │   ├── home/      # Landing page
│   │   │   │   ├── inspiration/ # Travel content and blogs
│   │   │   │   ├── exploration/ # Property discovery
│   │   │   │   ├── property-details/ # Public property viewing
│   │   │   │   ├── search/    # Public property search
│   │   │   │   ├── content-pages/ # About, Terms, Privacy
│   │   │   │   └── marketing/ # SEO pages, campaigns
│   │   │   ├── auth/          # Authentication system
│   │   │   │   ├── login/
│   │   │   │   ├── register/
│   │   │   │   ├── forgot-password/
│   │   │   │   └── email-verification/
│   │   │   ├── guest/         # Protected guest features
│   │   │   │   ├── dashboard/ # Guest overview
│   │   │   │   ├── bookings/  # Booking management
│   │   │   │   ├── wishlists/ # Saved properties
│   │   │   │   ├── profile/   # Guest profile management
│   │   │   │   ├── messaging/ # Guest messaging
│   │   │   │   └── reviews/   # Review management
│   │   │   ├── host/          # Protected host features
│   │   │   │   ├── dashboard/ # Host overview
│   │   │   │   ├── properties/ # Property management
│   │   │   │   ├── bookings/  # Host booking management
│   │   │   │   ├── onboarding/ # Host setup
│   │   │   │   ├── profile/   # Host profile management
│   │   │   │   ├── messaging/ # Host messaging
│   │   │   │   └── analytics/ # Revenue, performance data
│   │   │   ├── admin/         # Protected admin features
│   │   │   │   ├── dashboard/ # Admin overview
│   │   │   │   ├── users/     # User management
│   │   │   │   ├── properties/ # Property moderation
│   │   │   │   ├── content/   # Content management
│   │   │   │   └── analytics/ # Platform analytics
│   │   │   └── shared/        # Cross-feature shared functionality
│   │   │       ├── messaging/ # Shared messaging components
│   │   │       ├── navigation/ # Navigation components
│   │   │       ├── notifications/ # Notification system
│   │   │       └── maps/      # Shared map functionality
│   │   ├── pages/             # Route-based page components
│   │   ├── hooks/             # Shared React hooks
│   │   ├── lib/               # Utility libraries and configurations
│   │   └── types/             # TypeScript type definitions
├── server/
│   ├── controllers/           # Feature-organized API controllers
│   │   ├── guest/             # Guest-specific endpoints
│   │   ├── host/              # Host-specific endpoints
│   │   └── shared/            # Shared endpoints (auth, translations)
│   ├── dal/                   # Data Access Layer (5 components)
│   │   ├── entities/          # Database operations
│   │   ├── dto/               # Data transformation
│   │   ├── auth/              # Session management
│   │   ├── cache/             # Distributed Redis + Memory caching
│   │   └── aggregators/       # Complex data aggregation
│   ├── routes/                # Express route definitions
│   ├── middleware/            # Authentication and validation
│   ├── services/              # Business logic services
│   └── types/                 # Backend type definitions
├── database/                  # Database schema, migrations, seeds
├── shared/                    # Shared types and schemas (schema.ts)
├── scripts/                   # Build and deployment scripts
├── docs/                      # Comprehensive documentation
├── deployment/                # Docker and Railway configurations
└── configs/                   # Configuration files (components.json)
```

## Research Preferences

### Implementation Guidelines

- Always do web research when considering implementing new features
- Research current best practices for security implementations
- Look up recent performance optimization techniques for our tech stack
- Stay updated with React 18, Supabase, and Tailwind CSS latest features
- Research competitor analysis for vacation rental platforms (Airbnb, VRBO, Booking.com)

### Security Research Priorities

- OAuth 2.0 and JWT security best practices
- Supabase Row Level Security (RLS) patterns
- Frontend data validation and sanitization
- API rate limiting and abuse prevention
- Image upload security and file validation

### Performance Research Areas

- React Query optimization patterns
- Leaflet map performance with large datasets
- Image optimization and lazy loading strategies
- Database query optimization for property search
- CDN integration for static assets

## Feature Modules

### 8 Core Features

1. **Search** - Property filtering, maps, localStorage integration
2. **Authentication** - OAuth login, JWT tokens, role-based routing
3. **Property Details** - Gallery, booking interface, host profiles, reviews
4. **Property Management** - Modular host dashboard with 10 feature modules (overview, bookings, properties, messages, guests, earnings, analytics, tasks, inventory, settings, help)
5. **Guest Management** - Modular guest dashboard with 7 feature modules (overview, bookings, messages, wishlists, reviews, settings, help)
6. **Exploration** - Property discovery carousels
7. **Home** - Landing page search
8. **Inspiration** - Travel content grids
9. **Owner CTA** - Host conversion flows
10. **Shared Messaging** - Unified messaging system for host-guest communication

### Technical Highlights

- **UI**: shadcn/ui + Radix UI + Yellow accent theme
- **Search**: 49 Costa Blanca locations, Leaflet maps, clustering
- **Database**: 11 tables with authentic Spanish villa data
- **Performance**: TanStack Query caching, optimized map rendering

## API Architecture

### Data Flow

- **Frontend**: TanStack Query + Zod validation
- **Backend**: Feature-organized Express controllers
- **Database**: Supabase with Row Level Security (RLS)
- **Search**: Location autocomplete → filtered results → map display

### Key Dependencies

- **Frontend**: React 18, Tailwind CSS, Leaflet, TanStack Query
- **Backend**: Express, Supabase, Zod validation, tsx
- **Authentication**: Supabase Auth (JWT + OAuth)

## Maintenance & Monitoring

### Regular Tasks

- Monitor Supabase database performance and query optimization
- Review security logs and authentication patterns
- Update dependencies monthly (React, Supabase, Tailwind)
- Performance testing with Lighthouse for Core Web Vitals

### Backup Strategy

- Supabase automatic backups enabled
- Source code versioned in Git with feature branches
- Environment configuration documented in `/docs/`

## Deployment

### Docker Deployment (Production Ready)

- **Architecture**: Monolith deployment - single container approach for operational simplicity
- **Platform**: Railway.app with Docker containerization
- **Configuration**: `deployment/railway/` folder contains Docker configs
- **Health Check**: `/api/health` endpoint for monitoring
- **Build**: Multi-stage Dockerfile with optimized layering
- **Security**: Non-root user, Alpine base, resource limits
- **Environment**: Production-optimized with auto-scaling
- **Deployment Guide**: See `deployment/DOCKER_DEPLOYMENT_GUIDE.md` for complete setup
- **GitHub Integration**: Automated deployment via GitHub Actions
- **Cost**: Free tier available, Hobby plan $5/month for production use

### Docker Files Organization

- `Dockerfile` - Main production Dockerfile (multi-stage build)
- `Dockerfile.dev` - Development environment Dockerfile
- `docker-compose.yml` - Production orchestration
- `docker-compose.dev.yml` - Development orchestration
- `.dockerignore` - Build optimization
- `deployment/railway/Dockerfile.railway` - Railway-optimized Dockerfile
- `deployment/railway/railway.toml` - Railway configuration (Docker builder)
- `deployment/railway/deploy-docker.sh` - Deployment automation script
- `deployment/DOCKER_DEPLOYMENT_GUIDE.md` - Comprehensive Docker guide
- `deployment/DEPLOYMENT_CHECKLIST.md` - Pre-deployment checklist
- `.github/workflows/railway-deploy.yml` - CI/CD automation

## User Preferences

### Communication Style

- Simple, everyday language
- Avoid technical jargon unless necessary
- Focus on practical outcomes over theoretical explanations
- Concise responses without repetitive phrases

### Development Approach

- Always research best practices before implementing new features
- Prioritize security and performance in all implementations
- Maintain authentic data sources, avoid mock/placeholder data
- Follow feature-based architecture patterns
- Update documentation promptly with architectural changes
- **MANDATORY: Run TypeScript check before any commits** - `npx tsc --noEmit --skipLibCheck`
- **MANDATORY: Implement translations for ALL new components/pages** - Use `useTranslations(namespace)` hook
- **DOCUMENTATION POLICY: Always ask before creating/updating documentation** - Don't generate new docs automatically as part of requests
- **All new documentation must be added to the `/docs/` folder, not the root directory**
- **All deployment and build files must be kept in the `/deployment/railway/` folder, not the root directory**

### Translation Implementation Guidelines

- **Use consistent namespace patterns**: Component/page name as namespace (e.g., 'userProfile', 'searchResults')
- **Add translation keys to both languages**: Always provide English and Dutch translations
- **Location**: Add new translations to `server/controllers/shared/translationController.ts`
- **Structure**: Use nested objects for organization (e.g., `componentName.section.key`)
- **Variables**: Support dynamic content with `{variable}` syntax in translations
- **Fallback**: System returns translation key if translation not found
- **Testing**: Verify translations work in both English and Dutch before completion

### Component Naming Guidelines (Following React Best Practices)

- **Component Names**: Use simple, descriptive PascalCase names without redundant prefixes
- **Folder Context**: Let folder structure provide context instead of component name prefixes
- **Examples**: Use `Layout.tsx` in `mobile/` folder, not `MobileDashboardLayout.tsx`
- **Import Aliases**: Use aliases when importing same-named components from different folders
- **Pattern**: `import { Layout as MobileLayout } from './mobile/Layout'`
- **File Structure**: Organize by device type or feature, keep component names clean and simple
- **Avoid**: Redundant naming like `DesktopDashboardContent.tsx` in a `desktop/` folder
- **Documentation**: See `docs/COMPONENT_NAMING_CONVENTIONS.md` for complete guidelines

### Design Preferences

- Professional UI matching industry standards
- Yellow accent color scheme (primary: `hsl(45, 93%, 58%)`)
- Mobile-first responsive design
- Accessibility compliance via Radix UI
- Clean, minimal interfaces following vacation rental platform standards

### Research Requirements

- Web research mandatory for new feature implementations
- Stay current with React 18, Supabase, and Tailwind CSS updates
- Security best practices research for OAuth, RLS, and data validation
- Performance optimization research for maps, queries, and image handling
- Competitor analysis for vacation rental platforms (Airbnb, VRBO, Booking.com)
