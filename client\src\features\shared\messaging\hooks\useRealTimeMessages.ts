import { useEffect, useState, useCallback } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { createClient } from '@supabase/supabase-js';
import type { RealtimeChannel } from '@supabase/supabase-js';
import { apiRequest } from '@/lib/queryClient';

// Initialize Supabase client for realtime
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

let supabaseClient: any = null;
if (supabaseUrl && supabaseKey) {
  supabaseClient = createClient(supabaseUrl, supabaseKey, {
    realtime: {
      params: {
        eventsPerSecond: 10
      }
    }
  });
}

export interface MessageData {
  id: string;
  conversation_id: string;
  sender_id: string;
  sender_type: 'guest' | 'host';
  content: string;
  message_type: 'text' | 'template' | 'system' | 'automated';
  message_status: 'sent' | 'delivered' | 'read';
  created_at: string;
  sender?: {
    id: string;
    name: string;
    avatar_url?: string;
    user_type: string;
  };
}

export interface TypingIndicator {
  userId: string;
  conversationId: string;
  isTyping: boolean;
  timestamp: string;
}

export interface OnlinePresence {
  userId: string;
  status: 'online' | 'away' | 'offline';
  lastSeen: string;
}

/**
 * Hook for real-time messaging functionality
 */
export const useRealTimeMessages = (conversationId: string, userId?: string) => {
  const [messages, setMessages] = useState<MessageData[]>([]);
  const [typingUsers, setTypingUsers] = useState<Set<string>>(new Set());
  const [onlineUsers, setOnlineUsers] = useState<Map<string, OnlinePresence>>(new Map());
  const [isConnected, setIsConnected] = useState(false);
  const queryClient = useQueryClient();
  
  // Load initial messages
  const { data: initialMessages, isLoading } = useQuery({
    queryKey: ['/api/messaging/conversations', conversationId, 'messages'],
    enabled: !!conversationId
  });
  
  // Set initial messages when loaded
  useEffect(() => {
    if (initialMessages && Array.isArray(initialMessages)) {
      setMessages(initialMessages);
    }
  }, [initialMessages]);
  
  // Real-time subscription setup
  useEffect(() => {
    if (!conversationId || !userId || !supabaseClient) {
      return;
    }
    
    let channel: RealtimeChannel | null = null;
    
    const setupRealtime = async () => {
      try {
        const channelName = `conversation:${conversationId}`;
        
        channel = supabaseClient
          .channel(channelName)
          // Listen for new messages
          .on('postgres_changes', {
            event: 'INSERT',
            schema: 'public',
            table: 'messages',
            filter: `conversation_id=eq.${conversationId}`
          }, (payload: any) => {
            console.log('📨 New message received:', payload.new);
            const newMessage = payload.new as MessageData;
            
            setMessages(prev => {
              // Avoid duplicates
              if (prev.find(m => m.id === newMessage.id)) {
                return prev;
              }
              return [...prev, newMessage];
            });
            
            // Invalidate conversation list to update unread counts
            queryClient.invalidateQueries({ queryKey: ['/api/messaging/conversations'] });
          })
          // Listen for message updates (read status, edits)
          .on('postgres_changes', {
            event: 'UPDATE',
            schema: 'public',
            table: 'messages',
            filter: `conversation_id=eq.${conversationId}`
          }, (payload: any) => {
            console.log('✏️ Message updated:', payload.new);
            const updatedMessage = payload.new as MessageData;
            
            setMessages(prev => 
              prev.map(msg => 
                msg.id === updatedMessage.id ? updatedMessage : msg
              )
            );
          })
          // Listen for typing indicators
          .on('broadcast', {
            event: 'typing'
          }, (payload: any) => {
            const typing = payload.payload as TypingIndicator;
            
            if (typing.userId !== userId) {
              setTypingUsers(prev => {
                const newSet = new Set(prev);
                if (typing.isTyping) {
                  newSet.add(typing.userId);
                } else {
                  newSet.delete(typing.userId);
                }
                return newSet;
              });
              
              // Auto-clear typing after 3 seconds
              if (typing.isTyping) {
                setTimeout(() => {
                  setTypingUsers(prev => {
                    const newSet = new Set(prev);
                    newSet.delete(typing.userId);
                    return newSet;
                  });
                }, 3000);
              }
            }
          })
          // Listen for presence updates
          .on('presence', {
            event: 'sync'
          }, () => {
            const state = channel?.presenceState();
            if (state) {
              const presenceMap = new Map<string, OnlinePresence>();
              Object.values(state).forEach((presence: any) => {
                if (presence[0]?.userId !== userId) {
                  presenceMap.set(presence[0].userId, presence[0]);
                }
              });
              setOnlineUsers(presenceMap);
            }
          })
          .subscribe((status: string) => {
            console.log(`🔄 Realtime status: ${status}`);
            setIsConnected(status === 'SUBSCRIBED');
            
            if (status === 'SUBSCRIBED') {
              // Track user presence
              channel?.track({
                userId,
                status: 'online',
                lastSeen: new Date().toISOString()
              });
            }
          });
        
      } catch (error) {
        console.error('❌ Failed to setup realtime:', error);
        setIsConnected(false);
      }
    };
    
    setupRealtime();
    
    // Cleanup function
    return () => {
      if (channel) {
        supabaseClient.removeChannel(channel);
        setIsConnected(false);
      }
    };
  }, [conversationId, userId, queryClient]);
  
  // Send message function
  const sendMessage = useCallback(async (content: string, options?: {
    messageType?: 'text' | 'template';
    templateId?: string;
    variables?: Record<string, string>;
  }) => {
    if (!conversationId) return null;
    
    try {
      const messageData = {
        content,
        messageType: options?.messageType || 'text',
        templateId: options?.templateId,
        variables: options?.variables
      };
      
      const response = await apiRequest(`/api/messaging/conversations/${conversationId}/messages`, {
        method: 'POST',
        body: JSON.stringify(messageData)
      });
      
      console.log('✅ Message sent successfully');
      return response.data;
    } catch (error) {
      console.error('❌ Failed to send message:', error);
      throw error;
    }
  }, [conversationId]);
  
  // Send typing indicator
  const sendTypingIndicator = useCallback(async (isTyping: boolean) => {
    if (!conversationId || !isConnected) return;
    
    try {
      await apiRequest(`/api/messaging/conversations/${conversationId}/typing`, {
        method: 'POST',
        body: JSON.stringify({ isTyping })
      });
    } catch (error) {
      console.error('❌ Failed to send typing indicator:', error);
    }
  }, [conversationId, isConnected]);
  
  // Mark messages as read
  const markAsRead = useCallback(async () => {
    if (!conversationId) return;
    
    try {
      await apiRequest(`/api/messaging/conversations/${conversationId}/read`, {
        method: 'PATCH'
      });
      
      // Update local message states
      setMessages(prev => 
        prev.map(msg => 
          msg.sender_id !== userId 
            ? { ...msg, message_status: 'read' as const }
            : msg
        )
      );
      
      // Invalidate conversation list to update unread counts
      queryClient.invalidateQueries({ queryKey: ['/api/messaging/conversations'] });
    } catch (error) {
      console.error('❌ Failed to mark as read:', error);
    }
  }, [conversationId, userId, queryClient]);
  
  // Edit message
  const editMessage = useCallback(async (messageId: string, newContent: string) => {
    try {
      const response = await apiRequest(`/api/messaging/messages/${messageId}`, {
        method: 'PUT',
        body: JSON.stringify({ content: newContent })
      });
      
      console.log('✅ Message edited successfully');
      return response.data;
    } catch (error) {
      console.error('❌ Failed to edit message:', error);
      throw error;
    }
  }, []);
  
  // Delete message
  const deleteMessage = useCallback(async (messageId: string) => {
    try {
      await apiRequest(`/api/messaging/messages/${messageId}`, {
        method: 'DELETE'
      });
      
      // Remove from local state
      setMessages(prev => prev.filter(msg => msg.id !== messageId));
      
      console.log('✅ Message deleted successfully');
    } catch (error) {
      console.error('❌ Failed to delete message:', error);
      throw error;
    }
  }, []);
  
  return {
    // Data
    messages,
    typingUsers: Array.from(typingUsers),
    onlineUsers: Array.from(onlineUsers.values()),
    isConnected,
    isLoading,
    
    // Actions
    sendMessage,
    sendTypingIndicator,
    markAsRead,
    editMessage,
    deleteMessage
  };
};

/**
 * Hook for conversation list with real-time updates
 */
export const useRealTimeConversations = (userId?: string, userType?: 'guest' | 'host') => {
  const queryClient = useQueryClient();
  const [isConnected, setIsConnected] = useState(false);
  
  // Load conversations
  const { data: conversations, isLoading } = useQuery({
    queryKey: ['/api/messaging/conversations'],
    enabled: !!userId
  });
  
  // Real-time subscription for conversation updates
  useEffect(() => {
    if (!userId || !userType || !supabaseClient) {
      return;
    }
    
    let channel: RealtimeChannel | null = null;
    
    const setupRealtime = async () => {
      try {
        const channelName = `user:${userId}:conversations`;
        
        channel = supabaseClient
          .channel(channelName)
          // Listen for conversation updates
          .on('postgres_changes', {
            event: 'UPDATE',
            schema: 'public',
            table: 'conversations',
            filter: userType === 'guest' 
              ? `guest_id=eq.${userId}`
              : `host_id=eq.${userId}`
          }, () => {
            console.log('🔄 Conversation updated, refreshing list');
            queryClient.invalidateQueries({ queryKey: ['/api/messaging/conversations'] });
          })
          // Listen for new conversations
          .on('postgres_changes', {
            event: 'INSERT',
            schema: 'public',
            table: 'conversations',
            filter: userType === 'guest' 
              ? `guest_id=eq.${userId}`
              : `host_id=eq.${userId}`
          }, () => {
            console.log('➕ New conversation created, refreshing list');
            queryClient.invalidateQueries({ queryKey: ['/api/messaging/conversations'] });
          })
          .subscribe((status: string) => {
            console.log(`🔄 Conversations realtime status: ${status}`);
            setIsConnected(status === 'SUBSCRIBED');
          });
        
      } catch (error) {
        console.error('❌ Failed to setup conversations realtime:', error);
        setIsConnected(false);
      }
    };
    
    setupRealtime();
    
    return () => {
      if (channel) {
        supabaseClient.removeChannel(channel);
        setIsConnected(false);
      }
    };
  }, [userId, userType, queryClient]);
  
  return {
    conversations: (conversations as any)?.data || [],
    isLoading,
    isConnected
  };
};