import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useTranslations } from '@/lib/translations';
import { 
  CalendarDays, 
  MapPin, 
  Users, 
  DollarSign, 
  TrendingUp, 
  Eye 
} from 'lucide-react';

interface OverviewContentProps {
  hostProperties: any[];
  bookings: any[];
  hostMessages: any[];
  user: any;
}

const OverviewContent: React.FC<OverviewContentProps> = ({
  hostProperties,
  bookings,
  hostMessages,
  user
}) => {
  const t = useTranslations('hostDashboard');

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const totalEarnings = bookings.reduce((sum, booking) => sum + (booking.total_amount || 0), 0);
  const activeBookings = bookings.filter(b => b.status === 'confirmed' || b.status === 'active').length;
  const occupancyRate = hostProperties.length > 0 ? 
    ((activeBookings / hostProperties.length) * 100).toFixed(1) : 0;

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">{t('greeting')}</h1>
        <p className="text-gray-600 mt-2">{t('description')}</p>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card className="bg-primary/5 border-primary/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-primary">{t('overview.totalEarnings')}</p>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(totalEarnings)}</p>
              </div>
              <div className="h-12 w-12 bg-primary/10 rounded-lg flex items-center justify-center">
                <DollarSign className="h-6 w-6 text-primary" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{t('overview.activeBookings')}</p>
                <p className="text-2xl font-bold text-gray-900">{activeBookings}</p>
              </div>
              <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <CalendarDays className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{t('overview.occupancyRate')}</p>
                <p className="text-2xl font-bold text-gray-900">{occupancyRate}%</p>
              </div>
              <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                <TrendingUp className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Bookings */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle className="flex items-center">
              <CalendarDays className="h-5 w-5 mr-2" />
              <span>{t('recentBookings.title')}</span>
            </CardTitle>
            <Button variant="outline" size="sm">
              {t('recentBookings.viewAll')}
            </Button>
          </CardHeader>
          <CardContent>
            {bookings.length > 0 ? (
              <div className="space-y-4">
                {bookings.slice(0, 3).map((booking: any) => (
                  <div key={booking.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                        <Users className="h-5 w-5 text-gray-500" />
                      </div>
                      <div>
                        <p className="font-medium">{booking.guest_name || 'Guest'}</p>
                        <p className="text-sm text-gray-600">
                          {formatDate(booking.check_in_date)} - {formatDate(booking.check_out_date)}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <Badge variant="outline" className={
                        booking.status === 'confirmed' ? 'bg-green-50 text-green-700' :
                        booking.status === 'pending' ? 'bg-yellow-50 text-yellow-700' :
                        'bg-gray-50 text-gray-700'
                      }>
                        {booking.status === 'confirmed' ? t('recentBookings.confirmed') : 
                         booking.status === 'pending' ? t('recentBookings.pending') : 
                         booking.status}
                      </Badge>
                      <p className="text-sm text-gray-600 mt-1">
                        {booking.guest_count} {t('recentBookings.guests')}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-600">{t('recentBookings.noBookings')}</p>
                <Button variant="outline" className="mt-2">
                  {t('recentBookings.startExploring')}
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Properties */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle className="flex items-center">
              <MapPin className="h-5 w-5 mr-2" />
              <span>{t('properties.title')}</span>
            </CardTitle>
            <Button variant="outline" size="sm">
              {t('properties.viewAll')}
            </Button>
          </CardHeader>
          <CardContent>
            {hostProperties.length > 0 ? (
              <div className="space-y-4">
                {hostProperties.slice(0, 3).map((property: any) => (
                  <div key={property.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                        <MapPin className="h-5 w-5 text-gray-500" />
                      </div>
                      <div>
                        <p className="font-medium">{property.title}</p>
                        <p className="text-sm text-gray-600">{property.location}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <Badge variant="outline" className="bg-blue-50 text-blue-700">
                        {bookings.filter((b: any) => b.property_id === property.id).length} {t('properties.bookings')}
                      </Badge>
                      <Button variant="outline" size="sm" className="mt-1">
                        <Eye className="h-4 w-4 mr-1" />
                        View
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-600">{t('properties.noProperties')}</p>
                <Button variant="outline" className="mt-2">
                  {t('properties.addProperty')}
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default OverviewContent;