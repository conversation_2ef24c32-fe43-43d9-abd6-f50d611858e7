import { Request, Response } from 'express';
import { Logger } from '../../utils/logger';
import { templateService } from '../services/templateService';
import { z } from 'zod';

// Validation schemas
const createTemplateSchema = z.object({
  name: z.string().min(1).max(100),
  content: z.string().min(1).max(5000),
  category: z.enum(['inquiry', 'booking', 'checkin', 'checkout', 'support', 'custom']),
  dynamicFields: z.record(z.string()).optional()
});

const updateTemplateSchema = createTemplateSchema.partial();

const getTemplatesSchema = z.object({
  category: z.enum(['inquiry', 'booking', 'checkin', 'checkout', 'support', 'custom']).optional(),
  activeOnly: z.coerce.boolean().default(true)
});

const processTemplateSchema = z.object({
  variables: z.record(z.string()).default({})
});

export class TemplateController {
  
  /**
   * GET /api/messaging/templates
   * Get templates for authenticated host
   */
  async getTemplates(req: Request, res: Response): Promise<void> {
    const startTime = Date.now();
    try {
      const userId = (req as any).userId;
      const userType = (req as any).user?.user_metadata?.role || 'guest';
      
      // Only hosts can access templates
      if (userType !== 'host') {
        res.status(403).json({
          success: false,
          message: 'Templates are only available for hosts'
        });
        return;
      }
      
      const validation = getTemplatesSchema.safeParse(req.query);
      if (!validation.success) {
        res.status(400).json({
          success: false,
          message: 'Invalid query parameters',
          errors: validation.error.errors
        });
        return;
      }
      
      const { category, activeOnly } = validation.data;
      
      const templates = await templateService.getTemplates(
        userId,
        category,
        activeOnly
      );
      
      // If no templates exist, create default ones
      if (templates.length === 0 && !category) {
        Logger.info(`Creating default templates for new host: ${userId}`);
        const defaultTemplates = await templateService.createDefaultTemplatesForHost(userId);
        
        Logger.api('GET', '/api/messaging/templates', 200, Date.now() - startTime);
        res.json({
          success: true,
          data: defaultTemplates,
          message: 'Default templates created'
        });
        return;
      }
      
      Logger.api('GET', '/api/messaging/templates', 200, Date.now() - startTime);
      res.json({
        success: true,
        data: templates
      });
    } catch (error) {
      Logger.error('TemplateController.getTemplates error:', error);
      Logger.api('GET', '/api/messaging/templates', 500, Date.now() - startTime);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch templates',  
        error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
      });
    }
  }
  
  /**
   * POST /api/messaging/templates
   * Create new template
   */
  async createTemplate(req: Request, res: Response): Promise<void> {
    const startTime = Date.now();
    try {
      const userId = (req as any).userId;
      const userType = (req as any).user?.user_metadata?.role || 'guest';
      
      // Only hosts can create templates
      if (userType !== 'host') {
        res.status(403).json({
          success: false,
          message: 'Template creation is only available for hosts'
        });
        return;
      }
      
      const validation = createTemplateSchema.safeParse(req.body);
      if (!validation.success) {
        res.status(400).json({
          success: false,
          message: 'Invalid template data',
          errors: validation.error.errors
        });
        return;
      }
      
      const templateData = validation.data;
      
      const template = await templateService.createTemplate({
        ...templateData,
        hostId: userId
      });
      
      Logger.api('POST', '/api/messaging/templates', 201, Date.now() - startTime);
      res.status(201).json({
        success: true,
        data: template,
        message: 'Template created successfully'
      });
    } catch (error) {
      Logger.error('TemplateController.createTemplate error:', error);
      Logger.api('POST', '/api/messaging/templates', 500, Date.now() - startTime);
      res.status(500).json({
        success: false,
        message: 'Failed to create template',
        error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
      });
    }
  }
  
  /**
   * PUT /api/messaging/templates/:templateId
   * Update existing template
   */
  async updateTemplate(req: Request, res: Response): Promise<void> {
    const startTime = Date.now();
    try {
      const userId = (req as any).userId;
      const userType = (req as any).user?.user_metadata?.role || 'guest';
      const templateId = req.params.templateId;
      
      // Only hosts can update templates
      if (userType !== 'host') {
        res.status(403).json({
          success: false,
          message: 'Template updates are only available for hosts'
        });
        return;
      }
      
      if (!templateId) {
        res.status(400).json({
          success: false,
          message: 'Template ID is required'
        });
        return;
      }
      
      const validation = updateTemplateSchema.safeParse(req.body);
      if (!validation.success) {
        res.status(400).json({
          success: false,
          message: 'Invalid template data',
          errors: validation.error.errors
        });
        return;
      }
      
      const updates = validation.data;
      
      const updatedTemplate = await templateService.updateTemplate(
        templateId,
        userId,
        updates
      );
      
      if (!updatedTemplate) {
        res.status(404).json({
          success: false,
          message: 'Template not found or access denied'
        });
        return;
      }
      
      Logger.api('PUT', `/api/messaging/templates/${templateId}`, 200, Date.now() - startTime);
      res.json({
        success: true,
        data: updatedTemplate,
        message: 'Template updated successfully'
      });
    } catch (error) {
      Logger.error('TemplateController.updateTemplate error:', error);
      Logger.api('PUT', `/api/messaging/templates/${req.params.templateId}`, 500, Date.now() - startTime);
      res.status(500).json({
        success: false,
        message: 'Failed to update template',
        error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
      });
    }
  }
  
  /**
   * DELETE /api/messaging/templates/:templateId
   * Delete template
   */
  async deleteTemplate(req: Request, res: Response): Promise<void> {
    const startTime = Date.now();
    try {
      const userId = (req as any).userId;
      const userType = (req as any).user?.user_metadata?.role || 'guest';
      const templateId = req.params.templateId;
      
      // Only hosts can delete templates
      if (userType !== 'host') {
        res.status(403).json({
          success: false,
          message: 'Template deletion is only available for hosts'
        });
        return;
      }
      
      if (!templateId) {
        res.status(400).json({
          success: false,
          message: 'Template ID is required'
        });
        return;
      }
      
      const success = await templateService.deleteTemplate(templateId, userId);
      
      if (!success) {
        res.status(404).json({
          success: false,
          message: 'Template not found or access denied'
        });
        return;
      }
      
      Logger.api('DELETE', `/api/messaging/templates/${templateId}`, 200, Date.now() - startTime);
      res.json({
        success: true,
        message: 'Template deleted successfully'
      });
    } catch (error) {
      Logger.error('TemplateController.deleteTemplate error:', error);
      Logger.api('DELETE', `/api/messaging/templates/${req.params.templateId}`, 500, Date.now() - startTime);
      res.status(500).json({
        success: false,
        message: 'Failed to delete template',
        error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
      });
    }
  }
  
  /**
   * POST /api/messaging/templates/:templateId/process
   * Process template with variables for preview
   */
  async processTemplate(req: Request, res: Response): Promise<void> {
    const startTime = Date.now();
    try {
      const userId = (req as any).userId;
      const userType = (req as any).user?.user_metadata?.role || 'guest';
      const templateId = req.params.templateId;
      
      // Only hosts can process templates
      if (userType !== 'host') {
        res.status(403).json({
          success: false,
          message: 'Template processing is only available for hosts'
        });
        return;
      }
      
      if (!templateId) {
        res.status(400).json({
          success: false,
          message: 'Template ID is required'
        });
        return;
      }
      
      const validation = processTemplateSchema.safeParse(req.body);
      if (!validation.success) {
        res.status(400).json({
          success: false,
          message: 'Invalid variables data',
          errors: validation.error.errors
        });
        return;
      }
      
      const { variables } = validation.data;
      
      const result = await templateService.processTemplate(
        templateId,
        userId,
        variables
      );
      
      Logger.api('POST', `/api/messaging/templates/${templateId}/process`, 200, Date.now() - startTime);
      res.json({
        success: true,
        data: {
          processedContent: result.content,
          template: result.template,
          variables
        },
        message: 'Template processed successfully'
      });
    } catch (error) {
      Logger.error('TemplateController.processTemplate error:', error);
      Logger.api('POST', `/api/messaging/templates/${req.params.templateId}/process`, 500, Date.now() - startTime);
      res.status(500).json({
        success: false,
        message: 'Failed to process template',
        error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
      });
    }
  }
  
  /**
   * GET /api/messaging/templates/default
   * Get default template suggestions
   */
  async getDefaultTemplates(req: Request, res: Response): Promise<void> {
    const startTime = Date.now();
    try {
      const userType = (req as any).user?.user_metadata?.role || 'guest';
      
      // Only hosts can access default templates
      if (userType !== 'host') {
        res.status(403).json({
          success: false,
          message: 'Default templates are only available for hosts'
        });
        return;
      }
      
      const defaultTemplates = await templateService.getDefaultTemplates();
      
      Logger.api('GET', '/api/messaging/templates/default', 200, Date.now() - startTime);
      res.json({
        success: true,
        data: defaultTemplates,
        message: 'Default templates retrieved'
      });
    } catch (error) {
      Logger.error('TemplateController.getDefaultTemplates error:', error);
      Logger.api('GET', '/api/messaging/templates/default', 500, Date.now() - startTime);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch default templates',
        error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
      });
    }
  }
}

export const templateController = new TemplateController();