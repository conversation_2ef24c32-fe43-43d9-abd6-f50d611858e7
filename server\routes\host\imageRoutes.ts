import { Router } from 'express';
import { imageController } from '../../controllers/host/imageController';

const router = Router();

// Upload image to property
router.post('/properties/:propertyId/images', imageController.uploadPropertyImage);

// Delete image from property
router.delete('/properties/:propertyId/images', imageController.deletePropertyImage);

// Reorder property images
router.put('/properties/:propertyId/images/reorder', imageController.reorderPropertyImages);

// Get property images
router.get('/properties/:propertyId/images', imageController.getPropertyImages);

export default router;