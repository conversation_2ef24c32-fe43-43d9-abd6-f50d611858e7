import express from 'express'
import { cacheRoute, cacheMonitor } from '../../middleware/cache'
import { getGuestDashboardData } from '../../dal/aggregators/dashboard'
import { getBookings, createBooking } from '../../dal/entities/bookings'
import { getMessages, sendMessage, markMessageAsRead, getConversations } from '../../dal/entities/messages'
import { getCurrentUserProfile, updateUserProfile } from '../../dal/entities/users'

export const guestDashboardRouter = express.Router()

// Apply cache monitoring to all routes
guestDashboardRouter.use(cacheMonitor)

// Guest Dashboard Overview
guestDashboardRouter.get('/dashboard', 
  cacheRoute(120), // 2 minute cache
  async (req, res) => {
    try {
      const authHeader = req.headers.authorization
      const dashboardData = await getGuestDashboardData(authHeader!)
      
      res.json({
        success: true,
        data: dashboardData,
        cached: true
      })
    } catch (error) {
      console.error('Guest dashboard error:', error)
      res.status(401).json({ 
        success: false, 
        message: error instanceof Error ? error.message : 'Failed to load dashboard' 
      })
    }
  }
)

// Guest Bookings
guestDashboardRouter.get('/bookings', 
  cacheRoute(120), // 2 minute cache
  async (req, res) => {
    try {
      const authHeader = req.headers.authorization
      const { status } = req.query
      
      const filters: any = {}
      if (status && typeof status === 'string') {
        filters.status = status.split(',')
      }
      
      const bookings = await getBookings(authHeader!, 'guest', filters)
      
      res.json({
        success: true,
        data: bookings
      })
    } catch (error) {
      console.error('Guest bookings error:', error)
      res.status(401).json({ 
        success: false, 
        message: error instanceof Error ? error.message : 'Failed to load bookings' 
      })
    }
  }
)

// Create Booking
guestDashboardRouter.post('/bookings', async (req, res) => {
  try {
    const authHeader = req.headers.authorization
    const booking = await createBooking(authHeader!, req.body)
    
    res.json({
      success: true,
      data: booking
    })
  } catch (error) {
    console.error('Create booking error:', error)
    res.status(400).json({ 
      success: false, 
      message: error instanceof Error ? error.message : 'Failed to create booking' 
    })
  }
})

// Guest Messages
guestDashboardRouter.get('/messages', 
  cacheRoute(60), // 1 minute cache
  async (req, res) => {
    try {
      const authHeader = req.headers.authorization
      const { conversationWith, unreadOnly } = req.query
      
      const filters: any = {}
      if (conversationWith) {
        filters.conversationWith = conversationWith as string
      }
      if (unreadOnly === 'true') {
        filters.unreadOnly = true
      }
      
      const messages = await getMessages(authHeader!, 'guest', filters)
      
      res.json({
        success: true,
        data: messages
      })
    } catch (error) {
      console.error('Guest messages error:', error)
      res.status(401).json({ 
        success: false, 
        message: error instanceof Error ? error.message : 'Failed to load messages' 
      })
    }
  }
)

// Guest Conversations
guestDashboardRouter.get('/conversations', 
  cacheRoute(120), // 2 minute cache
  async (req, res) => {
    try {
      const authHeader = req.headers.authorization
      const conversations = await getConversations(authHeader!)
      
      res.json({
        success: true,
        data: conversations
      })
    } catch (error) {
      console.error('Guest conversations error:', error)
      res.status(401).json({ 
        success: false, 
        message: error instanceof Error ? error.message : 'Failed to load conversations' 
      })
    }
  }
)

// Send Message
guestDashboardRouter.post('/messages', async (req, res) => {
  try {
    const authHeader = req.headers.authorization
    const message = await sendMessage(authHeader!, req.body)
    
    res.json({
      success: true,
      data: message
    })
  } catch (error) {
    console.error('Send message error:', error)
    res.status(401).json({ 
      success: false, 
      message: error instanceof Error ? error.message : 'Failed to send message' 
    })
  }
})

// Mark Message as Read
guestDashboardRouter.patch('/messages/:id/read', async (req, res) => {
  try {
    const authHeader = req.headers.authorization
    await markMessageAsRead(authHeader!, req.params.id)
    
    res.json({
      success: true,
      message: 'Message marked as read'
    })
  } catch (error) {
    console.error('Mark message as read error:', error)
    res.status(401).json({ 
      success: false, 
      message: error instanceof Error ? error.message : 'Failed to mark message as read' 
    })
  }
})

// Guest Profile
guestDashboardRouter.get('/profile', 
  cacheRoute(300), // 5 minute cache
  async (req, res) => {
    try {
      const authHeader = req.headers.authorization
      const profile = await getCurrentUserProfile(authHeader!)
      
      res.json({
        success: true,
        data: profile
      })
    } catch (error) {
      console.error('Guest profile error:', error)
      res.status(401).json({ 
        success: false, 
        message: error instanceof Error ? error.message : 'Failed to load profile' 
      })
    }
  }
)

// Update Guest Profile
guestDashboardRouter.put('/profile', async (req, res) => {
  try {
    const authHeader = req.headers.authorization
    const profile = await updateUserProfile(authHeader!, req.body)
    
    res.json({
      success: true,
      data: profile
    })
  } catch (error) {
    console.error('Update guest profile error:', error)
    res.status(401).json({ 
      success: false, 
      message: error instanceof Error ? error.message : 'Failed to update profile' 
    })
  }
})

// Guest Wishlists (placeholder for future implementation)
guestDashboardRouter.get('/wishlists', 
  cacheRoute(300), // 5 minute cache
  async (req, res) => {
    try {
      // For now, return empty array - can be implemented later with DAL
      res.json({
        success: true,
        data: []
      })
    } catch (error) {
      console.error('Guest wishlists error:', error)
      res.status(401).json({ 
        success: false, 
        message: 'Failed to load wishlists' 
      })
    }
  }
)

// Guest Reviews (placeholder for future implementation)
guestDashboardRouter.get('/reviews', 
  cacheRoute(300), // 5 minute cache
  async (req, res) => {
    try {
      // For now, return empty array - can be implemented later with DAL
      res.json({
        success: true,
        data: []
      })
    } catch (error) {
      console.error('Guest reviews error:', error)
      res.status(401).json({ 
        success: false, 
        message: 'Failed to load reviews' 
      })
    }
  }
)