import React from 'react';
import { Link } from 'wouter';

export default function PrivacyPolicy() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 py-12">
        <div className="bg-white rounded-lg shadow-sm p-8">
          <Link href="/">
            <button className="text-primary hover:text-primary/80 mb-6 flex items-center gap-2">
              ← Back to VillaWise
            </button>
          </Link>
          
          <h1 className="text-3xl font-bold mb-8">Privacy Policy</h1>
          
          <div className="prose max-w-none space-y-6">
            <p className="text-gray-600">
              Last updated: {new Date().toLocaleDateString()}
            </p>
            
            <section>
              <h2 className="text-xl font-semibold mb-3">1. Information We Collect</h2>
              <p>
                VillaWise collects information you provide directly to us, such as when you create an account, 
                search for properties, or contact us for support.
              </p>
              
              <h3 className="text-lg font-medium mt-4 mb-2">Google OAuth Information</h3>
              <p>
                When you sign in with Google, we collect:
              </p>
              <ul className="list-disc list-inside ml-4 space-y-1">
                <li>Your email address</li>
                <li>Your name</li>
                <li>Your Google profile picture (if available)</li>
              </ul>
            </section>

            <section>
              <h2 className="text-xl font-semibold mb-3">2. How We Use Your Information</h2>
              <p>We use the information we collect to:</p>
              <ul className="list-disc list-inside ml-4 space-y-1">
                <li>Provide and maintain our vacation rental platform</li>
                <li>Authenticate your account securely</li>
                <li>Personalize your search experience</li>
                <li>Send you important updates about our service</li>
                <li>Improve our platform and develop new features</li>
              </ul>
            </section>

            <section>
              <h2 className="text-xl font-semibold mb-3">3. Information Sharing</h2>
              <p>
                We do not sell, trade, or otherwise transfer your personal information to third parties 
                except as described in this privacy policy. We may share your information with:
              </p>
              <ul className="list-disc list-inside ml-4 space-y-1">
                <li>Property hosts when you make a booking inquiry</li>
                <li>Service providers who assist in operating our platform</li>
                <li>Legal authorities when required by law</li>
              </ul>
            </section>

            <section>
              <h2 className="text-xl font-semibold mb-3">4. Data Security</h2>
              <p>
                We implement appropriate security measures to protect your personal information against 
                unauthorized access, alteration, disclosure, or destruction. Your data is stored securely 
                using industry-standard encryption.
              </p>
            </section>

            <section>
              <h2 className="text-xl font-semibold mb-3">5. Your Rights</h2>
              <p>You have the right to:</p>
              <ul className="list-disc list-inside ml-4 space-y-1">
                <li>Access your personal information</li>
                <li>Correct inaccurate information</li>
                <li>Delete your account and personal data</li>
                <li>Export your data</li>
              </ul>
            </section>

            <section>
              <h2 className="text-xl font-semibold mb-3">6. Contact Us</h2>
              <p>
                If you have any questions about this Privacy Policy, please contact us at:
                <br />
                Email: <EMAIL>
              </p>
            </section>
          </div>
        </div>
      </div>
    </div>
  );
}