import { List, LayoutGrid, Map } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useTranslations } from '@/lib/translations';
import type { ViewMode } from '@/types/property';

interface ViewToggleProps {
  currentView: ViewMode;
  onViewChange: (view: ViewMode) => void;
  className?: string;
}

export function ViewToggle({ currentView, onViewChange, className }: ViewToggleProps) {
  const t = useTranslations('searchPage');

  const viewOptions: Array<{ mode: ViewMode; icon: React.ReactNode; label: string }> = [
    { mode: 'list', icon: <List className="h-4 w-4" />, label: 'List' },
    { mode: 'split', icon: <LayoutGrid className="h-4 w-4" />, label: 'Split' },
    { mode: 'map', icon: <Map className="h-4 w-4" />, label: 'Map' }
  ];

  return (
    <div className={`flex items-center space-x-1 bg-white border border-gray-200 rounded-lg p-1 ${className || ''}`}>
      {viewOptions.map(({ mode, icon, label }) => (
        <Button
          key={mode}
          variant={currentView === mode ? "default" : "ghost"}
          size="sm"
          onClick={() => onViewChange(mode)}
          className={`h-8 px-3 ${
            currentView === mode 
              ? 'bg-black text-white shadow-sm' 
              : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
          }`}
        >
          {icon}
          <span className="ml-1 hidden sm:inline">{label}</span>
        </Button>
      ))}
    </div>
  );
}