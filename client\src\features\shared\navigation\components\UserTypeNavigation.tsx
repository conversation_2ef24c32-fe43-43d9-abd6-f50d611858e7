import { Link, useLocation } from 'wouter';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Users, Home, Search, Settings } from 'lucide-react';

interface UserTypeNavigationProps {
  userType: 'guest' | 'host' | 'admin';
}

export default function UserTypeNavigation({ userType }: UserTypeNavigationProps) {
  const [location] = useLocation();

  const isActive = (path: string) => location === path;

  if (userType === 'guest') {
    return (
      <nav className="flex items-center gap-4">
        <Link href="/">
          <Button 
            variant={isActive('/') ? "default" : "ghost"} 
            size="sm"
            className="flex items-center gap-2"
          >
            <Home className="h-4 w-4" />
            Home
          </Button>
        </Link>
        <Link href="/search">
          <Button 
            variant={isActive('/search') ? "default" : "ghost"} 
            size="sm"
            className="flex items-center gap-2"
          >
            <Search className="h-4 w-4" />
            Search
          </Button>
        </Link>
      </nav>
    );
  }

  if (userType === 'host') {
    return (
      <nav className="flex items-center gap-4">
        <Link href="/host/dashboard">
          <Button 
            variant={isActive('/host/dashboard') ? "default" : "ghost"} 
            size="sm"
            className="flex items-center gap-2"
          >
            <Home className="h-4 w-4" />
            Dashboard
          </Button>
        </Link>
        <Link href="/host/add-property">
          <Button 
            variant={isActive('/host/add-property') ? "default" : "ghost"} 
            size="sm"
            className="flex items-center gap-2"
          >
            <Users className="h-4 w-4" />
            Add Property
          </Button>
        </Link>
      </nav>
    );
  }

  if (userType === 'admin') {
    return (
      <nav className="flex items-center gap-4">
        <Link href="/admin">
          <Button 
            variant={isActive('/admin') ? "default" : "ghost"} 
            size="sm"
            className="flex items-center gap-2"
          >
            <Settings className="h-4 w-4" />
            Admin Panel
            <Badge variant="secondary" className="ml-1">
              Admin
            </Badge>
          </Button>
        </Link>
      </nav>
    );
  }

  return null;
}