import { Star, Users, Bed, Bath, Wifi } from 'lucide-react';
import { useTranslations } from '@/lib/translations';
import type { PropertyDetails } from '../types';

interface PropertyHeaderProps {
  property: PropertyDetails;
}

export function PropertyHeader({ property }: PropertyHeaderProps) {
  const t = useTranslations('propertyDetails');

  return (
    <div className="space-y-3 lg:space-y-4">
      {/* Title and Location */}
      <div>
        <h1 className="text-xl md:text-2xl lg:text-3xl font-bold text-gray-900 mb-1 lg:mb-2">
          {property.title}
        </h1>
        <p className="text-gray-600 text-base lg:text-lg">
          {property.subtitle}
        </p>
      </div>

      {/* Rating and Reviews */}
      <div className="flex flex-col sm:flex-row sm:items-center space-y-1 sm:space-y-0 sm:space-x-4">
        <div className="flex items-center space-x-1">
          <Star className="h-4 w-4 fill-current text-yellow-400" />
          <span className="font-medium text-sm lg:text-base">{property.rating}</span>
          <span className="text-gray-600">·</span>
          <span className="text-gray-600 underline cursor-pointer hover:text-gray-900 text-sm lg:text-base">
            {property.reviewCount} reviews
          </span>
        </div>
        
        <span className="text-gray-600 hidden sm:inline">·</span>
        
        <span className="text-gray-600 text-sm lg:text-base">
          {property.location.city}, {property.location.region}, {property.location.country}
        </span>
      </div>

      {/* Property Details Icons */}
      <div className="flex flex-wrap items-center gap-3 lg:gap-6 text-gray-600">
        <div className="flex items-center space-x-1.5">
          <Users className="h-3.5 w-3.5 lg:h-4 lg:w-4" />
          <span className="text-xs lg:text-sm">
            1-{property.maxGuests} {t('guests', { count: property.maxGuests })}
          </span>
        </div>
        
        <div className="flex items-center space-x-1.5">
          <Bed className="h-3.5 w-3.5 lg:h-4 lg:w-4" />
          <span className="text-xs lg:text-sm">
            {property.bedrooms} {t('bedrooms', { count: property.bedrooms })}
          </span>
        </div>
        
        <div className="flex items-center space-x-1.5">
          <Bath className="h-3.5 w-3.5 lg:h-4 lg:w-4" />
          <span className="text-xs lg:text-sm">
            {property.bathrooms} {t('bathrooms', { count: property.bathrooms })}
          </span>
        </div>
        
        {property.amenities.wifi && (
          <div className="flex items-center space-x-1.5">
            <Wifi className="h-3.5 w-3.5 lg:h-4 lg:w-4" />
            <span className="text-xs lg:text-sm">{t('wifi')}</span>
          </div>
        )}
      </div>
    </div>
  );
}