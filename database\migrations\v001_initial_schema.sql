-- Migration v001: Initial VillaWise Schema
-- Creates core tables for users, properties, bookings, and reviews

-- UP Migration
BEGIN;

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- <PERSON><PERSON> custom types
CREATE TYPE booking_status AS ENUM ('pending', 'confirmed', 'cancelled', 'completed');
CREATE TYPE property_type AS ENUM ('villa', 'apartment', 'house', 'cottage', 'loft', 'penthouse', 'studio', 'townhouse', 'castle', 'farm');

-- Users table (extends Supabase auth.users)
CREATE TABLE IF NOT EXISTS public.users (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    email VARCHAR(255) UNIQUE NOT NULL,
    username VARCHAR(50) UNIQUE NOT NULL,
    first_name <PERSON><PERSON><PERSON><PERSON>(100),
    last_name <PERSON><PERSON><PERSON><PERSON>(100),
    phone VARCHAR(20),
    is_host BOOLEAN DEFAULT FALSE,
    profile_image_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Host Properties table
CREATE TABLE IF NOT EXISTS public.host_properties (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    host_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    location VARCHAR(255) NOT NULL,
    city VARCHAR(100) NOT NULL,
    country VARCHAR(100) NOT NULL,
    coordinates JSONB NOT NULL,
    price_per_night DECIMAL(10,2) NOT NULL,
    max_guests INTEGER NOT NULL DEFAULT 1,
    bedrooms INTEGER NOT NULL DEFAULT 1,
    bathrooms INTEGER NOT NULL DEFAULT 1,
    amenities TEXT[] DEFAULT '{}',
    images TEXT[] DEFAULT '{}',
    property_type property_type NOT NULL DEFAULT 'apartment',
    is_active BOOLEAN DEFAULT TRUE,
    rating DECIMAL(2,1) DEFAULT NULL,
    review_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT positive_price CHECK (price_per_night > 0),
    CONSTRAINT positive_guests CHECK (max_guests > 0),
    CONSTRAINT valid_rating CHECK (rating >= 1.0 AND rating <= 5.0)
);

-- Guest Bookings table
CREATE TABLE IF NOT EXISTS public.guest_bookings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    host_property_id UUID NOT NULL REFERENCES public.host_properties(id) ON DELETE CASCADE,
    guest_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    check_in DATE NOT NULL,
    check_out DATE NOT NULL,
    guests JSONB NOT NULL DEFAULT '{"adults": 1, "children": 0, "infants": 0, "pets": 0}',
    total_price DECIMAL(10,2) NOT NULL,
    status booking_status DEFAULT 'pending',
    special_requests TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT valid_dates CHECK (check_out > check_in),
    CONSTRAINT positive_total CHECK (total_price > 0)
);

-- Guest Reviews table
CREATE TABLE IF NOT EXISTS public.guest_reviews (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    booking_id UUID NOT NULL REFERENCES public.guest_bookings(id) ON DELETE CASCADE,
    guest_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    host_property_id UUID NOT NULL REFERENCES public.host_properties(id) ON DELETE CASCADE,
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    comment TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Guest Search History table
CREATE TABLE IF NOT EXISTS public.guest_search_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    session_id VARCHAR(255),
    location VARCHAR(255) NOT NULL,
    check_in DATE,
    check_out DATE,
    guests JSONB DEFAULT '{"adults": 1, "children": 0, "infants": 0, "pets": 0}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Content Properties table (for curated content)
CREATE TABLE IF NOT EXISTS public.content_properties (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    image_url TEXT NOT NULL,
    location VARCHAR(255) NOT NULL,
    coordinates JSONB,
    price_range VARCHAR(50),
    category VARCHAR(100) DEFAULT 'featured',
    is_active BOOLEAN DEFAULT TRUE,
    display_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_host_properties_host_id ON host_properties(host_id);
CREATE INDEX IF NOT EXISTS idx_host_properties_location ON host_properties(city, country);
CREATE INDEX IF NOT EXISTS idx_host_properties_active ON host_properties(is_active);
CREATE INDEX IF NOT EXISTS idx_guest_bookings_property_id ON guest_bookings(host_property_id);
CREATE INDEX IF NOT EXISTS idx_guest_bookings_guest_id ON guest_bookings(guest_id);
CREATE INDEX IF NOT EXISTS idx_guest_bookings_dates ON guest_bookings(check_in, check_out);
CREATE INDEX IF NOT EXISTS idx_guest_reviews_property_id ON guest_reviews(host_property_id);
CREATE INDEX IF NOT EXISTS idx_guest_search_history_user_id ON guest_search_history(user_id);
CREATE INDEX IF NOT EXISTS idx_content_properties_category ON content_properties(category, display_order);

-- Enable Row Level Security
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE host_properties ENABLE ROW LEVEL SECURITY;
ALTER TABLE guest_bookings ENABLE ROW LEVEL SECURITY;
ALTER TABLE guest_reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE guest_search_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_properties ENABLE ROW LEVEL SECURITY;

-- RLS Policies

-- Users: Users can read all profiles, update own profile
CREATE POLICY "Users can view all profiles" ON users FOR SELECT USING (true);
CREATE POLICY "Users can update own profile" ON users FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Users can insert own profile" ON users FOR INSERT WITH CHECK (auth.uid() = id);

-- Host Properties: Public read, hosts can manage their own
CREATE POLICY "Anyone can view active properties" ON host_properties FOR SELECT USING (is_active = true);
CREATE POLICY "Hosts can manage own properties" ON host_properties FOR ALL USING (auth.uid() = host_id);

-- Guest Bookings: Users can see their own bookings, hosts can see bookings for their properties
CREATE POLICY "Guests can view own bookings" ON guest_bookings FOR SELECT USING (auth.uid() = guest_id);
CREATE POLICY "Hosts can view property bookings" ON guest_bookings FOR SELECT USING (
    auth.uid() IN (SELECT host_id FROM host_properties WHERE id = host_property_id)
);
CREATE POLICY "Guests can create bookings" ON guest_bookings FOR INSERT WITH CHECK (auth.uid() = guest_id);
CREATE POLICY "Guests can update own bookings" ON guest_bookings FOR UPDATE USING (auth.uid() = guest_id);

-- Guest Reviews: Public read, guests can create/update own reviews
CREATE POLICY "Anyone can view reviews" ON guest_reviews FOR SELECT USING (true);
CREATE POLICY "Guests can create reviews" ON guest_reviews FOR INSERT WITH CHECK (auth.uid() = guest_id);
CREATE POLICY "Guests can update own reviews" ON guest_reviews FOR UPDATE USING (auth.uid() = guest_id);

-- Search History: Users can manage their own search history
CREATE POLICY "Users can manage own search history" ON guest_search_history FOR ALL USING (auth.uid() = user_id);

-- Content Properties: Public read
CREATE POLICY "Anyone can view content properties" ON content_properties FOR SELECT USING (is_active = true);

COMMIT;

-- DOWN Migration (Rollback)
-- Note: This will completely remove all tables and data
-- Only use in development or for complete reset

-- BEGIN;
-- DROP TABLE IF EXISTS content_properties CASCADE;
-- DROP TABLE IF EXISTS guest_search_history CASCADE;
-- DROP TABLE IF EXISTS guest_reviews CASCADE;
-- DROP TABLE IF EXISTS guest_bookings CASCADE;
-- DROP TABLE IF EXISTS host_properties CASCADE;
-- DROP TABLE IF EXISTS users CASCADE;
-- DROP TYPE IF EXISTS property_type;
-- DROP TYPE IF EXISTS booking_status;
-- COMMIT;