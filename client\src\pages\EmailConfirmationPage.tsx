import React, { useEffect } from 'react';
import { useLocation } from 'wouter';
import { useTranslations } from '../lib/translations';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Mail, CheckCircle, AlertCircle } from 'lucide-react';

export default function EmailConfirmationPage() {
  const t = useTranslations('emailConfirmation');
  const [location, navigate] = useLocation();
  
  // Get email from URL search params or localStorage
  const urlParams = new URLSearchParams(window.location.search);
  const email = urlParams.get('email') || localStorage.getItem('registration_email');

  useEffect(() => {
    // If no email is provided, redirect to home
    if (!email) {
      navigate('/');
    }
  }, [email, navigate]);

  const handleResendEmail = () => {
    // TODO: Implement resend email functionality
    console.log('Resending confirmation email to:', email);
  };

  const handleBackToHome = () => {
    navigate('/');
  };

  if (!email) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-amber-50 to-orange-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <Card className="shadow-xl">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 p-3 bg-amber-100 rounded-full w-16 h-16 flex items-center justify-center">
              <Mail className="w-8 h-8 text-amber-600" />
            </div>
            <CardTitle className="text-2xl font-bold text-gray-900">
              {t('title')}
            </CardTitle>
            <CardDescription className="text-gray-600">
              {t('description')}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-sm font-medium text-blue-900">
                    {t('emailSent')}
                  </p>
                  <p className="text-sm text-blue-700 mt-1">
                    {t('emailSentDescription', { email })}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <AlertCircle className="w-5 h-5 text-amber-600 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-sm font-medium text-amber-900">
                    {t('nextSteps')}
                  </p>
                  <ul className="text-sm text-amber-700 mt-1 space-y-1">
                    <li>• {t('step1')}</li>
                    <li>• {t('step2')}</li>
                    <li>• {t('step3')}</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <Button 
                onClick={handleResendEmail} 
                variant="outline" 
                className="w-full"
              >
                {t('resendEmail')}
              </Button>
              <Button 
                onClick={handleBackToHome} 
                className="w-full"
              >
                {t('backToHome')}
              </Button>
            </div>

            <div className="text-center">
              <p className="text-sm text-gray-500">
                {t('helpText')}
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}