-- Migration v004: Translation System with Code-Based Approach
-- Creates tables for numeric codes and centralized translations

BEGIN;

-- Country codes table (ISO 3166-1 numeric codes)
CREATE TABLE IF NOT EXISTS country_codes (
    id SMALLINT PRIMARY KEY,                    -- ISO 3166-1 numeric (724 for Spain)
    iso_alpha2 CHAR(2) UNIQUE NOT NULL,         -- ES
    iso_alpha3 CHAR(3) UNIQUE NOT NULL,         -- ESP
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Region codes table (for admin1 regions)
CREATE TABLE IF NOT EXISTS region_codes (
    id SERIAL PRIMARY KEY,
    country_id SMALLINT REFERENCES country_codes(id) ON DELETE CASCADE,
    iso_code VARCHAR(10),                       -- ES-AN (Andalusia)
    geonames_admin1_code VARCHAR(20),           -- For mapping to existing data
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Tourism region codes table
CREATE TABLE IF NOT EXISTS tourism_region_codes (
    id SERIAL PRIMARY KEY,
    country_id SMALLINT REFERENCES country_codes(id) ON DELETE CASCADE,
    region_type VARCHAR(50),                    -- 'coastal', 'mountain', 'cultural', 'island'
    coordinate_bounds JSONB,                    -- Geographic boundaries
    center_coordinates POINT,                   -- Center point
    popularity_score INTEGER DEFAULT 50,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Universal translations table
CREATE TABLE IF NOT EXISTS translations (
    id SERIAL PRIMARY KEY,
    entity_type VARCHAR(20) NOT NULL,           -- 'country', 'region', 'tourism_region'
    entity_id INTEGER NOT NULL,                 -- The numeric code
    language_code CHAR(2) NOT NULL,             -- 'en', 'es', 'nl', etc.
    text VARCHAR(255) NOT NULL,                 -- Translated text
    is_official BOOLEAN DEFAULT false,          -- Official translation flag
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(entity_type, entity_id, language_code)
);

-- Popular locations table (replaces hardcoded POPULAR_LOCATIONS)
CREATE TABLE IF NOT EXISTS popular_locations (
    id SERIAL PRIMARY KEY,
    geonames_id BIGINT,                         -- Reference to geonames_locations
    name VARCHAR(255) NOT NULL,
    country_id SMALLINT REFERENCES country_codes(id),
    region_id INTEGER REFERENCES region_codes(id),
    coordinates POINT NOT NULL,
    latitude DECIMAL(10,8) NOT NULL,
    longitude DECIMAL(11,8) NOT NULL,
    popularity_score INTEGER DEFAULT 50,
    display_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_country_codes_alpha2 ON country_codes(iso_alpha2);
CREATE INDEX IF NOT EXISTS idx_region_codes_country ON region_codes(country_id);
CREATE INDEX IF NOT EXISTS idx_region_codes_admin1 ON region_codes(geonames_admin1_code);
CREATE INDEX IF NOT EXISTS idx_tourism_region_codes_country ON tourism_region_codes(country_id);
CREATE INDEX IF NOT EXISTS idx_translations_lookup ON translations(entity_type, entity_id, language_code);
CREATE INDEX IF NOT EXISTS idx_translations_language ON translations(language_code);
CREATE INDEX IF NOT EXISTS idx_popular_locations_country ON popular_locations(country_id);
CREATE INDEX IF NOT EXISTS idx_popular_locations_active ON popular_locations(is_active);
CREATE INDEX IF NOT EXISTS idx_popular_locations_order ON popular_locations(display_order);

-- Add columns to existing geonames_locations table for code references
ALTER TABLE geonames_locations 
ADD COLUMN IF NOT EXISTS country_code_id SMALLINT REFERENCES country_codes(id),
ADD COLUMN IF NOT EXISTS region_code_id INTEGER REFERENCES region_codes(id),
ADD COLUMN IF NOT EXISTS tourism_region_code_id INTEGER REFERENCES tourism_region_codes(id);

-- Create indexes for new columns
CREATE INDEX IF NOT EXISTS idx_geonames_country_code_id ON geonames_locations(country_code_id);
CREATE INDEX IF NOT EXISTS idx_geonames_region_code_id ON geonames_locations(region_code_id);
CREATE INDEX IF NOT EXISTS idx_geonames_tourism_region_code_id ON geonames_locations(tourism_region_code_id);

-- Add comments for documentation
COMMENT ON TABLE country_codes IS 'ISO 3166-1 country codes with numeric IDs';
COMMENT ON TABLE region_codes IS 'Regional administrative codes mapped to countries';
COMMENT ON TABLE tourism_region_codes IS 'Tourism regions with geographic boundaries';
COMMENT ON TABLE translations IS 'Centralized translations for all geographic entities';
COMMENT ON TABLE popular_locations IS 'Popular destinations for autocomplete and suggestions';

COMMIT;

-- DOWN Migration (Rollback)
-- Note: This will remove all translation system tables
-- Uncomment to rollback:

-- BEGIN;
-- DROP TABLE IF EXISTS popular_locations CASCADE;
-- DROP TABLE IF EXISTS translations CASCADE;
-- DROP TABLE IF EXISTS tourism_region_codes CASCADE;
-- DROP TABLE IF EXISTS region_codes CASCADE;
-- DROP TABLE IF EXISTS country_codes CASCADE;
-- 
-- ALTER TABLE geonames_locations 
-- DROP COLUMN IF EXISTS country_code_id,
-- DROP COLUMN IF EXISTS region_code_id,
-- DROP COLUMN IF EXISTS tourism_region_code_id;
-- COMMIT;
