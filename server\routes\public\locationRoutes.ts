import { Router } from 'express';
import { locationController } from '../../controllers/public/locationController';

const router = Router();

// Location autocomplete - Database first approach
router.get('/autocomplete', (req, res) => locationController.autocomplete(req, res));

// Location search - alias for autocomplete
router.get('/search', (req, res) => locationController.autocomplete(req, res));

// Get popular destinations (before /:id route)
router.get('/popular', (req, res) => locationController.getPopularDestinations(req, res));

// Get suggestions - alias for popular destinations
router.get('/suggestions', (req, res) => locationController.getPopularDestinations(req, res));

// Get locations within radius
router.get('/radius', (req, res) => locationController.getLocationsByRadius(req, res));

// Get location by ID (must be last to avoid conflicts)
router.get('/:id', (req, res) => locationController.getLocationById(req, res));

export { router as locationRoutes };