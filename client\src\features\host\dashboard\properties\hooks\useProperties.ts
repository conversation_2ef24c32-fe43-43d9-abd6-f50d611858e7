import { useQuery } from '@tanstack/react-query';
import { useUser } from '@/features/shared/auth/hooks/useAuth';

export const useProperties = () => {
  const { data: user } = useUser();

  const { data: hostProperties = [], isLoading, error } = useQuery({
    queryKey: ['/api/properties/host'],
    enabled: !!user,
  });

  // Cast to proper type
  const propertiesArray = hostProperties as any[];

  // Property statistics
  const activeProperties = propertiesArray.filter((p: any) => p.status === 'active');
  const draftProperties = propertiesArray.filter((p: any) => p.status === 'draft');
  const pausedProperties = propertiesArray.filter((p: any) => p.status === 'paused');

  return {
    hostProperties: propertiesArray,
    activeProperties,
    draftProperties, 
    pausedProperties,
    isLoading,
    error,
    propertyCounts: {
      total: propertiesArray.length,
      active: activeProperties.length,
      draft: draftProperties.length,
      paused: pausedProperties.length
    }
  };
};