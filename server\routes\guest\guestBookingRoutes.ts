import { Router } from 'express';
import { guestBooking<PERSON>ontroller } from '../../controllers/guest/guestBookingController';

const router = Router();

// Guest booking routes
router.get('/bookings/:guestId', guestBookingController.getBookings.bind(guestBookingController));
router.get('/booking/:bookingId', guestBookingController.getBooking.bind(guestBookingController));
router.post('/booking', guestBookingController.createBooking.bind(guestBookingController));
router.put('/booking/:bookingId', guestBookingController.updateBooking.bind(guestBookingController));

export default router;