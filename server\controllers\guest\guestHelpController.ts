import { Request, Response } from 'express';
import { Logger } from '../../utils/logger';
import { storage } from '../../storage';
import { insertHelpArticleSchema } from '../../../shared/schema';
import { z } from 'zod';

export class GuestHelpController {
  
  async getHelpArticles(req: Request, res: Response) {
    const startTime = Date.now();
    try {
      const category = req.query.category as string;
      
      Logger.info(`Guest help articles requested, category: ${category || 'all'}`);
      
      const articles = await storage.getHelpArticles(category);
      
      Logger.api('GET', '/api/guest/help/articles', 200, Date.now() - startTime);
      res.json(articles);
    } catch (error) {
      Logger.error('Error fetching help articles', error);
      Logger.api('GET', '/api/guest/help/articles', 500, Date.now() - startTime);
      res.status(500).json({ error: 'Failed to fetch help articles' });
    }
  }

  async getHelpArticle(req: Request, res: Response) {
    const startTime = Date.now();
    try {
      const articleId = req.params.articleId;
      
      if (!articleId) {
        return res.status(400).json({ error: 'Article ID is required' });
      }
      
      Logger.info(`Guest help article requested: ${articleId}`);
      
      const article = await storage.getHelpArticle(articleId);
      
      if (!article) {
        return res.status(404).json({ error: 'Help article not found' });
      }
      
      Logger.api('GET', `/api/guest/help/article/${articleId}`, 200, Date.now() - startTime);
      res.json(article);
    } catch (error) {
      Logger.error('Error fetching help article', error);
      Logger.api('GET', `/api/guest/help/article/${req.params.articleId}`, 500, Date.now() - startTime);
      res.status(500).json({ error: 'Failed to fetch help article' });
    }
  }

  async getFAQs(req: Request, res: Response) {
    const startTime = Date.now();
    try {
      Logger.info('Guest FAQs requested');
      
      const faqs = await storage.getFAQs();
      
      Logger.api('GET', '/api/guest/help/faqs', 200, Date.now() - startTime);
      res.json(faqs);
    } catch (error) {
      Logger.error('Error fetching FAQs', error);
      Logger.api('GET', '/api/guest/help/faqs', 500, Date.now() - startTime);
      res.status(500).json({ error: 'Failed to fetch FAQs' });
    }
  }

  async createHelpArticle(req: Request, res: Response) {
    const startTime = Date.now();
    try {
      const validation = insertHelpArticleSchema.safeParse(req.body);
      
      if (!validation.success) {
        return res.status(400).json({ 
          error: 'Invalid help article data', 
          details: validation.error.errors 
        });
      }
      
      const articleData = validation.data;
      Logger.info(`Creating help article: ${articleData.title}`);
      
      const article = await storage.createHelpArticle(articleData);
      
      Logger.api('POST', '/api/guest/help/article', 201, Date.now() - startTime);
      res.status(201).json(article);
    } catch (error) {
      Logger.error('Error creating help article', error);
      Logger.api('POST', '/api/guest/help/article', 500, Date.now() - startTime);
      res.status(500).json({ error: 'Failed to create help article' });
    }
  }

  async updateHelpArticle(req: Request, res: Response) {
    const startTime = Date.now();
    try {
      const articleId = req.params.articleId;
      
      if (!articleId) {
        return res.status(400).json({ error: 'Article ID is required' });
      }
      
      const updateSchema = insertHelpArticleSchema.partial();
      const validation = updateSchema.safeParse(req.body);
      
      if (!validation.success) {
        return res.status(400).json({ 
          error: 'Invalid help article data', 
          details: validation.error.errors 
        });
      }
      
      const updates = validation.data;
      Logger.info(`Updating help article: ${articleId}`);
      
      const article = await storage.updateHelpArticle(articleId, updates);
      
      if (!article) {
        return res.status(404).json({ error: 'Help article not found' });
      }
      
      Logger.api('PUT', `/api/guest/help/article/${articleId}`, 200, Date.now() - startTime);
      res.json(article);
    } catch (error) {
      Logger.error('Error updating help article', error);
      Logger.api('PUT', `/api/guest/help/article/${req.params.articleId}`, 500, Date.now() - startTime);
      res.status(500).json({ error: 'Failed to update help article' });
    }
  }

  async deleteHelpArticle(req: Request, res: Response) {
    const startTime = Date.now();
    try {
      const articleId = req.params.articleId;
      
      if (!articleId) {
        return res.status(400).json({ error: 'Article ID is required' });
      }
      
      Logger.info(`Deleting help article: ${articleId}`);
      
      await storage.deleteHelpArticle(articleId);
      
      Logger.api('DELETE', `/api/guest/help/article/${articleId}`, 200, Date.now() - startTime);
      res.json({ success: true });
    } catch (error) {
      Logger.error('Error deleting help article', error);
      Logger.api('DELETE', `/api/guest/help/article/${req.params.articleId}`, 500, Date.now() - startTime);
      res.status(500).json({ error: 'Failed to delete help article' });
    }
  }
}

export const guestHelpController = new GuestHelpController();