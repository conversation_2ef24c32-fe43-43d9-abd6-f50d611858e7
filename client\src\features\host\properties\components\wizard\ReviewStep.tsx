import React, { useState } from 'react';
import { useTranslations } from '@/lib/translations';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  CheckCircle, 
  AlertTriangle, 
  Edit, 
  Eye, 
  Home, 
  MapPin, 
  Users, 
  Camera, 
  Euro, 
  Shield,
  Sparkles,
  Send
} from 'lucide-react';
import { PropertyWizardData } from '../../types/property';
import { useMutation } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';

interface ReviewStepProps {
  data: Partial<PropertyWizardData>;
  onUpdate: (data: Partial<PropertyWizardData>) => void;
}

interface ValidationItem {
  id: string;
  label: string;
  isValid: boolean;
  isRequired: boolean;
  icon: React.ElementType;
}

export const ReviewStep = ({ data, onUpdate }: ReviewStepProps) => {
  const t = useTranslations('hostOnboarding.review');
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Validation logic
  const validationItems: ValidationItem[] = [
    {
      id: 'propertyTypeSelected',
      label: t('checklist.propertyTypeSelected'),
      isValid: !!data.propertyType,
      isRequired: true,
      icon: Home
    },
    {
      id: 'locationDetailsProvided',
      label: t('checklist.locationDetailsProvided'),
      isValid: !!(data.address && data.city && data.coordinates),
      isRequired: true,
      icon: MapPin
    },
    {
      id: 'guestCapacityConfigured',
      label: t('checklist.guestCapacityConfigured'),
      isValid: !!(data.maxGuests && data.bedrooms !== undefined && data.bathrooms !== undefined),
      isRequired: true,
      icon: Users
    },
    {
      id: 'amenitiesSelected',
      label: t('checklist.amenitiesSelected'),
      isValid: !!(data.amenities && data.amenities.length >= 3),
      isRequired: true,
      icon: Sparkles
    },
    {
      id: 'photosUploaded',
      label: t('checklist.photosUploaded'),
      isValid: !!(data.photos && data.photos.length >= 5),
      isRequired: true,
      icon: Camera
    },
    {
      id: 'titleAndDescriptionComplete',
      label: t('checklist.titleAndDescriptionComplete'),
      isValid: !!(data.title && data.title.length >= 10 && data.description && data.description.length >= 100),
      isRequired: true,
      icon: Edit
    },
    {
      id: 'pricingConfigured',
      label: t('checklist.pricingConfigured'),
      isValid: !!(data.pricePerNight && data.pricePerNight > 0),
      isRequired: true,
      icon: Euro
    },
    {
      id: 'policiesAndTouristLicense',
      label: t('checklist.policiesAndTouristLicense'),
      isValid: !!(data.touristLicense && data.cancellationPolicy && data.checkInTime && data.checkOutTime),
      isRequired: true,
      icon: Shield
    }
  ];

  const requiredItems = validationItems.filter(item => item.isRequired);
  const validItems = requiredItems.filter(item => item.isValid);
  const completionPercentage = (validItems.length / requiredItems.length) * 100;
  const isReadyToSubmit = completionPercentage === 100;

  const submitMutation = useMutation({
    mutationFn: async (propertyData: Partial<PropertyWizardData>) => {
      // Convert the data to match the API schema
      const submissionData = {
        host_id: '1', // This would come from current user
        title: propertyData.title || '',
        description: propertyData.description || '',
        location: propertyData.address || '',
        city: propertyData.city || '',
        country: propertyData.country || 'Spain',
        coordinates: propertyData.coordinates || { lat: 0, lng: 0 },
        price_per_night: propertyData.pricePerNight?.toString() || '0',
        max_guests: propertyData.maxGuests || 1,
        bedrooms: propertyData.bedrooms || 0,
        bathrooms: propertyData.bathrooms || 0,
        amenities: propertyData.amenities || [],
        images: propertyData.photos || [],
        property_type: propertyData.propertyType || 'villa'
      };

      const response = await fetch('/api/properties', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submissionData)
      });

      if (!response.ok) {
        throw new Error('Failed to create property listing');
      }

      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Property Listed Successfully!",
        description: "Your property has been submitted and will be reviewed within 24 hours.",
      });
      // Redirect to host dashboard
      window.location.href = '/owner/dashboard';
    },
    onError: (error) => {
      toast({
        title: "Submission Failed",
        description: "There was an error creating your listing. Please try again.",
        variant: "destructive"
      });
      console.error('Submission error:', error);
    }
  });

  const handleSubmit = async () => {
    if (!isReadyToSubmit) return;
    
    setIsSubmitting(true);
    try {
      await submitMutation.mutateAsync(data);
    } finally {
      setIsSubmitting(false);
    }
  };

  const getSectionSummary = () => {
    return {
      propertyType: data.propertyType || t('propertySummaryLabels.notSelected'),
      location: data.city ? `${data.city}, ${data.country}` : t('propertySummaryLabels.notProvided'),
      capacity: data.maxGuests ? `${data.maxGuests} guests, ${data.bedrooms} bed, ${data.bathrooms} bath` : t('propertySummaryLabels.notConfigured'),
      amenities: data.amenities ? `${data.amenities.length} amenities selected` : t('propertySummaryLabels.notSelected'),
      photos: data.photos ? `${data.photos.length} ${t('propertySummaryLabels.photosUploaded')}` : t('propertySummaryLabels.noPhotos'),
      pricing: data.pricePerNight ? `€${data.pricePerNight}/night` : t('propertySummaryLabels.notConfigured'),
      title: data.title || t('propertySummaryLabels.noTitle'),
      description: data.description ? `${data.description.length} characters` : t('propertySummaryLabels.noDescription')
    };
  };

  const summary = getSectionSummary();

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          {t('title')}
        </h2>
        <p className="text-gray-600 dark:text-gray-400 mb-4">
          {t('description')}
        </p>
        
        <div className="max-w-md mx-auto">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-gray-600 dark:text-gray-400">
              {t('completionProgress')}
            </span>
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              {validItems.length}/{requiredItems.length}
            </span>
          </div>
          <Progress value={completionPercentage} className="h-2" />
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            {Math.round(completionPercentage)}% complete
          </p>
        </div>
      </div>

      {/* Validation Checklist */}
      <Card className="border-gray-200 dark:border-gray-700">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <CheckCircle className="h-5 w-5 text-green-600" />
            <span>{t('validationChecklist')}</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {validationItems.map((item) => (
              <div key={item.id} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <item.icon className={`h-4 w-4 ${
                    item.isValid ? 'text-green-600' : 'text-gray-400'
                  }`} />
                  <span className={`text-sm ${
                    item.isValid ? 'text-gray-900 dark:text-white' : 'text-gray-500 dark:text-gray-400'
                  }`}>
                    {item.label}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  {item.isRequired && (
                    <Badge variant="outline" className="text-xs">
                      Required
                    </Badge>
                  )}
                  {item.isValid ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : (
                    <AlertTriangle className="h-4 w-4 text-orange-500" />
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Property Summary */}
      <Card className="border-gray-200 dark:border-gray-700">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Eye className="h-5 w-5 text-primary" />
            <span>{t('propertySummary')}</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white mb-1">
                  {t('propertySummaryLabels.propertyType')}
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {summary.propertyType}
                </p>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white mb-1">
                  {t('propertySummaryLabels.location')}
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {summary.location}
                </p>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white mb-1">
                  {t('propertySummaryLabels.capacity')}
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {summary.capacity}
                </p>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white mb-1">
                  {t('propertySummaryLabels.amenities')}
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {summary.amenities}
                </p>
              </div>
            </div>
            
            <div className="space-y-4">
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white mb-1">
                  {t('propertySummaryLabels.photos')}
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {summary.photos}
                </p>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white mb-1">
                  {t('propertySummaryLabels.pricing')}
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {summary.pricing}
                </p>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white mb-1">
                  {t('propertySummaryLabels.listingTitle')}
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {summary.title}
                </p>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white mb-1">
                  {t('propertySummaryLabels.listingDescription')}
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {summary.description}
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Missing Requirements */}
      {!isReadyToSubmit && (
        <Card className="border-orange-200 dark:border-orange-800 bg-orange-50 dark:bg-orange-900/20">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 text-orange-800 dark:text-orange-200">
              <AlertTriangle className="h-5 w-5" />
              <span>{t('missingRequirements')}</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {validationItems
                .filter(item => item.isRequired && !item.isValid)
                .map(item => (
                  <li key={item.id} className="flex items-center space-x-2 text-sm text-orange-700 dark:text-orange-300">
                    <AlertTriangle className="h-4 w-4" />
                    <span>{item.label}</span>
                  </li>
                ))}
            </ul>
          </CardContent>
        </Card>
      )}

      {/* Ready to Submit */}
      {isReadyToSubmit && (
        <Card className="border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-900/20">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 text-green-800 dark:text-green-200">
              <CheckCircle className="h-5 w-5" />
              <span>{t('readyToSubmit')}</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-green-700 dark:text-green-300 mb-4">
              {t('readyToSubmitDescription')}
            </p>
            <Button
              type="button"
              onClick={handleSubmit}
              disabled={isSubmitting}
              className="w-full bg-green-600 hover:bg-green-700 text-white flex items-center space-x-2"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>{t('submitting')}</span>
                </>
              ) : (
                <>
                  <Send className="h-4 w-4" />
                  <span>{t('submitListing')}</span>
                </>
              )}
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Next Steps */}
      <div className="bg-blue-50 dark:bg-blue-900/20 p-6 rounded-lg">
        <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">
          {t('nextSteps')}
        </h4>
        <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
          <li>• {t('step1')}</li>
          <li>• {t('step2')}</li>
          <li>• {t('step3')}</li>
          <li>• {t('step4')}</li>
        </ul>
      </div>
    </div>
  );
};