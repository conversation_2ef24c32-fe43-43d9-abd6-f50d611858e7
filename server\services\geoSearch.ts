import { supabase } from '../supabase';
import { locationService } from './locationService';
import { geocodingService } from './geocoding';
import { HostProperty } from '../supabase';

export interface GeographicSearchParams {
  location?: string;
  coordinates?: { lat: number; lng: number };
  radius?: number; // in meters, default 50km
  checkIn?: Date;
  checkOut?: Date;
  maxGuests?: number;
  minPrice?: number;
  maxPrice?: number;
  propertyTypes?: string[];
  amenities?: string[];
}

export interface GeographicSearchResult {
  property: HostProperty;
  distance: number; // in meters
  coordinates: { lat: number; lng: number };
}

export interface SearchResponse {
  results: GeographicSearchResult[];
  total: number;
  center: { lat: number; lng: number } | null;
  radius: number;
  searchLocation: string;
}

export class GeoSearchService {
  private static readonly DEFAULT_RADIUS = 50000; // 50km in meters
  private static readonly MAX_RADIUS = 200000; // 200km maximum

  /**
   * Search properties geographically with optional filters
   */
  async searchPropertiesGeographically(params: GeographicSearchParams): Promise<SearchResponse> {
    let searchCenter: { lat: number; lng: number } | null = null;
    let searchLocation = '';

    // Determine search center coordinates
    if (params.coordinates) {
      searchCenter = params.coordinates;
      searchLocation = `${params.coordinates.lat},${params.coordinates.lng}`;
    } else if (params.location) {
      searchLocation = params.location;
      // Use database location lookup instead of external geocoding
      const locations = await locationService.searchLocations({ 
        query: params.location, 
        limit: 1 
      });
      
      if (locations.length > 0 && locations[0].coordinates) {
        searchCenter = locations[0].coordinates;
        console.log(`[GEO_SEARCH] Found coordinates for ${params.location}:`, searchCenter);
      } else {
        // Fallback to text-based search if location not found in database
        console.log(`[GEO_SEARCH] Location ${params.location} not found in database, using text search`);
        return this.fallbackTextSearch(params);
      }
    }

    if (!searchCenter) {
      throw new Error('No valid search location provided');
    }

    const radius = Math.min(params.radius || GeoSearchService.DEFAULT_RADIUS, GeoSearchService.MAX_RADIUS);

    // Execute geographic search
    const results = await this.executeGeographicQuery(searchCenter, radius, params);

    return {
      results,
      total: results.length,
      center: searchCenter,
      radius,
      searchLocation
    };
  }

  /**
   * Execute PostGIS geographic query
   */
  private async executeGeographicQuery(
    center: { lat: number; lng: number },
    radius: number,
    params: GeographicSearchParams
  ): Promise<GeographicSearchResult[]> {
    if (!supabase) {
      console.warn('Supabase not available, falling back to in-memory search');
      return [];
    }

    try {
      // Build the PostGIS query
      let query = supabase
        .from('host_properties')
        .select('*')
        .eq('is_active', true);

      // Add geographic filtering using PostGIS
      // Note: We'll use the coordinates JSONB field since PostGIS geography column doesn't exist yet
      query = query.not('coordinates', 'is', null);

      const { data: properties, error } = await query;

      if (error) {
        console.error('Error executing geographic query:', error);
        return [];
      }

      if (!properties) return [];

      // Filter by distance and apply other filters
      const results: GeographicSearchResult[] = [];

      for (const property of properties) {
        if (!property.coordinates) continue;

        const propCoords = property.coordinates as { lat: number; lng: number };
        const distance = this.calculateDistance(center, propCoords);

        // Check if property is within radius
        if (distance <= radius) {
          // Apply additional filters
          if (this.passesFilters(property, params)) {
            results.push({
              property,
              distance,
              coordinates: propCoords
            });
          }
        }
      }

      // Sort by distance
      results.sort((a, b) => a.distance - b.distance);

      return results;
    } catch (error) {
      console.error('Error in geographic search:', error);
      return [];
    }
  }

  /**
   * Enhanced PostGIS query (when geography columns are available)
   */
  async executePostGISQuery(
    center: { lat: number; lng: number },
    radius: number,
    params: GeographicSearchParams
  ): Promise<GeographicSearchResult[]> {
    if (!supabase) return [];

    try {
      // Since PostGIS may not be available, use fallback to regular property search
      // This is a placeholder for future PostGIS implementation
      return this.executeGeographicQuery(center, radius, params);
    } catch (error) {
      console.error('PostGIS query execution error:', error);
      return [];
    }
  }

  /**
   * Fallback to text-based search when geocoding fails
   */
  private async fallbackTextSearch(params: GeographicSearchParams): Promise<SearchResponse> {
    if (!supabase) {
      return {
        results: [],
        total: 0,
        center: null,
        radius: 0,
        searchLocation: params.location || ''
      };
    }

    let query = supabase
      .from('host_properties')
      .select('*')
      .eq('is_active', true);

    if (params.location) {
      query = query.or(`city.ilike.%${params.location}%,location.ilike.%${params.location}%`);
    }

    const { data: properties, error } = await query;

    if (error || !properties) {
      return {
        results: [],
        total: 0,
        center: null,
        radius: 0,
        searchLocation: params.location || ''
      };
    }

    const results: GeographicSearchResult[] = properties
      .filter(property => this.passesFilters(property, params))
      .map(property => ({
        property,
        distance: 0, // No distance calculation in text search
        coordinates: property.coordinates as { lat: number; lng: number }
      }));

    return {
      results,
      total: results.length,
      center: null,
      radius: 0,
      searchLocation: params.location || ''
    };
  }

  /**
   * Apply additional filters to properties
   */
  private passesFilters(property: HostProperty, params: GeographicSearchParams): boolean {
    if (params.maxGuests && property.max_guests < params.maxGuests) {
      return false;
    }

    if (params.minPrice && parseFloat(property.price_per_night) < params.minPrice) {
      return false;
    }

    if (params.maxPrice && parseFloat(property.price_per_night) > params.maxPrice) {
      return false;
    }

    if (params.propertyTypes && params.propertyTypes.length > 0) {
      if (!params.propertyTypes.includes(property.property_type)) {
        return false;
      }
    }

    if (params.amenities && params.amenities.length > 0) {
      const propertyAmenities = property.amenities || [];
      if (!params.amenities.every(amenity => propertyAmenities.includes(amenity))) {
        return false;
      }
    }

    return true;
  }

  /**
   * Calculate distance between two coordinates using Haversine formula
   */
  private calculateDistance(coord1: { lat: number; lng: number }, coord2: { lat: number; lng: number }): number {
    const R = 6371000; // Earth's radius in meters
    const dLat = this.toRadians(coord2.lat - coord1.lat);
    const dLng = this.toRadians(coord2.lng - coord1.lng);
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(this.toRadians(coord1.lat)) * Math.cos(this.toRadians(coord2.lat)) *
              Math.sin(dLng / 2) * Math.sin(dLng / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  /**
   * Convert degrees to radians
   */
  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  /**
   * Get properties within a specific radius of a point
   */
  async getPropertiesWithinRadius(
    center: { lat: number; lng: number },
    radius: number
  ): Promise<GeographicSearchResult[]> {
    return this.executeGeographicQuery(center, radius, {});
  }

  /**
   * Find nearest properties to a location
   */
  async findNearestProperties(
    location: string,
    limit: number = 10
  ): Promise<GeographicSearchResult[]> {
    const coords = await geocodingService.geocodeLocation(location);
    if (!coords) return [];

    const results = await this.executeGeographicQuery(coords, GeoSearchService.DEFAULT_RADIUS, {});
    return results.slice(0, limit);
  }
}

export const geoSearchService = new GeoSearchService();