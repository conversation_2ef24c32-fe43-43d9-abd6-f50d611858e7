import { Request, Response } from 'express';
import { Logger } from '../../utils/logger';

export class TranslationController {
  private translations = {
    en: {
      indexPage: {
        hero: {
          title: "Find your perfect vacation villa",
          subtitle: "Discover thousands of unique accommodations around the world"
        },
        location: {
          placeholder: "Where are you going?"
        },
        searchButton: "Search",
        popularInSpain: "Popular in Spain",
        newInFrance: "New in France",
        guestFavorites: "Guest favorites"
      },
      navigation: {
        stays: "Stays",
        experiences: "Experiences",
        onlineExperiences: "Online experiences",
        host: "Host",
        guest: "Guest",
        villaWiseYourHome: "VillaWise your home",
        becomeHost: "Become a host"
      },
      common: {
        search: "Search",
        filters: "Filters",
        map: "Map",
        next: "Next",
        previous: "Previous",
        clear: "Clear",
        apply: "Apply",
        cancel: "Cancel",
        close: "Close",
        guest: "Guest"
      },
      headerTabs: {
        guest: "Guest",
        host: "Host",
        owner: "Host"
      },
      brand: {
        name: "<PERSON><PERSON><PERSON>"
      },

      auth: {
        login: {
          title: "Welcome Back to VillaWise",
          subtitle: "Sign in to continue your villa search journey",
          emailLabel: "Email Address",
          emailPlaceholder: "Enter your email",
          passwordLabel: "Password",
          passwordPlaceholder: "Enter your password",
          rememberMe: "Remember me",
          forgotPassword: "Forgot password?",
          signInButton: "Sign In",
          signingIn: "Signing in...",
          orContinueWith: "Or continue with email",
          loginFailed: "Login Failed",
          loginFailedDescription: "Please check your credentials and try again",
          dontHaveAccount: "Don't have an account?",
          signUp: "Sign up",
          emailVerificationSuccess: "Welcome to VillaWise! 🎉 We've sent a confirmation email to {email}. Please check your inbox and click the activation link to complete your registration."
        },
        register: {
          title: "Join VillaWise Today", 
          subtitle: "Create your account and unlock access to thousands of verified vacation rentals",
          socialLogin: "Social Login",
          emailRegistration: "Email Registration",
          firstNameLabel: "First Name",
          firstNamePlaceholder: "First name",
          lastNameLabel: "Last Name",
          lastNamePlaceholder: "Last name",
          usernameLabel: "Username",
          usernamePlaceholder: "Choose a username",
          emailLabel: "Email Address",
          emailPlaceholder: "Enter your email",
          passwordLabel: "Password",
          passwordPlaceholder: "Create a password",
          confirmPasswordLabel: "Confirm Password",
          confirmPasswordPlaceholder: "Confirm your password",
          agreeToTerms: "I agree to the Terms of Service and Privacy Policy",
          createAccountButton: "Create Account",
          creatingAccount: "Creating account...",
          orContinueWith: "Or continue with email",
          passwordMismatch: "Password Mismatch",
          passwordMismatchDescription: "Please make sure your passwords match",
          passwordTooShort: "Password Too Short",
          passwordTooShortDescription: "Password must be at least 8 characters long",
          termsRequired: "Terms Agreement Required",
          termsRequiredDescription: "Please agree to the Terms of Service and Privacy Policy",
          accountCreated: "Account Created!",
          accountCreatedDescription: "Your account has been created successfully. You can now start exploring properties.",
          registrationFailed: "Registration Failed",
          registrationFailedDescription: "Please try again",
          alreadyHaveAccount: "Already have an account?",
          signIn: "Sign in"
        },
        forgotPassword: {
          title: "Forgot Password",
          subtitle: "Enter your email address and we'll send you a reset link",
          emailLabel: "Email Address",
          emailPlaceholder: "Enter your email address",
          sendResetButton: "Send Reset Email",
          sendingReset: "Sending reset email...",
          checkEmailTitle: "Check Your Email",
          checkEmailSubtitle: "We've sent password reset instructions to your email",
          emailSentTitle: "Email Sent!",
          emailSentMessage: "We've sent a password reset link to",
          emailSentHelp: "Didn't receive the email? Check your spam folder or try again.",
          tryDifferentEmail: "Try Different Email",
          backToSignIn: "Back to Sign In",
          resetEmailSent: "Reset Email Sent",
          resetEmailSentDescription: "Check your email for password reset instructions",
          resetEmailError: "Error",
          resetEmailErrorDescription: "Failed to send reset email",
          rememberPassword: "Remember your password?",
          signIn: "Sign in",
          checkEmailDescription: "We've sent password reset instructions to your email",
          emailSentDescription: "We've sent a password reset link to",
          emailNotReceived: "Didn't receive the email? Check your spam folder or try again."
        },
        resetPassword: {
          title: "Reset Password",
          subtitle: "Enter your new password",
          newPasswordLabel: "New Password",
          newPasswordPlaceholder: "Enter your new password",
          confirmPasswordLabel: "Confirm New Password",
          confirmPasswordPlaceholder: "Confirm your new password",
          updatePasswordButton: "Update Password",
          updatingPassword: "Updating password...",
          successTitle: "Password Updated Successfully",
          successSubtitle: "Your password has been changed",
          successMessage: "Your password has been successfully updated. You can now sign in with your new password.",
          continueToSignIn: "Continue to Sign In",
          invalidLinkTitle: "Invalid Reset Link",
          invalidLinkSubtitle: "This password reset link is invalid or has expired",
          requestNewLink: "Request New Reset Link",
          rememberPassword: "Remember your password?",
          passwordMismatch: "Password Mismatch",
          passwordMismatchDescription: "Please make sure your passwords match",
          passwordTooShort: "Password Too Short",
          passwordTooShortDescription: "Password must be at least 8 characters long",
          invalidToken: "Invalid Token",
          invalidTokenDescription: "Reset token is missing or invalid",
          passwordUpdated: "Password Updated",
          passwordUpdatedDescription: "Your password has been successfully updated",
          resetFailed: "Reset Failed",
          resetFailedDescription: "Failed to reset password"
        },
        social: {
          continueWithGoogle: "Continue with Google",
          continueWithFacebook: "Continue with Facebook",
          continueWithApple: "Continue with Apple"
        },
        passwordStrength: {
          weak: "Weak",
          fair: "Fair",
          good: "Good",
          strong: "Strong",
          helpText: "Use 8+ characters with letters, numbers, and symbols"
        }
      },

      // Host upgrade page namespace
      hostUpgrade: {
        title: "Become a Host",
        subtitle: "Start hosting your property and earn extra income",
        loading: "Loading...",
        
        benefits: {
          title: "Benefits of Becoming a Host",
          listProperty: {
            title: "List Your Property",
            description: "Add your property to our platform and reach thousands of potential guests"
          },
          earnMoney: {
            title: "Earn Money",
            description: "Generate extra income by renting your property to travelers"
          },
          manageBookings: {
            title: "Manage Bookings",
            description: "Easily handle reservations, payments, and guest communication"
          },
          hostCommunity: {
            title: "Host Community",
            description: "Join our community of successful hosts and get support"
          }
        },
        
        upgrade: {
          title: "Upgrade to Host",
          requirements: {
            title: "Requirements",
            verified: "Verified VillaWise account",
            agreement: "Agreement to host terms and conditions",
            guidelines: "Understanding of community guidelines"
          },
          button: "Become a Host",
          upgrading: "Upgrading...",
          backToDashboard: "Back to Dashboard",
          backToHome: "Back to Home"
        },
        
        upgradeSuccess: "Upgrade Successful",
        upgradeSuccessDescription: "Welcome to the VillaWise host community! You can now add properties.",
        upgradeFailed: "Upgrade Failed",
        upgradeFailedDescription: "Something went wrong during the upgrade. Please try again."
      },

      // Auth error messages namespace
      authErrors: {
        // Authentication failures
        authenticationRequired: "Authentication required",
        authenticationFailed: "Authentication failed",
        invalidToken: "Invalid or expired token",
        tokenMissing: "Authorization token missing",
        sessionExpired: "Your session has expired. Please sign in again",
        
        // Authorization failures
        hostPrivilegesRequired: "Host privileges required",
        hostUpgradeNeeded: "You need to upgrade to host status to access this feature",
        accessDenied: "Access denied",
        insufficientPermissions: "Insufficient permissions",
        unauthorizedAccess: "Unauthorized access",
        restrictedArea: "This area is restricted to verified hosts only",
        
        // Loading states
        checkingAuthorization: "Checking authorization...",
        loadingDashboard: "Loading dashboard...",
        verifyingAccess: "Verifying access...",
        
        // User upgrade messages
        upgradeToHost: "Upgrade to Host",
        becomeHostToday: "Become a host today",
        upgradeSuccess: "Successfully upgraded to host",
        upgradeFailed: "Failed to upgrade to host",
        
        // Navigation messages
        backToHome: "Back to Home",
        gotoGuestDashboard: "Go to Guest Dashboard",
        returnToHome: "Return to Home",
        redirectingToLogin: "Redirecting to login...",
        
        // Feature access messages
        hostAccessRequired: "Host Access Required",
        guestAccessRequired: "Guest Access Required",
        loginRequired: "Login Required",
        verificationRequired: "Verification Required",
        
        // Generic error messages
        somethingWentWrong: "Something went wrong",
        tryAgainLater: "Please try again later",
        contactSupport: "Contact support if the problem persists"
      },
      validation: {
        required: "This field is required",
        email: {
          invalid: "Please enter a valid email address",
          exists: "A user with this email already exists"
        },
        username: {
          invalid: "Username must be at least 3 characters long",
          exists: "This username is already taken"
        },
        password: {
          minLength: "Password must be at least {{min}} characters long",
          weak: "Password is too weak",
          mismatch: "Passwords do not match"
        },
        auth: {
          emailExists: "A user with this email already exists",
          usernameExists: "This username is already taken",
          invalidCredentials: "Invalid email or password",
          accountDisabled: "Account has been disabled",
          authServiceUnavailable: "Authentication service unavailable",
          requiredFields: "Email, password, and username are required",
          emailNotConfirmed: "Please check your email and confirm your account before logging in."
        }
      },
      userMenu: {
        profile: "Profile",
        account: "Account",
        settings: "Settings",
        help: "Help",
        logout: "Log out",
        login: "Log in",
        signup: "Sign up",
        favorites: "Favorites",
        trips: "Trips",
        messages: "Messages",
        accountSettings: "Account settings",
        languageCurrency: "Language & Currency",
        helpCenter: "Help Center",
        becomeHost: "Become a Host",
        hostInfo: "Host an experience",
        referHost: "Refer a Host",
        loading: "Loading...",
        menu: "Menu",
        welcome: "Welcome to VillaWise",
        signInToAccess: "Sign in to access your account"
      },
      locationAutocomplete: {
        placeholder: "Where",
        searchResults: "Search results",
        recentSearches: "Recent searches",
        clearHistory: "Clear history",
        popularDestinations: "Popular destinations",
        beachAndSea: "Beach & Sea",
        cityTrips: "City trips",
        noResults: "No locations found"
      },
      dateRangePicker: {
        placeholder: "Add dates",
        checkIn: "Check-in",
        checkOut: "Check-out",
        flexible: "I'm flexible",
        exactDates: "Exact dates",
        flexibilityOptions: "Flexibility",
        plusMinus1: "± 1 day",
        plusMinus2: "± 2 days", 
        plusMinus3: "± 3 days",
        plusMinus7: "± 1 week",
        nights: "nights",
        night: "night",
        day: "day",
        days: "days",
        clear: "Clear",
        done: "Done"
      },
      guestSelector: {
        placeholder: "Add guests",
        adults: {
          title: "Adults",
          description: "Ages 13 or above"
        },
        children: {
          title: "Children",
          description: "Ages 2-12"
        },
        infants: {
          title: "Infants", 
          description: "Under 2"
        },
        pets: {
          title: "Pets",
          description: "Bringing a service animal?"
        },
        guest: "guest",
        guests: "guests",
        done: "Done"
      },
      searchPage: {
        title: "Search Results",
        noResults: "No properties found",
        tryAdjusting: "Try adjusting your search or filters to find what you're looking for.",
        showMap: "Show map",
        hideMap: "Hide map",
        filters: "Filters",
        sortBy: "Sort by",
        breadcrumb: {
          home: "Home",
          spain: "Spain"
        },
        discoverAccommodations: "Discover accommodations",
        showPricesPerNight: "Show prices per night",
        sortOptions: {
          recommended: "Recommended",
          priceLow: "Price: Low to High",
          priceHigh: "Price: High to Low", 
          rating: "Rating",
          newest: "Newest"
        },
        guest: "guest"
      },
      propertyCard: {
        night: "night",
        reviews: "reviews",
        superhost: "Superhost",
        guestFavorite: "Guest favorite",
        guests: "guests",
        bedrooms: "bedrooms",
        bathrooms: "bathrooms",
        perWeek: "per week",
        priceFrom: "Weekly from",
        badges: {
          luxe: "Luxe",
          "favoriet-van-gasten": "Guest favorite",
          nieuw: "New",
          "favoriet van gasten": "Guest favorite",
          superhost: "Superhost",
          scenic: "Scenic",
          "beach-front": "Beachfront",
          traditional: "Traditional",
          rural: "Rural",
          modern: "Modern",
          premium: "Premium",
          central: "Central",
          port: "Port",
          family: "Family",
          "sea-view": "Sea view",
          "beach-villa": "Beach villa",
          luxury: "Luxury",
          "mountain-view": "Mountain view",
          romantic: "Romantic",
          rustic: "Rustic",
          "eco-friendly": "Eco-friendly",
          authentic: "Authentic",
          popular: "Popular",
          rare: "Rare",
          panoramic: "Panoramic",
          cozy: "Cozy",
          elite: "Elite",
          marina: "Marina",
          village: "Village",
          "beach-access": "Beach access",
          garden: "Garden",
          sunset: "Sunset",
          terrace: "Terrace",
          "bay-view": "Bay view",
          "beach-nearby": "Beach nearby",
          "iconic-view": "Iconic view",
          historic: "Historic",
          harbor: "Harbor",
          "cliff-top": "Cliff-top",
          natural: "Natural",
          verified: "Verified"
        }
      },
      searchBar: {
        where: "Where",
        addDates: "Add dates",
        addGuests: "Add guests",
        search: "Search",
        searchDestination: "Search destination",
        when: "When",
        who: "Who",
        anyWeek: "Any week",
        guest: "guest",
        guests: "guests",
        datesSelected: "Dates selected"
      },
      exploration: {
        title: "Explore",
        description: "Discover amazing places",
        scrollLeft: "Scroll left",
        scrollRight: "Scroll right"
      },
      inspiration: {
        title: "Get inspired",
        description: "Ideas for your next trip"
      },
      ownerCta: {
        title: "Become a host",
        subtitle: "Earn extra income and unlock new opportunities",
        buttonText: "Become a host"
      },
      propertyDetails: {
        guests: "guests",
        bedrooms: "bedrooms",
        bathrooms: "bathrooms",
        wifi: "WiFi",
        perNight: "per night",
        selectDates: "Select dates",
        addGuests: "Add guests",
        reserve: "Reserve",
        noChargeYet: "You won't be charged yet",
        nights: "nights",
        serviceFee: "Service fee",
        total: "Total",
        trustIndicators: {
          freeBookingChanges: "Free booking changes",
          directCommunication: "Direct communication with host",
          trustedReviews: "Trusted by 10,000+ reviews",
          securePayment: "Safe and secure payment"
        },
        hostedBy: "Hosted by",
        superhost: "Superhost",
        memberSince: "Member since",
        responseTime: "Response time",
        responseRate: "Response rate",
        contactHost: "Contact host",
        viewProfile: "View profile",
        hostVerifications: "Host verifications",
        identityVerified: "Identity verified",
        aboutProperty: "About this property",
        amenitiesTitle: "Amenities",
        amenities: {
          wifi: "WiFi",
          parking: "Free parking",
          pool: "Pool",
          kitchen: "Kitchen",
          airConditioning: "Air conditioning",
          heating: "Heating",
          tv: "TV",
          washer: "Washing machine",
          balcony: "Balcony",
          garden: "Garden"
        },
        houseRules: "House rules",
        checkIn: "Check-in",
        checkOut: "Check-out",
        minimumStay: "Minimum stay",
        reviews: "reviews",
        showAllReviews: "Show all {{count}} reviews",
        propertyNotFound: "Property not found",
        propertyNotFoundDescription: "The property you're looking for doesn't exist or has been removed.",
        goBack: "Go back",
        back: "Back"
      },
      footer: {
        company: "Company",
        community: "Community",
        host: "Host",
        support: "Support",
        languages: {
          list: {
            nl: "Nederlands",
            en: "English",
            de: "Deutsch",
            fr: "Français"
          }
        },
        sections: {
          support: {
            title: "Support",
            items: {
              helpCenter: "Help Center",
              airCover: "VillaCover",
              antiDiscrimination: "Anti-discrimination",
              disabilitySupport: "Disability support",
              cancellationOptions: "Cancellation options"
            }
          },
          guests: {
            title: "Guests",
            items: {
              villaWiseYourHome: "VillaWise your home",
              airCoverHosts: "VillaCover for hosts",
              hostingInfo: "Hosting information",
              responsibleHosting: "Responsible hosting"
            }
          },
          villaWise: {
            title: "VillaWise",
            items: {
              newsroom: "Newsroom",
              newFeatures: "New features",
              careers: "Careers",
              investors: "Investors",
              giftCards: "Gift cards"
            }
          }
        }
      },
      // Improved namespace structure for dashboards (prevents conflicts)
      guestDashboard: {
        title: "Dashboard",
        greeting: "Good evening",
        description: "Your bookings, saved homes, messages and reviews in one place.",
        navigation: {
          dashboard: "Dashboard",
          bookings: "Bookings",
          wishlists: "Wishlists",
          reviews: "Reviews",
          messages: "Messages",
          settings: "Settings",
          help: "Help",
          carRental: "Rent a car"
        },
        becomeHost: {
          title: "Earn money hosting your space",
          description: "Join thousands of hosts earning extra income by sharing their properties on VillaWise",
          button: "Become a Host",
          upgrading: "Upgrading..."
        },
        recentBookings: {
          title: "Recent bookings",
          viewAll: "View all bookings",
          noBookings: "No bookings yet",
          startExploring: "Start exploring properties",
          accepted: "Accepted",
          amount: "Amount: € 1345",
          view: "View",
          newBooking: "New booking"
        },
        wishlists: {
          title: "Your wishlists",
          viewAll: "View all wishlists",
          noWishlists: "No saved properties yet",
          startSaving: "Start saving your favorite properties"
        },
        overview: {
          totalSpent: "Total Spent",
          upcomingTrips: "Upcoming Trips", 
          messages: "Messages",
          noMessages: "No messages",
          viewAllMessages: "View All"
        },
        wishlistPage: {
          description: "Your saved properties and favorites",
          property: "Property",
          location: "Location", 
          reviews: "reviews",
          perNight: "per night",
          viewProperty: "View Property"
        },
        account: {
          personalInfo: {
            title: "Personal Information",
            firstName: "First Name",
            lastName: "Last Name",
            email: "Email",
            editProfile: "Edit Profile"
          },
          dashboardSettings: {
            title: "Dashboard Settings",
            becomeHostSection: "Become Host Section",
            becomeHostDescription: "Show the host invitation section on your dashboard",
            visible: "Visible",
            hidden: "Hidden",
            hide: "Hide",
            restore: "Restore"
          }
        }
      },
      hostDashboard: {
        title: "Dashboard",
        greeting: "Good evening host!",
        description: "Manage your properties, bookings and earnings clearly.",
        navigation: {
          dashboard: "Dashboard",
          bookings: "Bookings",
          properties: "My homes",
          guests: "Guests",
          earnings: "Earnings",
          analytics: "Performance",
          tasks: "Tasks",
          inventory: "Inventory",
          messages: "Messages",
          settings: "Settings",
          help: "Help"
        },
        overview: {
          title: "Overview of your hosting activities",
          totalEarnings: "Total earnings",
          activeBookings: "Active bookings",
          occupancyRate: "Occupancy rate"
        },
        recentBookings: {
          title: "Recent bookings",
          viewAll: "View all bookings",
          confirmed: "Confirmed",
          pending: "Pending",
          guests: "guests",
          newBooking: "New booking",
          noBookings: "No recent bookings to display.",
          startExploring: "Bookings will appear here once guests make reservations."
        },
        properties: {
          title: "My properties",
          viewAll: "View all properties",
          bookings: "bookings",
          newProperty: "New property",
          noProperties: "No properties listed yet.",
          addProperty: "Add your first property to start welcoming guests."
        }
      },

      hostOnboarding: {
        title: "Add New Property",
        previous: "Previous",
        next: "Next", 
        finish: "Finish",
        publish: "Publish Property",
        step: "Step",
        of: "of",
        steps: {
          propertyType: "Property Type",
          location: "Location",
          capacity: "Guest Capacity",
          amenities: "Amenities",
          photos: "Photos",
          listing: "Title & Description",
          pricing: "Pricing",
          policies: "Policies",
          review: "Review"
        },
        propertyType: {
          title: "What type of property are you listing?",
          description: "Choose the option that best describes your space",
          propertyTypeTitle: "Property Type",
          spaceTypeTitle: "What will guests have access to?",
          illustrationText: "Help guests understand what kind of space you're offering",
          types: {
            house: {
              name: "House",
              description: "A standalone house with private entrance"
            },
            apartment: {
              name: "Apartment",
              description: "A unit in a building with shared entrance"
            },
            villa: {
              name: "Villa",
              description: "Luxury property with high-end amenities"
            },
            townhouse: {
              name: "Townhouse",
              description: "Multi-level house connected to other properties"
            },
            cottage: {
              name: "Cottage",
              description: "Cozy countryside or rural property"
            },
            penthouse: {
              name: "Penthouse",
              description: "Top floor apartment with premium views"
            },
            beachhouse: {
              name: "Beach House",
              description: "Property located near or on the beach"
            },
            mountain_cabin: {
              name: "Mountain Cabin",
              description: "Rustic property in mountainous area"
            }
          },
          spaceTypes: {
            entire_place: {
              name: "Entire place",
              description: "Guests have the whole place to themselves"
            },
            private_room: {
              name: "Private room",
              description: "Guests have their own private room in a shared space"
            },
            shared_room: {
              name: "Shared room",
              description: "Guests share a room with others"
            }
          },
          labels: {
            popular: "Popular",
            recommended: "Recommended"
          }
        },
        location: {
          title: "Where is your property located?",
          description: "We'll only share your exact address with confirmed guests",
          searchPlaceholder: "Search for a city or town",
          popularLocations: "Popular Costa Blanca Locations",
          addressDetails: "Address Details",
          streetAddress: "Street Address",
          addressPlaceholder: "Enter your full address",
          city: "City",
          cityPlaceholder: "Enter city name",
          postalCode: "Postal Code",
          province: "Province",
          confirmAddress: "Confirm Address",
          privacyNoticeTitle: "Privacy Notice",
          privacyNoticeText: "Your exact address will only be shared with guests after booking confirmation.",
          illustrationText: "Your location helps guests find your property easily"
        },
        capacity: {
          title: "How many guests can your space accommodate?",
          description: "Tell us about the sleeping arrangements and facilities",
          maxGuests: "Maximum Guests",
          maxGuestsDescription: "Number of guests you can accommodate",
          bedrooms: "Bedrooms",
          bedroomsDescription: "Available bedrooms for guests",
          bathrooms: "Bathrooms",
          bathroomsDescription: "Half bathrooms count as 0.5",
          popularConfigurations: "Popular Configurations",
          tipsTitle: "Capacity Tips",
          tip1: "Be honest about your space's capacity",
          tip2: "Think about maximum comfort, not just available beds",
          tip3: "Half bathrooms count as 0.5 bathrooms",
          tip4: "Consider shared spaces when determining guest numbers",
          illustrationText: "Accurate capacity information helps guests choose your space"
        },
        amenities: {
          title: "What amenities do you offer?",
          description: "Select all amenities and facilities available to your guests",
          selectedCount: "amenities selected",
          popular: "Popular",
          tipsTitle: "Amenity Tips",
          tip1: "Highlight unique features like pool or sea view",
          tip2: "Essential amenities like WiFi and parking are very important",
          tip3: "Kitchen facilities attract longer stays",
          tip4: "Air conditioning is highly valued in summer",
          illustrationText: "Great amenities make your property stand out"
        },
        photos: {
          title: "Add photos of your space",
          description: "High-quality photos help your listing stand out",
          photoCategories: "Photo Categories",
          uploadPhotos: "Upload Photos",
          uploadTitle: "Drag photos here or click to browse",
          uploadDescription: "Upload at least 5 photos to showcase your space",
          chooseFiles: "Choose Files",
          photoPreview: "Photo Preview",
          photographyTips: "Photography Tips",
          required: "Required",
          more: "more",
          minimumPhotos: "minimum photos",
          readyToContinue: "Ready to continue",
          exterior: "Exterior",
          exteriorDescription: "Outside views of your property",
          livingSpaces: "Living Spaces",
          livingSpacesDescription: "Common areas where guests relax",
          bedrooms: "Bedrooms",
          bedroomsDescription: "All sleeping areas",
          bathrooms: "Bathrooms",
          bathroomsDescription: "All bathrooms and toilets",
          amenities: "Amenities",
          amenitiesDescription: "Special features and facilities",
          views: "Views",
          viewsDescription: "Beautiful views from your property",
          frontOfHouse: "Front of house",
          gardenPatio: "Garden/patio",
          poolArea: "Pool area",
          balconyTerrace: "Balcony/terrace",
          livingRoom: "Living room",
          diningRoom: "Dining room",
          kitchen: "Kitchen",
          loungeArea: "Lounge area",
          masterBedroom: "Master bedroom",
          guestBedrooms: "Guest bedrooms",
          bedDetails: "Bed details",
          bedroomViews: "Bedroom views",
          mainBathroom: "Main bathroom",
          ensuiteBathrooms: "Ensuite bathrooms",
          showerBath: "Shower/bath",
          toiletAreas: "Toilet areas",
          pool: "Pool",
          gym: "Gym",
          bbqArea: "BBQ area",
          workspace: "Workspace",
          entertainmentRoom: "Entertainment room",
          seaView: "Sea view",
          mountainView: "Mountain view",
          cityView: "City view",
          gardenView: "Garden view",
          tip1: "Use natural lighting when possible",
          tip2: "Take photos during daytime",
          tip3: "Show all spaces accessible to guests",
          tip4: "Keep spaces clean and clutter-free",
          tip5: "Include exterior and amenity photos",
          illustrationText: "Great photos are key to attracting guests"
        },
        listing: {
          title: "Create your listing",
          description: "Help guests understand what makes your space special",
          propertyTitle: "Property Title",
          titlePlaceholder: "e.g. Beautiful villa with pool in Costa Blanca",
          titleSuggestions: "Title Suggestions",
          generateTitle: "Generate Title",
          propertyDescription: "Property Description",
          descriptionPlaceholder: "Describe your space, amenities, and what makes it special...",
          descriptionTemplates: "Description Templates",
          preview: "Preview",
          photoPlaceholder: "Photo Placeholder",
          writingTips: "Writing Tips",
          tip1: "Highlight unique features and amenities",
          tip2: "Mention nearby attractions and beaches",
          tip3: "Be specific about what's included",
          tip4: "Use descriptive language to paint a picture",
          tip5: "Keep it concise but informative",
          completionRequired: "Vul het volgende in:",
          completionRequiredTitle: "Titel moet minimaal 10 tekens zijn",
          completionRequiredDescription: "Omschrijving moet minimaal 100 tekens zijn",
          villaWithPool: "Villa met zwembad",
          villaWithPoolDescription: "Welkom bij onze prachtige [property_type] in [location]! Deze verbluffende eigendom...",
          beachfrontApartment: "Strandhuis appartement",
          beachfrontApartmentDescription: "Ervaar de magie van het kustleven in onze [property_type] direct aan het strand...",
          mountainRetreat: "Bergtoevlucht",
          mountainRetreatDescription: "Ontsnap naar onze vredige [property_type] genesteld in de bergen van [location]...",
          charactersCount: "tekens",
          wordsCount: "woorden"
        },
        pricing: {
          title: "Set your price",
          description: "You can always adjust your pricing after publishing",
          aiSuggestion: "AI Price Suggestion",
          useThisPrice: "Use This Price",
          pricingStrategies: "Pricing Strategies",
          customPricing: "Custom Pricing",
          pricePerNight: "Price per night",
          priceRange: "Price Range",
          earningsProjection: "Earnings Projection",
          marketComparison: "Market Comparison",
          pricingTips: "Pricing Tips",
          tip1: "Start competitive and adjust based on demand",
          tip2: "Consider seasonal price variations",
          tip3: "Factor in cleaning and service costs",
          tip4: "Research similar properties in your area"
        },
        policies: {
          title: "Set your policies",
          description: "Create clear rules and policies for your guests",
          legalRequirements: "Spanish Legal Requirements",
          touristLicense: "Tourist License Number",
          touristLicenseDescription: "Required for all vacation rentals in Spain",
          businessRegistration: "Business Registration",
          businessRegistrationDescription: "Required for commercial operation",
          checkInOut: "Check-in & Check-out Times",
          checkInTime: "Check-in Time",
          checkOutTime: "Check-out Time",
          houseRules: "House Rules",
          houseRulesDescription: "Set clear expectations for guests",
          cancellationPolicy: "Cancellation Policy",
          cancellationDescription: "Choose how flexible you want to be",
          policyTips: "Policy Tips",
          tip1: "Clear rules prevent misunderstandings",
          tip2: "Flexible policies attract more bookings",
          tip3: "Tourist license is mandatory in Spain",
          tip4: "Consider your local regulations",
          required: "Required",
          validationRequired: "Validation Required",
          touristLicenseRequired: "Tourist license is required to list your property",
          selectedPolicy: "Selected Policy",
          noSmoking: "No smoking",
          noPets: "No pets",
          noParties: "No parties or events",
          noShoes: "No shoes inside",
          quietHours: "Quiet hours 10 PM - 8 AM",
          noChildren: "Not suitable for children under 12",
          cleanBeforeCheckout: "Clean before checkout",
          respectNeighbors: "Be respectful of neighbors",
          popular: "Popular",
          flexible: "Flexible",
          flexibleDescription: "Full refund 1 day prior to arrival",
          moderate: "Moderate",
          moderateDescription: "Full refund 5 days prior to arrival",
          strict: "Strict",
          strictDescription: "Full refund 14 days prior to arrival",
          superStrict: "Super Strict",
          superStrictDescription: "Non-refundable",
          guestFriendly: "Guest-friendly",
          flexibleDetails: "Guests can cancel up to 24 hours before check-in and get a full refund (minus service fees).",
          moderateDetails: "Guests can cancel up to 5 days before check-in and get a full refund (minus service fees).",
          strictDetails: "Guests can cancel up to 14 days before check-in and get a full refund (minus service fees).",
          superStrictDetails: "No refunds for cancellations. Only exceptions for extenuating circumstances."
        },
        review: {
          title: "Review your listing",
          description: "Make sure everything looks good before publishing",
          completionStatus: "Completion Status",
          readyToPublish: "Ready to publish",
          missingRequired: "Missing required information",
          publishListing: "Publish Listing",
          saveAsDraft: "Save as Draft",
          editSection: "Edit Section",
          previewListing: "Preview Listing",
          nextSteps: "Next Steps",
          step1: "Your listing will be reviewed (usually takes 24 hours)",
          step2: "You'll receive a confirmation email",
          step3: "Your property will appear in search results",
          step4: "Start receiving booking requests",
          publishingTips: "Publishing Tips",
          tip1: "Double-check all information for accuracy",
          tip2: "High-quality photos increase booking rates",
          tip3: "Competitive pricing attracts more guests",
          tip4: "Respond quickly to inquiries for better rankings",
          validationChecklist: "Validatie checklist",
          checklist: {
            propertyTypeSelected: "Woningtype geselecteerd",
            locationDetailsProvided: "Locatiegegevens verstrekt",
            guestCapacityConfigured: "Gastencapaciteit geconfigureerd",
            amenitiesSelected: "Voorzieningen geselecteerd",
            photosUploaded: "Foto's geüpload (minimaal 5)",
            titleAndDescriptionComplete: "Titel en beschrijving compleet",
            pricingConfigured: "Prijzen geconfigureerd",
            policiesAndTouristLicense: "Beleid en toeristenvergunning"
          },
          required: "Vereist",
          propertySummaryLabels: {
            notSelected: "Niet geselecteerd",
            notProvided: "Niet verstrekt",
            notConfigured: "Niet geconfigureerd"
          }
        }
      },
      emailConfirmation: {
        title: "Check Your Email",
        description: "We've sent you a confirmation email",
        emailSent: "Email sent successfully",
        emailSentDescription: "We've sent a confirmation email to {email}",
        nextSteps: "Next steps",
        step1: "Open your email inbox",
        step2: "Click the confirmation link in the email",
        step3: "You'll be redirected back to VillaWise",
        resendEmail: "Resend Email",
        backToHome: "Back to Home",
        helpText: "Didn't receive the email? Check your spam folder or contact support."
      },
      oauthCallback: {
        loginSuccessful: "Login Successful",
        welcomeBack: "Welcome back, {name}!",
        user: "user",
        loginFailed: "Login Failed", 
        loginValidationError: "Unable to validate your login. Please try again.",
        loginError: "Login Error",
        networkError: "A network error occurred during login. Please try again.",
        authenticationError: "Authentication Error",
        authenticationFailed: "Authentication failed",
        authFailed: "Authentication failed. Please try again.",
        userNotFound: "User account could not be found.",
        profileCreationFailed: "Failed to create user profile.",
        callbackFailed: "Authentication callback failed.",
        noTokensReceived: "No authentication tokens received. Please try logging in again.",
        processingLogin: "Processing Login...",
        pleaseWait: "Please wait while we complete your authentication."
      },
      imageUpload: {
        uploadSuccess: "Image uploaded successfully",
        uploadSuccessDescription: "Your image has been added to the property gallery.",
        uploadFailed: "Upload failed",
        uploadFailedDescription: "Failed to upload image. Please try again.",
        imageDeleted: "Image deleted",
        imageDeletedDescription: "The image has been removed from the gallery.",
        deleteFailed: "Delete failed",
        deleteFailedDescription: "Failed to delete image. Please try again.",
        reorderFailed: "Reorder failed", 
        reorderFailedDescription: "Failed to reorder images. Please try again.",
        invalidFileType: "Invalid file type",
        invalidFileTypeDescription: "Please select an image file (JPG, PNG, etc.)",
        fileTooLarge: "File too large",
        fileTooLargeDescription: "Please select an image smaller than 5MB",
        maxImagesReached: "Maximum images reached",
        maxImagesReachedDescription: "You can only upload up to {maxImages} images"
      }
    },
    nl: {
      hostOnboarding: {
        title: "Nieuwe woning toevoegen",
        previous: "Vorige",
        next: "Volgende", 
        finish: "Afronden",
        publish: "Woning publiceren",
        step: "Stap",
        of: "van",
        steps: {
          propertyType: "Woningtype",
          location: "Locatie",
          capacity: "Gastencapaciteit",
          amenities: "Voorzieningen",
          photos: "Foto's",
          listing: "Titel & Beschrijving",
          pricing: "Prijzen",
          policies: "Beleid",
          review: "Beoordeling"
        },
        propertyType: {
          title: "Wat voor type woning verhuur je?",
          description: "Kies de optie die het beste bij jouw ruimte past",
          propertyTypeTitle: "Type woning",
          spaceTypeTitle: "Wat krijgen gasten toegang tot?",
          illustrationText: "Help gasten begrijpen wat voor ruimte je aanbiedt",
          types: {
            house: {
              name: "Huis",
              description: "Een vrijstaand huis met eigen ingang"
            },
            apartment: {
              name: "Appartement",
              description: "Een unit in een gebouw met gedeelde ingang"
            },
            villa: {
              name: "Villa",
              description: "Luxe woning met hoogwaardige voorzieningen"
            },
            townhouse: {
              name: "Rijtjeshuis",
              description: "Meerlaags huis verbonden met andere woningen"
            },
            cottage: {
              name: "Huisje",
              description: "Gezellige landelijke of plattelandswoning"
            },
            penthouse: {
              name: "Penthouse",
              description: "Bovenste verdieping appartement met uitzicht"
            },
            beachhouse: {
              name: "Strandhuis",
              description: "Woning gelegen nabij of aan het strand"
            },
            mountain_cabin: {
              name: "Berghut",
              description: "Rustieke woning in bergachtig gebied"
            }
          },
          spaceTypes: {
            entire_place: {
              name: "Gehele woning",
              description: "Gasten hebben de hele woning voor zichzelf"
            },
            private_room: {
              name: "Privé kamer",
              description: "Gasten hebben hun eigen privé kamer in een gedeelde ruimte"
            },
            shared_room: {
              name: "Gedeelde kamer",
              description: "Gasten delen een kamer met anderen"
            }
          },
          labels: {
            popular: "Populair",
            recommended: "Aanbevolen"
          }
        },
        location: {
          title: "Waar is je woning gelegen?",
          description: "We delen je exacte adres alleen met bevestigde gasten",
          searchPlaceholder: "Zoek naar een stad",
          popularLocations: "Populaire Costa Blanca locaties",
          addressDetails: "Adresgegevens",
          streetAddress: "Straatnaam en nummer",
          addressPlaceholder: "Vul je volledige adres in",
          city: "Stad",
          cityPlaceholder: "Vul stadsnaam in",
          postalCode: "Postcode",
          province: "Provincie",
          confirmAddress: "Bevestig adres",
          privacyNoticeTitle: "Privacyverklaring",
          privacyNoticeText: "Je exacte adres wordt alleen gedeeld met gasten na boekingsbevestiging.",
          illustrationText: "Je locatie helpt gasten je woning gemakkelijk te vinden"
        },
        capacity: {
          title: "Hoeveel gasten kan je ruimte herbergen?",
          description: "Vertel ons over de slaapgelegenheid en faciliteiten",
          maxGuests: "Maximum gasten",
          maxGuestsDescription: "Aantal gasten dat je kunt huisvesten",
          bedrooms: "Slaapkamers",
          bedroomsDescription: "Beschikbare slaapkamers voor gasten",
          bathrooms: "Badkamers",
          bathroomsDescription: "Halve badkamers tellen als 0,5",
          popularConfigurations: "Populaire configuraties",
          configurations: {
            couples_retreat: "Koppel retreat",
            small_family: "Klein gezin",
            large_family: "Groot gezin",
            group_getaway: "Groepsuitje",
            large_villa: "Grote villa",
            luxury_estate: "Luxe landgoed"
          },
          configurationDetails: {
            couples_retreat: "2 gasten • 1 bed • 1 badkamer",
            small_family: "4 gasten • 2 bed • 1 badkamer",
            large_family: "6 gasten • 3 bed • 2 badkamer",
            group_getaway: "8 gasten • 4 bed • 2 badkamer",
            large_villa: "10 gasten • 5 bed • 3 badkamer",
            luxury_estate: "12 gasten • 6 bed • 4 badkamer"
          },
          tipsTitle: "Capaciteit tips",
          tip1: "Wees eerlijk over de capaciteit van je ruimte",
          tip2: "Denk aan maximaal comfort, niet alleen beschikbare bedden",
          tip3: "Halve badkamers tellen als 0,5 badkamers",
          tip4: "Denk aan gemeenschappelijke ruimtes bij het bepalen van gastenaantallen",
          illustrationText: "Juiste capaciteitsinformatie helpt gasten je ruimte te kiezen"
        },
        amenities: {
          title: "Welke voorzieningen bied je aan?",
          description: "Selecteer alle voorzieningen en faciliteiten beschikbaar voor je gasten",
          selectedCount: "voorzieningen geselecteerd",
          popular: "Populair",
          categories: {
            essentials: "Essentiële voorzieningen",
            features: "Bijzondere kenmerken",
            location: "Locatie kenmerken",
            entertainment: "Entertainment",
            family: "Familie",
            accessibility: "Toegankelijkheid",
            safety: "Veiligheidsvoorzieningen"
          },
          items: {
            wifi: "WiFi",
            kitchen: "Keuken",
            parking: "Gratis parkeren",
            tv: "TV",
            air_conditioning: "Airconditioning",
            heating: "Verwarming",
            washer: "Wasmachine",
            dryer: "Droger",
            pool: "Zwembad",
            hot_tub: "Hottub",
            gym: "Fitnessruimte",
            bbq_grill: "Barbecue",
            garden: "Tuin of achtertuin",
            balcony: "Balkon",
            terrace: "Terras",
            fireplace: "Openhaard",
            beach_access: "Strand toegang",
            mountain_view: "Bergzicht",
            sea_view: "Zeezicht",
            lake_access: "Meer toegang",
            ski_in_out: "Ski-in/Ski-out",
            city_view: "Stadsgezicht",
            game_console: "Spelconsole",
            ping_pong: "Pingpong tafel",
            pool_table: "Pooltafel",
            sound_system: "Geluidssysteem",
            piano: "Piano",
            exercise_equipment: "Sportapparatuur",
            suitable_for_children: "Geschikt voor kinderen (2-12 jaar)",
            suitable_for_infants: "Geschikt voor baby's (onder 2 jaar)",
            high_chair: "Kinderstoel",
            crib: "Kinderbedje",
            children_toys: "Kinderspeelgoed",
            children_books: "Kinderboeken en speelgoed",
            step_free_access: "Drempelvrije toegang",
            wide_doorway: "Brede deuren",
            accessible_bathroom: "Toegankelijke badkamer",
            wide_hallway: "Brede gang",
            wide_entryway: "Brede entree",
            smoke_detector: "Rookmelder",
            fire_extinguisher: "Brandblusser",
            first_aid_kit: "EHBO-kit",
            security_cameras: "Beveiligingscamera's (buitenkant)",
            carbon_monoxide_detector: "Koolmonoxidemelder"
          },
          tipsTitle: "Voorziening tips",
          tip1: "Benadruk unieke kenmerken zoals zwembad of zeezicht",
          tip2: "Essentiële voorzieningen zoals WiFi en parkeren zijn zeer belangrijk",
          tip3: "Keukenfaciliteiten trekken langere verblijven aan",
          tip4: "Airconditioning wordt zeer gewaardeerd in de zomer",
          illustrationText: "Geweldige voorzieningen maken je woning bijzonder"
        },
        photos: {
          title: "Voeg foto's van je ruimte toe",
          description: "Hoogwaardige foto's helpen je advertentie op te vallen",
          photoCategories: "Foto categorieën",
          uploadPhotos: "Upload foto's",
          uploadTitle: "Sleep foto's hierheen of klik om te bladeren",
          uploadDescription: "Upload minimaal 5 foto's om je ruimte te tonen",
          chooseFiles: "Kies bestanden",
          photoPreview: "Foto voorvertoning",
          photographyTips: "Fotografie tips",
          required: "Verplicht",
          more: "meer",
          minimumPhotos: "minimaal foto's",
          readyToContinue: "Klaar om door te gaan",
          exterior: "Buiten",
          exteriorDescription: "Buitenaanzichten van je woning",
          livingSpaces: "Woonruimtes",
          livingSpacesDescription: "Gemeenschappelijke ruimtes waar gasten ontspannen",
          bedrooms: "Slaapkamers",
          bedroomsDescription: "Alle slaapgebieden",
          bathrooms: "Badkamers",
          bathroomsDescription: "Alle badkamers en toiletten",
          amenities: "Voorzieningen",
          amenitiesDescription: "Bijzondere voorzieningen en faciliteiten",
          views: "Uitzichten",
          viewsDescription: "Mooie uitzichten vanaf je woning",
          frontOfHouse: "Voorkant huis",
          gardenPatio: "Tuin/patio",
          poolArea: "Zwembad gebied",
          balconyTerrace: "Balkon/terras",
          livingRoom: "Woonkamer",
          diningRoom: "Eetkamer",
          kitchen: "Keuken",
          loungeArea: "Lounge gebied",
          masterBedroom: "Hoofdslaapkamer",
          guestBedrooms: "Gastslaapkamers",
          bedDetails: "Bed details",
          bedroomViews: "Slaapkamer uitzichten",
          mainBathroom: "Hoofdbadkamer",
          ensuiteBathrooms: "Ensuite badkamers",
          showerBath: "Douche/bad",
          toiletAreas: "Toilet gebieden",
          pool: "Zwembad",
          gym: "Fitnessruimte",
          bbqArea: "Barbecue gebied",
          workspace: "Werkruimte",
          entertainmentRoom: "Entertainmentruimte",
          seaView: "Zeezicht",
          mountainView: "Bergzicht",
          cityView: "Stadszicht",
          gardenView: "Tuinzicht",
          tip1: "Gebruik natuurlijk licht indien mogelijk",
          tip2: "Maak foto's overdag",
          tip3: "Toon alle ruimtes toegankelijk voor gasten",
          tip4: "Houd ruimtes schoon en opgeruimd",
          tip5: "Voeg buitenkant en voorzieningen foto's toe",
          illustrationText: "Geweldige foto's zijn de sleutel tot het aantrekken van gasten"
        },
        listing: {
          title: "Maak je advertentie",
          description: "Help gasten begrijpen wat je ruimte speciaal maakt",
          propertyTitle: "Woningtitel",
          titlePlaceholder: "bijv. Prachtige villa met zwembad aan Costa Blanca",
          titleSuggestions: "Titel suggesties",
          generateTitle: "Genereer titel",
          propertyDescription: "Woningomschrijving", 
          descriptionPlaceholder: "Beschrijf je ruimte, voorzieningen en wat het bijzonder maakt...",
          descriptionTemplates: "Beschrijving sjablonen",
          preview: "Voorvertoning",
          photoPlaceholder: "Foto tijdelijke aanduiding",
          writingTips: "Schrijf tips",
          tip1: "Benadruk unieke kenmerken en voorzieningen",
          tip2: "Noem nabijgelegen attracties en stranden",
          tip3: "Wees specifiek over wat inbegrepen is",
          tip4: "Gebruik beschrijvende taal om een beeld te schetsen",
          tip5: "Houd het beknopt maar informatief",
          completionRequired: "Vul het volgende in:",
          completionRequiredTitle: "Titel moet minimaal 10 tekens zijn",
          completionRequiredDescription: "Omschrijving moet minimaal 100 tekens zijn",
          villaWithPool: "Villa met zwembad",
          villaWithPoolDescription: "Welkom bij onze prachtige [property_type] in [location]! Deze verbluffende woning biedt [bedrooms] slaapkamers, [bathrooms] badkamers en kan [guests] gasten huisvesten. Geniet van het prachtige privézwembad, het ruime terras en het adembenemende [view_type] uitzicht. Gelegen op slechts enkele minuten van [local_attraction], is dit de perfecte plek voor een onvergetelijke vakantie.",
          beachfrontApartment: "Strand appartement",
          beachfrontApartmentDescription: "Ervaar de magie van het kustleven in onze [property_type] direct aan het strand in [location]! Dit moderne appartement met [bedrooms] slaapkamers en [bathrooms] badkamers biedt plaats aan [guests] gasten. Stap vanuit je voordeur direct het zand op en geniet van ongeëvenaarde toegang tot het strand. Perfect voor degenen die wakker willen worden met het geluid van de golven.",
          mountainRetreat: "Bergtoevlucht",
          mountainRetreatDescription: "Ontsnap naar onze vredige [property_type] genesteld in de bergen van [location]! Deze rustieke woning met [bedrooms] slaapkamers en [bathrooms] badkamers biedt een serene toevlucht voor [guests] gasten. Geniet van spectaculaire bergzichten, wandelpaden direct voor de deur en de perfecte omgeving om te ontspannen en weer contact te maken met de natuur.",
          charactersCount: "tekens",
          wordsCount: "woorden"
        },
        pricing: {
          title: "Stel je prijs in",
          description: "Je kunt je prijzen altijd aanpassen na publicatie",
          aiSuggestion: "AI prijs suggestie",
          useThisPrice: "Gebruik deze prijs",
          pricingStrategies: "Prijs strategieën",
          customPricing: "Aangepaste prijzen",
          pricePerNight: "Prijs per nacht",
          priceRange: "Prijsrange",
          earningsProjection: "Inkomsten projectie",
          marketComparison: "Markt vergelijking",
          pricingTips: "Prijs tips",
          tip1: "Begin competitief en pas aan op basis van vraag",
          tip2: "Overweeg seizoensgebonden prijsvariaties",
          tip3: "Bereken schoonmaak- en servicekosten mee",
          tip4: "Onderzoek vergelijkbare woningen in je omgeving",
          recommended: "Aanbevolen",
          moreBookings: "Meer boekingen",
          marketRate: "Marktprijs",
          higherIncome: "Hogere inkomsten",
          competitive: "Competitief",
          competitiveDescription: "Prijs onder marktgemiddelde om meer boekingen aan te trekken",
          marketRateDescription: "Prijs op marktgemiddelde voor uitgewogen boekingen en inkomsten",
          premium: "Premium",
          premiumDescription: "Prijs boven marktgemiddelde voor hogere inkomsten per boeking",
          basedOnFeatures: "Gebaseerd op je woning kenmerken, locatie en marktgegevens",
          perNight: "/nacht",
          lowDemand: "Lage vraag",
          averageDemand: "Gemiddelde vraag",
          highDemand: "Hoge vraag",
          conservativeProjection: "Conservatief",
          lowRange: "Laag",
          marketAverage: "Marktgemiddelde",
          highRange: "Hoog",
          premiumRange: "Premium",
          occupancyRate: "Bezettingsgraad",
          monthlyEarnings: "Maandelijkse inkomsten",
          perMonth: "/maand",
          perYear: "/jaar",
          lowSeason: "Laag seizoen",
          mediumSeason: "Midden seizoen",
          highSeason: "Hoog seizoen"
        },
        policies: {
          title: "Stel je beleid in",
          description: "Maak duidelijke regels en beleid voor je gasten",
          legalRequirements: "Spaanse wettelijke vereisten",
          touristLicense: "Toeristenvergunning nummer",
          touristLicenseDescription: "Verplicht voor alle vakantieverhuur in Spanje",
          businessRegistration: "Bedrijfsregistratie",
          businessRegistrationDescription: "Verplicht bij bedrijfsmatige exploitatie",
          checkInOut: "Check-in & check-out tijden",
          checkInTime: "Check-in tijd",
          checkOutTime: "Check-out tijd",
          houseRules: "Huisregels",
          houseRulesDescription: "Stel duidelijke verwachtingen voor gasten",
          cancellationPolicy: "Annuleringsbeleid",
          cancellationDescription: "Kies hoe flexibel je wilt zijn",
          policyTips: "Beleid tips",
          tip1: "Duidelijke regels voorkomen misverstanden",
          tip2: "Flexibel beleid trekt meer boekingen aan",
          tip3: "Toeristenvergunning is verplicht in Spanje",
          tip4: "Denk aan je lokale regelgeving",
          required: "Verplicht",
          validationRequired: "Validatie vereist",
          touristLicenseRequired: "Toeristenvergunning is verplicht om je woning te verhuren",
          selectedPolicy: "Geselecteerd beleid",
          noSmoking: "Niet roken",
          noPets: "Geen huisdieren",
          noParties: "Geen feesten of evenementen",
          noShoes: "Geen schoenen binnen",
          quietHours: "Rustige uren 22:00 - 08:00",
          noChildren: "Niet geschikt voor kinderen onder 12",
          cleanBeforeCheckout: "Schoonmaken voor uitchecken",
          respectNeighbors: "Respectvol zijn voor buren",
          popular: "Populair",
          flexible: "Flexibel",
          flexibleDescription: "Volledige terugbetaling 1 dag voor aankomst",
          moderate: "Gematigd",
          moderateDescription: "Volledige terugbetaling 5 dagen voor aankomst",
          strict: "Strikt",
          strictDescription: "Volledige terugbetaling 14 dagen voor aankomst",
          superStrict: "Super strikt",
          superStrictDescription: "Niet terugbetaalbaar",
          guestFriendly: "Gastvriendelijk",
          flexibleDetails: "Gasten kunnen tot 24 uur voor inchecken annuleren en krijgen volledige terugbetaling (minus servicekosten).",
          moderateDetails: "Gasten kunnen tot 5 dagen voor inchecken annuleren en krijgen volledige terugbetaling (minus servicekosten).",
          strictDetails: "Gasten kunnen tot 14 dagen voor inchecken annuleren en krijgen volledige terugbetaling (minus servicekosten).",
          superStrictDetails: "Geen terugbetaling voor annuleringen. Alleen uitzonderingen voor buitengewone omstandigheden."
        },
        review: {
          title: "Controleer je advertentie",
          description: "Zorg ervoor dat alles er goed uitziet voor publicatie",
          completionStatus: "Voltooiingsstatus",
          validationChecklist: "Validatie checklist",
          propertySummary: "Woning samenvatting",
          readyToPublish: "Klaar voor publicatie",
          missingRequired: "Ontbrekende verplichte informatie",
          missingRequirements: "Ontbrekende vereisten",
          publishListing: "Advertentie publiceren",
          saveAsDraft: "Opslaan als concept",
          editSection: "Sectie bewerken",
          previewListing: "Advertentie voorvertoning",
          checklist: {
            propertyTypeSelected: "Woningtype geselecteerd",
            locationDetailsProvided: "Locatiegegevens verstrekt",
            guestCapacityConfigured: "Gastencapaciteit geconfigureerd",
            amenitiesSelected: "Voorzieningen geselecteerd",
            photosUploaded: "Foto's geüpload (minimaal 5)",
            titleAndDescriptionComplete: "Titel en beschrijving compleet",
            pricingConfigured: "Prijzen geconfigureerd",
            policiesAndTouristLicense: "Beleid en toeristenvergunning"
          },
          propertySummaryLabels: {
            propertyType: "Woningtype",
            location: "Locatie",
            capacity: "Capaciteit",
            amenities: "Voorzieningen",
            photos: "Foto's",
            pricing: "Prijzen",
            listingTitle: "Titel",
            listingDescription: "Beschrijving",
            notSelected: "Niet geselecteerd",
            notProvided: "Niet verstrekt",
            notConfigured: "Niet geconfigureerd",
            photosUploaded: "foto's geüpload",
            noTitle: "Geen titel",
            noDescription: "Geen beschrijving"
          },
          nextSteps: "Volgende stappen",
          step1: "Je advertentie wordt beoordeeld (duurt meestal 24 uur)",
          step2: "Je ontvangt een bevestigingsmail",
          step3: "Je woning verschijnt in zoekresultaten",
          step4: "Begin met het ontvangen van boekingsverzoeken",
          publishingTips: "Publicatie tips",
          tip1: "Controleer alle informatie op juistheid",
          tip2: "Hoogwaardige foto's verhogen boekingspercentages",
          tip3: "Concurrerende prijzen trekken meer gasten aan",
          tip4: "Reageer snel op verzoeken voor betere rankings"
        }
      },
      indexPage: {
        hero: {
          title: "Vind jouw perfecte vakantievilla",
          subtitle: "Ontdek duizenden unieke accommodaties over de hele wereld"
        },
        location: {
          placeholder: "Waar ga je naartoe?"
        },
        searchButton: "Zoeken",
        popularInSpain: "Populair in Spanje",
        newInFrance: "Nieuw in Frankrijk", 
        guestFavorites: "Gastenfavorieten"
      },
      navigation: {
        stays: "Verblijven",
        experiences: "Ervaringen",
        onlineExperiences: "Online ervaringen",
        host: "Verhuurder",
        guest: "Gast",
        villaWiseYourHome: "VillaWise je huis",
        becomeHost: "Word verhuurder"
      },
      common: {
        search: "Zoeken",
        filters: "Filters",
        map: "Kaart",
        next: "Volgende",
        previous: "Vorige",
        clear: "Wissen",
        apply: "Toepassen",
        cancel: "Annuleren",
        close: "Sluiten",
        guest: "Gast"
      },
      headerTabs: {
        guest: "Gast",
        host: "Verhuurder",
        owner: "Verhuurder"
      },
      brand: {
        name: "VillaWise"
      },
      userMenu: {
        profile: "Profiel",
        account: "Account",
        settings: "Instellingen",
        help: "Help",
        logout: "Uitloggen",
        login: "Inloggen",
        signup: "Registreren",
        favorites: "Favorieten",
        trips: "Reizen",
        messages: "Berichten",
        accountSettings: "Accountinstellingen",
        languageCurrency: "Taal & Valuta",
        helpCenter: "Helpcentrum",
        becomeHost: "Word verhuurder",
        hostInfo: "Organiseer een ervaring",
        referHost: "Verwijs een verhuurder",
        loading: "Bezig met laden...",
        menu: "Menu",
        welcome: "Welkom bij VillaWise",
        signInToAccess: "Log in om toegang te krijgen tot je account"
      },
      auth: {
        login: {
          title: "Welkom terug bij VillaWise",
          subtitle: "Log in om je villa zoektocht voort te zetten",
          emailLabel: "E-mailadres",
          emailPlaceholder: "Voer je e-mailadres in",
          passwordLabel: "Wachtwoord",
          passwordPlaceholder: "Voer je wachtwoord in",
          rememberMe: "Onthoud mij",
          forgotPassword: "Wachtwoord vergeten?",
          signInButton: "Inloggen",
          signingIn: "Inloggen...",
          orContinueWith: "Of ga verder met e-mail",
          loginFailed: "Inloggen mislukt",
          loginFailedDescription: "Controleer je inloggegevens en probeer opnieuw",
          dontHaveAccount: "Heb je nog geen account?",
          signUp: "Registreren",
          emailVerificationSuccess: "Welkom bij VillaWise! 🎉 We hebben een bevestigingsmail gestuurd naar {email}. Controleer je inbox en klik op de activatielink om je registratie af te ronden."
        },
        register: {
          title: "Word vandaag lid van VillaWise",
          subtitle: "Maak je account aan en krijg toegang tot duizenden geverifieerde vakantiewoningen",
          socialLogin: "Social Login",
          emailRegistration: "E-mail Registratie",
          firstNameLabel: "Voornaam",
          firstNamePlaceholder: "Voornaam",
          lastNameLabel: "Achternaam",
          lastNamePlaceholder: "Achternaam",
          usernameLabel: "Gebruikersnaam",
          usernamePlaceholder: "Kies een gebruikersnaam",
          emailLabel: "E-mailadres",
          emailPlaceholder: "Voer je e-mailadres in",
          passwordLabel: "Wachtwoord",
          passwordPlaceholder: "Maak een wachtwoord",
          confirmPasswordLabel: "Bevestig wachtwoord",
          confirmPasswordPlaceholder: "Bevestig je wachtwoord",
          agreeToTerms: "Ik ga akkoord met de Algemene Voorwaarden en Privacyverklaring",
          createAccountButton: "Account aanmaken",
          creatingAccount: "Account aanmaken...",
          orContinueWith: "Of ga verder met e-mail",
          passwordMismatch: "Wachtwoorden komen niet overeen",
          passwordMismatchDescription: "Zorg ervoor dat je wachtwoorden overeenkomen",
          passwordTooShort: "Wachtwoord te kort",
          passwordTooShortDescription: "Wachtwoord moet minimaal 8 tekens lang zijn",
          termsRequired: "Akkoord met voorwaarden vereist",
          termsRequiredDescription: "Ga akkoord met de Algemene Voorwaarden en Privacyverklaring",
          accountCreated: "Account aangemaakt!",
          accountCreatedDescription: "Je account is succesvol aangemaakt. Je kunt nu accommodaties gaan verkennen.",
          registrationFailed: "Registratie mislukt",
          registrationFailedDescription: "Probeer opnieuw",
          alreadyHaveAccount: "Heb je al een account?",
          signIn: "Inloggen"
        },
        forgotPassword: {
          title: "Wachtwoord vergeten",
          subtitle: "Voer je e-mailadres in en we sturen je een herstellink",
          emailLabel: "E-mailadres",
          emailPlaceholder: "Voer je e-mailadres in",
          sendResetButton: "Herstel e-mail verzenden",
          sendingReset: "Herstel e-mail verzenden...",
          checkEmailTitle: "Controleer je e-mail",
          checkEmailSubtitle: "We hebben wachtwoord herstel instructies naar je e-mail gestuurd",
          emailSentTitle: "E-mail verzonden!",
          emailSentMessage: "We hebben een wachtwoord herstellink gestuurd naar",
          emailSentHelp: "E-mail niet ontvangen? Controleer je spam map of probeer opnieuw.",
          tryDifferentEmail: "Probeer ander e-mailadres",
          backToSignIn: "Terug naar inloggen",
          resetEmailSent: "Herstel e-mail verzonden",
          resetEmailSentDescription: "Controleer je e-mail voor wachtwoord herstel instructies",
          resetEmailError: "Fout",
          resetEmailErrorDescription: "Kan herstel e-mail niet verzenden",
          rememberPassword: "Wachtwoord weer weten?",
          signIn: "Inloggen",
          checkEmailDescription: "We hebben wachtwoord herstel instructies naar je e-mail gestuurd",
          emailSentDescription: "We hebben een wachtwoord herstellink gestuurd naar",
          emailNotReceived: "E-mail niet ontvangen? Controleer je spam map of probeer opnieuw."
        },
        resetPassword: {
          title: "Wachtwoord opnieuw instellen",
          subtitle: "Voer je nieuwe wachtwoord in",
          newPasswordLabel: "Nieuw wachtwoord",
          newPasswordPlaceholder: "Voer je nieuwe wachtwoord in",
          confirmPasswordLabel: "Bevestig nieuw wachtwoord",
          confirmPasswordPlaceholder: "Bevestig je nieuwe wachtwoord",
          updatePasswordButton: "Wachtwoord bijwerken",
          updatingPassword: "Wachtwoord bijwerken...",
          successTitle: "Wachtwoord succesvol bijgewerkt",
          successSubtitle: "Je wachtwoord is gewijzigd",
          successMessage: "Je wachtwoord is succesvol bijgewerkt. Je kunt nu inloggen met je nieuwe wachtwoord.",
          continueToSignIn: "Ga verder naar inloggen",
          invalidLinkTitle: "Ongeldige herstellink",
          invalidLinkSubtitle: "Deze wachtwoord herstellink is ongeldig of verlopen",
          requestNewLink: "Vraag nieuwe herstellink aan",
          rememberPassword: "Wachtwoord weer weten?",
          passwordMismatch: "Wachtwoorden komen niet overeen",
          passwordMismatchDescription: "Zorg ervoor dat je wachtwoorden overeenkomen",
          passwordTooShort: "Wachtwoord te kort",
          passwordTooShortDescription: "Wachtwoord moet minimaal 8 tekens lang zijn",
          invalidToken: "Ongeldig token",
          invalidTokenDescription: "Herstel token ontbreekt of is ongeldig",
          passwordUpdated: "Wachtwoord bijgewerkt",
          passwordUpdatedDescription: "Je wachtwoord is succesvol bijgewerkt",
          resetFailed: "Herstel mislukt",
          resetFailedDescription: "Kan wachtwoord niet herstellen"
        },
        social: {
          continueWithGoogle: "Ga verder met Google",
          continueWithFacebook: "Ga verder met Facebook",
          continueWithApple: "Ga verder met Apple"
        },
        passwordStrength: {
          weak: "Zwak",
          fair: "Redelijk",
          good: "Goed",
          strong: "Sterk",
          helpText: "Gebruik 8+ karakters met letters, cijfers en symbolen"
        }
      },
      validation: {
        required: "Dit veld is verplicht",
        email: {
          invalid: "Voer een geldig e-mailadres in",
          exists: "Een gebruiker met dit e-mailadres bestaat al"
        },
        username: {
          invalid: "Gebruikersnaam moet minimaal 3 tekens lang zijn",
          exists: "Deze gebruikersnaam is al bezet"
        },
        password: {
          minLength: "Wachtwoord moet minimaal {{min}} tekens lang zijn",
          weak: "Wachtwoord is te zwak",
          mismatch: "Wachtwoorden komen niet overeen"
        },
        auth: {
          emailExists: "Een gebruiker met dit e-mailadres bestaat al",
          usernameExists: "Deze gebruikersnaam is al bezet",
          invalidCredentials: "Ongeldig e-mailadres of wachtwoord",
          accountDisabled: "Account is uitgeschakeld",
          authServiceUnavailable: "Authenticatiedienst niet beschikbaar",
          requiredFields: "E-mail, wachtwoord en gebruikersnaam zijn verplicht",
          emailNotConfirmed: "Controleer je e-mail en bevestig je account voordat je inlogt."
        }
      },
      locationAutocomplete: {
        placeholder: "Waar",
        searchResults: "Zoekresultaten",
        recentSearches: "Recente zoekopdrachten",
        clearHistory: "Geschiedenis wissen",
        popularDestinations: "Populaire bestemmingen",
        beachAndSea: "Strand & Zee",
        cityTrips: "Stedentrips",
        noResults: "Geen locaties gevonden"
      },
      dateRangePicker: {
        placeholder: "Datums toevoegen",
        checkIn: "Inchecken",
        checkOut: "Uitchecken",
        flexible: "Ik ben flexibel",
        exactDates: "Exacte datums",
        flexibilityOptions: "Flexibiliteit",
        plusMinus1: "± 1 dag",
        plusMinus2: "± 2 dagen",
        plusMinus3: "± 3 dagen", 
        plusMinus7: "± 1 week",
        nights: "nachten",
        night: "nacht",
        day: "dag",
        days: "dagen",
        clear: "Wissen",
        done: "Klaar"
      },
      guestSelector: {
        placeholder: "Gasten toevoegen",
        adults: {
          title: "Volwassenen",
          description: "13 jaar en ouder"
        },
        children: {
          title: "Kinderen",
          description: "2-12 jaar"
        },
        infants: {
          title: "Baby's",
          description: "Jonger dan 2"
        },
        pets: {
          title: "Huisdieren",
          description: "Neem je een hulpdier mee?"
        },
        guest: "gast",
        guests: "gasten",
        done: "Klaar"
      },
      searchPage: {
        title: "Zoekresultaten",
        noResults: "Geen accommodaties gevonden",
        tryAdjusting: "Probeer je zoekopdracht of filters aan te passen om te vinden wat je zoekt.",
        showMap: "Kaart tonen",
        hideMap: "Kaart verbergen",
        filters: "Filters",
        sortBy: "Sorteren op",
        breadcrumb: {
          home: "Home",
          spain: "Spanje"
        },
        discoverAccommodations: "Ontdek accommodaties",
        showPricesPerNight: "Toon prijzen per nacht",
        sortOptions: {
          recommended: "Aanbevolen",
          priceLow: "Prijs: Laag naar Hoog",
          priceHigh: "Prijs: Hoog naar Laag",
          rating: "Beoordeling", 
          newest: "Nieuwste"
        },
        guest: "gast",
        guests: "gasten"
      },
      propertyCard: {
        night: "nacht",
        reviews: "beoordelingen",
        superhost: "Superhost",
        guestFavorite: "Gastenfavoriet",
        guests: "gasten",
        bedrooms: "slaapkamers",
        bathrooms: "badkamers",
        perWeek: "per week",
        priceFrom: "Weekprijs v.a.",
        badges: {
          luxe: "Luxe",
          "favoriet-van-gasten": "Gastenfavoriet",
          nieuw: "Nieuw",
          "favoriet van gasten": "Gastenfavoriet",
          new: "Nieuw",
          superhost: "Superhost",
          popular: "Populair",
          rare: "Zeldzaam",
          "eco-friendly": "Milieuvriendelijk",
          authentic: "Authentiek",
          central: "Centraal",
          scenic: "Schilderachtig",
          "beach-front": "Aan het strand",
          traditional: "Traditioneel",
          rural: "Landelijk",
          modern: "Modern",
          premium: "Premium",
          port: "Haven",
          family: "Familie",
          "sea-view": "Zeezicht",
          "beach-villa": "Strandvilla",
          luxury: "Luxe",
          "mountain-view": "Bergzicht",
          romantic: "Romantisch",
          rustic: "Rustiek",
          panoramic: "Panoramisch",
          cozy: "Gezellig",
          elite: "Elite",
          marina: "Jachthaven",
          village: "Dorp",
          "beach-access": "Strandtoegang",
          garden: "Tuin",
          sunset: "Zonsondergang",
          terrace: "Terras",
          "bay-view": "Uitzicht op baai",
          "beach-nearby": "Strand dichtbij",
          "iconic-view": "Iconisch uitzicht",
          verified: "Geverifieerd",
          historic: "Historisch",
          harbor: "Haven",
          "cliff-top": "Op de klif",
          natural: "Natuurlijk"
        }
      },
      searchBar: {
        where: "Waar",
        addDates: "Datums toevoegen",
        addGuests: "Gasten toevoegen",
        search: "Zoeken",
        searchDestination: "Zoek bestemming",
        when: "Wanneer",
        who: "Wie",
        anyWeek: "Elke week",
        guest: "gast",
        guests: "gasten",
        datesSelected: "Datums geselecteerd"
      },
      exploration: {
        title: "Ontdekken",
        description: "Ontdek geweldige plekken",
        scrollLeft: "Scroll naar links",
        scrollRight: "Scroll naar rechts"
      },
      inspiration: {
        title: "Laat je inspireren",
        description: "Ideeën voor je volgende reis"
      },
      ownerCta: {
        title: "Word verhuurder",
        subtitle: "Verdien extra inkomen en ontdek nieuwe mogelijkheden",
        buttonText: "Word verhuurder"
      },
      propertyDetails: {
        guests: "gasten",
        bedrooms: "slaapkamers",
        bathrooms: "badkamers",
        wifi: "WiFi",
        perNight: "per nacht",
        selectDates: "Selecteer datums",
        addGuests: "Gasten toevoegen",
        reserve: "Reserveren",
        noChargeYet: "Je wordt nog niet in rekening gebracht",
        nights: "nachten",
        serviceFee: "Servicekosten",
        total: "Totaal",
        trustIndicators: {
          freeBookingChanges: "Gratis boekingswijzigingen",
          directCommunication: "Directe communicatie met verhuurder",
          trustedReviews: "Vertrouwd door 10.000+ beoordelingen",
          securePayment: "Veilige en beveiligde betaling"
        },
        hostedBy: "Gehost door",
        superhost: "Superhost",
        memberSince: "Lid sinds",
        responseTime: "Reactietijd",
        responseRate: "Reactiepercentage",
        contactHost: "Contact verhuurder",
        viewProfile: "Bekijk profiel",
        hostVerifications: "Verhuurder verificaties",
        identityVerified: "Identiteit geverifieerd",
        aboutProperty: "Over deze accommodatie",
        amenitiesTitle: "Voorzieningen",
        amenities: {
          wifi: "WiFi",
          parking: "Gratis parkeren",
          pool: "Zwembad",
          kitchen: "Keuken",
          airConditioning: "Airconditioning",
          heating: "Verwarming",
          tv: "TV",
          washer: "Wasmachine",
          balcony: "Balkon",
          garden: "Tuin"
        },
        houseRules: "Huisregels",
        checkIn: "Inchecken",
        checkOut: "Uitchecken",
        minimumStay: "Minimum verblijf",
        reviews: "beoordelingen",
        showAllReviews: "Toon alle {{count}} beoordelingen",
        propertyNotFound: "Accommodatie niet gevonden",
        propertyNotFoundDescription: "De accommodatie die je zoekt bestaat niet of is verwijderd.",
        goBack: "Ga terug",
        back: "Terug"
      },
      footer: {
        company: "Bedrijf",
        community: "Gemeenschap",
        host: "Verhuurder",
        support: "Ondersteuning",
        languages: {
          list: {
            nl: "Nederlands",
            en: "English",
            de: "Deutsch",
            fr: "Français"
          }
        },
        sections: {
          support: {
            title: "Ondersteuning",
            items: {
              helpCenter: "Helpcentrum",
              airCover: "VillaCover",
              antiDiscrimination: "Anti-discriminatie",
              disabilitySupport: "Ondersteuning bij beperking",
              cancellationOptions: "Annuleringsopties"
            }
          },
          guests: {
            title: "Gasten",
            items: {
              villaWiseYourHome: "VillaWise je huis",
              airCoverHosts: "VillaCover voor verhuurders",
              hostingInfo: "Informatie over verhuren",
              responsibleHosting: "Verantwoord verhuren"
            }
          },
          villaWise: {
            title: "VillaWise",
            items: {
              newsroom: "Nieuwsruimte",
              newFeatures: "Nieuwe functies",
              careers: "Carrières",
              investors: "Investeerders",
              giftCards: "Cadeaubonnen"
            }
          }
        }
      },
      // Verbeterde namespace structuur voor dashboards (voorkomt conflicten)
      guestDashboard: {
        title: "Dashboard",
        greeting: "Goedenavond",
        description: "Je boekingen, bewaarde huizen, berichten en reviews overzichtelijk op één plek.",
        navigation: {
          dashboard: "Dashboard",
          bookings: "Boekingen",
          wishlists: "Verlanglijsten",
          reviews: "Reviews",
          messages: "Berichten",
          settings: "Instellingen",
          help: "Hulp",
          carRental: "Een auto huren"
        },
        becomeHost: {
          title: "Verdien geld door je ruimte te verhuren",
          description: "Sluit je aan bij duizenden hosts die extra inkomsten genereren door hun eigendommen te delen op VillaWise",
          button: "Word een host",
          upgrading: "Upgraden..."
        },
        recentBookings: {
          title: "Laatste boekingen",
          viewAll: "Bekijk alle boekingen",
          noBookings: "Nog geen boekingen",
          startExploring: "Begin met het verkennen van eigendommen",
          accepted: "Geaccepteerd",
          amount: "Bedrag: € 1345",
          view: "Bekijk",
          newBooking: "Nieuwe boeking"
        },
        wishlists: {
          title: "Je verlanglijsten",
          viewAll: "Bekijk alle verlanglijsten",
          noWishlists: "Nog geen bewaarde eigendommen",
          startSaving: "Begin met het bewaren van je favoriete eigendommen"
        },
        overview: {
          totalSpent: "Totaal uitgegeven",
          upcomingTrips: "Komende reizen",
          messages: "Berichten", 
          noMessages: "Geen berichten",
          viewAllMessages: "Bekijk alle"
        },
        wishlistPage: {
          description: "Je bewaarde eigendommen en favorieten",
          property: "Eigendom",
          location: "Locatie",
          reviews: "beoordelingen", 
          perNight: "per nacht",
          viewProperty: "Eigendom bekijken"
        },
        account: {
          personalInfo: {
            title: "Persoonlijke informatie",
            firstName: "Voornaam",
            lastName: "Achternaam",
            email: "E-mail",
            editProfile: "Profiel bewerken"
          },
          dashboardSettings: {
            title: "Dashboard instellingen",
            becomeHostSection: "Word host sectie",
            becomeHostDescription: "Toon de host uitnodiging sectie op je dashboard",
            visible: "Zichtbaar",
            hidden: "Verborgen",
            hide: "Verbergen",
            restore: "Herstellen"
          }
        }
      },
      hostDashboard: {
        title: "Dashboard",
        greeting: "Goedenavond host!",
        description: "Beheer je eigenschappen, boekingen en inkomsten overzichtelijk.",
        navigation: {
          dashboard: "Dashboard",
          bookings: "Boekingen",
          properties: "Mijn huizen",
          guests: "Gasten",
          earnings: "Inkomsten",
          analytics: "Prestaties",
          tasks: "Taken",
          inventory: "Inventaris",
          messages: "Berichten",
          settings: "Instellingen",
          help: "Hulp"
        },
        overview: {
          title: "Overzicht van je hosting activiteiten",
          totalEarnings: "Totale inkomsten",
          activeBookings: "Actieve boekingen",
          occupancyRate: "Bezettingsgraad"
        },
        recentBookings: {
          title: "Recente boekingen",
          viewAll: "Alle boekingen bekijken",
          confirmed: "Bevestigd",
          pending: "In afwachting",
          guests: "gasten",
          newBooking: "Nieuwe boeking",
          noBookings: "Nog geen recente boekingen om te tonen.",
          startExploring: "Boekingen verschijnen hier zodra gasten reserveringen maken."
        },
        properties: {
          title: "Mijn woningen",
          viewAll: "Alle woningen bekijken",
          bookings: "boekingen",
          newProperty: "Nieuwe woning",
          noProperties: "Nog geen woningen aangemeld.",
          addProperty: "Voeg je eerste woning toe om te beginnen met het ontvangen van gasten."
        }
      },

      emailConfirmation: {
        title: "Controleer je e-mail",
        description: "We hebben je een bevestigings-e-mail gestuurd",
        emailSent: "E-mail succesvol verzonden",
        emailSentDescription: "We hebben een bevestigings-e-mail gestuurd naar {email}",
        nextSteps: "Volgende stappen",
        step1: "Open je e-mail inbox",
        step2: "Klik op de bevestigingslink in de e-mail",
        step3: "Je wordt teruggeleid naar VillaWise",
        resendEmail: "E-mail opnieuw versturen",
        backToHome: "Terug naar home",
        helpText: "E-mail niet ontvangen? Controleer je spam-map of neem contact op met de support."
      },
      oauthCallback: {
        loginSuccessful: "Inloggen Succesvol",
        welcomeBack: "Welkom terug, {name}!",
        user: "gebruiker",
        loginFailed: "Inloggen Mislukt",
        loginValidationError: "Kan je login niet valideren. Probeer het opnieuw.",
        loginError: "Inlog Fout",
        networkError: "Er is een netwerkfout opgetreden tijdens het inloggen. Probeer het opnieuw.",
        authenticationError: "Authenticatie Fout",
        authenticationFailed: "Authenticatie mislukt",
        authFailed: "Authenticatie mislukt. Probeer het opnieuw.",
        userNotFound: "Gebruikersaccount kon niet worden gevonden.",
        profileCreationFailed: "Kon gebruikersprofiel niet aanmaken.",
        callbackFailed: "Authenticatie callback mislukt.",
        noTokensReceived: "Geen authenticatietokens ontvangen. Probeer opnieuw in te loggen.",
        processingLogin: "Bezig met Inloggen...",
        pleaseWait: "Even geduld terwijl we je authenticatie voltooien."
      },
      imageUpload: {
        uploadSuccess: "Afbeelding succesvol geüpload",
        uploadSuccessDescription: "Je afbeelding is toegevoegd aan de woning galerij.",
        uploadFailed: "Upload mislukt",
        uploadFailedDescription: "Kon afbeelding niet uploaden. Probeer het opnieuw.",
        imageDeleted: "Afbeelding verwijderd",
        imageDeletedDescription: "De afbeelding is verwijderd uit de galerij.",
        deleteFailed: "Verwijderen mislukt",
        deleteFailedDescription: "Kon afbeelding niet verwijderen. Probeer het opnieuw.",
        reorderFailed: "Herordenen mislukt",
        reorderFailedDescription: "Kon afbeeldingen niet herordenen. Probeer het opnieuw.",
        invalidFileType: "Ongeldig bestandstype",
        invalidFileTypeDescription: "Selecteer een afbeeldingsbestand (JPG, PNG, enz.)",
        fileTooLarge: "Bestand te groot",
        fileTooLargeDescription: "Selecteer een afbeelding kleiner dan 5MB",
        maxImagesReached: "Maximum aantal afbeeldingen bereikt",
        maxImagesReachedDescription: "Je kunt maximaal {maxImages} afbeeldingen uploaden"
      },

      // Host upgrade page namespace - Dutch
      hostUpgrade: {
        title: "Word Verhuurder",
        subtitle: "Begin met het verhuren van je woning en verdien extra inkomsten",
        loading: "Bezig met laden...",
        
        benefits: {
          title: "Voordelen van Verhuurder worden",
          listProperty: {
            title: "Verhuur je Woning",
            description: "Voeg je woning toe aan ons platform en bereik duizenden potentiële gasten"
          },
          earnMoney: {
            title: "Verdien Geld",
            description: "Genereer extra inkomsten door je woning te verhuren aan reizigers"
          },
          manageBookings: {
            title: "Beheer Boekingen",
            description: "Eenvoudig reserveringen, betalingen en gastcommunicatie afhandelen"
          },
          hostCommunity: {
            title: "Verhuurders Community",
            description: "Word lid van onze gemeenschap van succesvolle verhuurders en krijg ondersteuning"
          }
        },
        
        upgrade: {
          title: "Upgrade naar Verhuurder",
          requirements: {
            title: "Vereisten",
            verified: "Geverifieerd VillaWise account",
            agreement: "Akkoord met verhuurder voorwaarden",
            guidelines: "Begrip van community richtlijnen"
          },
          button: "Word Verhuurder",
          upgrading: "Bezig met upgraden...",
          backToHome: "Terug naar Home"
        },
        
        upgradeSuccess: "Upgrade Succesvol",
        upgradeSuccessDescription: "Welkom bij de VillaWise verhuurder community! Je kunt nu woningen toevoegen.",
        upgradeFailed: "Upgrade Mislukt",
        upgradeFailedDescription: "Er ging iets mis tijdens de upgrade. Probeer het opnieuw."
      },

      // Auth error messages namespace - Dutch
      authErrors: {
        // Authentication failures
        authenticationRequired: "Authenticatie vereist",
        authenticationFailed: "Authenticatie mislukt",
        invalidToken: "Ongeldig of verlopen token",
        tokenMissing: "Autorisatie token ontbreekt",
        sessionExpired: "Je sessie is verlopen. Log opnieuw in",
        
        // Authorization failures
        hostPrivilegesRequired: "Host privileges vereist",
        hostUpgradeNeeded: "Je moet upgraden naar host status om deze functie te gebruiken",
        accessDenied: "Toegang geweigerd",
        insufficientPermissions: "Onvoldoende rechten",
        unauthorizedAccess: "Ongeautoriseerde toegang",
        restrictedArea: "Dit gebied is alleen toegankelijk voor geverifieerde verhuurders",
        
        // Loading states
        checkingAuthorization: "Autorisatie controleren...",
        loadingDashboard: "Dashboard laden...",
        verifyingAccess: "Toegang verifiëren...",
        
        // User upgrade messages
        upgradeToHost: "Upgrade naar Verhuurder",
        becomeHostToday: "Word vandaag nog verhuurder",
        upgradeSuccess: "Succesvol geüpgraded naar verhuurder",
        upgradeFailed: "Upgrade naar verhuurder mislukt",
        
        // Navigation messages
        backToHome: "Terug naar Home",
        gotoGuestDashboard: "Ga naar Gast Dashboard",
        returnToHome: "Terug naar Home",
        redirectingToLogin: "Doorsturen naar inloggen...",
        
        // Feature access messages
        hostAccessRequired: "Verhuurder Toegang Vereist",
        guestAccessRequired: "Gast Toegang Vereist",
        loginRequired: "Inloggen Vereist",
        verificationRequired: "Verificatie Vereist",
        
        // Generic error messages
        somethingWentWrong: "Er is iets misgegaan",
        tryAgainLater: "Probeer het later opnieuw",
        contactSupport: "Neem contact op met support als het probleem aanhoudt"
      }
    },

    // Messaging system translations (Dutch) 
    messaging: {
      // General
      title: 'Berichten',
      newConversation: 'Nieuw Gesprek',
      search: 'Zoek gesprekken...',
      noConversations: 'Geen gesprekken gevonden',
      selectConversation: 'Selecteer een gesprek',
      startNewConversation: 'Start Nieuw Gesprek',
      
      // Conversation states
      active: 'Actief',
      archived: 'Gearchiveerd',
      online: 'Online',
      offline: 'Offline',
      connecting: 'Verbinding maken...',
      connected: 'Verbonden',
      
      // Message states
      sent: 'Verzonden',
      delivered: 'Afgeleverd',
      read: 'Gelezen',
      edited: 'Bewerkt',
      messageDeleted: 'Bericht verwijderd',
      
      // Actions
      edit: 'Bewerken',
      delete: 'Verwijderen',
      report: 'Rapporteren',
      cancel: 'Annuleren',
      send: 'Verzenden',
      
      // Typing indicators
      isTyping: 'is aan het typen...',
      areTyping: 'zijn aan het typen...',
      and: 'en',
      peopleTyping: 'mensen typen',
      
      // Message types
      templateMessage: 'Sjabloon Bericht',
      
      // Templates
      templates: {
        title: 'Bericht Sjablonen',
        description: 'Kies uit voorgeschreven berichten om tijd te besparen',
        search: 'Zoek sjablonen...',
        allCategories: 'Alle Categorieën',
        noTemplates: 'Geen sjablonen gevonden',
        preview: 'Voorbeeld',
        variables: 'Variabelen',
        selectTemplate: 'Selecteer een sjabloon voor voorbeeld',
        send: 'Verzend Sjabloon',
        uses: 'gebruikt'
      },
      
      // Template categories
      categories: {
        inquiry: 'Aanvragen',
        booking: 'Boekingen',
        checkin: 'Inchecken',
        checkout: 'Uitchecken',
        support: 'Ondersteuning',
        custom: 'Aangepast'
      },
      
      // Errors
      errors: {
        failedToSend: 'Bericht verzenden mislukt',
        failedToEdit: 'Bericht bewerken mislukt',
        failedToDelete: 'Bericht verwijderen mislukt',
        failedToFlag: 'Bericht rapporteren mislukt',
        connectionLost: 'Verbinding verloren. Proberen opnieuw te verbinden...',
        invalidTemplate: 'Ongeldig sjabloongegevens'
      },
      
      // Safety
      safety: {
        reportMessage: 'Rapporteer Bericht',
        reportReason: 'Waarom rapporteer je dit bericht?',
        inappropriate: 'Ongepaste inhoud',
        spam: 'Spam of ongewenste berichten',
        harassment: 'Intimidatie of pesterijen',
        other: 'Andere reden',
        reportSubmitted: 'Rapport succesvol ingediend'
      }
    },


  };

  async getTranslations(req: Request, res: Response) {
    const startTime = Date.now();
    try {
      const { locale } = req.params;
      const supportedLocales = ['en', 'nl'];
      
      if (!supportedLocales.includes(locale)) {
        Logger.api('GET', `/api/translations/${locale}`, 400, Date.now() - startTime);
        return res.status(400).json({ error: 'Unsupported locale' });
      }

      const translations = this.translations[locale as keyof typeof this.translations];
      
      Logger.info(`Translations requested for locale: ${locale}`);
      Logger.api('GET', `/api/translations/${locale}`, 200, Date.now() - startTime);
      
      res.json(translations);
    } catch (error) {
      Logger.error('Error fetching translations', error);
      Logger.api('GET', `/api/translations/${req.params.locale}`, 500, Date.now() - startTime);
      res.status(500).json({ error: 'Failed to fetch translations' });
    }
  }
}

export const translationController = new TranslationController();