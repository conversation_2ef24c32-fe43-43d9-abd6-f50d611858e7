name: Pull Request Checks

on:
  pull_request:
    branches: [ main, develop ]
    types: [opened, synchronize, reopened]

env:
  NODE_VERSION: '20.x'

jobs:
  # Quick validation for PRs
  pr-validation:
    runs-on: ubuntu-latest
    name: PR Validation
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run TypeScript type check
        run: npx tsc --noEmit

      - name: Check code formatting
        run: |
          echo "Checking code format..."
          # Add prettier or eslint checks here if configured
          echo "Format check passed"

      - name: Build check
        run: npm run build

      - name: Size check
        run: |
          echo "Checking build size..."
          du -sh dist/ 2>/dev/null || du -sh build/ 2>/dev/null || echo "No build output found"

      - name: Security audit
        run: node scripts/security/audit-security.js

  # Comment PR with build info
  pr-comment:
    runs-on: ubuntu-latest
    name: PR Build Summary
    needs: pr-validation
    if: always()
    steps:
      - name: Comment PR
        uses: actions/github-script@v7
        with:
          script: |
            const { data: pullRequest } = await github.rest.pulls.get({
              owner: context.repo.owner,
              repo: context.repo.repo,
              pull_number: context.issue.number,
            });

            const buildStatus = '${{ needs.pr-validation.result }}';
            const statusIcon = buildStatus === 'success' ? '✅' : '❌';
            
            const comment = `
            ## ${statusIcon} Build Summary for PR #${context.issue.number}
            
            **Status**: ${buildStatus}
            **Commit**: ${context.sha.substring(0, 7)}
            **Branch**: ${pullRequest.head.ref}
            
            ### Checks Performed:
            - TypeScript compilation
            - Code formatting
            - Build process
            - Security audit
            
            ${buildStatus === 'success' ? 
              '🎉 All checks passed! Ready for review.' : 
              '⚠️ Some checks failed. Please review the logs above.'}
            `;

            // Find existing comment
            const { data: comments } = await github.rest.issues.listComments({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number,
            });

            const existingComment = comments.find(comment => 
              comment.body.includes('Build Summary for PR')
            );

            if (existingComment) {
              await github.rest.issues.updateComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                comment_id: existingComment.id,
                body: comment,
              });
            } else {
              await github.rest.issues.createComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: context.issue.number,
                body: comment,
              });
            }