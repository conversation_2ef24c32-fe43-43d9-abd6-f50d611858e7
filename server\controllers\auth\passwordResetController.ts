import { Request, Response } from 'express';
import { supabase } from '../../supabase';
import { storage } from '../../storage';

export async function handleForgotPassword(req: Request, res: Response) {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({
        success: false,
        message: 'Email is required'
      });
    }

    // First check if user exists in our database
    const user = await storage.getUserByEmail(email);
    if (!user) {
      // For security, we don't reveal if the email exists or not
      return res.json({
        success: true,
        message: 'If an account with this email exists, you will receive a password reset link'
      });
    }

    // If using Supabase, send reset email through Supabase Auth
    if (supabase) {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${req.protocol}://${req.get('host')}/reset-password`
      });

      if (error) {
        console.error('Supabase password reset error:', error);
        return res.status(500).json({
          success: false,
          message: 'Failed to send password reset email'
        });
      }
    } else {
      // For development/testing without Supabase
      console.log(`Password reset requested for: ${email}`);
      console.log('In production, this would send an email with a reset link');
    }

    res.json({
      success: true,
      message: 'Password reset email sent successfully'
    });

  } catch (error) {
    console.error('Password reset error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
}

export async function handleResetPassword(req: Request, res: Response) {
  try {
    const { token, password } = req.body;

    if (!token || !password) {
      return res.status(400).json({
        success: false,
        message: 'Token and password are required'
      });
    }

    if (password.length < 8) {
      return res.status(400).json({
        success: false,
        message: 'Password must be at least 8 characters long'
      });
    }

    if (supabase) {
      const { error } = await supabase.auth.updateUser({
        password: password
      });

      if (error) {
        console.error('Supabase password update error:', error);
        return res.status(400).json({
          success: false,
          message: 'Invalid or expired reset token'
        });
      }
    } else {
      // For development/testing without Supabase
      console.log('Password reset completed for token:', token);
    }

    res.json({
      success: true,
      message: 'Password updated successfully'
    });

  } catch (error) {
    console.error('Password update error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
}