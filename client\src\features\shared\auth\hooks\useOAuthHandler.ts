import { useQueryClient } from "@tanstack/react-query";
import { useEffect } from "react";
import { useNavigate } from "react-router-dom";

const AUTH_KEYS = {
  user: ["auth", "user"] as const,
};

export function useOAuthHandler() {
  const queryClient = useQueryClient();
  const navigate = useNavigate();

  useEffect(() => {
    const hash = window.location.hash;
    if (hash) {
      const params = new URLSearchParams(hash.substring(1)); // Remove '#'
      const accessToken = params.get("access_token");
      const refreshToken = params.get("refresh_token");

      if (accessToken && refreshToken) {
        console.log(
          "OAuthHandler: Tokens found in URL, storing in localStorage."
        );
        localStorage.setItem("sb_access_token", accessToken);
        localStorage.setItem("sb_refresh_token", refreshToken);

        // Clean up the URL
        navigate(window.location.pathname, { replace: true });

        // Invalidate user query to refetch with new token
        queryClient.invalidateQueries({ queryKey: AUTH_KEYS.user });
      }
    }
  }, [navigate, queryClient]);
}
