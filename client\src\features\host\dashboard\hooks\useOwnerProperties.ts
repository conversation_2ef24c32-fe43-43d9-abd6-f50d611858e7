import { useQuery } from "@tanstack/react-query";
import { Property } from "../types";
import { useUser } from "@/features/shared/auth/hooks/useAuth";

export function useOwnerProperties() {
  const { data: user } = useUser();
  
  return useQuery({
    queryKey: ['/api/host/properties', user?.id],
    queryFn: async () => {
      const token = localStorage.getItem('authToken');
      const response = await fetch('/api/host/properties', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('Authentication required');
        }
        throw new Error('Failed to fetch properties');
      }
      
      const data = await response.json();
      return data.properties || [];
    },
    enabled: !!user?.id && !!localStorage.getItem('authToken'),
    refetchInterval: 30000, // Refetch every 30 seconds for real-time updates
    staleTime: 10000, // Data is fresh for 10 seconds
  });
}

export function useOwnerBookings() {
  const { data: user } = useUser();
  
  return useQuery({
    queryKey: ['/api/host/bookings', user?.id],
    queryFn: async () => {
      const token = localStorage.getItem('authToken');
      const response = await fetch('/api/host/bookings', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('Authentication required');
        }
        throw new Error('Failed to fetch bookings');
      }
      
      const data = await response.json();
      return data.bookings || [];
    },
    enabled: !!user?.id && !!localStorage.getItem('authToken'),
    refetchInterval: 20000, // More frequent updates for bookings
    staleTime: 5000,
  });
}

export function useOwnerAnalytics() {
  return useQuery({
    queryKey: ['/api/host/analytics'],
    queryFn: async () => {
      const response = await fetch('/api/host/analytics');
      if (!response.ok) {
        throw new Error('Failed to fetch analytics');
      }
      const data = await response.json();
      return data.analytics || data || {};
    },
    refetchInterval: 60000, // Analytics updates every minute
    staleTime: 30000,
  });
}

// New hook for real-time earnings tracking
export function useOwnerEarnings() {
  const { data: user } = useUser();
  
  return useQuery({
    queryKey: ['/api/host/earnings', user?.id],
    queryFn: async () => {
      const token = localStorage.getItem('authToken');
      const response = await fetch('/api/host/earnings', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('Authentication required');
        }
        throw new Error('Failed to fetch earnings');
      }
      
      const data = await response.json();
      return data.earnings || {};
    },
    enabled: !!user?.id && !!localStorage.getItem('authToken'),
    refetchInterval: 30000,
    staleTime: 15000,
  });
}

// New hook for property views and metrics
export function usePropertyMetrics() {
  const { data: user } = useUser();
  
  return useQuery({
    queryKey: ['/api/host/metrics', user?.id],
    queryFn: async () => {
      const token = localStorage.getItem('authToken');
      const response = await fetch('/api/host/metrics', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('Authentication required');
        }
        throw new Error('Failed to fetch property metrics');
      }
      
      const data = await response.json();
      return data.metrics || {};
    },
    enabled: !!user?.id && !!localStorage.getItem('authToken'),
    refetchInterval: 45000,
    staleTime: 20000,
  });
}