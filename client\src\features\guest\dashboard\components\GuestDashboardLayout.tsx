import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { useTranslations } from '@/lib/translations';
import { useIsMobile } from '@/hooks/use-mobile';
import { 
  User, 
  CalendarDays, 
  Heart, 
  MessageSquare,
  Star,
  Settings,
  HelpCircle
} from 'lucide-react';

// Feature imports
import { OverviewContent } from '../overview';
import { BookingsContent } from '../bookings';
import { WishlistsContent } from '../wishlists';
import { MessagesContent } from '@/features/shared/messaging';

// Mobile components
import { BottomNavigation } from './mobile/BottomNavigation';

// Placeholder imports
import PlaceholderContent from './PlaceholderContent';

interface GuestDashboardLayoutProps {
  guestBookings: any[];
  wishlists: any[];
  guestMessages: any[];
  user: any;
  onUpgradeToHost: () => void;
  isUpgrading: boolean;
  initialTab?: string;
}

const GuestDashboardLayout: React.FC<GuestDashboardLayoutProps> = ({
  guestBookings,
  wishlists,
  guestMessages,
  user,
  onUpgradeToHost,
  isUpgrading,
  initialTab = 'dashboard'
}) => {
  const t = useTranslations('guestDashboard');
  const isMobile = useIsMobile();
  const [activeTab, setActiveTab] = useState(initialTab);
  
  // Force translation refresh  
  useEffect(() => {
    // This will trigger translation reload
  }, []);
  const [showBecomeHostSection, setShowBecomeHostSection] = useState(true);

  const DesktopNavigationSidebar = () => (
    <div className="hidden md:block w-64 bg-white border-r min-h-[calc(100vh-64px)]">
      <div className="p-6">
        <h1 className="text-2xl font-bold text-gray-900">{t('title')}</h1>
        <p className="text-gray-600 mt-2">{t('greeting')}</p>
      </div>
      
      <nav className="px-4 space-y-2">
        <Button 
          variant={activeTab === 'dashboard' ? 'default' : 'ghost'}
          className="w-full justify-start"
          onClick={() => setActiveTab('dashboard')}
        >
          <User className="h-4 w-4 mr-2" />
          {t('navigation.dashboard')}
        </Button>
        
        <Button 
          variant={activeTab === 'bookings' ? 'default' : 'ghost'}
          className="w-full justify-start"
          onClick={() => setActiveTab('bookings')}
        >
          <CalendarDays className="h-4 w-4 mr-2" />
          {t('navigation.bookings')}
        </Button>
        
        <Button 
          variant={activeTab === 'messages' ? 'default' : 'ghost'}
          className="w-full justify-start"
          onClick={() => setActiveTab('messages')}
        >
          <MessageSquare className="h-4 w-4 mr-2" />
          {t('navigation.messages')}
        </Button>
        
        <Button 
          variant={activeTab === 'wishlists' ? 'default' : 'ghost'}
          className="w-full justify-start"
          onClick={() => setActiveTab('wishlists')}
        >
          <Heart className="h-4 w-4 mr-2" />
          {t('navigation.wishlists')}
        </Button>
        
        <Button 
          variant={activeTab === 'reviews' ? 'default' : 'ghost'}
          className="w-full justify-start"
          onClick={() => setActiveTab('reviews')}
        >
          <Star className="h-4 w-4 mr-2" />
          {t('navigation.reviews')}
        </Button>
        
        <Button 
          variant={activeTab === 'settings' ? 'default' : 'ghost'}
          className="w-full justify-start"
          onClick={() => setActiveTab('settings')}
        >
          <Settings className="h-4 w-4 mr-2" />
          {t('navigation.settings')}
        </Button>
        
        <Button 
          variant={activeTab === 'help' ? 'default' : 'ghost'}
          className="w-full justify-start"
          onClick={() => setActiveTab('help')}
        >
          <HelpCircle className="h-4 w-4 mr-2" />
          {t('navigation.help')}
        </Button>
      </nav>
    </div>
  );

  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return (
          <OverviewContent
            guestBookings={guestBookings}
            wishlists={wishlists}
            guestMessages={guestMessages}
            user={user}
            onUpgradeToHost={onUpgradeToHost}
            isUpgrading={isUpgrading}
            showBecomeHostSection={showBecomeHostSection}
          />
        );
      case 'bookings':
        return <BookingsContent guestBookings={guestBookings} />;
      case 'messages':
        return <MessagesContent messages={guestMessages} userType="guest" />;
      case 'wishlists':
        return <WishlistsContent wishlists={wishlists} />;
      case 'reviews':
        return (
          <PlaceholderContent
            icon={Star}
            title={t('navigation.reviews')}
            description="Review management coming soon!"
          />
        );
      case 'settings':
        return (
          <PlaceholderContent
            icon={Settings}
            title={t('navigation.settings')}
            description="Account settings coming soon!"
          />
        );
      case 'help':
        return (
          <PlaceholderContent
            icon={HelpCircle}
            title={t('navigation.help')}
            description="Help & support coming soon!"
          />
        );
      default:
        return (
          <OverviewContent
            guestBookings={guestBookings}
            wishlists={wishlists}
            guestMessages={guestMessages}
            user={user}
            onUpgradeToHost={onUpgradeToHost}
            isUpgrading={isUpgrading}
            showBecomeHostSection={showBecomeHostSection}
          />
        );
    }
  };

  if (isMobile) {
    return (
      <div className="min-h-screen bg-gray-50 overflow-x-hidden max-w-full">
        {/* Mobile Main Content - Full Width */}
        <div className="pb-20 w-full max-w-full overflow-x-hidden">
          <div className="min-h-[calc(100vh-80px)] w-full max-w-full">
            {renderContent()}
          </div>
        </div>

        {/* Mobile Bottom Navigation */}
        <BottomNavigation
          activeTab={activeTab}
          onTabChange={setActiveTab}
        />
      </div>
    );
  }

  // Desktop Layout
  return (
    <div className="flex min-h-[calc(100vh-64px)] bg-gray-50">
      {/* Desktop Sidebar */}
      <DesktopNavigationSidebar />
      
      {/* Desktop Main Content */}
      <div className="flex-1">
        {renderContent()}
      </div>
    </div>
  );
};

export default GuestDashboardLayout;