import { Router } from 'express';
import { conversationController } from '../controllers/conversationController';
import { messageController } from '../controllers/messageController';
import { templateController } from '../controllers/templateController';
import { requireAuth } from '../../middleware/auth';
import { Logger } from '../../utils/logger';

const router = Router();

// Apply authentication middleware to all messaging routes
router.use(requireAuth);

// Log all messaging API requests
router.use((req, res, next) => {
  Logger.info(`[MESSAGING] ${req.method} ${req.path}`);
  next();
});

// ==========================================
// CONVERSATION ROUTES
// ==========================================

/**
 * @route GET /api/messaging/conversations
 * @desc Get conversations for authenticated user
 * @access Private
 */
router.get('/conversations', conversationController.getConversations.bind(conversationController));

/**
 * @route POST /api/messaging/conversations
 * @desc Create new conversation
 * @access Private
 */
router.post('/conversations', conversationController.createConversation.bind(conversationController));

/**
 * @route GET /api/messaging/conversations/:id
 * @desc Get single conversation with details
 * @access Private
 */
router.get('/conversations/:id', conversationController.getConversation.bind(conversationController));

/**
 * @route PATCH /api/messaging/conversations/:id/archive
 * @desc Archive conversation
 * @access Private
 */
router.patch('/conversations/:id/archive', conversationController.archiveConversation.bind(conversationController));

/**
 * @route PATCH /api/messaging/conversations/:id/read
 * @desc Mark conversation as read
 * @access Private
 */
router.patch('/conversations/:id/read', conversationController.markAsRead.bind(conversationController));

/**
 * @route POST /api/messaging/conversations/:id/subscribe
 * @desc Get subscription info for real-time updates
 * @access Private
 */
router.post('/conversations/:id/subscribe', conversationController.subscribeToConversation.bind(conversationController));

// ==========================================
// MESSAGE ROUTES
// ==========================================

/**
 * @route GET /api/messaging/conversations/:conversationId/messages
 * @desc Get messages for a conversation
 * @access Private
 */
router.get('/conversations/:conversationId/messages', messageController.getMessages.bind(messageController));

/**
 * @route POST /api/messaging/conversations/:conversationId/messages
 * @desc Send a new message
 * @access Private
 */
router.post('/conversations/:conversationId/messages', messageController.sendMessage.bind(messageController));

/**
 * @route POST /api/messaging/conversations/:conversationId/typing
 * @desc Send typing indicator
 * @access Private
 */
router.post('/conversations/:conversationId/typing', messageController.sendTypingIndicator.bind(messageController));

/**
 * @route PUT /api/messaging/messages/:messageId
 * @desc Edit a message
 * @access Private
 */
router.put('/messages/:messageId', messageController.editMessage.bind(messageController));

/**
 * @route DELETE /api/messaging/messages/:messageId
 * @desc Delete/unsend a message
 * @access Private
 */
router.delete('/messages/:messageId', messageController.deleteMessage.bind(messageController));

/**
 * @route POST /api/messaging/messages/:messageId/flag
 * @desc Flag a message for review
 * @access Private
 */
router.post('/messages/:messageId/flag', messageController.flagMessage.bind(messageController));

/**
 * @route GET /api/messaging/search
 * @desc Search messages across conversations
 * @access Private
 */
router.get('/search', messageController.searchMessages.bind(messageController));

// ==========================================
// TEMPLATE ROUTES (Host Only)
// ==========================================

/**
 * @route GET /api/messaging/templates
 * @desc Get templates for authenticated host
 * @access Private (Host only)
 */
router.get('/templates', templateController.getTemplates.bind(templateController));

/**
 * @route POST /api/messaging/templates
 * @desc Create new template
 * @access Private (Host only)
 */
router.post('/templates', templateController.createTemplate.bind(templateController));

/**
 * @route PUT /api/messaging/templates/:templateId
 * @desc Update existing template
 * @access Private (Host only)
 */
router.put('/templates/:templateId', templateController.updateTemplate.bind(templateController));

/**
 * @route DELETE /api/messaging/templates/:templateId
 * @desc Delete template
 * @access Private (Host only)
 */
router.delete('/templates/:templateId', templateController.deleteTemplate.bind(templateController));

/**
 * @route POST /api/messaging/templates/:templateId/process
 * @desc Process template with variables for preview
 * @access Private (Host only)
 */
router.post('/templates/:templateId/process', templateController.processTemplate.bind(templateController));

/**
 * @route GET /api/messaging/templates/default
 * @desc Get default template suggestions
 * @access Private (Host only)
 */
router.get('/templates/default', templateController.getDefaultTemplates.bind(templateController));

// ==========================================
// HEALTH CHECK
// ==========================================

/**
 * @route GET /api/messaging/health
 * @desc Messaging service health check
 * @access Private
 */
router.get('/health', (req, res) => {
  res.json({
    success: true,
    service: 'messaging',
    status: 'healthy',
    timestamp: new Date().toISOString(),
    features: {
      conversations: 'active',
      messages: 'active', 
      templates: 'active',
      realtime: 'active',
      search: 'active'
    }
  });
});

export default router;