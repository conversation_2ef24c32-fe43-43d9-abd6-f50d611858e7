import { z } from 'zod';

// User schema - updated for Supabase UUID compatibility
export const userSchema = z.object({
  id: z.string(), // Changed from number to string for Supabase UUIDs
  username: z.string().optional(), // Make optional for registration flow
  email: z.string().email().optional(),
  first_name: z.string().nullable().optional(),
  last_name: z.string().nullable().optional(),
  phone: z.string().nullable().optional(),
  is_host: z.boolean().nullable().optional(),
  avatar_url: z.string().nullable().optional(),
  oauth_provider: z.string().nullable().optional(),
  oauth_id: z.string().nullable().optional(),
  locale: z.string().nullable().optional(),
  created_at: z.string().nullable().optional(),
  updated_at: z.string().nullable().optional(),
  email_confirmed: z.boolean().optional(), // Add email_confirmed field
});

// Login schema
export const loginSchema = z.object({
  username: z.string().min(1, 'Username is required'),
  password: z.string().min(1, 'Password is required'),
});

// Register schema
export const registerSchema = z.object({
  username: z.string().min(3, 'Username must be at least 3 characters'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  email: z.string().email('Valid email is required'),
  firstName: z.string().optional(),
  lastName: z.string().optional(),
});

// Type exports
export type User = z.infer<typeof userSchema>;
export type LoginCredentials = z.infer<typeof loginSchema>;
export type RegisterCredentials = z.infer<typeof registerSchema>;