import { Router } from 'express';

const router = Router();

// Health check endpoint for Docker and deployment monitoring
router.get('/health', (req, res) => {
  res.status(200).json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    cache: process.env.USE_REDIS_CACHE === 'true' ? 'redis' : 'memory',
    version: process.env.npm_package_version || '1.0.0'
  });
});

// Liveness probe endpoint
router.get('/health/live', (req, res) => {
  res.status(200).json({ status: 'alive' });
});

// Readiness probe endpoint
router.get('/health/ready', (req, res) => {
  // In the future, add checks for database connectivity, etc.
  res.status(200).json({ status: 'ready' });
});

export default router;