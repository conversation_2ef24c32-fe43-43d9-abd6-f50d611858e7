#!/usr/bin/env node

/**
 * TypeScript compilation check script
 * Runs TypeScript compiler with proper error handling and timeout
 */

import { spawn } from 'child_process';

console.log('🔍 Running TypeScript compilation check...');

const child = spawn('npx', ['tsc', '--noEmit', '--skipLibCheck'], {
  stdio: ['pipe', 'pipe', 'pipe'],
  timeout: 30000 // 30 second timeout
});

let stdout = '';
let stderr = '';

child.stdout.on('data', (data) => {
  stdout += data.toString();
  process.stdout.write(data);
});

child.stderr.on('data', (data) => {
  stderr += data.toString();
  process.stderr.write(data);
});

child.on('close', (code) => {
  if (code === 0) {
    console.log('✅ TypeScript compilation check passed');
    process.exit(0);
  } else {
    console.error(`❌ TypeScript compilation failed with exit code ${code}`);
    process.exit(1);
  }
});

child.on('error', (error) => {
  console.error('❌ TypeScript check failed:', error.message);
  process.exit(1);
});

// Handle timeout
setTimeout(() => {
  if (!child.killed) {
    console.error('⏱️  TypeScript check timed out after 30 seconds');
    child.kill('SIGTERM');
    process.exit(1);
  }
}, 30000);