/**
 * Direct Supabase test to check user retrieval
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseAnonKey || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

// Create both clients
const supabase = createClient(supabaseUrl, supabaseAnonKey);
const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);

async function testSupabaseQueries() {
  console.log('🔍 Testing Supabase queries directly...');
  
  const userId = '50c1b056-db73-40c5-ae5f-80117d798cfb';
  const userEmail = '<EMAIL>';
  
  console.log('\n📊 Test 1: Anonymous client - Get user by ID');
  try {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId);
    
    console.log('Anonymous client result:');
    console.log('  Data:', data);
    console.log('  Error:', error);
    console.log('  Data length:', data?.length || 0);
  } catch (err) {
    console.error('Anonymous client error:', err);
  }
  
  console.log('\n📊 Test 2: Service role client - Get user by ID');
  try {
    const { data, error } = await supabaseAdmin
      .from('users')
      .select('*')
      .eq('id', userId);
    
    console.log('Service role client result:');
    console.log('  Data:', data);
    console.log('  Error:', error);
    console.log('  Data length:', data?.length || 0);
  } catch (err) {
    console.error('Service role client error:', err);
  }
  
  console.log('\n📊 Test 3: Anonymous client - Get user by email');
  try {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('email', userEmail);
    
    console.log('Anonymous client by email result:');
    console.log('  Data:', data);
    console.log('  Error:', error);
    console.log('  Data length:', data?.length || 0);
  } catch (err) {
    console.error('Anonymous client by email error:', err);
  }
  
  console.log('\n📊 Test 4: Service role client - Get user by email');
  try {
    const { data, error } = await supabaseAdmin
      .from('users')
      .select('*')
      .eq('email', userEmail);
    
    console.log('Service role client by email result:');
    console.log('  Data:', data);
    console.log('  Error:', error);
    console.log('  Data length:', data?.length || 0);
  } catch (err) {
    console.error('Service role client by email error:', err);
  }
  
  console.log('\n📊 Test 5: Service role client - List all users');
  try {
    const { data, error } = await supabaseAdmin
      .from('users')
      .select('id, email, username')
      .limit(5);
    
    console.log('All users result:');
    console.log('  Data:', data);
    console.log('  Error:', error);
    console.log('  Data length:', data?.length || 0);
  } catch (err) {
    console.error('All users error:', err);
  }
  
  console.log('\n📊 Test 6: Check RLS policies');
  try {
    const { data, error } = await supabase
      .from('users')
      .select('id, email')
      .limit(1);
    
    console.log('RLS test result:');
    console.log('  Data:', data);
    console.log('  Error:', error);
  } catch (err) {
    console.error('RLS test error:', err);
  }
  
  console.log('\n✅ Supabase query testing complete');
}

testSupabaseQueries().catch(console.error);