import { useState } from 'react';
import { useVersionCheck } from './VersionChecker';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { RefreshCw, Trash2, Info } from 'lucide-react';

// Helper functions for enabling/disabling cache manager
declare global {
  interface Window {
    enableCacheManager: () => void;
    disableCacheManager: () => void;
  }
}

// Add global helper functions
if (typeof window !== 'undefined') {
  window.enableCacheManager = () => {
    localStorage.setItem('VITE_DEV_CACHE_MANAGER', 'true');
    window.location.reload();
  };
  
  window.disableCacheManager = () => {
    localStorage.setItem('VITE_DEV_CACHE_MANAGER', 'false');
    window.location.reload();
  };
}

interface DevCacheManagerProps {
  isVisible?: boolean;
}

/**
 * Development utility for managing translation cache
 * Only visible when VITE_DEV_CACHE_MANAGER=true or localStorage override is set
 */
export function DevCacheManager({ 
  isVisible = import.meta.env.VITE_DEV_CACHE_MANAGER === 'true' || 
              (typeof window !== 'undefined' && localStorage.getItem('VITE_DEV_CACHE_MANAGER') === 'true')
}: DevCacheManagerProps) {
  const [buildInfo, setBuildInfo] = useState<any>(null);
  const [cacheStats, setCacheStats] = useState<any>(null);
  const { checkAndRefresh, clearCache, getBuildInfo } = useVersionCheck();

  if (!isVisible) return null;

  const loadBuildInfo = async () => {
    const info = await getBuildInfo();
    setBuildInfo(info);
  };

  const loadCacheStats = () => {
    const stats = {
      localStorageKeys: Object.keys(localStorage).filter(key => key.startsWith('villawise_')),
      totalSize: new Blob([JSON.stringify(localStorage)]).size,
      cacheEntries: Object.keys(localStorage).filter(key => key.startsWith('villawise_translations_')).length
    };
    setCacheStats(stats);
  };

  const handleRefreshCache = async () => {
    await checkAndRefresh();
    loadCacheStats();
  };

  const handleClearCache = async () => {
    await clearCache();
    loadCacheStats();
  };

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <Card className="w-80 shadow-lg border-yellow-200">
        <CardHeader className="pb-3">
          <CardTitle className="text-sm flex items-center gap-2">
            <Info className="w-4 h-4" />
            Translation Cache Manager
          </CardTitle>
          <CardDescription className="text-xs">
            Development utility for cache management
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex gap-2">
            <Button
              size="sm"
              variant="outline"
              onClick={loadBuildInfo}
              className="flex-1"
            >
              <Info className="w-3 h-3 mr-1" />
              Build Info
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={loadCacheStats}
              className="flex-1"
            >
              Stats
            </Button>
          </div>

          <div className="flex gap-2">
            <Button
              size="sm"
              variant="default"
              onClick={handleRefreshCache}
              className="flex-1"
            >
              <RefreshCw className="w-3 h-3 mr-1" />
              Refresh
            </Button>
            <Button
              size="sm"
              variant="destructive"
              onClick={handleClearCache}
              className="flex-1"
            >
              <Trash2 className="w-3 h-3 mr-1" />
              Clear
            </Button>
          </div>

          {buildInfo && (
            <div className="space-y-2 text-xs">
              <div className="flex justify-between">
                <span>Version:</span>
                <Badge variant="secondary">{buildInfo.version}</Badge>
              </div>
              <div className="flex justify-between">
                <span>Build Hash:</span>
                <Badge variant="outline">{buildInfo.buildHash}</Badge>
              </div>
              <div className="flex justify-between">
                <span>Environment:</span>
                <Badge variant={buildInfo.env === 'production' ? 'default' : 'secondary'}>
                  {buildInfo.env}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span>Build Time:</span>
                <span className="text-muted-foreground">
                  {new Date(buildInfo.buildTime).toLocaleString()}
                </span>
              </div>
            </div>
          )}

          {cacheStats && (
            <div className="space-y-2 text-xs">
              <div className="flex justify-between">
                <span>Cache Entries:</span>
                <Badge variant="secondary">{cacheStats.cacheEntries}</Badge>
              </div>
              <div className="flex justify-between">
                <span>Total Keys:</span>
                <Badge variant="outline">{cacheStats.localStorageKeys.length}</Badge>
              </div>
              <div className="flex justify-between">
                <span>Storage Size:</span>
                <span className="text-muted-foreground">
                  {(cacheStats.totalSize / 1024).toFixed(1)} KB
                </span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}