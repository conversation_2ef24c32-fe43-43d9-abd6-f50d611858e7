import { fileURLToPath } from 'url';
import * as path from 'path';

// Create a cross-platform __dirname equivalent for ES modules
export function getDirname(importMetaUrl: string): string {
  return path.dirname(fileURLToPath(importMetaUrl));
}

// Export a function that works with both development and production
export function getProjectRoot(): string {
  // Use standard Node.js approach for ES modules
  const currentDir = getDirname(import.meta.url);
  
  // Navigate up from server/utils to project root
  return path.resolve(currentDir, '..', '..');
}