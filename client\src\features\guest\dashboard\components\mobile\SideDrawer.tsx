import React from 'react';
import { Button } from '@/components/ui/button';
import { X, User, CalendarDays, Heart, MessageSquare, Star, Settings, HelpCircle } from 'lucide-react';
import { useTranslations } from '@/lib/translations';

interface SideDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  activeTab: string;
  onTabChange: (tab: string) => void;
  user?: any;
}

export const SideDrawer: React.FC<SideDrawerProps> = ({
  isOpen,
  onClose,
  activeTab,
  onTabChange,
  user
}) => {
  const t = useTranslations('guestDashboard');

  const navItems = [
    { id: 'dashboard', icon: User, label: t('navigation.dashboard') },
    { id: 'bookings', icon: CalendarDays, label: t('navigation.bookings') },
    { id: 'messages', icon: MessageSquare, label: t('navigation.messages') },
    { id: 'wishlists', icon: Heart, label: t('navigation.wishlists') },
    { id: 'reviews', icon: Star, label: t('navigation.reviews') },
    { id: 'settings', icon: Settings, label: t('navigation.settings') },
    { id: 'help', icon: HelpCircle, label: t('navigation.help') }
  ];

  const handleTabChange = (tab: string) => {
    onTabChange(tab);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 z-50 md:hidden"
        onClick={onClose}
      />
      
      {/* Drawer */}
      <div className="fixed left-0 top-0 bottom-0 w-80 bg-white z-50 shadow-xl transform transition-transform duration-300 ease-in-out md:hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <div>
            <h2 className="text-xl font-bold text-gray-900">{t('title')}</h2>
            {user?.name && (
              <p className="text-sm text-gray-600">{t('greeting')}</p>
            )}
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="h-8 w-8 text-gray-600"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>

        {/* User Profile Section */}
        {user && (
          <div className="p-4 border-b bg-gray-50">
            <div className="flex items-center space-x-3">
              <div className="h-12 w-12 bg-primary rounded-full flex items-center justify-center">
                <User className="h-6 w-6 text-white" />
              </div>
              <div>
                <p className="font-medium text-gray-900">{user.name || 'Guest User'}</p>
                <p className="text-sm text-gray-600">{user.email}</p>
              </div>
            </div>
          </div>
        )}

        {/* Navigation */}
        <nav className="flex-1 px-4 py-2 space-y-1">
          {navItems.map(({ id, icon: Icon, label }) => (
            <Button
              key={id}
              variant={activeTab === id ? 'default' : 'ghost'}
              className="w-full justify-start py-3 px-3 text-left"
              onClick={() => handleTabChange(id)}
            >
              <Icon className="h-5 w-5 mr-3" />
              {label}
            </Button>
          ))}
        </nav>

        {/* Footer */}
        <div className="p-4 border-t bg-gray-50">
          <p className="text-xs text-gray-500 text-center">
            VillaWise © 2025
          </p>
        </div>
      </div>
    </>
  );
};