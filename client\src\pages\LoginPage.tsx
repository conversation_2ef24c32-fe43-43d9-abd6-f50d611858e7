import React, { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { useTranslations } from "@/lib/translations";
import { MainLayout } from "@/components/MainLayout";
import { SocialLoginButtons } from "@/components/auth/SocialLoginButtons";
import { PasswordInput } from "@/components/auth/PasswordInput";
import { RememberMeCheckbox } from "@/components/auth/RememberMeCheckbox";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { Link } from "wouter";
import { Loader2, ArrowLeft, CheckCircle, X } from "lucide-react";

export function LoginPage() {
  const [, navigate] = useLocation();
  const { toast } = useToast();
  const t = useTranslations('auth.login');
  const [isLoading, setIsLoading] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [registrationEmail, setRegistrationEmail] = useState<string>('');
  const [formData, setFormData] = useState({
    email: "",
    password: "",
  });

  useEffect(() => {
    // Check if user just registered and needs email confirmation
    const registrationSuccess = localStorage.getItem('registration_success');
    const email = localStorage.getItem('registration_email');
    
    console.log("🔍 LoginPage useEffect - checking localStorage:");
    console.log("  - registrationSuccess:", registrationSuccess);
    console.log("  - email:", email);
    console.log("  - showSuccessMessage before:", showSuccessMessage);
    
    if (registrationSuccess === 'true' && email) {
      console.log("✅ Found registration success flag - showing banner");
      setShowSuccessMessage(true);
      setRegistrationEmail(email);
      setFormData(prev => ({ ...prev, email }));
      
      // Clear the flags after showing
      localStorage.removeItem('registration_success');
      localStorage.removeItem('registration_email');
      
      // Add additional debugging
      console.log("🎯 Banner state set:");
      console.log("  - showSuccessMessage:", true);
      console.log("  - registrationEmail:", email);
    } else {
      console.log("❌ No registration success flag found");
    }
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          rememberMe,
        }),
      });

      const data = await response.json();

      if (data.success) {
        // Store session data
        if (data.session) {
          localStorage.setItem('sb_access_token', data.session.access_token);
          localStorage.setItem('sb_refresh_token', data.session.refresh_token);
          
          // Store remember me preference
          if (rememberMe) {
            localStorage.setItem('remember_me', 'true');
            // Set longer expiration for remember me
            localStorage.setItem('remember_me_expires', 
              new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
            );
          }
        }

        // Navigate to home page
        navigate('/');
        
        // Refresh page to show logged-in state
        setTimeout(() => {
          window.location.reload();
        }, 100);
      } else {
        throw new Error(data.message || 'Login failed');
      }
    } catch (error) {
      console.error('Login error:', error);
      
      // Check if the error is about email not being confirmed
      const errorMessage = error instanceof Error ? error.message : '';
      const isEmailNotConfirmed = errorMessage.includes('email') && errorMessage.includes('confirm');
      
      if (isEmailNotConfirmed) {
        // Show success message instead of redirecting
        setShowSuccessMessage(true);
        setRegistrationEmail(formData.email);
        return;
      }
      
      toast({
        title: t('loginFailed'),
        description: errorMessage || t('loginFailedDescription'),
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDismissSuccess = () => {
    setShowSuccessMessage(false);
  };

  // Clean up debug code
  console.log("🎨 LoginPage render - showSuccessMessage:", showSuccessMessage, "registrationEmail:", registrationEmail);
  
  return (
    <MainLayout showFooter={false}>
      <div className="flex items-center justify-center min-h-[80vh] py-12 px-4">
        <div className="w-full max-w-md mx-auto space-y-6">
          {/* Success Banner - Positioned above login form */}
          {showSuccessMessage && (
            <div className="bg-green-500 text-white px-6 py-4 rounded-xl shadow-lg">
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-6 h-6 flex-shrink-0 mt-0.5" />
                <div className="flex-1">
                  <p className="text-sm text-green-100 leading-relaxed">
                    Welkom bij VillaWise! 🎉 We hebben een bevestigingsmail gestuurd naar {registrationEmail}. Controleer je inbox en klik op de activatielink om je registratie af te ronden.
                  </p>
                </div>
                <button
                  onClick={handleDismissSuccess}
                  className="text-white hover:text-green-200 transition-colors flex-shrink-0"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
            </div>
          )}
        
        <Card className="w-full max-w-md mx-auto bg-white/95 backdrop-blur-sm border shadow-xl">
          <CardHeader className="text-center relative">
            <Link href="/">
              <Button
                variant="ghost"
                size="sm"
                className="absolute left-0 top-0 h-8 w-8 p-0 text-gray-500 hover:text-gray-700"
              >
                <ArrowLeft className="h-4 w-4" />
              </Button>
            </Link>
            <CardTitle className="text-2xl font-bold text-gray-900">
              {t('title')}
            </CardTitle>
            <p className="text-gray-600 mt-2">
              {t('subtitle')}
            </p>
          </CardHeader>
          <CardContent className="space-y-6">
            <SocialLoginButtons />
            
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="bg-white px-2 text-gray-500">{t('orContinueWith')}</span>
              </div>
            </div>
            
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">{t('emailLabel')}</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  placeholder={t('emailPlaceholder')}
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                  disabled={isLoading}
                  className="h-12"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="password">{t('passwordLabel')}</Label>
                <PasswordInput
                  id="password"
                  name="password"
                  placeholder={t('passwordPlaceholder')}
                  value={formData.password}
                  onChange={handleInputChange}
                  required
                  disabled={isLoading}
                  className="h-12"
                />
              </div>
              
              <div className="flex items-center justify-between">
                <RememberMeCheckbox
                  checked={rememberMe}
                  onCheckedChange={setRememberMe}
                  disabled={isLoading}
                />
                <Link href="/forgot-password">
                  <Button variant="link" className="p-0 h-auto text-sm">
                    {t('forgotPassword')}
                  </Button>
                </Link>
              </div>
              
              <Button
                type="submit"
                className="w-full h-12"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {t('signingIn')}
                  </>
                ) : (
                  t('signInButton')
                )}
              </Button>
            </form>
            
            <div className="text-center">
              <p className="text-sm text-gray-600">
                {t('dontHaveAccount')}{' '}
                <Link href="/register">
                  <Button variant="link" className="p-0 h-auto text-sm">
                    {t('signUp')}
                  </Button>
                </Link>
              </p>
            </div>
          </CardContent>
          </Card>
        </div>
      </div>
    </MainLayout>
  );
}