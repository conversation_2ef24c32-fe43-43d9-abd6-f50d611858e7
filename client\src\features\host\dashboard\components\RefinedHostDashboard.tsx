import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

import { useTranslations } from '@/lib/translations';
import { 
  CalendarDays, 
  MapPin, 
  Users, 
  DollarSign, 
  TrendingUp, 
  Settings,
  HelpCircle,
  BarChart3,
  CheckCircle,
  Clock,
  Plus,
  Eye,
  MessageSquare,
  ClipboardList,
  Package
} from 'lucide-react';

interface RefinedHostDashboardProps {
  hostProperties: any[];
  bookings: any[];
  hostMessages: any[];
  user: any;
}

const RefinedHostDashboard: React.FC<RefinedHostDashboardProps> = ({
  hostProperties,
  bookings,
  hostMessages,
  user
}) => {
  const t = useTranslations('hostDashboard');
  const [activeTab, setActiveTab] = useState('dashboard');

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const totalEarnings = bookings.reduce((sum, booking) => sum + (booking.total_amount || 0), 0);
  const activeBookings = bookings.filter(b => b.status === 'confirmed' || b.status === 'active').length;
  const occupancyRate = hostProperties.length > 0 ? 
    ((activeBookings / hostProperties.length) * 100).toFixed(1) : 0;

  const NavigationSidebar = () => (
    <div className="w-64 bg-white border-r min-h-[calc(100vh-64px)]">
      <div className="p-6">
        <h1 className="text-2xl font-bold text-gray-900">{t('title')}</h1>
        <p className="text-gray-600 mt-2">{t('greeting')}</p>
      </div>
      
      <nav className="px-4 space-y-2">
        <Button 
          variant={activeTab === 'dashboard' ? 'default' : 'ghost'}
          className="w-full justify-start"
          onClick={() => setActiveTab('dashboard')}
        >
          <BarChart3 className="h-4 w-4 mr-2" />
          {t('navigation.dashboard')}
        </Button>
        
        <Button 
          variant={activeTab === 'bookings' ? 'default' : 'ghost'}
          className="w-full justify-start"
          onClick={() => setActiveTab('bookings')}
        >
          <CalendarDays className="h-4 w-4 mr-2" />
          {t('navigation.bookings')}
        </Button>
        
        <Button 
          variant={activeTab === 'properties' ? 'default' : 'ghost'}
          className="w-full justify-start"
          onClick={() => setActiveTab('properties')}
        >
          <MapPin className="h-4 w-4 mr-2" />
          {t('navigation.properties')}
        </Button>
        
        <Button 
          variant={activeTab === 'guests' ? 'default' : 'ghost'}
          className="w-full justify-start"
          onClick={() => setActiveTab('guests')}
        >
          <Users className="h-4 w-4 mr-2" />
          {t('navigation.guests')}
        </Button>
        
        <Button 
          variant={activeTab === 'earnings' ? 'default' : 'ghost'}
          className="w-full justify-start"
          onClick={() => setActiveTab('earnings')}
        >
          <DollarSign className="h-4 w-4 mr-2" />
          {t('navigation.earnings')}
        </Button>
        
        <Button 
          variant={activeTab === 'analytics' ? 'default' : 'ghost'}
          className="w-full justify-start"
          onClick={() => setActiveTab('analytics')}
        >
          <TrendingUp className="h-4 w-4 mr-2" />
          {t('navigation.analytics')}
        </Button>
        
        <Button 
          variant={activeTab === 'tasks' ? 'default' : 'ghost'}
          className="w-full justify-start"
          onClick={() => setActiveTab('tasks')}
        >
          <ClipboardList className="h-4 w-4 mr-2" />
          {t('navigation.tasks')}
        </Button>
        
        <Button 
          variant={activeTab === 'inventory' ? 'default' : 'ghost'}
          className="w-full justify-start"
          onClick={() => setActiveTab('inventory')}
        >
          <Package className="h-4 w-4 mr-2" />
          {t('navigation.inventory')}
        </Button>
        
        <Button 
          variant={activeTab === 'settings' ? 'default' : 'ghost'}
          className="w-full justify-start"
          onClick={() => setActiveTab('settings')}
        >
          <Settings className="h-4 w-4 mr-2" />
          {t('navigation.settings')}
        </Button>
        
        <Button 
          variant={activeTab === 'help' ? 'default' : 'ghost'}
          className="w-full justify-start"
          onClick={() => setActiveTab('help')}
        >
          <HelpCircle className="h-4 w-4 mr-2" />
          {t('navigation.help')}
        </Button>
      </nav>
    </div>
  );

  const DashboardContent = () => (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">{t('greeting')}</h1>
        <p className="text-gray-600 mt-2">{t('description')}</p>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card className="bg-primary/5 border-primary/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-primary">{t('overview.totalEarnings')}</p>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(totalEarnings)}</p>
              </div>
              <div className="h-12 w-12 bg-primary/10 rounded-lg flex items-center justify-center">
                <DollarSign className="h-6 w-6 text-primary" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{t('overview.activeBookings')}</p>
                <p className="text-2xl font-bold text-gray-900">{activeBookings}</p>
              </div>
              <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <CalendarDays className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{t('overview.occupancyRate')}</p>
                <p className="text-2xl font-bold text-gray-900">{occupancyRate}%</p>
              </div>
              <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                <TrendingUp className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Bookings */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle className="flex items-center">
              <CalendarDays className="h-5 w-5 mr-2" />
              <span>{t('recentBookings.title')}</span>
            </CardTitle>
            <Button variant="outline" size="sm">
              {t('recentBookings.viewAll')}
            </Button>
          </CardHeader>
          <CardContent>
            {bookings.length > 0 ? (
              <div className="space-y-4">
                {bookings.slice(0, 3).map((booking: any) => (
                  <div key={booking.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                        <Users className="h-5 w-5 text-gray-500" />
                      </div>
                      <div>
                        <p className="font-medium">{booking.guest_name || 'Guest'}</p>
                        <p className="text-sm text-gray-600">
                          {formatDate(booking.check_in_date)} - {formatDate(booking.check_out_date)}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <Badge variant="outline" className={
                        booking.status === 'confirmed' ? 'bg-green-50 text-green-700' :
                        booking.status === 'pending' ? 'bg-yellow-50 text-yellow-700' :
                        'bg-gray-50 text-gray-700'
                      }>
                        {booking.status === 'confirmed' ? t('recentBookings.confirmed') : 
                         booking.status === 'pending' ? t('recentBookings.pending') : 
                         booking.status}
                      </Badge>
                      <p className="text-sm text-gray-600 mt-1">
                        {booking.guest_count} {t('recentBookings.guests')}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-600">{t('recentBookings.noBookings')}</p>
                <Button variant="outline" className="mt-2">
                  {t('recentBookings.startExploring')}
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Properties */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle className="flex items-center">
              <MapPin className="h-5 w-5 mr-2" />
              <span>{t('properties.title')}</span>
            </CardTitle>
            <Button variant="outline" size="sm">
              {t('properties.viewAll')}
            </Button>
          </CardHeader>
          <CardContent>
            {hostProperties.length > 0 ? (
              <div className="space-y-4">
                {hostProperties.slice(0, 3).map((property: any) => (
                  <div key={property.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                        <MapPin className="h-5 w-5 text-gray-500" />
                      </div>
                      <div>
                        <p className="font-medium">{property.title}</p>
                        <p className="text-sm text-gray-600">{property.location}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <Badge variant="outline" className="bg-blue-50 text-blue-700">
                        {bookings.filter((b: any) => b.property_id === property.id).length} {t('properties.bookings')}
                      </Badge>
                      <Button variant="outline" size="sm" className="mt-1">
                        <Eye className="h-4 w-4 mr-1" />
                        View
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-600">{t('properties.noProperties')}</p>
                <Button variant="outline" className="mt-2">
                  {t('properties.addProperty')}
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );

  const BookingsContent = () => (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-semibold text-gray-900">{t('navigation.bookings')}</h2>
        <Button variant="outline" className="mt-4">
          {t('recentBookings.newBooking')}
        </Button>
      </div>

      {bookings.length > 0 ? (
        <div className="space-y-4">
          {bookings.map((booking: any) => (
            <Card key={booking.id}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                      <Users className="h-6 w-6 text-gray-500" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-lg">{booking.guest_name || 'Guest'}</h3>
                      <p className="text-gray-600">{booking.property?.title || 'Property'}</p>
                      <p className="text-sm text-gray-500">
                        {formatDate(booking.check_in_date)} - {formatDate(booking.check_out_date)}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <Badge variant="outline" className={
                      booking.status === 'confirmed' ? 'bg-green-50 text-green-700' :
                      booking.status === 'pending' ? 'bg-yellow-50 text-yellow-700' :
                      'bg-gray-50 text-gray-700'
                    }>
                      {booking.status === 'confirmed' ? t('recentBookings.confirmed') : 
                       booking.status === 'pending' ? t('recentBookings.pending') : 
                       booking.status}
                    </Badge>
                    <p className="text-sm text-gray-600 mt-1">
                      {booking.guest_count} {t('recentBookings.guests')} • {formatCurrency(booking.total_amount)}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <CalendarDays className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600">{t('recentBookings.noBookings')}</p>
        </div>
      )}
    </div>
  );

  const PropertiesContent = () => (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-semibold text-gray-900">{t('properties.title')}</h2>
        <Button variant="outline" onClick={() => window.location.href = '/host/add-property'}>
          <Plus className="h-4 w-4 mr-2" />
          {t('properties.newProperty')}
        </Button>
      </div>

      {hostProperties.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {hostProperties.map((property: any) => (
            <Card key={property.id} className="cursor-pointer hover:shadow-lg transition-shadow">
              <CardContent className="p-4">
                <div className="aspect-video bg-gray-200 rounded-lg mb-4 flex items-center justify-center">
                  <MapPin className="h-8 w-8 text-gray-400" />
                </div>
                <h3 className="font-semibold text-lg mb-2">{property.title}</h3>
                <p className="text-gray-600 text-sm mb-4">{property.location}</p>
                <div className="flex items-center justify-between">
                  <Badge variant="outline" className="bg-blue-50 text-blue-700">
                    {bookings.filter((b: any) => b.property_id === property.id).length} {t('properties.bookings')}
                  </Badge>
                  <Button variant="outline" size="sm">
                    <Eye className="h-4 w-4 mr-1" />
                    View
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600">{t('properties.noProperties')}</p>
          <Button variant="outline" className="mt-4">
            <Plus className="h-4 w-4 mr-2" />
            {t('properties.addProperty')}
          </Button>
        </div>
      )}
    </div>
  );

  return (
    <div className="flex min-h-[calc(100vh-64px)] bg-gray-50">
      <NavigationSidebar />
      <div className="flex-1">
        {activeTab === 'dashboard' && <DashboardContent />}
        {activeTab === 'bookings' && <BookingsContent />}
        {activeTab === 'properties' && <PropertiesContent />}
        {activeTab === 'guests' && (
          <div className="p-6">
            <div className="text-center py-12">
              <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-gray-900 mb-2">{t('navigation.guests')}</h2>
              <p className="text-gray-600">Guest management features coming soon!</p>
            </div>
          </div>
        )}
        {activeTab === 'earnings' && (
          <div className="p-6">
            <div className="text-center py-12">
              <DollarSign className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-gray-900 mb-2">{t('navigation.earnings')}</h2>
              <p className="text-gray-600">Earnings analytics coming soon!</p>
            </div>
          </div>
        )}
        {activeTab === 'analytics' && (
          <div className="p-6">
            <div className="text-center py-12">
              <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-gray-900 mb-2">{t('navigation.analytics')}</h2>
              <p className="text-gray-600">Analytics dashboard coming soon!</p>
            </div>
          </div>
        )}
        {activeTab === 'tasks' && (
          <div className="p-6">
            <div className="text-center py-12">
              <ClipboardList className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-gray-900 mb-2">{t('navigation.tasks')}</h2>
              <p className="text-gray-600">Task management coming soon!</p>
            </div>
          </div>
        )}
        {activeTab === 'inventory' && (
          <div className="p-6">
            <div className="text-center py-12">
              <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-gray-900 mb-2">{t('navigation.inventory')}</h2>
              <p className="text-gray-600">Inventory management coming soon!</p>
            </div>
          </div>
        )}
        {activeTab === 'settings' && (
          <div className="p-6">
            <div className="text-center py-12">
              <Settings className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-gray-900 mb-2">{t('navigation.settings')}</h2>
              <p className="text-gray-600">Settings panel coming soon!</p>
            </div>
          </div>
        )}
        {activeTab === 'help' && (
          <div className="p-6">
            <div className="text-center py-12">
              <HelpCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-gray-900 mb-2">{t('navigation.help')}</h2>
              <p className="text-gray-600">Help & support coming soon!</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default RefinedHostDashboard;