import { NextFunction, Request, Response } from "express";
import { storage } from "../../storage";
import { supabase, supabaseAdmin } from "../../supabase";

export class AuthController {
  async register(req: Request, res: Response) {
    try {
      const { email, password, username, firstName, lastName, isHost } =
        req.body;

      if (!email || !password || !username) {
        return res.status(400).json({
          success: false,
          message: "Email, password, and username are required",
          messageKey: "auth.requiredFields",
        });
      }

      // Check if user already exists with this email
      const existingUser = await storage.getUserByEmail(email);
      if (existingUser) {
        return res.status(400).json({
          success: false,
          message: "A user with this email already exists",
          messageKey: "auth.emailExists",
        });
      }

      // Check if username is already taken
      const existingUsername = await storage.getUserByUsername(username);
      if (existingUsername) {
        return res.status(400).json({
          success: false,
          message: "This username is already taken",
          messageKey: "auth.usernameExists",
        });
      }

      // Ensure Supabase Auth is available
      if (!supabase || !supabaseAdmin) {
        return res.status(500).json({
          success: false,
          message: "Authentication service unavailable",
        });
      }

      // Create user with Supabase Auth
      console.log("🔄 Creating user with Supabase Auth");

      try {
        const { data: authData, error: authError } =
          await supabaseAdmin.auth.admin.createUser({
            email,
            password,
            email_confirm: false, // Require email confirmation
            user_metadata: {
              username,
              first_name: firstName,
              last_name: lastName,
              is_host: isHost || false,
            },
          });

        if (authError) {
          console.error("❌ Supabase admin createUser error:", authError);
          throw authError;
        }

        if (!authData.user) {
          throw new Error("User creation failed - no user returned");
        }

        console.log("✅ User created in Supabase Auth:", authData.user.id);

        // Create corresponding user in our database (NO PASSWORD - handled by Supabase Auth)
        const newUser = await storage.createUser({
          id: authData.user.id,
          email,
          username,
          first_name: firstName,
          last_name: lastName,
          is_host: isHost || false,
        });

        console.log("✅ User created in database:", newUser.id);

        // Return success response with email confirmation message
        res.status(201).json({
          success: true,
          message:
            "Registration successful. Please check your email to confirm your account.",
          requiresEmailConfirmation: true,
          user: {
            id: newUser.id,
            email: newUser.email,
            username: newUser.username,
            first_name: newUser.first_name,
            last_name: newUser.last_name,
            is_host: newUser.is_host,
            email_confirmed: false,
          },
        });
      } catch (error) {
        console.error("❌ Supabase Auth registration failed:", error);
        return res.status(500).json({
          success: false,
          message: "Failed to create user account",
        });
      }
    } catch (error) {
      console.error("Registration error:", error);
      res.status(500).json({
        success: false,
        message: "Internal server error",
      });
    }
  }

  async login(req: Request, res: Response, next: NextFunction) {
    try {
      const { email, password, rememberMe } = req.body;

      if (!email || !password) {
        return res.status(400).json({
          success: false,
          message: "Email and password are required",
        });
      }

      console.log("🔍 Attempting login for email:", email);

      // Ensure Supabase Auth is available
      if (!supabase) {
        return res.status(500).json({
          success: false,
          message: "Authentication service unavailable",
        });
      }

      try {
        const { data, error } = await supabase.auth.signInWithPassword({
          email,
          password,
        });

        if (error || !data.user) {
          console.log("❌ Supabase Auth login failed:", error?.message);
          console.log("🔍 Error details:", JSON.stringify(error, null, 2));

          // Check if it's an email not confirmed error (more robust check)
          const errorMessage = error?.message?.toLowerCase() || "";
          if (
            errorMessage.includes("email not confirmed") ||
            errorMessage.includes("email_not_confirmed")
          ) {
            console.log("🚨 Email not confirmed error detected");
            return res.status(401).json({
              success: false,
              message:
                "Please check your email and confirm your account before logging in.",
              errorCode: "EMAIL_NOT_CONFIRMED",
            });
          }

          return res.status(401).json({
            success: false,
            message: "Invalid login credentials",
          });
        }

        console.log("✅ Supabase Auth login successful");

        // Get user data from our database
        const user = await storage.getUserByEmail(email);
        if (!user) {
          console.log("❌ User not found in database, but exists in Supabase");
          return res.status(404).json({
            success: false,
            message: "User profile not found",
          });
        }

        return res.json({
          success: true,
          message: "Login successful",
          user: user,
          session: data.session,
          rememberMe: rememberMe || false,
        });
      } catch (supabaseError) {
        console.log("❌ Supabase Auth error:", supabaseError);
        return res.status(500).json({
          success: false,
          message: "Authentication service error",
        });
      }
    } catch (error) {
      console.error("Login error:", error);
      res.status(500).json({
        success: false,
        message: "Internal server error",
      });
    }
  }

  async logout(req: Request, res: Response) {
    try {
      if (!supabase) {
        return res.status(500).json({
          success: false,
          message: "Authentication service unavailable",
        });
      }

      const { error } = await supabase.auth.signOut();

      if (error) {
        return res.status(500).json({
          success: false,
          message: error.message,
        });
      }

      res.json({
        success: true,
        message: "Logout successful",
      });
    } catch (error) {
      console.error("Logout error:", error);
      res.status(500).json({
        success: false,
        message: "Internal server error",
      });
    }
  }

  async getCurrentUser(req: Request, res: Response) {
    try {
      const authHeader = req.headers.authorization;

      console.log("🔍 getCurrentUser called:");
      console.log("  - Authorization header present:", !!authHeader);
      console.log(
        "  - Authorization header:",
        authHeader ? `${authHeader.substring(0, 30)}...` : "null"
      );
      console.log("  - Request headers:", Object.keys(req.headers));

      if (!authHeader) {
        console.log("❌ No authorization header found");
        return res.status(401).json({
          success: false,
          message: "No authorization header",
        });
      }

      const token = authHeader.replace("Bearer ", "");
      console.log("🔑 Token extracted:");
      console.log("  - Token length:", token?.length);
      console.log(
        "  - Token preview:",
        token ? `${token.substring(0, 30)}...` : "null"
      );

      if (!supabase) {
        // Fallback for development without Supabase
        if (token.startsWith("test_oauth_token_")) {
          // Return a mock user for development
          const mockUser = {
            id: "a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11",
            email: "<EMAIL>",
            username: "testuser",
            first_name: "Test",
            last_name: "User",
            phone: null,
            is_host: false,
            avatar_url: null,
            oauth_provider: "development",
            oauth_id: "a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11",
            locale: null,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          };

          return res.json({
            success: true,
            user: mockUser,
          });
        }

        return res.status(500).json({
          success: false,
          message: "Authentication service unavailable",
        });
      }

      const {
        data: { user },
        error,
      } = await supabase.auth.getUser(token);

      if (error || !user) {
        return res.status(401).json({
          success: false,
          message: "Invalid token",
        });
      }

      // Get user data from our database by ID first, then by email
      console.log(
        `OAuth getCurrentUser: Looking up user by ID: ${user.id} and email: ${user.email}`
      );
      let dbUser = await storage.getUser(user.id);

      if (!dbUser) {
        // Also check by email in case user exists with different ID
        dbUser = await storage.getUserByEmail(user.email!);
      }

      // If user doesn't exist in our database (OAuth user), create the record
      if (!dbUser) {
        console.log(
          `OAuth getCurrentUser: User NOT found in database, creating new record for: ${user.email}`
        );
        console.log("OAuth user metadata:", user.user_metadata);
        console.log("User first login - creating profile in local database");

        // Extract user data from Supabase user object with enhanced Google profile support
        const userData = {
          id: user.id,
          email: user.email!,
          username:
            user.user_metadata?.username ||
            user.user_metadata?.preferred_username ||
            user.email!.split("@")[0],
          first_name:
            user.user_metadata?.first_name ||
            user.user_metadata?.given_name ||
            user.user_metadata?.name?.split(" ")[0] ||
            null,
          last_name:
            user.user_metadata?.last_name ||
            user.user_metadata?.family_name ||
            user.user_metadata?.name?.split(" ").slice(1).join(" ") ||
            null,
          phone:
            user.user_metadata?.phone ||
            user.user_metadata?.phone_number ||
            null,
          is_host: false, // Default for OAuth users
          avatar_url:
            user.user_metadata?.avatar_url ||
            user.user_metadata?.picture ||
            null,
          oauth_provider: user.app_metadata?.provider || "google",
          oauth_id: user.user_metadata?.sub || user.id,
          locale: user.user_metadata?.locale || null,
        };

        try {
          dbUser = await storage.createUser(userData);
          console.log(
            `User record created successfully for: ${user.email} (${user.id})`
          );
        } catch (createError) {
          console.error("Failed to create user record:", createError);
          return res.status(500).json({
            success: false,
            message: "Failed to create user profile",
          });
        }
      } else {
        console.log(
          `OAuth getCurrentUser: User FOUND in database for: ${user.email}`
        );
      }

      res.json({
        success: true,
        user: dbUser,
      });
    } catch (error) {
      console.error("Get current user error:", error);
      res.status(500).json({
        success: false,
        message: "Internal server error",
      });
    }
  }

  // Helper method to construct redirect URL from request origin
  private getRedirectUrl(req: Request): string {
    // Always use the actual request origin for maximum compatibility
    // This ensures users stay on the same domain they started from
    
    // Auto-detect from request headers with comprehensive fallback logic
    const protocol =
      req.get("x-forwarded-proto") ||
      req.get("x-forwarded-protocol") ||
      req.protocol ||
      "https";

    const host =
      req.get("x-forwarded-host") ||
      req.get("host") ||
      req.get("x-original-host") ||
      "localhost:5000";

    // Keep the port if it's not standard ports for the protocol
    // This preserves development environments like Replit with custom ports
    const cleanHost = host.replace(/:(80|443)$/, "");

    const redirectUrl = `${protocol}://${cleanHost}`;
    
    console.log(`🔗 Detected redirect URL: ${redirectUrl}`);
    console.log(`📋 Request headers: protocol=${protocol}, host=${host}`);
    
    return redirectUrl;
  }

  // Debug endpoint to test redirect URL detection
  async testRedirectUrl(req: Request, res: Response) {
    try {
      const redirectUrl = this.getRedirectUrl(req);

      res.json({
        success: true,
        redirectUrl,
        detectionMethod: "origin-based",
        requestHeaders: {
          "x-forwarded-proto": req.get("x-forwarded-proto"),
          "x-forwarded-host": req.get("x-forwarded-host"),
          host: req.get("host"),
          protocol: req.protocol,
          origin: req.get("origin"),
          referer: req.get("referer"),
        },
      });
    } catch (error) {
      console.error("Test redirect URL error:", error);
      res.status(500).json({
        success: false,
        message: "Internal server error",
      });
    }
  }

  // Social OAuth login initiation
  async loginWithGoogle(req: Request, res: Response) {
    try {
      const redirectUrl = this.getRedirectUrl(req);

      console.log(`OAuth redirect URL: ${redirectUrl}`);
      console.log(
        `Request headers - Host: ${req.get("host")}, Protocol: ${
          req.get("x-forwarded-proto") || req.protocol
        }`
      );

      if (!supabase) {
        return res.status(500).json({
          success: false,
          message: "Authentication service unavailable",
        });
      }

      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: "google",
        options: {
          redirectTo: `${redirectUrl}/auth/callback?origin=${encodeURIComponent(
            redirectUrl
          )}`, // Include origin domain
          queryParams: {
            access_type: "offline",
            prompt: "consent",
          },
        },
      });

      if (error) {
        return res.status(400).json({
          success: false,
          message: error.message,
        });
      }

      res.json({
        success: true,
        url: data.url,
        message: "Redirect to Google OAuth",
      });
    } catch (error) {
      console.error("Google OAuth error:", error);
      res.status(500).json({
        success: false,
        message: "Internal server error",
      });
    }
  }

  async loginWithFacebook(req: Request, res: Response) {
    try {
      const redirectUrl = this.getRedirectUrl(req);

      console.log(`Facebook OAuth redirect URL: ${redirectUrl}/auth/callback`);
      console.log(
        `Request headers - Host: ${req.get("host")}, Protocol: ${
          req.get("x-forwarded-proto") || req.protocol
        }`
      );

      if (!supabase) {
        return res.status(500).json({
          success: false,
          message: "Database service unavailable",
        });
      }

      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: "facebook",
        options: {
          redirectTo: `${redirectUrl}/auth/callback?origin=${encodeURIComponent(
            redirectUrl
          )}`, // Include origin domain
          scopes: "email",
        },
      });

      if (error) {
        return res.status(400).json({
          success: false,
          message: error.message,
        });
      }

      res.json({
        success: true,
        url: data.url,
        message: "Redirect to Facebook OAuth",
      });
    } catch (error) {
      console.error("Facebook OAuth error:", error);
      res.status(500).json({
        success: false,
        message: "Internal server error",
      });
    }
  }

  // OAuth callback handler
  async handleOAuthCallback(req: Request, res: Response) {
    console.log("🔄 OAuth Callback Handler Started");
    console.log("Query parameters:", req.query);

    // Extract origin for proper domain redirects
    const { origin } = req.query;
    const baseUrl = origin
      ? decodeURIComponent(origin as string)
      : this.getRedirectUrl(req);

    try {
      const { access_token, refresh_token } = req.query;

      if (!access_token) {
        console.log("❌ No access token in callback");
        return res.redirect(`${baseUrl}/?error=auth_failed`);
      }

      console.log(
        "🔑 Access token received (length):",
        (access_token as string).length
      );
      console.log("🔑 Refresh token received:", refresh_token ? "Yes" : "No");

      if (!supabase) {
        console.log("❌ Supabase client unavailable in callback");
        return res.redirect(`${baseUrl}/?error=service_unavailable`);
      }

      console.log("📡 Getting user data from access token...");
      const {
        data: { user },
        error,
      } = await supabase.auth.getUser(access_token as string);

      if (error || !user) {
        console.log(
          "❌ Failed to get user from token:",
          error?.message || "No user returned"
        );
        return res.redirect(`${baseUrl}/?error=user_not_found`);
      }

      console.log("✅ OAuth Callback - Google Authentication Result:");
      console.log("==========================================");
      console.log("User ID:", user.id);
      console.log("Email:", user.email);
      console.log("Email Verified:", user.email_confirmed_at ? "Yes" : "No");
      console.log("Provider:", user.app_metadata?.provider);
      console.log("Created At:", user.created_at);
      console.log("Updated At:", user.updated_at);
      console.log("Last Sign In:", user.last_sign_in_at);
      console.log("");
      console.log("🔍 User Metadata (Google Profile Data):");
      console.log("Full Name:", user.user_metadata?.name);
      console.log("Given Name:", user.user_metadata?.given_name);
      console.log("Family Name:", user.user_metadata?.family_name);
      console.log("Picture URL:", user.user_metadata?.picture);
      console.log("Locale:", user.user_metadata?.locale);
      console.log("Email Verified:", user.user_metadata?.email_verified);
      console.log("Sub (Google ID):", user.user_metadata?.sub);
      console.log("");
      console.log("📋 App Metadata:");
      console.log("Provider:", user.app_metadata?.provider);
      console.log("Providers:", user.app_metadata?.providers);
      console.log("");
      console.log("🔍 Raw User Object:");
      console.log(JSON.stringify(user, null, 2));
      console.log("==========================================");

      // Enhanced dual-check pattern with better error handling (callback)
      console.log("🔍 Checking database for existing user (callback)...");
      console.log("🔍 Looking for user by ID:", user.id);
      console.log("🔍 Looking for user by email:", user.email);

      let dbUser = null;

      // First check by ID
      try {
        dbUser = await storage.getUser(user.id);
        if (dbUser) {
          console.log("✅ User found by ID (callback):", dbUser.email);
        }
      } catch (error) {
        console.log("⚠️ User lookup by ID failed (callback):", error);
      }

      // If not found by ID, check by email
      if (!dbUser) {
        try {
          dbUser = await storage.getUserByEmail(user.email!);
          if (dbUser) {
            console.log("✅ User found by email (callback):", dbUser.email);
          }
        } catch (error) {
          console.log("⚠️ User lookup by email failed (callback):", error);
        }
      }

      // If user still not found, create new profile
      if (!dbUser) {
        console.log(
          "👤 User not found in database, creating new profile (callback)..."
        );

        // Create user record from OAuth data with complete field mapping
        const userData = {
          id: user.id, // Use Supabase Auth user ID as primary key
          email: user.email!,
          password: "oauth_managed", // Placeholder for OAuth users
          username:
            user.user_metadata?.preferred_username ||
            user.user_metadata?.username ||
            user.email!.split("@")[0] + "_" + Date.now(),
          first_name:
            user.user_metadata?.given_name ||
            user.user_metadata?.first_name ||
            user.user_metadata?.full_name?.split(" ")[0] ||
            null,
          last_name:
            user.user_metadata?.family_name ||
            user.user_metadata?.last_name ||
            user.user_metadata?.full_name?.split(" ").slice(1).join(" ") ||
            null,
          phone:
            user.user_metadata?.phone_number ||
            user.user_metadata?.phone ||
            null,
          is_host: false,
          avatar_url:
            user.user_metadata?.picture ||
            user.user_metadata?.avatar_url ||
            null,
          oauth_provider: user.app_metadata?.provider || "google",
          oauth_id: user.user_metadata?.sub || user.id,
          locale: user.user_metadata?.locale || null,
        };

        console.log("📝 Prepared user data for database (callback):");
        console.log({
          email: userData.email,
          username: userData.username,
          first_name: userData.first_name,
          last_name: userData.last_name,
          avatar_url: userData.avatar_url,
          oauth_provider: userData.oauth_provider,
          oauth_id: userData.oauth_id,
          locale: userData.locale,
        });

        try {
          console.log("💾 Creating user in database (callback)...");
          dbUser = await storage.createUser(userData);
          console.log("✅ OAuth callback: User created successfully:", {
            email: dbUser.email,
            oauth_provider: userData.oauth_provider,
            oauth_id: userData.oauth_id,
          });
        } catch (dbError: any) {
          console.error("❌ Failed to create OAuth user in callback:", dbError);

          // If it's a duplicate key error, try to fetch the existing user
          if (
            dbError &&
            typeof dbError === "object" &&
            "code" in dbError &&
            dbError.code === "23505"
          ) {
            console.log(
              "🔄 Duplicate key error in callback - attempting to fetch existing user..."
            );
            try {
              dbUser = await storage.getUser(user.id);
              if (!dbUser) {
                dbUser = await storage.getUserByEmail(user.email!);
              }
              if (dbUser) {
                console.log(
                  "✅ Successfully retrieved existing user after duplicate error (callback):",
                  dbUser.email
                );
              } else {
                console.error(
                  "❌ Could not retrieve existing user after duplicate error (callback)"
                );
                return res.redirect(
                  `${baseUrl}/?error=profile_creation_failed`
                );
              }
            } catch (fetchError) {
              console.error(
                "❌ Failed to fetch existing user after duplicate error (callback):",
                fetchError
              );
              return res.redirect(`${baseUrl}/?error=profile_creation_failed`);
            }
          } else {
            // Other database errors
            return res.redirect(`${baseUrl}/?error=profile_creation_failed`);
          }
        }
      } else {
        console.log("✅ User found in database (callback):", dbUser.email);
      }

      // Set session tokens in cookies for frontend
      console.log("🍪 Setting session cookies...");
      res.cookie("sb_access_token", access_token, {
        httpOnly: false, // Allow frontend to read
        secure: process.env.NODE_ENV === "production",
        sameSite: "lax",
        maxAge: 60 * 60 * 1000, // 1 hour
      });

      if (refresh_token) {
        res.cookie("sb_refresh_token", refresh_token, {
          httpOnly: false,
          secure: process.env.NODE_ENV === "production",
          sameSite: "lax",
          maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
        });
      }

      // Redirect to home with tokens in the URL fragment (for frontend OAuth flow)
      const tokenParams = new URLSearchParams();
      tokenParams.append("access_token", access_token as string);
      if (refresh_token) {
        tokenParams.append("refresh_token", refresh_token as string);
      }

      // Role-based redirect after OAuth login with tokens in URL fragment
      const redirectPath = dbUser.is_host
        ? `/owner/dashboard#${tokenParams.toString()}`
        : `/#${tokenParams.toString()}`;

      const fullRedirectUrl = `${baseUrl}${redirectPath}`;
      console.log("🔄 Redirecting to:", fullRedirectUrl);
      res.redirect(fullRedirectUrl);
    } catch (error) {
      console.error("❌ OAuth callback error:", error);
      res.redirect(`${baseUrl}/?error=callback_failed`);
    }
  }

  // New method for refreshing tokens
  async refreshToken(req: Request, res: Response) {
    try {
      const { refresh_token } = req.body;

      if (!refresh_token) {
        return res.status(400).json({
          success: false,
          message: "Refresh token is required",
        });
      }

      if (!supabase) {
        return res.status(500).json({
          success: false,
          message: "Database service unavailable",
        });
      }

      const { data, error } = await supabase.auth.refreshSession({
        refresh_token,
      });

      if (error) {
        return res.status(401).json({
          success: false,
          message: error.message,
        });
      }

      res.json({
        success: true,
        session: data.session,
      });
    } catch (error) {
      console.error("Token refresh error:", error);
      res.status(500).json({
        success: false,
        message: "Internal server error",
      });
    }
  }

  // OAuth user creation/verification endpoint (for frontend OAuth flow)
  async createOrGetOAuthUser(req: Request, res: Response) {
    console.log("🔐 OAuth User Validation Flow Started");
    console.log("Request method:", req.method);
    console.log("Request URL:", req.originalUrl);
    console.log("Request headers:", Object.keys(req.headers));
    console.log("Authorization header present:", !!req.headers.authorization);

    try {
      const authHeader = req.headers.authorization;
      if (!authHeader) {
        console.log("❌ No authorization header provided");
        console.log("Available headers:", req.headers);
        return res.status(401).json({
          success: false,
          message: "No authorization header",
        });
      }

      const token = authHeader.replace("Bearer ", "");
      console.log("🔑 Token received (length):", token.length);
      console.log("🔑 Token prefix:", token.substring(0, 20) + "...");

      if (!supabase) {
        console.log("❌ Supabase client unavailable");
        return res.status(500).json({
          success: false,
          message: "Authentication service unavailable",
        });
      }

      console.log("📡 Validating token with Supabase...");
      const {
        data: { user },
        error,
      } = await supabase.auth.getUser(token);

      if (error || !user) {
        console.log(
          "❌ Token validation failed:",
          error?.message || "No user returned"
        );
        console.log("❌ Full Supabase error:", error);
        return res.status(401).json({
          success: false,
          message: "Invalid token",
          error: error?.message || "Token validation failed",
        });
      }

      console.log("✅ Google OAuth Authentication Result:");
      console.log("==========================================");
      console.log("User ID:", user.id);
      console.log("Email:", user.email);
      console.log("Email Verified:", user.email_confirmed_at ? "Yes" : "No");
      console.log("Provider:", user.app_metadata?.provider);
      console.log("Created At:", user.created_at);
      console.log("Updated At:", user.updated_at);
      console.log("Last Sign In:", user.last_sign_in_at);
      console.log("");
      console.log("🔍 User Metadata (Google Profile Data):");
      console.log("Full Name:", user.user_metadata?.name);
      console.log("Given Name:", user.user_metadata?.given_name);
      console.log("Family Name:", user.user_metadata?.family_name);
      console.log("Picture URL:", user.user_metadata?.picture);
      console.log("Locale:", user.user_metadata?.locale);
      console.log("Email Verified:", user.user_metadata?.email_verified);
      console.log("Sub (Google ID):", user.user_metadata?.sub);
      console.log("");
      console.log("📋 App Metadata:");
      console.log("Provider:", user.app_metadata?.provider);
      console.log("Providers:", user.app_metadata?.providers);
      console.log("");
      console.log("🔍 Raw User Object:");
      console.log(JSON.stringify(user, null, 2));
      console.log("==========================================");

      // Enhanced dual-check pattern with better error handling
      console.log("🔍 Checking database for existing user...");
      console.log("🔍 Looking for user by ID:", user.id);
      console.log("🔍 Looking for user by email:", user.email);

      let dbUser = null;

      // First check by ID
      try {
        dbUser = await storage.getUser(user.id);
        if (dbUser) {
          console.log("✅ User found by ID:", dbUser.email);
        }
      } catch (error) {
        console.log("⚠️ User lookup by ID failed:", error);
      }

      // If not found by ID, check by email
      if (!dbUser) {
        try {
          dbUser = await storage.getUserByEmail(user.email!);
          if (dbUser) {
            console.log("✅ User found by email:", dbUser.email);
          }
        } catch (error) {
          console.log("⚠️ User lookup by email failed:", error);
        }
      }

      // If user still not found, create new profile
      if (!dbUser) {
        console.log("👤 User not found in database, creating new profile...");

        // Enhanced user creation with comprehensive Google profile data
        const userData = {
          id: user.id, // Use Supabase Auth user ID as primary key
          email: user.email!,
          username:
            user.user_metadata?.preferred_username ||
            user.user_metadata?.username ||
            user.email!.split("@")[0],
          first_name:
            user.user_metadata?.given_name ||
            user.user_metadata?.first_name ||
            user.user_metadata?.name?.split(" ")[0] ||
            null,
          last_name:
            user.user_metadata?.family_name ||
            user.user_metadata?.last_name ||
            user.user_metadata?.name?.split(" ").slice(1).join(" ") ||
            null,
          phone:
            user.user_metadata?.phone_number ||
            user.user_metadata?.phone ||
            null,
          is_host: false,
          avatar_url:
            user.user_metadata?.picture ||
            user.user_metadata?.avatar_url ||
            null,
          oauth_provider: user.app_metadata?.provider || "google",
          oauth_id: user.user_metadata?.sub || user.id,
          locale: user.user_metadata?.locale || null,
        };

        console.log("📝 Prepared user data for database:");
        console.log({
          email: userData.email,
          username: userData.username,
          first_name: userData.first_name,
          last_name: userData.last_name,
          avatar_url: userData.avatar_url,
          oauth_provider: userData.oauth_provider,
          oauth_id: userData.oauth_id,
          locale: userData.locale,
        });

        try {
          console.log("💾 Creating user in database...");
          dbUser = await storage.createUser(userData);
          console.log(
            "✅ User successfully created in database:",
            dbUser.email
          );
        } catch (dbError: any) {
          console.error("❌ Failed to create OAuth user in database:", dbError);

          // If it's a duplicate key error, try to fetch the existing user
          if (
            dbError &&
            typeof dbError === "object" &&
            "code" in dbError &&
            dbError.code === "23505"
          ) {
            console.log(
              "🔄 Duplicate key error - attempting to fetch existing user..."
            );
            try {
              dbUser = await storage.getUser(user.id);
              if (!dbUser) {
                dbUser = await storage.getUserByEmail(user.email!);
              }
              if (dbUser) {
                console.log(
                  "✅ Successfully retrieved existing user after duplicate error:",
                  dbUser.email
                );
              } else {
                console.error(
                  "❌ Could not retrieve existing user after duplicate error"
                );
                return res.status(500).json({
                  success: false,
                  message: "Failed to retrieve existing user profile",
                });
              }
            } catch (fetchError) {
              console.error(
                "❌ Failed to fetch existing user after duplicate error:",
                fetchError
              );
              return res.status(500).json({
                success: false,
                message: "Failed to resolve user profile conflict",
              });
            }
          } else {
            // Other database errors
            return res.status(500).json({
              success: false,
              message: "Failed to create user profile",
            });
          }
        }
      } else {
        console.log("✅ User found in database:", dbUser.email);
      }

      console.log("🎉 OAuth flow completed successfully for:", dbUser.email);
      res.json({
        success: true,
        user: dbUser,
        message: "OAuth user authenticated successfully",
      });
    } catch (error) {
      console.error("❌ OAuth user creation error:", error);
      res.status(500).json({
        success: false,
        message: "Internal server error",
      });
    }
  }
}

export const authController = new AuthController();
