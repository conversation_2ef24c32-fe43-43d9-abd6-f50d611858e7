import { InspirationData, inspirationDataSchema } from '../types';

class InspirationService {
  async getInspirations(): Promise<InspirationData> {
    // This would normally be an API call
    const data = {
      header: "Inspiratie voor je volgende reis",
      items: [
        {
          image: "https://images.unsplash.com/photo-1507525428034-b723cf961d3e?w=400&h=300&fit=crop&crop=center",
          alt: "Beach destination",
          title: "Beach Getaways",
          desc: "Ontspan aan de oceaan met prachtige kustgezichten"
        },
        {
          image: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop&crop=center", 
          alt: "Mountain destination",
          title: "Mountain Retreats",
          desc: "Ontsnap naar vredige berglandschappen"
        },
        {
          image: "https://images.unsplash.com/photo-1477959858617-67f85cf4f1df?w=400&h=300&fit=crop&crop=center",
          alt: "City destination", 
          title: "City Adventures",
          desc: "Verken levendige stedelijke cultuur en attracties"
        },
        {
          image: "https://images.unsplash.com/photo-1500382017468-9049fed747ef?w=400&h=300&fit=crop&crop=center",
          alt: "Countryside destination",
          title: "Countryside Escapes", 
          desc: "Ontdek rustige landelijke omgevingen en natuur"
        }
      ]
    };

    return inspirationDataSchema.parse(data);
  }
}

export const inspirationService = new InspirationService();