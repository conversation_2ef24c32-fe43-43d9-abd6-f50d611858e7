{"version": "0.2.0", "configurations": [{"name": "Debug Server", "type": "node", "request": "launch", "program": "${workspaceFolder}/server/index.ts", "env": {"NODE_ENV": "development"}, "runtimeArgs": ["--loader", "tsx/esm"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "skipFiles": ["<node_internals>/**"], "outputCapture": "std", "envFile": "${workspaceFolder}/.env"}, {"name": "Attach to Chrome", "type": "chrome", "request": "attach", "port": 9222, "webRoot": "${workspaceFolder}/client/src", "sourceMapPathOverrides": {"webpack:///src/*": "${webRoot}/*"}}, {"name": "Launch Chrome", "type": "chrome", "request": "launch", "url": "http://localhost:5000", "webRoot": "${workspaceFolder}/client/src", "sourceMapPathOverrides": {"webpack:///src/*": "${webRoot}/*"}}, {"name": "Debug TypeScript Check", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/.bin/tsc", "args": ["--noEmit", "--skip<PERSON><PERSON><PERSON><PERSON><PERSON>"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "skipFiles": ["<node_internals>/**"]}, {"name": "Debug Production Build", "type": "node", "request": "launch", "program": "${workspaceFolder}/scripts/build/build-production.js", "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "skipFiles": ["<node_internals>/**"], "env": {"NODE_ENV": "production"}}, {"name": "Test API Health", "type": "node", "request": "launch", "program": "${workspaceFolder}/scripts/test/test-api-health.js", "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "skipFiles": ["<node_internals>/**"]}], "compounds": [{"name": "Full Debug Setup", "configurations": ["Debug Server", "Launch Chrome"], "stopAll": true}]}