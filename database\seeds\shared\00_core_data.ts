/**
 * Core Data Seeder
 *
 * Seeds essential content properties and location data.
 * Safe for production use.
 */

export default {
  name: "00_core_data",
  description: "Essential content properties and base location data",
  environment: "shared" as const,
  order: 1,

  async execute(supabase: any): Promise<void> {
    console.log("   🌍 Seeding core geographic data...");

    // 1. Country codes (ISO 3166-1)
    console.log("   📍 Seeding country codes...");
    const countryCodes = [
      { id: 724, iso_alpha2: "ES", iso_alpha3: "ESP" }, // Spain
      { id: 250, iso_alpha2: "FR", iso_alpha3: "FRA" }, // France
      { id: 380, iso_alpha2: "IT", iso_alpha3: "ITA" }, // Italy
      { id: 620, iso_alpha2: "PT", iso_alpha3: "PRT" }, // Portugal
      { id: 276, iso_alpha2: "DE", iso_alpha3: "DEU" }, // Germany
      { id: 528, iso_alpha2: "NL", iso_alpha3: "NLD" }, // Netherlands
      { id: 826, iso_alpha2: "GB", iso_alpha3: "GBR" }, // United Kingdom
      { id: 840, iso_alpha2: "US", iso_alpha3: "USA" }, // United States
      { id: 124, iso_alpha2: "CA", iso_alpha3: "CAN" }, // Canada
    ];

    const { error: countryError } = await supabase
      .from("country_codes")
      .upsert(countryCodes, { onConflict: "id" });

    if (countryError) {
      throw new Error(`Failed to seed country codes: ${countryError.message}`);
    }

    // 2. Spanish regions
    console.log("   🏛️ Seeding Spanish regions...");
    const spanishRegions = [
      { country_id: 724, iso_code: "ES-AN", geonames_admin1_code: "AN" }, // Andalusia
      { country_id: 724, iso_code: "ES-VC", geonames_admin1_code: "VC" }, // Valencia
      { country_id: 724, iso_code: "ES-CT", geonames_admin1_code: "CT" }, // Catalonia
      { country_id: 724, iso_code: "ES-IB", geonames_admin1_code: "IB" }, // Balearic Islands
      { country_id: 724, iso_code: "ES-CN", geonames_admin1_code: "CN" }, // Canary Islands
      { country_id: 724, iso_code: "ES-MD", geonames_admin1_code: "MD" }, // Madrid
      { country_id: 724, iso_code: "ES-PV", geonames_admin1_code: "PV" }, // Basque Country
      { country_id: 724, iso_code: "ES-GA", geonames_admin1_code: "GA" }, // Galicia
      { country_id: 724, iso_code: "ES-AS", geonames_admin1_code: "AS" }, // Asturias
      { country_id: 724, iso_code: "ES-MC", geonames_admin1_code: "MC" }, // Murcia
    ];

    const { data: insertedRegions, error: regionError } = await supabase
      .from("region_codes")
      .upsert(spanishRegions, { onConflict: "iso_code" })
      .select("id, geonames_admin1_code");

    if (regionError) {
      throw new Error(`Failed to seed Spanish regions: ${regionError.message}`);
    }

    // 3. Tourism regions
    console.log("   🏖️ Seeding tourism regions...");
    const tourismRegions = [
      {
        country_id: 724,
        region_type: "coastal",
        coordinate_bounds: {
          lat_min: 41.6,
          lat_max: 42.3,
          lng_min: 2.8,
          lng_max: 3.3,
        },
        center_coordinates: `(41.8833,2.9167)`,
        popularity_score: 85,
      },
      {
        country_id: 724,
        region_type: "coastal",
        coordinate_bounds: {
          lat_min: 36.3,
          lat_max: 36.8,
          lng_min: -5.2,
          lng_max: -3.8,
        },
        center_coordinates: `(36.5,-4.5)`,
        popularity_score: 90,
      },
      {
        country_id: 724,
        region_type: "coastal",
        coordinate_bounds: {
          lat_min: 37.8,
          lat_max: 38.8,
          lng_min: -0.8,
          lng_max: -0.1,
        },
        center_coordinates: `(38.3,-0.45)`,
        popularity_score: 88,
      },
    ];

    const { error: tourismError } = await supabase
      .from("tourism_region_codes")
      .insert(tourismRegions);

    if (tourismError) {
      throw new Error(
        `Failed to seed tourism regions: ${tourismError.message}`
      );
    }

    // 4. Core translations
    console.log("   🗣️ Seeding core translations...");
    const coreTranslations = [
      // Spain translations
      {
        entity_type: "country",
        entity_id: 724,
        language_code: "en",
        text: "Spain",
        is_official: true,
      },
      {
        entity_type: "country",
        entity_id: 724,
        language_code: "es",
        text: "España",
        is_official: true,
      },
      {
        entity_type: "country",
        entity_id: 724,
        language_code: "nl",
        text: "Spanje",
        is_official: false,
      },

      // Tourism regions
      {
        entity_type: "tourism_region",
        entity_id: 1,
        language_code: "en",
        text: "Costa Brava",
        is_official: true,
      },
      {
        entity_type: "tourism_region",
        entity_id: 2,
        language_code: "en",
        text: "Costa del Sol",
        is_official: true,
      },
      {
        entity_type: "tourism_region",
        entity_id: 3,
        language_code: "en",
        text: "Costa Blanca",
        is_official: true,
      },
    ];

    const { error: translationError } = await supabase
      .from("translations")
      .upsert(coreTranslations, {
        onConflict: "entity_type,entity_id,language_code",
      });

    if (translationError) {
      throw new Error(
        `Failed to seed core translations: ${translationError.message}`
      );
    }

    // 5. Popular locations (Costa Blanca)
    console.log("   📍 Seeding popular locations...");
    const valenciaRegion = insertedRegions?.find(
      (r: any) => r.geonames_admin1_code === "VC"
    );

    if (valenciaRegion) {
      const popularLocations = [
        {
          name: "Benidorm",
          country_id: 724,
          region_id: valenciaRegion.id,
          coordinates: `(38.5385,-0.1313)`,
          latitude: 38.5385,
          longitude: -0.1313,
          popularity_score: 95,
          display_order: 1,
        },
        {
          name: "Javea",
          country_id: 724,
          region_id: valenciaRegion.id,
          coordinates: `(38.7914,0.1616)`,
          latitude: 38.7914,
          longitude: 0.1616,
          popularity_score: 85,
          display_order: 2,
        },
        {
          name: "Alicante",
          country_id: 724,
          region_id: valenciaRegion.id,
          coordinates: `(38.3452,-0.4815)`,
          latitude: 38.3452,
          longitude: -0.4815,
          popularity_score: 90,
          display_order: 3,
        },
      ];

      const { error: locationError } = await supabase
        .from("popular_locations")
        .upsert(popularLocations, { onConflict: "name,country_id" });

      if (locationError) {
        throw new Error(
          `Failed to seed popular locations: ${locationError.message}`
        );
      }
    }

    console.log("   📍 Seeding content properties...");

    // Core content properties (curated content)
    const contentProperties = [
      {
        id: "test-property",
        title: "Luxury Villa with Sea View",
        description:
          "Experience the ultimate Mediterranean getaway in this stunning villa overlooking the crystal-clear waters of Costa Blanca.",
        image_url:
          "https://images.unsplash.com/photo-1564013799919-ab600027ffc6?auto=format&fit=crop&w=800&q=80",
        location: "Alicante, Spain",
        coordinates: { lat: 38.3452, lng: -0.481 },
        price_range: "€150-300/night",
        category: "featured",
        is_active: true,
        display_order: 1,
      },
      {
        id: "villa-costa-brava",
        title: "Costa Brava Beach House",
        description:
          "Charming beachfront property with direct access to pristine sandy beaches and authentic Catalan culture.",
        image_url:
          "https://images.unsplash.com/photo-1571003123894-1f0594d2b5d9?auto=format&fit=crop&w=800&q=80",
        location: "Girona, Spain",
        coordinates: { lat: 41.9794, lng: 2.8214 },
        price_range: "€200-400/night",
        category: "beach",
        is_active: true,
        display_order: 2,
      },
      {
        id: "mountain-retreat",
        title: "Mountain Retreat Cottage",
        description:
          "Peaceful mountain escape surrounded by nature, perfect for hiking and outdoor adventures.",
        image_url:
          "https://images.unsplash.com/photo-1449824913935-59a10b8d2000?auto=format&fit=crop&w=800&q=80",
        location: "Asturias, Spain",
        coordinates: { lat: 43.3614, lng: -5.8593 },
        price_range: "€100-250/night",
        category: "nature",
        is_active: true,
        display_order: 3,
      },
    ];

    // Insert content properties
    const { error: contentError } = await supabase
      .from("content_properties")
      .upsert(contentProperties, { onConflict: "id" });

    if (contentError) {
      throw new Error(
        `Failed to seed content properties: ${contentError.message}`
      );
    }

    console.log(`   ✅ Seeded ${contentProperties.length} content properties`);
  },

  async rollback(supabase: any): Promise<void> {
    // Clean up in reverse order due to foreign key constraints
    await supabase.from("popular_locations").delete().neq("id", 0);
    await supabase.from("translations").delete().neq("id", 0);
    await supabase.from("tourism_region_codes").delete().neq("id", 0);
    await supabase.from("region_codes").delete().neq("id", 0);
    await supabase.from("country_codes").delete().neq("id", 0);
    await supabase.from("content_properties").delete().neq("id", "");
    console.log("   🧹 Cleaned all core data");
  },
};
