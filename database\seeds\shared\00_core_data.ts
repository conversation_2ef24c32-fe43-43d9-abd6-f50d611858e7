/**
 * Core Data Seeder
 * 
 * Seeds essential content properties and location data.
 * Safe for production use.
 */

export default {
  name: '00_core_data',
  description: 'Essential content properties and base location data',
  environment: 'shared' as const,
  order: 1,

  async execute(supabase: any): Promise<void> {
    console.log('   📍 Seeding content properties...');

    // Core content properties (curated content)
    const contentProperties = [
      {
        id: 'test-property',
        title: 'Luxury Villa with Sea View',
        description: 'Experience the ultimate Mediterranean getaway in this stunning villa overlooking the crystal-clear waters of Costa Blanca.',
        image_url: 'https://images.unsplash.com/photo-1564013799919-ab600027ffc6?auto=format&fit=crop&w=800&q=80',
        location: 'Alicante, Spain',
        coordinates: { lat: 38.3452, lng: -0.4810 },
        price_range: '€150-300/night',
        category: 'featured',
        is_active: true,
        display_order: 1
      },
      {
        id: 'villa-costa-brava',
        title: 'Costa Brava Beach House',
        description: 'Charming beachfront property with direct access to pristine sandy beaches and authentic Catalan culture.',
        image_url: 'https://images.unsplash.com/photo-1571003123894-1f0594d2b5d9?auto=format&fit=crop&w=800&q=80',
        location: 'Girona, Spain',
        coordinates: { lat: 41.9794, lng: 2.8214 },
        price_range: '€200-400/night',
        category: 'beach',
        is_active: true,
        display_order: 2
      },
      {
        id: 'mountain-retreat',
        title: 'Mountain Retreat Cottage',
        description: 'Peaceful mountain escape surrounded by nature, perfect for hiking and outdoor adventures.',
        image_url: 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?auto=format&fit=crop&w=800&q=80',
        location: 'Asturias, Spain',
        coordinates: { lat: 43.3614, lng: -5.8593 },
        price_range: '€100-250/night',
        category: 'nature',
        is_active: true,
        display_order: 3
      }
    ];

    // Insert content properties
    const { error: contentError } = await supabase
      .from('content_properties')
      .upsert(contentProperties, { onConflict: 'id' });

    if (contentError) {
      throw new Error(`Failed to seed content properties: ${contentError.message}`);
    }

    console.log(`   ✅ Seeded ${contentProperties.length} content properties`);
  },

  async rollback(supabase: any): Promise<void> {
    await supabase.from('content_properties').delete().neq('id', '');
    console.log('   🧹 Cleaned content properties');
  }
};