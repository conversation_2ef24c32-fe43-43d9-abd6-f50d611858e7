import { z } from 'zod';

// Search filters schema
export const searchFiltersSchema = z.object({
  location: z.string(),
  checkIn: z.string(),
  checkOut: z.string(),
  dateFlexibility: z.union([z.number(), z.literal("exact"), z.null()]).optional(),
  guests: z.object({
    adults: z.number(),
    children: z.number(),
    infants: z.number(),
    pets: z.number(),
  }),
  dateRange: z.object({
    from: z.date(),
    to: z.date(),
  }).optional(),
  priceRange: z.tuple([z.number(), z.number()]).optional(),
  propertyTypes: z.array(z.string()).optional(),
  amenities: z.array(z.string()).optional(),
});

// Property schema matching server response
export const propertySchema = z.object({
  id: z.string(),
  title: z.string(),
  location: z.object({
    city: z.string(),
    region: z.string(),
    country: z.string(),
  }),
  price: z.object({
    amount: z.number(),
    currency: z.string(),
    period: z.string(),
  }),
  rating: z.number(),
  reviewCount: z.number(),
  images: z.array(z.string()),
  amenities: z.array(z.string()),
  bedrooms: z.number(),
  bathrooms: z.number(),
  maxGuests: z.number(),
  propertyType: z.string(),
  badges: z.array(z.string()).optional(),
  features: z.array(z.string()).optional(),
  isInstantBook: z.boolean().optional(),
});

// Search response schema
export const searchResponseSchema = z.object({
  properties: z.array(propertySchema),
  total: z.number(),
  filters: z.object({
    cities: z.array(z.object({
      id: z.string(),
      name: z.string(),
      count: z.number(),
    })).optional(),
    regions: z.array(z.object({
      id: z.string(),
      name: z.string(),
      count: z.number(),
    })).optional(),
    propertyTypes: z.array(z.object({
      id: z.string(),
      name: z.string(),
      count: z.number(),
    })).optional(),
  }).optional(),
});

// Type exports
export type SearchFilters = z.infer<typeof searchFiltersSchema>;
export type Property = z.infer<typeof propertySchema>;
export type SearchResponse = z.infer<typeof searchResponseSchema>;

// Location types
export interface Location {
  id: string;
  name: string;
  type: 'country' | 'city' | 'village' | 'beach' | 'region';
  country: string;
  region?: string;
  searchTerms: string[];
}

export interface SuggestionCategory {
  titleKey: string;
  icon: string;
  locations: Location[];
}