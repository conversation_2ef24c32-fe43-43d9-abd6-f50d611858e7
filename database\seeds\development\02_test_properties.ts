/**
 * Development Test Properties Seeder
 * 
 * Creates test property listings for development and testing.
 * NOT safe for production use.
 */

export default {
  name: '02_test_properties',
  description: 'Test property listings for development',
  environment: 'development' as const,
  order: 20,

  async execute(supabase: any): Promise<void> {
    console.log('   🏠 Seeding test properties...');

    const testProperties = [
      {
        id: '10000000-0000-0000-0000-000000000001',
        host_id: '00000000-0000-0000-0000-000000000001',
        title: 'Stunning Villa in Costa Blanca',
        description: 'Beautiful villa with pool and sea views. Perfect for families looking for a relaxing vacation.',
        location: 'Alicante Province',
        city: 'Alicante',
        country: 'Spain',
        coordinates: { lat: 38.3452, lng: -0.4810 },
        price_per_night: 250.00,
        max_guests: 8,
        bedrooms: 4,
        bathrooms: 3,
        amenities: ['Pool', 'WiFi', 'Air Conditioning', 'Kitchen', 'Parking'],
        images: [
          'https://images.unsplash.com/photo-1564013799919-ab600027ffc6?auto=format&fit=crop&w=800&q=80',
          'https://images.unsplash.com/photo-1571003123894-1f0594d2b5d9?auto=format&fit=crop&w=800&q=80'
        ],
        property_type: 'villa',
        is_active: true,
        rating: 4.8,
        review_count: 24
      },
      {
        id: '10000000-0000-0000-0000-000000000002',
        host_id: '00000000-0000-0000-0000-000000000003',
        title: 'Cozy Apartment in Barcelona',
        description: 'Modern apartment in the heart of Barcelona, walking distance to major attractions.',
        location: 'Barcelona, Catalonia',
        city: 'Barcelona',
        country: 'Spain',
        coordinates: { lat: 41.3851, lng: 2.1734 },
        price_per_night: 120.00,
        max_guests: 4,
        bedrooms: 2,
        bathrooms: 1,
        amenities: ['WiFi', 'Air Conditioning', 'Kitchen', 'Washing Machine'],
        images: [
          'https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?auto=format&fit=crop&w=800&q=80',
          'https://images.unsplash.com/photo-1493809842364-78817add7ffb?auto=format&fit=crop&w=800&q=80'
        ],
        property_type: 'apartment',
        is_active: true,
        rating: 4.5,
        review_count: 12
      },
      {
        id: '10000000-0000-0000-0000-000000000003',
        host_id: '00000000-0000-0000-0000-000000000001',
        title: 'Beachfront House in Valencia',
        description: 'Spectacular beachfront property with direct beach access and panoramic ocean views.',
        location: 'Valencia Province',
        city: 'Valencia',
        country: 'Spain',
        coordinates: { lat: 39.4699, lng: -0.3763 },
        price_per_night: 180.00,
        max_guests: 6,
        bedrooms: 3,
        bathrooms: 2,
        amenities: ['Beach Access', 'WiFi', 'Air Conditioning', 'Kitchen', 'Terrace'],
        images: [
          'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?auto=format&fit=crop&w=800&q=80'
        ],
        property_type: 'house',
        is_active: true,
        rating: 4.9,
        review_count: 18
      }
    ];

    // Insert test properties
    const { error } = await supabase
      .from('host_properties')
      .upsert(testProperties, { onConflict: 'id' });

    if (error) {
      throw new Error(`Failed to seed test properties: ${error.message}`);
    }

    console.log(`   ✅ Seeded ${testProperties.length} test properties`);
  },

  async rollback(supabase: any): Promise<void> {
    const testPropertyIds = [
      '10000000-0000-0000-0000-000000000001',
      '10000000-0000-0000-0000-000000000002',
      '10000000-0000-0000-0000-000000000003'
    ];

    for (const id of testPropertyIds) {
      await supabase.from('host_properties').delete().eq('id', id);
    }
    
    console.log('   🧹 Cleaned test properties');
  }
};