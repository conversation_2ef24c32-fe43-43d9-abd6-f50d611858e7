import {
  LoginCredentials,
  RegisterCredentials,
  User,
  userSchema,
} from "../types";

const API_BASE = "";

class AuthService {
  async login(credentials: LoginCredentials): Promise<User> {
    try {
      const response = await fetch(`${API_BASE}/api/auth/login`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(credentials),
        credentials: "include", // Include cookies for session
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Login failed");
      }

      const data = await response.json();
      return userSchema.parse(data.user);
    } catch (error) {
      // Login error occurred
      throw error;
    }
  }

  async register(credentials: RegisterCredentials): Promise<any> {
    try {
      const response = await fetch(`${API_BASE}/api/auth/register`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(credentials),
        credentials: "include",
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Registration failed");
      }

      const data = await response.json();

      console.log("🎯 Frontend Registration Response:");
      console.log("  - Full response:", data);
      console.log("  - User data:", data.user);
      console.log("  - Session present:", !!data.session);
      console.log("  - Access token present:", !!data.session?.access_token);
      console.log("  - Refresh token present:", !!data.session?.refresh_token);

      // Store session data from registration response
      if (data.session?.access_token) {
        console.log("🔐 Storing tokens in localStorage:");
        console.log(
          "  - Access token length:",
          data.session.access_token?.length
        );
        console.log(
          "  - Refresh token length:",
          data.session.refresh_token?.length
        );

        localStorage.setItem("sb_access_token", data.session.access_token);
        if (data.session.refresh_token) {
          localStorage.setItem("sb_refresh_token", data.session.refresh_token);
        }

        // Verify storage
        const storedAccess = localStorage.getItem("sb_access_token");
        const storedRefresh = localStorage.getItem("sb_refresh_token");
        console.log("✅ Token storage verification:");
        console.log("  - Access token stored:", !!storedAccess);
        console.log("  - Refresh token stored:", !!storedRefresh);
      }

      // Debug: Check what fields are in the user object
      console.log("🔍 User object fields:", Object.keys(data.user || {}));
      console.log("🔍 Username field:", data.user?.username);
      console.log("🔍 Email field:", data.user?.email);
      console.log("🔍 ID field:", data.user?.id);

      try {
        const user = userSchema.parse(data.user);
        // Return the full response including requiresEmailConfirmation flag
        return {
          ...data,
          user,
        };
      } catch (parseError) {
        console.error("❌ Schema validation failed:", parseError);
        console.error("❌ User data that failed:", data.user);
        throw parseError;
      }
    } catch (error) {
      console.error("Registration error:", error);
      throw error;
    }
  }

  async logout(): Promise<void> {
    try {
      // Get access token from localStorage
      const accessToken = localStorage.getItem("sb_access_token");

      const headers: HeadersInit = {
        "Content-Type": "application/json",
      };

      if (accessToken && accessToken !== "missing") {
        headers["Authorization"] = `Bearer ${accessToken}`;
      }

      const response = await fetch(`${API_BASE}/api/auth/logout`, {
        method: "POST",
        headers,
        credentials: "include",
      });

      if (!response.ok) {
        throw new Error("Logout failed");
      }

      // Clear tokens from localStorage after successful logout
      localStorage.removeItem("sb_access_token");
      localStorage.removeItem("sb_refresh_token");
    } catch (error) {
      console.error("Logout error:", error);
      throw error;
    }
  }

  async getCurrentUser(): Promise<User | null> {
    try {
      // Get access token from localStorage
      const accessToken = localStorage.getItem("sb_access_token");

      // Debug logging
      console.log("🔍 AuthService: Getting current user");
      console.log("  - Access token present:", !!accessToken);
      console.log("  - Access token length:", accessToken?.length);
      console.log(
        "  - Access token preview:",
        accessToken ? `${accessToken.substring(0, 20)}...` : "null"
      );

      if (!accessToken || accessToken === "missing") {
        console.log("AuthService: No access token found, cannot fetch user.");
        return null;
      }

      const headers: HeadersInit = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${accessToken}`,
      };

      const response = await fetch(`${API_BASE}/api/auth/me`, {
        headers,
        credentials: "include",
      });

      if (!response.ok) {
        console.log(
          "AuthService: getCurrentUser failed, status:",
          response.status
        );
        return null; // Not authenticated
      }

      const data = await response.json();
      console.log(
        "AuthService: getCurrentUser success, user:",
        data.user?.email
      );
      return userSchema.parse(data.user);
    } catch (error) {
      console.error("Get current user error:", error);
      return null;
    }
  }
}

export const authService = new AuthService();
