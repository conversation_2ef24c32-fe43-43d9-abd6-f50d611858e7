import { useState, useEffect, useRef } from 'react';
import { useQuery, UseQueryResult } from '@tanstack/react-query';

interface DebouncedSearchOptions {
  debounceMs?: number;
  enabled?: boolean;
}

/**
 * Custom hook that debounces search queries to prevent multiple rapid API calls
 * Automatically cancels previous requests when new ones are initiated
 */
export function useDebouncedSearch<TFilters, TResponse>(
  searchFn: (filters: TFilters, signal?: AbortSignal) => Promise<TResponse>,
  filters: TFilters,
  options: DebouncedSearchOptions = {}
): UseQueryResult<TResponse, Error> {
  const { debounceMs = 300, enabled = true } = options;
  const [debouncedFilters, setDebouncedFilters] = useState(filters);
  const debounceRef = useRef<NodeJS.Timeout>();
  
  // Create a stable string representation of filters for comparison
  const filtersString = JSON.stringify(filters);
  const [lastFiltersString, setLastFiltersString] = useState(filtersString);

  // Debounce the filters only when they actually change
  useEffect(() => {
    if (filtersString === lastFiltersString) {
      return; // No change, skip debouncing
    }

    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }

    debounceRef.current = setTimeout(() => {
      setDebouncedFilters(filters);
      setLastFiltersString(filtersString);
    }, debounceMs);

    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }
    };
  }, [filtersString, lastFiltersString, filters, debounceMs]);

  // Use the debounced filters for the actual query
  return useQuery({
    queryKey: ['debounced-search', debouncedFilters],
    queryFn: ({ signal }) => searchFn(debouncedFilters, signal),
    enabled: enabled && Boolean(debouncedFilters),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: (failureCount, error) => {
      // Don't retry if the request was aborted (cancelled)
      if (error?.name === 'AbortError') {
        return false;
      }
      return failureCount < 3;
    },
  });
}