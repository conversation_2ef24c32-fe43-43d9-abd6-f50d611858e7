{"inputs": [{"type": "promptString", "id": "supabase-access-token", "description": "Supabase personal access token", "password": true}], "servers": {"context7": {"type": "http", "url": "https://mcp.context7.com/mcp"}, "supabase": {"command": "cmd", "args": ["/c", "npx", "-y", "@supabase/mcp-server-supabase@latest", "--read-only", "--project-ref=bapymeimutdxrngejohd"], "env": {"SUPABASE_ACCESS_TOKEN": "${input:supabase-access-token}"}}}}