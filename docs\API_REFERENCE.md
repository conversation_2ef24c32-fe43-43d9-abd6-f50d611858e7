# API Reference

## Overview

VillaWise API provides RESTful endpoints for authentication, property management, booking operations, and user interactions. All endpoints return JSON responses with consistent error handling.

**Base URL**: `http://localhost:5000/api` (development)  
**Production URL**: `https://your-domain.com/api`

## Authentication

### Headers

```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

### Token Management

JWT tokens are obtained through login or OAuth flows and must be included in the Authorization header for protected endpoints.

## Error Responses

All API endpoints follow consistent error response format:

```json
{
  "success": false,
  "message": "Error description",
  "messageKey": "translation.key",
  "errors": ["Detailed error array (optional)"]
}
```

### HTTP Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `409` - Conflict
- `500` - Internal Server Error

## Authentication Endpoints

### Register User

```http
POST /api/auth/register
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "username": "username",
  "password": "StrongPass123!",
  "firstName": "John",
  "lastName": "Doe",
  "isHost": false
}
```

**Response (201):**
```json
{
  "success": true,
  "message": "Registration successful. Please check your email to confirm your account.",
  "requiresEmailConfirmation": true,
  "user": {
    "id": "user-uuid",
    "email": "<EMAIL>",
    "username": "username",
    "first_name": "John",
    "last_name": "Doe",
    "is_host": false,
    "email_confirmed": false
  }
}
```

### Login User

```http
POST /api/auth/login
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "rememberMe": false
}
```

**Response (200):**
```json
{
  "success": true,
  "message": "Login successful",
  "user": {
    "id": "user-uuid",
    "email": "<EMAIL>",
    "username": "username",
    "first_name": "John",
    "last_name": "Doe",
    "is_host": false,
    "avatar_url": null,
    "oauth_provider": null
  },
  "session": {
    "access_token": "jwt-token",
    "refresh_token": "refresh-token",
    "expires_in": 3600,
    "token_type": "Bearer"
  },
  "rememberMe": false
}
```

### OAuth Authentication

```http
POST /api/auth/oauth/validate
```

**Headers:**
```http
Authorization: Bearer <oauth_token>
```

**Response (200):**
```json
{
  "success": true,
  "user": {
    "id": "user-uuid",
    "email": "<EMAIL>",
    "username": "username",
    "first_name": "John",
    "last_name": "Doe",
    "is_host": false,
    "avatar_url": "https://example.com/avatar.jpg",
    "oauth_provider": "google",
    "oauth_id": "google-oauth2|123456789"
  }
}
```

### Get Current User

```http
GET /api/auth/me
```

**Headers:**
```http
Authorization: Bearer <jwt_token>
```

**Response (200):**
```json
{
  "success": true,
  "user": {
    "id": "user-uuid",
    "email": "<EMAIL>",
    "username": "username",
    "first_name": "John",
    "last_name": "Doe",
    "is_host": false,
    "avatar_url": null,
    "oauth_provider": null
  }
}
```

### Logout User

```http
POST /api/auth/logout
```

**Headers:**
```http
Authorization: Bearer <jwt_token>
```

**Response (200):**
```json
{
  "success": true,
  "message": "Logout successful"
}
```

## Property Endpoints

### Get Properties

```http
GET /api/properties
```

**Query Parameters:**
- `location` - Filter by location (optional)
- `checkIn` - Check-in date (YYYY-MM-DD)
- `checkOut` - Check-out date (YYYY-MM-DD)
- `guests` - Number of guests
- `minPrice` - Minimum price per night
- `maxPrice` - Maximum price per night
- `limit` - Number of results (default: 20)
- `offset` - Pagination offset (default: 0)

**Response (200):**
```json
{
  "success": true,
  "data": [
    {
      "id": "property-uuid",
      "title": "Beautiful Villa in Calpe",
      "description": "Stunning sea views...",
      "location": "Calpe, Spain",
      "city": "Calpe",
      "country": "Spain",
      "coordinates": {
        "lat": 38.6426,
        "lng": 0.0417
      },
      "price_per_night": "150.00",
      "max_guests": 6,
      "bedrooms": 3,
      "bathrooms": 2,
      "property_type": "Villa",
      "amenities": ["WiFi", "Pool", "Air Conditioning"],
      "images": ["image1.jpg", "image2.jpg"],
      "rating": "4.8",
      "review_count": 24,
      "host": {
        "id": "host-uuid",
        "username": "hostuser",
        "first_name": "Maria",
        "last_name": "Garcia",
        "avatar_url": "https://example.com/avatar.jpg"
      },
      "available": true
    }
  ],
  "pagination": {
    "total": 100,
    "limit": 20,
    "offset": 0,
    "hasMore": true
  }
}
```

### Get Property Details

```http
GET /api/properties/:id
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "id": "property-uuid",
    "title": "Beautiful Villa in Calpe",
    "description": "Stunning sea views with modern amenities...",
    "location": "Calpe, Spain",
    "city": "Calpe",
    "country": "Spain",
    "coordinates": {
      "lat": 38.6426,
      "lng": 0.0417
    },
    "price_per_night": "150.00",
    "max_guests": 6,
    "bedrooms": 3,
    "bathrooms": 2,
    "property_type": "Villa",
    "amenities": ["WiFi", "Pool", "Air Conditioning", "Kitchen"],
    "images": ["image1.jpg", "image2.jpg", "image3.jpg"],
    "rating": "4.8",
    "review_count": 24,
    "host": {
      "id": "host-uuid",
      "username": "hostuser",
      "first_name": "Maria",
      "last_name": "Garcia",
      "avatar_url": "https://example.com/avatar.jpg",
      "is_superhost": false,
      "joined_date": "2023-01-15"
    },
    "reviews": [
      {
        "id": "review-uuid",
        "guest": {
          "first_name": "John",
          "avatar_url": "https://example.com/guest-avatar.jpg"
        },
        "rating": 5,
        "comment": "Amazing stay! The villa was perfect.",
        "created_at": "2024-12-15T10:30:00Z"
      }
    ],
    "availability": {
      "available_dates": ["2025-08-01", "2025-08-02", "2025-08-03"],
      "blocked_dates": ["2025-07-15", "2025-07-16"]
    }
  }
}
```

### Create Property (Host Only)

```http
POST /api/properties
```

**Headers:**
```http
Authorization: Bearer <jwt_token>
```

**Request Body:**
```json
{
  "title": "Beautiful Villa in Calpe",
  "description": "Stunning sea views with modern amenities",
  "location": "Calpe, Spain",
  "city": "Calpe",
  "country": "Spain",
  "coordinates": {
    "lat": 38.6426,
    "lng": 0.0417
  },
  "price_per_night": "150.00",
  "max_guests": 6,
  "bedrooms": 3,
  "bathrooms": 2,
  "property_type": "Villa",
  "amenities": ["WiFi", "Pool", "Air Conditioning"]
}
```

**Response (201):**
```json
{
  "success": true,
  "data": {
    "id": "property-uuid",
    "title": "Beautiful Villa in Calpe",
    "host_id": "host-uuid",
    "created_at": "2024-01-18T12:00:00Z",
    "updated_at": "2024-01-18T12:00:00Z"
  }
}
```

### Update Property (Host Only)

```http
PUT /api/properties/:id
```

**Headers:**
```http
Authorization: Bearer <jwt_token>
```

**Request Body:** (Same as create, all fields optional)

**Response (200):**
```json
{
  "success": true,
  "data": {
    "id": "property-uuid",
    "title": "Updated Villa Title",
    "updated_at": "2024-01-18T12:00:00Z"
  }
}
```

### Delete Property (Host Only)

```http
DELETE /api/properties/:id
```

**Headers:**
```http
Authorization: Bearer <jwt_token>
```

**Response (200):**
```json
{
  "success": true,
  "message": "Property deleted successfully"
}
```

## Booking Endpoints

### Create Booking

```http
POST /api/bookings
```

**Headers:**
```http
Authorization: Bearer <jwt_token>
```

**Request Body:**
```json
{
  "propertyId": "property-uuid",
  "checkIn": "2025-08-01",
  "checkOut": "2025-08-05",
  "guests": {
    "adults": 2,
    "children": 1,
    "infants": 0,
    "pets": 0
  },
  "specialRequests": "Late check-in requested"
}
```

**Response (201):**
```json
{
  "success": true,
  "data": {
    "id": "booking-uuid",
    "property": {
      "id": "property-uuid",
      "title": "Beautiful Villa in Calpe",
      "location": "Calpe, Spain"
    },
    "guest_id": "guest-uuid",
    "check_in": "2025-08-01",
    "check_out": "2025-08-05",
    "guests": {
      "adults": 2,
      "children": 1,
      "infants": 0,
      "pets": 0
    },
    "total_price": "600.00",
    "status": "pending",
    "special_requests": "Late check-in requested",
    "created_at": "2024-01-18T12:00:00Z"
  }
}
```

### Get User Bookings

```http
GET /api/bookings/user
```

**Headers:**
```http
Authorization: Bearer <jwt_token>
```

**Response (200):**
```json
{
  "success": true,
  "data": [
    {
      "id": "booking-uuid",
      "property": {
        "id": "property-uuid",
        "title": "Beautiful Villa in Calpe",
        "location": "Calpe, Spain",
        "images": ["image1.jpg"]
      },
      "check_in": "2025-08-01",
      "check_out": "2025-08-05",
      "guests": {
        "adults": 2,
        "children": 1,
        "infants": 0,
        "pets": 0
      },
      "total_price": "600.00",
      "status": "confirmed",
      "created_at": "2024-01-18T12:00:00Z"
    }
  ]
}
```

### Get Host Bookings

```http
GET /api/bookings/host
```

**Headers:**
```http
Authorization: Bearer <jwt_token>
```

**Response (200):**
```json
{
  "success": true,
  "data": [
    {
      "id": "booking-uuid",
      "property": {
        "id": "property-uuid",
        "title": "Beautiful Villa in Calpe"
      },
      "guest": {
        "id": "guest-uuid",
        "first_name": "John",
        "last_name": "Doe",
        "email": "<EMAIL>"
      },
      "check_in": "2025-08-01",
      "check_out": "2025-08-05",
      "guests": {
        "adults": 2,
        "children": 1,
        "infants": 0,
        "pets": 0
      },
      "total_price": "600.00",
      "status": "pending",
      "created_at": "2024-01-18T12:00:00Z"
    }
  ]
}
```

### Update Booking Status (Host Only)

```http
PATCH /api/bookings/:id/status
```

**Headers:**
```http
Authorization: Bearer <jwt_token>
```

**Request Body:**
```json
{
  "status": "confirmed"
}
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "id": "booking-uuid",
    "status": "confirmed",
    "updated_at": "2024-01-18T12:00:00Z"
  }
}
```

## User Endpoints

### Get User Profile

```http
GET /api/users/profile
```

**Headers:**
```http
Authorization: Bearer <jwt_token>
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "id": "user-uuid",
    "email": "<EMAIL>",
    "username": "username",
    "first_name": "John",
    "last_name": "Doe",
    "phone": "+34123456789",
    "is_host": false,
    "avatar_url": "https://example.com/avatar.jpg",
    "oauth_provider": "google",
    "locale": "en",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-18T12:00:00Z"
  }
}
```

### Update User Profile

```http
PUT /api/users/profile
```

**Headers:**
```http
Authorization: Bearer <jwt_token>
```

**Request Body:**
```json
{
  "first_name": "John",
  "last_name": "Doe",
  "phone": "+34123456789",
  "locale": "en"
}
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "id": "user-uuid",
    "first_name": "John",
    "last_name": "Doe",
    "phone": "+34123456789",
    "updated_at": "2024-01-18T12:00:00Z"
  }
}
```

### Get User Search History

```http
GET /api/user/search-history
```

**Headers:**
```http
Authorization: Bearer <jwt_token>
```

**Response (200):**
```json
{
  "success": true,
  "data": [
    {
      "id": "search-uuid",
      "location": "Calpe, Spain",
      "check_in": "2025-08-01",
      "check_out": "2025-08-05",
      "guests": {
        "adults": 2,
        "children": 0,
        "infants": 0,
        "pets": 0
      },
      "created_at": "2024-01-18T12:00:00Z"
    }
  ]
}
```

### Add Search History

```http
POST /api/user/search-history
```

**Headers:**
```http
Authorization: Bearer <jwt_token>
```

**Request Body:**
```json
{
  "location": "Calpe, Spain",
  "check_in": "2025-08-01",
  "check_out": "2025-08-05",
  "guests": {
    "adults": 2,
    "children": 0,
    "infants": 0,
    "pets": 0
  }
}
```

**Response (201):**
```json
{
  "success": true,
  "data": {
    "id": "search-uuid",
    "location": "Calpe, Spain",
    "created_at": "2024-01-18T12:00:00Z"
  }
}
```

## Location Endpoints

### Get Popular Destinations

```http
GET /api/locations/suggestions
```

**Response (200):**
```json
{
  "success": true,
  "data": [
    {
      "id": "madrid",
      "name": "Madrid",
      "country": "Spain",
      "image": "madrid.jpg",
      "property_count": 45
    },
    {
      "id": "barcelona",
      "name": "Barcelona",
      "country": "Spain",
      "image": "barcelona.jpg",
      "property_count": 62
    }
  ]
}
```

### Location Autocomplete

```http
GET /api/locations/search?q=<query>
```

**Query Parameters:**
- `q` - Search query (required)
- `limit` - Number of results (default: 10)

**Response (200):**
```json
{
  "success": true,
  "data": [
    {
      "id": "calpe",
      "name": "Calpe",
      "country": "Spain",
      "full_name": "Calpe, Alicante, Spain",
      "coordinates": {
        "lat": 38.6426,
        "lng": 0.0417
      },
      "property_count": 23
    }
  ]
}
```

## Translation Endpoints

### Get Translations

```http
GET /api/translations/:locale
```

**Parameters:**
- `locale` - Language code (en, nl)

**Response (200):**
```json
{
  "auth": {
    "login": "Sign In",
    "register": "Create Account",
    "forgotPassword": "Forgot Password?",
    "emailPlaceholder": "Enter your email",
    "passwordPlaceholder": "Enter your password"
  },
  "search": {
    "placeholder": "Where would you like to go?",
    "searchButton": "Search",
    "filters": "Filters",
    "results": "properties found"
  },
  "property": {
    "bookNow": "Book Now",
    "amenities": "Amenities",
    "reviews": "Reviews",
    "location": "Location"
  }
}
```

## Content Endpoints

### Get Popular Spain Properties

```http
GET /api/content/popular-spain
```

**Response (200):**
```json
{
  "success": true,
  "data": [
    {
      "id": "property-uuid",
      "title": "Luxury Villa in Marbella",
      "location": "Marbella, Spain",
      "price_per_night": "350.00",
      "images": ["marbella1.jpg"],
      "rating": "4.9",
      "review_count": 87
    }
  ]
}
```

### Get Guest Favorites

```http
GET /api/content/guest-favorites
```

**Response (200):**
```json
{
  "success": true,
  "data": [
    {
      "id": "property-uuid",
      "title": "Cozy Apartment in Barcelona",
      "location": "Barcelona, Spain",
      "price_per_night": "120.00",
      "images": ["barcelona1.jpg"],
      "rating": "4.7",
      "review_count": 34
    }
  ]
}
```

### Get New France Properties

```http
GET /api/content/new-france
```

**Response (200):**
```json
{
  "success": true,
  "data": [
    {
      "id": "property-uuid",
      "title": "Château in Provence",
      "location": "Provence, France",
      "price_per_night": "280.00",
      "images": ["provence1.jpg"],
      "rating": "4.8",
      "review_count": 56
    }
  ]
}
```

## Health Check

### System Health

```http
GET /api/health
```

**Response (200):**
```json
{
  "success": true,
  "status": "healthy",
  "timestamp": "2024-01-18T12:00:00Z",
  "version": "1.0.0",
  "environment": "production",
  "services": {
    "database": "connected",
    "auth": "operational",
    "storage": "operational"
  }
}
```

## Rate Limits

- **Authentication endpoints**: 10 requests per minute per IP
- **General API**: 100 requests per minute per user
- **Search endpoints**: 50 requests per minute per user
- **Upload endpoints**: 5 requests per minute per user

## Webhook Events

### Booking Status Changes

```http
POST /webhooks/booking-status
```

**Payload:**
```json
{
  "event": "booking.status_changed",
  "data": {
    "booking_id": "booking-uuid",
    "old_status": "pending",
    "new_status": "confirmed",
    "property_id": "property-uuid",
    "guest_id": "guest-uuid",
    "timestamp": "2024-01-18T12:00:00Z"
  }
}
```

This API reference provides comprehensive documentation for all VillaWise endpoints, including authentication, property management, booking operations, and utility functions.