import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';

import { useTranslations } from '@/lib/translations';
import { 
  CalendarDays, 
  MapPin, 
  Heart, 
  User, 
  MessageSquare,
  Plus,
  Star,
  Car,
  Settings,
  HelpCircle
} from 'lucide-react';

interface GuestDashboardProps {
  guestBookings: any[];
  wishlists: any[];
  guestMessages: any[];
  user: any;
  onUpgradeToHost: () => void;
  isUpgrading: boolean;
}

const GuestDashboard: React.FC<GuestDashboardProps> = ({
  guestBookings,
  wishlists,
  guestMessages,
  user,
  onUpgradeToHost,
  isUpgrading
}) => {
  const t = useTranslations('guestDashboard');
  const [activeTab, setActiveTab] = useState('dashboard');
  const [showBecomeHostSection, setShowBecomeHostSection] = useState(true);

  const handleBecomeHost = () => {
    onUpgradeToHost();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const NavigationSidebar = () => (
    <div className="w-64 bg-white border-r min-h-[calc(100vh-64px)]">
      <div className="p-6">
        <h1 className="text-2xl font-bold text-gray-900">{t('title')}</h1>
        <p className="text-gray-600 mt-2">{t('greeting')}</p>
      </div>
      
      <nav className="px-4 space-y-2">
        <Button 
          variant={activeTab === 'dashboard' ? 'default' : 'ghost'}
          className="w-full justify-start"
          onClick={() => setActiveTab('dashboard')}
        >
          <User className="h-4 w-4 mr-2" />
          {t('navigation.dashboard')}
        </Button>
        
        <Button 
          variant={activeTab === 'bookings' ? 'default' : 'ghost'}
          className="w-full justify-start"
          onClick={() => setActiveTab('bookings')}
        >
          <CalendarDays className="h-4 w-4 mr-2" />
          {t('navigation.bookings')}
        </Button>
        
        <Button 
          variant={activeTab === 'wishlists' ? 'default' : 'ghost'}
          className="w-full justify-start"
          onClick={() => setActiveTab('wishlists')}
        >
          <Heart className="h-4 w-4 mr-2" />
          {t('navigation.wishlists')}
        </Button>
        
        <Button 
          variant={activeTab === 'messages' ? 'default' : 'ghost'}
          className="w-full justify-start"
          onClick={() => setActiveTab('messages')}
        >
          <MessageSquare className="h-4 w-4 mr-2" />
          {t('navigation.messages')}
        </Button>
        
        <Button 
          variant={activeTab === 'car-rental' ? 'default' : 'ghost'}
          className="w-full justify-start"
          onClick={() => setActiveTab('car-rental')}
        >
          <Car className="h-4 w-4 mr-2" />
          {t('navigation.carRental')}
        </Button>
        
        <Button 
          variant={activeTab === 'account' ? 'default' : 'ghost'}
          className="w-full justify-start"
          onClick={() => setActiveTab('account')}
        >
          <Settings className="h-4 w-4 mr-2" />
          {t('navigation.account')}
        </Button>
        
        <Button 
          variant={activeTab === 'help' ? 'default' : 'ghost'}
          className="w-full justify-start"
          onClick={() => setActiveTab('help')}
        >
          <HelpCircle className="h-4 w-4 mr-2" />
          {t('navigation.help')}
        </Button>
      </nav>
    </div>
  );

  const DashboardContent = () => (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">{t('greeting')}</h1>
        <p className="text-gray-600 mt-2">{t('description')}</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Become Host Section */}
        {showBecomeHostSection && (
          <Card className="bg-primary/5 border-primary/20">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Plus className="h-5 w-5 text-primary" />
                  <span className="text-sm font-medium text-primary">
                    {t('becomeHost.title')}
                  </span>
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {t('becomeHost.title')}
                  </h3>
                  <p className="text-gray-600 mb-4">
                    {t('becomeHost.description')}
                  </p>
                  <Button 
                    className="bg-primary hover:bg-primary/90"
                    onClick={handleBecomeHost}
                    disabled={isUpgrading}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    {isUpgrading ? t('becomeHost.upgrading') : t('becomeHost.button')}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Recent Bookings */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle className="flex items-center">
              <CalendarDays className="h-5 w-5 mr-2" />
              <span>{t('recentBookings.title')}</span>
            </CardTitle>
            <Button variant="outline" size="sm">
              {t('recentBookings.viewAll')}
            </Button>
          </CardHeader>
          <CardContent>
            {guestBookings.length > 0 ? (
              <div className="space-y-4">
                {guestBookings.slice(0, 3).map((booking: any) => (
                  <div key={booking.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                        <MapPin className="h-5 w-5 text-gray-500" />
                      </div>
                      <div>
                        <p className="font-medium">{booking.property?.title || 'Property'}</p>
                        <p className="text-sm text-gray-600">
                          {formatDate(booking.check_in_date)} - {formatDate(booking.check_out_date)}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <Badge variant="outline" className="bg-green-50 text-green-700">
                        {t('recentBookings.accepted')}
                      </Badge>
                      <p className="text-sm text-gray-600 mt-1">
                        {formatCurrency(booking.total_amount)} {t('recentBookings.amount')}
                      </p>
                      <Button variant="outline" size="sm" className="mt-1">
                        {t('recentBookings.view')}
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-600">{t('recentBookings.noBookings')}</p>
                <Button variant="outline" className="mt-2">
                  {t('recentBookings.startExploring')}
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Wishlists */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle className="flex items-center">
              <Heart className="h-5 w-5 mr-2" />
              <span>{t('wishlists.title')}</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {wishlists.length > 0 ? (
              <div className="grid grid-cols-1 gap-4">
                {wishlists.slice(0, 2).map((wishlist: any) => (
                  <div key={wishlist.id} className="border rounded-lg p-4 hover:bg-gray-50 cursor-pointer">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium">{wishlist.name}</h4>
                      <Heart className="h-4 w-4 text-red-500 fill-current" />
                    </div>
                    <p className="text-sm text-gray-600">{wishlist.description || 'My wishlist'}</p>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-600">{t('wishlists.noWishlists')}</p>
                <Button variant="outline" className="mt-2">
                  {t('wishlists.startSaving')}
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );

  const BookingsContent = () => (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-semibold text-gray-900">{t('navigation.bookings')}</h2>
        <div className="flex items-center space-x-4 mt-4">
          <Button variant="outline">
            {t('recentBookings.newBooking')}
          </Button>
        </div>
      </div>

      {guestBookings.length > 0 ? (
        <div className="space-y-4">
          {guestBookings.map((booking: any) => (
            <Card key={booking.id}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                      <MapPin className="h-6 w-6 text-gray-500" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-lg">{booking.property?.title || 'Property'}</h3>
                      <p className="text-gray-600">
                        {formatDate(booking.check_in_date)} - {formatDate(booking.check_out_date)}
                      </p>
                      <p className="text-sm text-gray-500">
                        {booking.guest_count} guests • {formatCurrency(booking.total_amount)}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <Badge variant="outline" className="bg-green-50 text-green-700 mb-2">
                      {booking.status}
                    </Badge>
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm">
                        {t('recentBookings.view')}
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <p className="text-gray-600">{t('recentBookings.noBookings')}</p>
          <Button variant="outline" className="mt-4">
            {t('recentBookings.startExploring')}
          </Button>
        </div>
      )}
    </div>
  );

  const WishlistsContent = () => (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-semibold text-gray-900">{t('wishlists.title')}</h2>
        <Button variant="outline" className="mt-4">
          {t('wishlists.startSaving')}
        </Button>
      </div>

      {wishlists.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {wishlists.map((wishlist: any) => (
            <Card key={wishlist.id} className="cursor-pointer hover:shadow-lg transition-shadow">
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="font-semibold text-lg">{wishlist.name}</h3>
                  <Heart className="h-5 w-5 text-red-500 fill-current" />
                </div>
                <p className="text-sm text-gray-600">{wishlist.description || 'My wishlist'}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <p className="text-gray-600">{t('wishlists.noWishlists')}</p>
          <Button variant="outline" className="mt-4">
            {t('wishlists.startSaving')}
          </Button>
        </div>
      )}
    </div>
  );

  const MessagesContent = () => (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-semibold text-gray-900">{t('navigation.messages')}</h2>
      </div>

      {guestMessages.length > 0 ? (
        <div className="space-y-4">
          {guestMessages.map((message: any) => (
            <Card key={message.id}>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <MessageSquare className="h-5 w-5 mr-2" />
                  <span>Messages</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="border rounded-lg p-4 hover:bg-gray-50">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-gray-900">{message.sender_name || 'Host'}</h4>
                      <span className="text-xs text-gray-500">
                        {message.created_at ? new Date(message.created_at).toLocaleDateString() : ''}
                      </span>
                    </div>
                    <p className="text-sm text-gray-700 mb-2">{message.content}</p>
                    <span className="text-xs text-gray-500">
                      {message.created_at ? new Date(message.created_at).toLocaleTimeString() : ''}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600">No messages yet</p>
        </div>
      )}
    </div>
  );

  const AccountContent = () => (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-semibold text-gray-900">{t('navigation.account')}</h2>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Personal Information */}
        <Card>
          <CardHeader>
            <CardTitle>
              {t('account.personalInfo.title')}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">{t('account.personalInfo.firstName')}</label>
                <div className="mt-1 p-2 border rounded-md bg-gray-50">
                  {user?.user_metadata?.first_name || 'N/A'}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">{t('account.personalInfo.lastName')}</label>
                <div className="mt-1 p-2 border rounded-md bg-gray-50">
                  {user?.user_metadata?.last_name || 'N/A'}
                </div>
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">{t('account.personalInfo.email')}</label>
              <div className="mt-1 p-2 border rounded-md bg-gray-50">
                {user?.email || 'N/A'}
              </div>
            </div>
            <Button variant="outline">
              {t('account.personalInfo.editProfile')}
            </Button>
          </CardContent>
        </Card>

        {/* Dashboard Settings */}
        <Card>
          <CardHeader>
            <CardTitle>
              {t('account.dashboardSettings.title')}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium text-gray-900">{t('account.dashboardSettings.becomeHostSection')}</p>
                <p className="text-sm text-gray-600">{t('account.dashboardSettings.becomeHostDescription')}</p>
              </div>
              <div className="flex items-center space-x-2">
                {showBecomeHostSection ? (
                  <Badge className="bg-green-100 text-green-800">{t('account.dashboardSettings.visible')}</Badge>
                ) : (
                  <Badge className="bg-gray-100 text-gray-800">{t('account.dashboardSettings.hidden')}</Badge>
                )}
                <Switch
                  checked={showBecomeHostSection}
                  onCheckedChange={setShowBecomeHostSection}
                />
              </div>
            </div>
            <Button variant="outline" onClick={() => setShowBecomeHostSection(!showBecomeHostSection)}>
              {showBecomeHostSection ? t('account.dashboardSettings.hide') : t('account.dashboardSettings.restore')}
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );

  return (
    <div className="flex min-h-[calc(100vh-64px)] bg-gray-50">
      <NavigationSidebar />
      <div className="flex-1">
        {activeTab === 'dashboard' && <DashboardContent />}
        {activeTab === 'bookings' && <BookingsContent />}
        {activeTab === 'wishlists' && <WishlistsContent />}
        {activeTab === 'messages' && <MessagesContent />}
        {activeTab === 'account' && <AccountContent />}
        {activeTab === 'car-rental' && (
          <div className="p-6">
            <div className="text-center py-12">
              <Car className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Car Rental</h2>
              <p className="text-gray-600">Car rental services coming soon!</p>
            </div>
          </div>
        )}
        {activeTab === 'help' && (
          <div className="p-6">
            <div className="text-center py-12">
              <HelpCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Help & Support</h2>
              <p className="text-gray-600">Need help? Contact our support team.</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default GuestDashboard;