import express from 'express'
import { cacheRoute, cacheMonitor } from '../../middleware/cache'
import { getProperties, getPropertyById, getPopularProperties } from '../../dal/entities/properties'
import { PropertySearchFilters } from '../../dal/dto/property.dto'

export const dalPropertyRouter = express.Router()

// Apply cache monitoring to all routes
dalPropertyRouter.use(cacheMonitor)

// Property Search with DAL
dalPropertyRouter.get('/search', 
  cacheRoute(300), // 5 minute cache
  async (req, res) => {
    try {
      const authHeader = req.headers.authorization
      const {
        location,
        minPrice,
        maxPrice,
        minGuests,
        bedrooms,
        amenities,
        checkIn,
        checkOut
      } = req.query
      
      const filters: PropertySearchFilters = {}
      
      if (location) filters.location = location as string
      if (minPrice) filters.minPrice = parseInt(minPrice as string)
      if (maxPrice) filters.maxPrice = parseInt(maxPrice as string)
      if (minGuests) filters.minGuests = parseInt(minGuests as string)
      if (bedrooms) filters.bedrooms = parseInt(bedrooms as string)
      if (amenities) {
        filters.amenities = Array.isArray(amenities) 
          ? amenities as string[]
          : [amenities as string]
      }
      if (checkIn && checkOut) {
        filters.dateRange = {
          checkIn: checkIn as string,
          checkOut: checkOut as string
        }
      }
      
      const result = await getProperties(authHeader, filters)
      
      res.json({
        success: true,
        data: result.properties,
        pagination: {
          total: result.total,
          page: result.page,
          limit: result.limit,
          hasMore: result.hasMore
        }
      })
    } catch (error) {
      console.error('Property search error:', error)
      res.status(500).json({ 
        success: false, 
        message: error instanceof Error ? error.message : 'Failed to search properties' 
      })
    }
  }
)

// Get Property by ID with DAL
dalPropertyRouter.get('/:id', 
  cacheRoute(600), // 10 minute cache
  async (req, res) => {
    try {
      const authHeader = req.headers.authorization
      const property = await getPropertyById(req.params.id, authHeader)
      
      if (!property) {
        return res.status(404).json({
          success: false,
          message: 'Property not found'
        })
      }
      
      res.json({
        success: true,
        data: property
      })
    } catch (error) {
      console.error('Property fetch error:', error)
      res.status(500).json({ 
        success: false, 
        message: error instanceof Error ? error.message : 'Failed to fetch property' 
      })
    }
  }
)

// Get Popular Properties with DAL
dalPropertyRouter.get('/featured/popular', 
  cacheRoute(1800), // 30 minute cache
  async (req, res) => {
    try {
      const authHeader = req.headers.authorization
      const limit = parseInt(req.query.limit as string) || 10
      const properties = await getPopularProperties(authHeader, limit)
      
      res.json({
        success: true,
        data: properties
      })
    } catch (error) {
      console.error('Popular properties error:', error)
      res.status(500).json({ 
        success: false, 
        message: error instanceof Error ? error.message : 'Failed to fetch popular properties' 
      })
    }
  }
)

// Get Properties by Location with DAL
dalPropertyRouter.get('/location/:location', 
  cacheRoute(600), // 10 minute cache
  async (req, res) => {
    try {
      const authHeader = req.headers.authorization
      const location = req.params.location
      const limit = parseInt(req.query.limit as string) || 20
      
      const filters: PropertySearchFilters = { location }
      const result = await getProperties(authHeader, filters)
      
      res.json({
        success: true,
        data: result.properties.slice(0, limit),
        total: result.total
      })
    } catch (error) {
      console.error('Properties by location error:', error)
      res.status(500).json({ 
        success: false, 
        message: error instanceof Error ? error.message : 'Failed to fetch properties by location' 
      })
    }
  }
)