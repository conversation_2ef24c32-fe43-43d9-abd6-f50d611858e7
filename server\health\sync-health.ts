// Health check for sync service
import { GeoNamesSyncService } from '../services/geonames/sync-service';

async function healthCheck(): Promise<void> {
  const syncService = new GeoNamesSyncService();
  
  try {
    const health = await syncService.getHealthStatus();
    
    if (health.status === 'healthy') {
      console.log('Health check: OK');
      process.exit(0);
    } else {
      console.log('Health check: DEGRADED');
      console.log('Issues:', health.checks.filter(c => c.status === 'fail'));
      process.exit(1);
    }
  } catch (error) {
    console.error('Health check: ERROR');
    console.error(error);
    process.exit(1);
  }
}

healthCheck();