import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useTranslations } from '@/lib/translations';
import { Heart, MapPin, Star, Eye } from 'lucide-react';

interface WishlistsContentProps {
  wishlists: any[];
}

const WishlistsContent: React.FC<WishlistsContentProps> = ({ wishlists }) => {
  const t = useTranslations('guestDashboard');

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-semibold text-gray-900">{t('wishlists.title')}</h2>
        <p className="text-gray-600 mt-2">{t('wishlistPage.description')}</p>
      </div>

      {wishlists.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {wishlists.map((wishlist: any) => (
            <Card key={wishlist.id} className="cursor-pointer hover:shadow-lg transition-shadow">
              <CardContent className="p-4">
                <div className="aspect-video bg-gray-200 rounded-lg mb-4 flex items-center justify-center">
                  <MapPin className="h-8 w-8 text-gray-400" />
                </div>
                <div className="flex items-start justify-between mb-2">
                  <h3 className="font-semibold text-lg">{wishlist.property?.title || t('wishlistPage.property')}</h3>
                  <Button variant="ghost" size="sm" className="text-red-500 hover:text-red-600">
                    <Heart className="h-4 w-4 fill-current" />
                  </Button>
                </div>
                <p className="text-gray-600 text-sm mb-2">{wishlist.property?.location || t('wishlistPage.location')}</p>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className="flex items-center">
                      <Star className="h-4 w-4 text-yellow-400 fill-current" />
                      <span className="text-sm font-medium ml-1">
                        {wishlist.property?.rating || '4.5'}
                      </span>
                    </div>
                    <Badge variant="outline" className="text-xs">
{wishlist.property?.reviews_count || 0} {t('wishlistPage.reviews')}
                    </Badge>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold">
                      {formatCurrency(wishlist.property?.price_per_night || 0)}
                    </p>
                    <p className="text-xs text-gray-500">{t('wishlistPage.perNight')}</p>
                  </div>
                </div>
                <Button variant="outline" className="w-full mt-4">
                  <Eye className="h-4 w-4 mr-2" />
{t('wishlistPage.viewProperty')}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <Heart className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600">{t('wishlists.noWishlists')}</p>
          <p className="text-sm text-gray-500 mt-2">{t('wishlists.startSaving')}</p>
        </div>
      )}
    </div>
  );
};

export default WishlistsContent;