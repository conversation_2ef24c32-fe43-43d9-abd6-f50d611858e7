// Enhanced location search service with GeoNames data
import { SupabaseClient } from '@supabase/supabase-js';
import { supabase } from '../../supabase';
import { LocationResult, LocationWithNames, LocationSearchParams } from './types';

export class GeoNamesSearchService {
  private db: SupabaseClient;

  constructor() {
    this.db = supabase;
  }

  async searchLocations(params: LocationSearchParams): Promise<LocationResult[]> {
    const {
      query,
      language = 'en',
      limit = 10,
      country,
      userCoordinates,
      minPopulation = 0
    } = params;

    try {
      // Build search query
      let dbQuery = this.db
        .from('geonames_locations')
        .select(`
          id,
          geonames_id,
          name,
          ascii_name,
          country_code,
          admin1_code,
          admin2_code,
          feature_class,
          feature_code,
          latitude,
          longitude,
          population,
          elevation,
          timezone,
          popularity_score,
          property_count
        `);

      // Apply filters
      if (country) {
        dbQuery = dbQuery.eq('country_code', country.toUpperCase());
      }

      if (minPopulation > 0) {
        dbQuery = dbQuery.gte('population', minPopulation);
      }

      // Apply text search based on query type
      if (query.length >= 2) {
        if (this.isCoordinateQuery(query)) {
          // Handle coordinate-based search
          const coords = this.parseCoordinates(query);
          if (coords) {
            dbQuery = dbQuery
              .gte('latitude', coords.lat - 0.5)
              .lte('latitude', coords.lat + 0.5)
              .gte('longitude', coords.lng - 0.5)
              .lte('longitude', coords.lng + 0.5);
          }
        } else {
          // Text-based search with language-aware ranking
          const searchTerm = this.normalizeSearchTerm(query);
          
          // Simple text search without database function for now
          dbQuery = dbQuery.or(`name.ilike.%${searchTerm}%,ascii_name.ilike.%${searchTerm}%`);
        }
      }

      // Apply ordering and limits with increased limit for deduplication
      dbQuery = dbQuery
        .order('popularity_score', { ascending: false })
        .order('population', { ascending: false })
        .limit(limit * 3); // Get more results to allow for deduplication

      const { data: locations, error } = await dbQuery;

      if (error) {
        console.error('[SEARCH] Database query error:', error);
        throw error;
      }

      if (!locations?.length) {
        return [];
      }

      // Deduplicate locations by name and admin1_code before converting
      const uniqueLocations = this.deduplicateLocations(locations);
      
      // Limit to requested number after deduplication
      const limitedLocations = uniqueLocations.slice(0, limit);
      
      // Convert to LocationResult format with distance calculation
      const results = limitedLocations.map(location => {
        const result: LocationResult = {
          id: location.id,
          geonames_id: location.geonames_id,
          name: location.name,
          ascii_name: location.ascii_name,
          country_code: location.country_code,
          admin1_code: location.admin1_code,
          admin2_code: location.admin2_code,
          feature_class: location.feature_class,
          feature_code: location.feature_code,
          coordinates: { 
            lat: location.latitude, 
            lng: location.longitude 
          },
          latitude: location.latitude,
          longitude: location.longitude,
          population: location.population,
          elevation: location.elevation,
          timezone: location.timezone,
          popularity_score: location.popularity_score,
          property_count: location.property_count
        };

        // Calculate distance if user coordinates provided
        if (userCoordinates) {
          result.distance_km = this.calculateDistance(
            userCoordinates.lat,
            userCoordinates.lng,
            location.latitude,
            location.longitude
          );
        }

        // Calculate relevance score
        result.relevance_score = this.calculateRelevanceScore(
          location,
          query,
          userCoordinates
        );

        // Add enhanced display name with regional context for disambiguation
        result.display_name = this.createDisplayName(location);
        result.region_name = this.getRegionName(location);
        result.country_name = this.getCountryName(location.country_code);
        
        // Also populate standard fields for backward compatibility
        result.country_name = result.country_name;
        result.admin1_name = result.region_name;

        return result;
      });

      return results;

    } catch (error) {
      console.error('[SEARCH] Search locations error:', error);
      throw error;
    }
  }

  private isCoordinateQuery(query: string): boolean {
    const coordPattern = /^-?\d+\.?\d*,\s*-?\d+\.?\d*$/;
    return coordPattern.test(query.trim());
  }

  private parseCoordinates(query: string): { lat: number; lng: number } | null {
    try {
      const parts = query.trim().split(',').map(part => parseFloat(part.trim()));
      if (parts.length === 2 && !isNaN(parts[0]) && !isNaN(parts[1])) {
        return { lat: parts[0], lng: parts[1] };
      }
      return null;
    } catch {
      return null;
    }
  }

  private normalizeSearchTerm(query: string): string {
    return query
      .trim()
      .toLowerCase()
      .replace(/[àáâãäå]/g, 'a')
      .replace(/[èéêë]/g, 'e')
      .replace(/[ìíîï]/g, 'i')
      .replace(/[òóôõö]/g, 'o')
      .replace(/[ùúûü]/g, 'u')
      .replace(/[ñ]/g, 'n')
      .replace(/[ç]/g, 'c')
      .replace(/[^a-z0-9\s]/g, '');
  }

  private calculateDistance(
    lat1: number, 
    lng1: number, 
    lat2: number, 
    lng2: number
  ): number {
    const R = 6371; // Earth's radius in kilometers
    const dLat = this.degToRad(lat2 - lat1);
    const dLng = this.degToRad(lng2 - lng1);
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(this.degToRad(lat1)) * Math.cos(this.degToRad(lat2)) * 
      Math.sin(dLng/2) * Math.sin(dLng/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    const distance = R * c;
    return Math.round(distance * 100) / 100;
  }

  private degToRad(deg: number): number {
    return deg * (Math.PI/180);
  }

  private calculateRelevanceScore(
    location: any,
    query: string,
    userCoordinates?: { lat: number; lng: number }
  ): number {
    let score = 0;

    // Name match scoring
    const queryLower = query.toLowerCase();
    const nameLower = location.name.toLowerCase();
    const asciiNameLower = location.ascii_name?.toLowerCase();

    if (nameLower === queryLower || asciiNameLower === queryLower) {
      score += 100; // Exact match
    } else if (nameLower.startsWith(queryLower) || asciiNameLower?.startsWith(queryLower)) {
      score += 80; // Prefix match
    } else if (nameLower.includes(queryLower) || asciiNameLower?.includes(queryLower)) {
      score += 60; // Contains match
    }

    // Population scoring
    if (location.population > 100000) score += 20;
    else if (location.population > 10000) score += 10;
    else if (location.population > 1000) score += 5;

    // Feature type scoring
    if (location.feature_class === 'P') {
      if (location.feature_code === 'PPLC') score += 25; // Capital
      else if (location.feature_code === 'PPLA') score += 20; // Admin center
      else if (location.feature_code === 'PPL') score += 15; // Populated place
    }

    // Distance scoring (if user coordinates provided)
    if (userCoordinates) {
      const distance = this.calculateDistance(
        userCoordinates.lat,
        userCoordinates.lng,
        location.latitude,
        location.longitude
      );
      if (distance < 50) score += 10;
      else if (distance < 100) score += 5;
    }

    // Popularity scoring
    if (location.popularity_score) {
      score += Math.min(location.popularity_score / 10, 10);
    }

    return Math.round(score);
  }

  private deduplicateLocations(locations: any[]): any[] {
    const groupedByName = new Map<string, any[]>();
    
    // Group locations by name first
    for (const location of locations) {
      const nameKey = location.name.toLowerCase();
      if (!groupedByName.has(nameKey)) {
        groupedByName.set(nameKey, []);
      }
      groupedByName.get(nameKey)!.push(location);
    }
    
    const uniqueLocations: any[] = [];
    
    // Process each name group
    for (const [name, nameGroup] of groupedByName) {
      if (nameGroup.length === 1) {
        // Only one location with this name, keep it
        uniqueLocations.push(nameGroup[0]);
      } else {
        // Multiple locations with same name
        // First try to separate by admin1_code (different regions)
        const regionGroups = this.groupByRegion(nameGroup);
        
        for (const regionGroup of regionGroups) {
          if (regionGroup.length === 1) {
            // Single location in this region
            uniqueLocations.push(regionGroup[0]);
          } else {
            // Multiple in same region - use coordinate clustering
            const clusters = this.clusterLocationsByDistance(regionGroup, 0.01); // ~1km radius
            
            // Keep the best representative from each cluster
            for (const cluster of clusters) {
              const best = this.getBestLocationFromCluster(cluster);
              uniqueLocations.push(best);
            }
          }
        }
      }
    }
    
    return uniqueLocations;
  }

  private groupByRegion(locations: any[]): any[][] {
    const regionMap = new Map<string, any[]>();
    
    for (const location of locations) {
      // Create region key including admin1_code and admin2_code for better separation
      const regionKey = `${location.admin1_code || 'none'}-${location.admin2_code || 'none'}`;
      
      if (!regionMap.has(regionKey)) {
        regionMap.set(regionKey, []);
      }
      regionMap.get(regionKey)!.push(location);
    }
    
    return Array.from(regionMap.values());
  }

  private clusterLocationsByDistance(locations: any[], maxDistance: number): any[][] {
    const clusters: any[][] = [];
    const used = new Set<number>();
    
    for (let i = 0; i < locations.length; i++) {
      if (used.has(i)) continue;
      
      const cluster = [locations[i]];
      used.add(i);
      
      // Find nearby locations
      for (let j = i + 1; j < locations.length; j++) {
        if (used.has(j)) continue;
        
        const distance = this.calculateDistance(
          locations[i].latitude,
          locations[i].longitude,
          locations[j].latitude,
          locations[j].longitude
        );
        
        if (distance <= maxDistance) {
          cluster.push(locations[j]);
          used.add(j);
        }
      }
      
      clusters.push(cluster);
    }
    
    return clusters;
  }

  private getBestLocationFromCluster(cluster: any[]): any {
    return cluster.reduce((best, current) => {
      const bestPriority = this.getLocationPriority(best);
      const currentPriority = this.getLocationPriority(current);
      return currentPriority > bestPriority ? current : best;
    });
  }

  private getLocationPriority(location: any): number {
    let priority = 0;
    
    // Feature class priority (cities > towns > areas > etc)
    if (location.feature_class === 'P') {
      if (location.feature_code === 'PPLA') priority += 1000; // Administrative capital
      else if (location.feature_code === 'PPL') priority += 500; // Populated place
      else if (location.feature_code === 'PPLC') priority += 2000; // National capital
    }
    
    // Population priority
    if (location.population && location.population > 0) {
      priority += Math.min(location.population / 1000, 500);
    }
    
    // Popularity score priority
    if (location.popularity_score && location.popularity_score > 0) {
      priority += location.popularity_score * 10;
    }
    
    return priority;
  }

  private createDisplayName(location: any): string {
    let displayName = location.name;
    
    // Add regional context for disambiguation
    const regionParts: string[] = [];
    
    // Add admin1 (state/province/region)
    if (location.admin1_code) {
      const regionName = this.getRegionName(location);
      if (regionName && regionName !== location.name) {
        regionParts.push(regionName);
      }
    }
    
    // Add country
    const countryName = this.getCountryName(location.country_code);
    if (countryName) {
      regionParts.push(countryName);
    }
    
    // Format: "City, Region, Country" or "City, Country"
    if (regionParts.length > 0) {
      displayName += ', ' + regionParts.join(', ');
    }
    
    return displayName;
  }

  private getRegionName(location: any): string {
    // Spanish administrative regions mapping
    const spanishRegions: { [key: string]: string } = {
      'AN': 'Andalucía',
      'AR': 'Aragón', 
      'AS': 'Asturias',
      'IB': 'Baleares',
      'PV': 'País Vasco',
      'CN': 'Canarias',
      'CB': 'Cantabria',
      'CM': 'Castilla-La Mancha',
      'CL': 'Castilla y León',
      'CT': 'Cataluña',
      'EX': 'Extremadura',
      'GA': 'Galicia',
      'MD': 'Madrid',
      'MC': 'Murcia',
      'NC': 'Navarra',
      'RI': 'La Rioja',
      'VC': 'Valencia',
      'CE': 'Ceuta',
      'ML': 'Melilla'
    };
    
    return spanishRegions[location.admin1_code] || location.admin1_code || '';
  }

  private getCountryName(countryCode: string): string {
    const countryNames: { [key: string]: string } = {
      'ES': 'España',
      'FR': 'France',
      'IT': 'Italia',
      'PT': 'Portugal',
      'DE': 'Deutschland',
      'GB': 'United Kingdom',
      'US': 'United States',
      'CA': 'Canada'
    };
    
    return countryNames[countryCode] || countryCode || '';
  }

  // Alias method for backward compatibility
  async autocompleteSearch(params: LocationSearchParams): Promise<LocationResult[]> {
    return this.searchLocations(params);
  }

  async getLocationWithNames(locationId: string, language?: string): Promise<LocationWithNames | null> {
    try {
      const { data: location, error: locationError } = await this.db
        .from('geonames_locations')
        .select('*')
        .eq('id', locationId)
        .single();

      if (locationError || !location) {
        console.warn('[SEARCH] Location not found:', locationError);
        return null;
      }

      // Get alternate names for this location
      const { data: names, error: namesError } = await this.db
        .from('geonames_location_names')
        .select('*')
        .eq('geonames_id', location.geonames_id);

      if (namesError) {
        console.warn('[SEARCH] Failed to fetch alternate names:', namesError);
        // Return location without alternate names
        return {
          ...location,
          coordinates: { lat: location.latitude, lng: location.longitude },
          alternate_names: []
        };
      }

      return {
        ...location,
        coordinates: { lat: location.latitude, lng: location.longitude },
        alternate_names: names || []
      };

    } catch (error) {
      console.error('[SEARCH] Get location with names error:', error);
      return null;
    }
  }

  async getPopularDestinations(country?: string, language = 'en', limit = 20): Promise<LocationResult[]> {
    try {
      let query = this.db
        .from('geonames_locations')
        .select('*');

      if (country) {
        query = query.eq('country_code', country.toUpperCase());
      }

      // Order by popularity and population
      query = query
        .order('popularity_score', { ascending: false })
        .order('population', { ascending: false })
        .limit(limit);

      const { data: locations, error } = await query;

      if (error) {
        console.error('[SEARCH] Popular destinations error:', error);
        throw error;
      }

      if (!locations?.length) {
        return [];
      }

      // Convert to LocationResult format
      return locations.map(location => ({
        id: location.id,
        geonames_id: location.geonames_id,
        name: location.name,
        ascii_name: location.ascii_name,
        country_code: location.country_code,
        admin1_code: location.admin1_code,
        admin2_code: location.admin2_code,
        feature_class: location.feature_class,
        feature_code: location.feature_code,
        coordinates: { 
          lat: location.latitude, 
          lng: location.longitude 
        },
        latitude: location.latitude,
        longitude: location.longitude,
        population: location.population,
        elevation: location.elevation,
        timezone: location.timezone,
        popularity_score: location.popularity_score,
        property_count: location.property_count,
        relevance_score: location.popularity_score
      }));

    } catch (error) {
      console.error('[SEARCH] Popular destinations error:', error);
      throw error;
    }
  }
}