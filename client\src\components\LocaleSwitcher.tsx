import { Globe } from 'lucide-react';
import { Button } from './ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from './ui/dropdown-menu';
import { useLocale } from '@/lib/i18n';
import { locales } from '@/lib/i18n';

// Import flag icons from country-flag-icons
const getFlagIcon = (countryCode: string) => {
  return `https://flagcdn.com/w20/${countryCode.toLowerCase()}.png`;
};

const languageData: Record<string, { name: string; countryCode: string }> = {
  en: { name: 'English', countryCode: 'GB' },
  nl: { name: 'Nederlands', countryCode: 'NL' }
};

export function LocaleSwitcher() {
  const { locale, setLocale } = useLocale();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="rounded-full hover:bg-secondary/80 transition-colors"
        >
          <Globe className="h-5 w-5 text-foreground/70" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {locales.map((loc) => (
          <DropdownMenuItem
            key={loc}
            onClick={() => setLocale(loc)}
            className={`${locale === loc ? 'bg-secondary' : ''}`}
          >
            <div className="flex items-center space-x-3">
              <img 
                src={getFlagIcon(languageData[loc]?.countryCode)}
                alt={`${languageData[loc]?.name} flag`}
                className="w-5 h-4 rounded-sm border border-gray-200 shadow-sm object-cover flex-shrink-0"
              />
              <span>{languageData[loc]?.name}</span>
            </div>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}