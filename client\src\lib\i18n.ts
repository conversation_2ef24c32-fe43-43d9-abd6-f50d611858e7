import { createContext, useContext } from 'react';

export const defaultLocale = 'nl';
export const locales = ['en', 'nl'] as const;
export type Locale = (typeof locales)[number];

export type Messages = {
  [key: string]: any;
};

export const getTranslations = async (locale: Locale): Promise<Messages> => {
  try {
    const response = await fetch(`/api/translations/${locale}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch translations for ${locale}`);
    }
    return response.json();
  } catch (error) {
    // Fallback to English if the locale API fails
    try {
      const fallbackResponse = await fetch('/api/translations/en');
      if (!fallbackResponse.ok) {
        throw new Error('Failed to fetch fallback translations');
      }
      return fallbackResponse.json();
    } catch (fallbackError) {
      // Final fallback to prevent crashes
      console.error('Failed to fetch translations:', fallbackError);
      return {};
    }
  }
};

interface LocaleContextType {
  locale: Locale;
  setLocale: (locale: Locale) => void;
}

export const LocaleContext = createContext<LocaleContextType>({
  locale: defaultLocale,
  setLocale: () => {}
});

export const useLocale = () => useContext(LocaleContext);