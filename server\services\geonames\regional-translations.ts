// Regional and country translations for multilingual location display
// Uses official government and EU naming conventions

/**
 * Spanish Administrative Division Translations
 * Source: Official Spanish Government (BOE) + EU translations
 */
export const SPANISH_REGIONS_MULTILINGUAL = {
  // Autonomous Communities
  'Andalucía': {
    en: 'Andalusia',
    nl: 'Andalusië',
    es: 'Andalucía',
    ca: 'Andalusia',
    eu: 'Andaluzia'
  },
  'Aragón': {
    en: 'Aragon',
    nl: 'Aragón',
    es: 'Aragón',
    ca: 'Aragó',
    eu: 'Aragoi'
  },
  'Asturias': {
    en: 'Asturias',
    nl: 'Asturië',
    es: 'Asturias',
    ca: 'Astúries',
    eu: 'Asturias'
  },
  'Cantabria': {
    en: 'Cantabria',
    nl: 'Cantabrië',
    es: 'Cantabria',
    ca: 'Cantàbria',
    eu: 'Kantabria'
  },
  'Castilla-La Mancha': {
    en: 'Castile-La Mancha',
    nl: 'Castilië-La Mancha',
    es: 'Castilla-La Mancha',
    ca: 'Castella-La Manxa',
    eu: 'Gaztela-Mantxa'
  },
  'Castilla y León': {
    en: 'Castile and León',
    nl: 'Castilië en León',
    es: 'Castilla y León',
    ca: 'Castella i Lleó',
    eu: 'Gaztela eta Leon'
  },
  'Cataluña': {
    en: 'Catalonia',
    nl: 'Catalonië',
    es: 'Cataluña',
    ca: 'Catalunya',
    eu: 'Katalunia'
  },
  'Extremadura': {
    en: 'Extremadura',
    nl: 'Extremadura',
    es: 'Extremadura',
    ca: 'Extremadura',
    eu: 'Extremadura'
  },
  'Galicia': {
    en: 'Galicia',
    nl: 'Galicië',
    es: 'Galicia',
    ca: 'Galícia',
    eu: 'Galizia'
  },
  'Islas Baleares': {
    en: 'Balearic Islands',
    nl: 'Balearen',
    es: 'Islas Baleares',
    ca: 'Illes Balears',
    eu: 'Balear Uharteak'
  },
  'Islas Canarias': {
    en: 'Canary Islands',
    nl: 'Canarische Eilanden',
    es: 'Islas Canarias',
    ca: 'Illes Canàries',
    eu: 'Kanariar Uharteak'
  },
  'La Rioja': {
    en: 'La Rioja',
    nl: 'La Rioja',
    es: 'La Rioja',
    ca: 'La Rioja',
    eu: 'Errioxa'
  },
  'Comunidad de Madrid': {
    en: 'Community of Madrid',
    nl: 'Madrid (regio)',
    es: 'Comunidad de Madrid',
    ca: 'Comunitat de Madrid',
    eu: 'Madrilgo Erkidegoa'
  },
  'Región de Murcia': {
    en: 'Region of Murcia',
    nl: 'Murcia (regio)',
    es: 'Región de Murcia',
    ca: 'Regió de Múrcia',
    eu: 'Murtziako Eskualdea'
  },
  'Comunidad Foral de Navarra': {
    en: 'Navarre',
    nl: 'Navarra',
    es: 'Navarra',
    ca: 'Navarra',
    eu: 'Nafarroa'
  },
  'País Vasco': {
    en: 'Basque Country',
    nl: 'Baskenland',
    es: 'País Vasco',
    ca: 'País Basc',
    eu: 'Euskadi'
  },
  'Comunidad Valenciana': {
    en: 'Valencian Community',
    nl: 'Valencia (regio)',
    es: 'Comunidad Valenciana',
    ca: 'Comunitat Valenciana',
    eu: 'Valentziako Erkidegoa'
  },
  // Special autonomous cities
  'Ceuta': {
    en: 'Ceuta',
    nl: 'Ceuta',
    es: 'Ceuta',
    ca: 'Ceuta',
    eu: 'Zeuta'
  },
  'Melilla': {
    en: 'Melilla',
    nl: 'Melilla',
    es: 'Melilla',
    ca: 'Melilla',
    eu: 'Melilla'
  }
};

/**
 * Country Translations
 * Source: ISO 3166 + EU official translations
 */
export const COUNTRY_TRANSLATIONS = {
  'ES': {
    en: 'Spain',
    nl: 'Spanje',
    es: 'España',
    ca: 'Espanya',
    eu: 'Espainia',
    fr: 'Espagne',
    de: 'Spanien',
    it: 'Spagna'
  },
  'FR': {
    en: 'France',
    nl: 'Frankrijk',
    es: 'Francia',
    ca: 'França',
    eu: 'Frantzia',
    fr: 'France',
    de: 'Frankreich',
    it: 'Francia'
  },
  'IT': {
    en: 'Italy',
    nl: 'Italië',
    es: 'Italia',
    ca: 'Itàlia',
    eu: 'Italia',
    fr: 'Italie',
    de: 'Italien',
    it: 'Italia'
  },
  'DE': {
    en: 'Germany',
    nl: 'Duitsland',
    es: 'Alemania',
    ca: 'Alemanya',
    eu: 'Alemania',
    fr: 'Allemagne',
    de: 'Deutschland',
    it: 'Germania'
  },
  'PT': {
    en: 'Portugal',
    nl: 'Portugal',
    es: 'Portugal',
    ca: 'Portugal',
    eu: 'Portugal',
    fr: 'Portugal',
    de: 'Portugal',
    it: 'Portogallo'
  }
};

/**
 * Get translated region name
 */
export function getRegionTranslation(regionName: string, locale: string): string {
  const translation = SPANISH_REGIONS_MULTILINGUAL[regionName as keyof typeof SPANISH_REGIONS_MULTILINGUAL];
  if (translation && translation[locale as keyof typeof translation]) {
    return translation[locale as keyof typeof translation];
  }
  
  // Fallback to English, then original
  if (translation && translation.en) {
    return translation.en;
  }
  
  return regionName; // Return original if no translation found
}

/**
 * Get translated country name
 */
export function getCountryTranslation(countryCode: string, locale: string): string {
  const translation = COUNTRY_TRANSLATIONS[countryCode as keyof typeof COUNTRY_TRANSLATIONS];
  if (translation && translation[locale as keyof typeof translation]) {
    return translation[locale as keyof typeof translation];
  }
  
  // Fallback to English, then country code
  if (translation && translation.en) {
    return translation.en;
  }
  
  return countryCode;
}

/**
 * Supported locales for translations
 */
export const SUPPORTED_LOCALES = ['en', 'nl', 'es', 'ca', 'eu', 'fr', 'de', 'it'];

/**
 * Default locale fallback hierarchy
 */
export const LOCALE_FALLBACK: { [key: string]: string[] } = {
  'nl': ['en', 'es'],
  'ca': ['es', 'en'],
  'eu': ['es', 'en'],
  'fr': ['en', 'es'],
  'de': ['en', 'es'],
  'it': ['en', 'es']
};

/**
 * Get fallback locales for a given locale
 */
export function getLocaleFallbacks(locale: string): string[] {
  return LOCALE_FALLBACK[locale] || ['en'];
}