import express, { type Express } from "express";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

// ES module equivalent of __dirname
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export function log(message: string, source = "express") {
  const formattedTime = new Date().toLocaleTimeString("en-US", {
    hour: "numeric",
    minute: "2-digit",
    second: "2-digit",
    hour12: true,
  });

  console.log(`${formattedTime} [${source}] ${message}`);
}

export function serveStatic(app: Express) {
  // For Docker production build, static files are in /app/dist/public
  const possiblePaths = [
    path.resolve(process.cwd(), "dist", "public"),  // Docker: /app/dist/public
    path.resolve(process.cwd(), "dist"),            // Alternative: /app/dist
    path.resolve(__dirname, "..", "dist", "public"), // Relative to server dir
    path.resolve(__dirname, "..", "dist"),          // Alternative relative
  ];

  let validDistPath = null;
  for (const testPath of possiblePaths) {
    if (fs.existsSync(testPath)) {
      validDistPath = testPath;
      break;
    }
  }

  if (!validDistPath) {
    const pathsChecked = possiblePaths.join(", ");
    log(`Error: Could not find build directory. Checked paths: ${pathsChecked}`, "static-server");
    
    // List directory contents for debugging
    try {
      const currentDir = fs.readdirSync(process.cwd());
      log(`Current directory contents: ${currentDir.join(", ")}`, "static-server");
      
      const distExists = fs.existsSync(path.resolve(process.cwd(), "dist"));
      if (distExists) {
        const distContents = fs.readdirSync(path.resolve(process.cwd(), "dist"));
        log(`Dist directory contents: ${distContents.join(", ")}`, "static-server");
      }
    } catch (e) {
      log(`Error reading directory: ${(e as Error).message}`, "static-server");
    }
    
    throw new Error(
      `Could not find the build directory. Checked paths: ${pathsChecked}. Make sure to build the client first.`
    );
  }

  log(`Serving static files from: ${validDistPath}`, "static-server");

  app.use(express.static(validDistPath));

  // fall through to index.html if the file doesn't exist
  app.use("*", (_req, res) => {
    const indexPath = path.resolve(validDistPath, "index.html");
    if (fs.existsSync(indexPath)) {
      res.sendFile(indexPath);
    } else {
      log(`Index file not found at: ${indexPath}`, "static-server");
      res.status(404).send("Index file not found");
    }
  });
}