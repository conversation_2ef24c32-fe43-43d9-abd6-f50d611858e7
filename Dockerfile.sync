# Dockerfile for GeoNames sync service
FROM node:20-alpine

WORKDIR /app

# Install system dependencies
RUN apk add --no-cache curl

# Copy package files
COPY package*.json ./
COPY tsconfig.json ./

# Install dependencies
RUN npm ci --only=production --silent

# Copy source code (only what's needed for sync)
COPY server/config ./server/config
COPY server/utils ./server/utils
COPY server/dal ./server/dal
COPY server/services/geonames ./server/services/geonames
COPY server/supabase.ts ./server/supabase.ts
COPY database/scripts ./database/scripts
COPY shared ./shared

# Build TypeScript
RUN npm run build

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S geonames -u 1001 -G nodejs

# Create data directory for downloads
RUN mkdir -p /app/data && chown geonames:nodejs /app/data

USER geonames

# Set environment
ENV NODE_ENV=production
ENV SERVICE_TYPE=sync

# Health check for Railway
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD node dist/server/health/sync-health.js || exit 1

# Default command
CMD ["node", "dist/server/services/geonames/sync-runner.js"]