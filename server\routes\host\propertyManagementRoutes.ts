import { Router } from "express";
import { propertyManagementController } from "../../controllers/host/propertyManagementController";

const router = Router();

// Property routes
router.get("/properties", propertyManagementController.getProperties);
router.get("/properties/:id", propertyManagementController.getProperty);
router.post("/properties", propertyManagementController.createProperty);
router.put("/properties/:id", propertyManagementController.updateProperty);

// Property draft routes
router.post("/properties/drafts", propertyManagementController.createPropertyDraft);
router.get("/properties/drafts/:id", propertyManagementController.getPropertyDraft);
router.patch("/properties/drafts/:id", propertyManagementController.updatePropertyDraft);
router.delete("/properties/drafts/:id", propertyManagementController.deletePropertyDraft);
router.post("/properties/drafts/:id/publish", propertyManagementController.publishPropertyDraft);

// Booking routes
router.get("/bookings", propertyManagementController.getBookings);
router.post("/bookings", propertyManagementController.createBooking);
router.put("/bookings/:id", propertyManagementController.updateBooking);

// Review routes
router.get("/reviews", propertyManagementController.getReviews);
router.post("/reviews", propertyManagementController.createReview);

// Analytics routes
router.get("/earnings", propertyManagementController.getOwnerEarnings);
router.get("/metrics", propertyManagementController.getPropertyMetrics);

export default router;