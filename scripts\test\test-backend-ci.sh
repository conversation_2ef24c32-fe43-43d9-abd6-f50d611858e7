#!/bin/bash
# Test script to emulate CI/CD backend build without Supabase credentials

echo "Testing backend build without Supabase credentials..."

# Save existing environment variables
ORIGINAL_SUPABASE_URL=$SUPABASE_URL
ORIGINAL_SUPABASE_ANON_KEY=$SUPABASE_ANON_KEY

# Clear Supabase environment variables to simulate CI/CD environment
unset SUPABASE_URL
unset SUPABASE_ANON_KEY

# Build the application
echo "Building application..."
npm run build

# Check if build was successful
if [ $? -ne 0 ]; then
    echo "❌ Build failed"
    exit 1
fi

echo "✅ Build successful"

# Start the application in background
echo "Starting application..."
NODE_ENV=production node dist/index.js &
APP_PID=$!

# Wait for application to start
sleep 3

# Test health endpoint
echo "Testing health endpoint..."
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:5000/api/health)

if [ "$HTTP_STATUS" = "200" ]; then
    echo "✅ Health endpoint responding successfully"
    curl -s http://localhost:5000/api/health | head -n 1
else
    echo "❌ Health endpoint failed with status: $HTTP_STATUS"
    kill $APP_PID
    exit 1
fi

# Clean up
kill $APP_PID

# Restore original environment variables
export SUPABASE_URL=$ORIGINAL_SUPABASE_URL
export SUPABASE_ANON_KEY=$ORIGINAL_SUPABASE_ANON_KEY

echo "✅ Backend CI test completed successfully"